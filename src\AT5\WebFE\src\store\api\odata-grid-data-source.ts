import { ODataFilterBuilder } from 'odata-filter-builder';
import type { GridDataSource, GridGetRowsParams, GridGetRowsResponse, GridUpdateRowParams } from '@mui/x-data-grid-premium';
import { store } from '../index';
import { type ODataParams } from './jobs-api';
import { StartQueryActionCreatorOptions } from '@reduxjs/toolkit/query';

export function createRTKODataGridDataSource(
  initiate: (arg: any, options?: StartQueryActionCreatorOptions) => any,
  getSelectFields?: () => string[] | undefined,
  additionalArgs?: any
): GridDataSource {
  return {
    getRows: async (params: GridGetRowsParams): Promise<GridGetRowsResponse> => {
      const odataParams: ODataParams = {
        $count: true,
      };

      // Use pagination from params (which now comes from Redux state via the grid)
      if (params.paginationModel) {
        odataParams.$top = params.paginationModel.pageSize;
        odataParams.$skip = params.paginationModel.page * params.paginationModel.pageSize;
      }

      // Use sorting from params (which now comes from Redux state via the grid)
      if (params.sortModel && params.sortModel.length > 0) {
        const sortItems = params.sortModel.map(item =>
          `${item.field} ${item.sort === 'desc' ? 'desc' : 'asc'}`
        );
        odataParams.$orderby = sortItems.join(', ');
      }

      // Handle filtering ($filter) - robust handling for v7 filter model shape and operator aliases
      const filterModel: any = params?.filterModel ?? {};
      const items: Array<{ field: string; operator?: string; operatorValue?: string; value?: any }>
        = Array.isArray(filterModel.items) ? filterModel.items : [];

      if (items.length > 0) {
        const builder = new ODataFilterBuilder();

        for (const rawItem of items) {
          const op = (rawItem.operator ?? (rawItem as any).operatorValue ?? '').toString();
          const field = rawItem.field;
          const value = rawItem.value;

          // Skip items that require a value but don't have one
          const operatorRequiresValue = !['isEmpty', 'isNotEmpty'].includes(op);
          if (operatorRequiresValue && (value === undefined || value === null || value === '')) {
            continue;
          }

          switch (op) {
            // String operations
            case 'contains':
              builder.contains(field, value);
              break;
            case 'equals':
              builder.eq(field, value);
              break;
            case 'startsWith':
              builder.startsWith(field, value);
              break;
            case 'endsWith':
              builder.endsWith(field, value);
              break;
            case 'is':
              builder.eq(field, value);
              break;
            case 'not':
            case 'isNot':
            case '!=':
              builder.ne(field, value);
              break;

            // Numeric / date comparisons (aliases included)
            case 'after':
            case '>':
              builder.gt(field, value);
              break;
            case 'onOrAfter':
            case '>=':
              builder.ge(field, value);
              break;
            case 'before':
            case '<':
              builder.lt(field, value);
              break;
            case 'onOrBefore':
            case '<=':
              builder.le(field, value);
              break;

            // Empty checks
            case 'isEmpty':
              builder.eq(field, null);
              break;
            case 'isNotEmpty':
              builder.ne(field, null);
              break;
          }
        }

        const filterString = builder.toString();
        if (filterString) {
          odataParams.$filter = filterString;
        }
      }

      // Handle field selection ($select) - use columns from the Columns menu (visible, non-action columns)
      try {
        const selectedFields = getSelectFields?.();
        if (selectedFields && selectedFields.length > 0) {
          const uniqueFields = Array.from(new Set(selectedFields.filter(Boolean)));
          // Ensure 'id' is included for row identification
          if (!uniqueFields.includes('id')) uniqueFields.unshift('id');
          odataParams.$select = uniqueFields.join(',');
        }
      } catch {
        // Ignore select field errors and fall back to server defaults
      }

      try {
        const params = additionalArgs ? {...odataParams, ...additionalArgs} : odataParams;

        const result = await store.dispatch(
          initiate(params)
        );

        if (result.error) {
          throw new Error('Failed to fetch jobs data');
        }

        const responseData = result.data;

        return {
          rows: responseData?.value ?? [],
          rowCount: responseData?.count ?? responseData?.value?.length ?? 0,
        };
      } catch (error) {
        console.error('Error fetching grid data:', error);
        throw error;
      }
    },

    updateRow: async (params: GridUpdateRowParams): Promise<any> => {
      console.log('Update row params:', params);
      return Promise.resolve();
    },
  };
}
