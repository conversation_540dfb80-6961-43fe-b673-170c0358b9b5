{"version": 3, "sources": ["../../@mui/material/esm/OutlinedInput/outlinedInputClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { inputBaseClasses } from \"../InputBase/index.js\";\nexport function getOutlinedInputUtilityClass(slot) {\n  return generateUtilityClass('MuiOutlinedInput', slot);\n}\nconst outlinedInputClasses = {\n  ...inputBaseClasses,\n  ...generateUtilityClasses('MuiOutlinedInput', ['root', 'notchedOutline', 'input'])\n};\nexport default outlinedInputClasses;"], "mappings": ";;;;;;;;;AAGO,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,oBAAoB,IAAI;AACtD;AACA,IAAM,uBAAuB;AAAA,EAC3B,GAAG;AAAA,EACH,GAAG,uBAAuB,oBAAoB,CAAC,QAAQ,kBAAkB,OAAO,CAAC;AACnF;AACA,IAAO,+BAAQ;", "names": []}