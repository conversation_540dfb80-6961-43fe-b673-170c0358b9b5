import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { GridRowId, GridFilterModel } from '@mui/x-data-grid-premium';

// Types for the jobs UI state
export interface JobsGridConfig {
  columnVisibility: Record<string, boolean>;
  sortModel: Array<{ field: string; sort: 'asc' | 'desc' | null }>;
  filterModel: GridFilterModel;
  pageSize: number;
  page: number;
}

export interface JobsSelection {
  selectedIds: GridRowId[];
}

export interface JobsUIState {
  activeTab: number;
}

export interface JobsState {
  gridConfig: JobsGridConfig;
  selection: JobsSelection;
  ui: JobsUIState;
}

// Initial state matching current component defaults
const initialState: JobsState = {
  gridConfig: {
    columnVisibility: {},
    sortModel: [],
    filterModel: { items: [] },
    pageSize: 10,
    page: 0,
  },
  selection: {
    selectedIds: [],
  },
  ui: {
    activeTab: 0,
  },
};

const jobsSlice = createSlice({
  name: 'jobs',
  initialState,
  reducers: {
    // Grid configuration actions
    setPageSize: (state, action: PayloadAction<number>) => {
      state.gridConfig.pageSize = action.payload;
      state.gridConfig.page = 0; // Reset to first page when page size changes
    },
    
    setPage: (state, action: PayloadAction<number>) => {
      state.gridConfig.page = action.payload;
    },
    
    setSortModel: (state, action: PayloadAction<Array<{ field: string; sort: 'asc' | 'desc' | null }>>) => {
      state.gridConfig.sortModel = action.payload;
    },

    setFilterModel: (state, action: PayloadAction<GridFilterModel>) => {
      state.gridConfig.filterModel = action.payload;
      state.gridConfig.page = 0; // Reset to first page when filters change
    },

    setColumnVisibility: (state, action: PayloadAction<Record<string, boolean>>) => {
      state.gridConfig.columnVisibility = action.payload;
    },
    
    // Selection actions
    setSelectedIds: (state, action: PayloadAction<GridRowId[]>) => {
      state.selection.selectedIds = action.payload;
    },

    clearSelection: (state) => {
      state.selection.selectedIds = [];
    },
    
    // UI actions
    setActiveTab: (state, action: PayloadAction<number>) => {
      state.ui.activeTab = action.payload;
    },
    
    // Reset all state
    resetJobsState: () => initialState,
  },
});

export const {
  setPageSize,
  setPage,
  setSortModel,
  setFilterModel,
  setColumnVisibility,
  setSelectedIds,
  clearSelection,
  setActiveTab,
  resetJobsState,
} = jobsSlice.actions;

// Selectors
export const selectJobsGridConfig = (state: { jobs: JobsState }) => state.jobs.gridConfig;
export const selectJobsSelection = (state: { jobs: JobsState }) => state.jobs.selection;
export const selectJobsUI = (state: { jobs: JobsState }) => state.jobs.ui;

// Specific selectors for common use cases
export const selectPageSize = (state: { jobs: JobsState }) => state.jobs.gridConfig.pageSize;
export const selectPage = (state: { jobs: JobsState }) => state.jobs.gridConfig.page;
export const selectSortModel = (state: { jobs: JobsState }) => state.jobs.gridConfig.sortModel;
export const selectFilterModel = (state: { jobs: JobsState }) => state.jobs.gridConfig.filterModel;
export const selectSelectedIds = (state: { jobs: JobsState }) => state.jobs.selection.selectedIds;
export const selectActiveTab = (state: { jobs: JobsState }) => state.jobs.ui.activeTab;

export default jobsSlice.reducer;
