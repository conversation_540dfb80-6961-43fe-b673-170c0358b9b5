{"version": 3, "sources": ["../../@mui/material/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = appendOwnerState(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nexport default useSlotProps;"], "mappings": ";;;;;;;;;;AAcA,SAAS,aAAa,YAAY;AAdlC;AAeE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,yBAAyB;AAAA,IACzB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,0BAA0B,yBAAyB,CAAC,IAAI,8BAAsB,mBAAmB,UAAU;AACjH,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,EACF,IAAI,uBAAe;AAAA,IACjB,GAAG;AAAA,IACH,mBAAmB;AAAA,EACrB,CAAC;AACD,QAAM,MAAM,WAAW,aAAa,mEAAyB,MAAK,gBAAW,oBAAX,mBAA4B,GAAG;AACjG,QAAM,QAAQ,yBAAiB,aAAa;AAAA,IAC1C,GAAG;AAAA,IACH;AAAA,EACF,GAAG,UAAU;AACb,SAAO;AACT;AACA,IAAO,uBAAQ;", "names": []}