{"x-generator": "NSwag v14.2.0.0 (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "ApiService", "version": "2"}, "servers": [{"url": "https://localhost:7488"}], "paths": {"/api/v2/users/{userId}": {"get": {"tags": ["Users"], "operationId": "ATApiServiceEndpointsUsersGetUserEndpoint", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsUsersModelsGetUserResponse"}}}}}}}, "/api/v2/database/namestest": {"get": {"tags": ["Database"], "operationId": "ATApiServiceEndpointsDatabaseDatabaseNamesEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsDatabaseModelsDatabaseNamesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/currentuser/basicinfo": {"get": {"tags": ["Current<PERSON>"], "operationId": "ATApiServiceEndpointsCurrentUserGetBasicInfoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsCurrentUserModelsCurrentUserBasicInfoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/currenttenant/basicinfo": {"get": {"tags": ["Currenttenant"], "operationId": "ATApiServiceEndpointsCurrentTenantGetBasicInfoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsCurrentTenantModelsCurrentTenantBasicInfoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{entityId}": {"delete": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsDeleteJobEndpoint", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "put": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsPutJobEndpoint", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "PutEntityRequest`2", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{entityId1}/triggers/{entityId2}": {"delete": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint", "parameters": [{"name": "entityId1", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId2", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "put": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint", "parameters": [{"name": "entityId1", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId2", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "PutEntityRequest`3", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{rootEntityId}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns/{rootEntityId}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobRunEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns/{jobRunId}/logs": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint", "parameters": [{"name": "jobRunId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint", "parameters": [{"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobsEndpoint", "parameters": [{"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "post": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsPostJobEndpoint", "requestBody": {"x-name": "job", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{rootEntityId}/triggers/{subEntityId1}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "subEntityId1", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{jobId}/triggers": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "post": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "job<PERSON><PERSON>ger", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobTriggerDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/demo/currentuserpermission": {"get": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}}, "components": {"schemas": {"ATApiServiceEndpointsUsersModelsGetUserResponse": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "userId": {"type": "string"}}}, "ATApiServiceEndpointsUsersModelsGetUserRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsDatabaseModelsDatabaseNamesResponse": {"type": "object", "additionalProperties": false, "properties": {"masterDbName": {"type": "string"}, "organizationDbName": {"type": "string"}}}, "ATApiServiceEndpointsCurrentUserModelsCurrentUserBasicInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"username": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}}, "ATApiServiceEndpointsCurrentTenantModelsCurrentTenantBasicInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "headerTitle": {"type": "string"}, "headerColor": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsAdministrationModelsDeleteEntityRequestOfJobId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsDeleteEntityRequestOfJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobDto"}]}}}, "ATApiServiceEndpointsAdministrationModelsJobDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "parameters": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "disabled": {"type": "boolean"}, "successEmails": {"type": "string", "nullable": true}, "errorEmails": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsAdministrationModelsGetEntityRequestOfJobId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobRunDto"}]}}}, "ATApiServiceEndpointsAdministrationModelsJobRunDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "jobId": {"type": "integer", "format": "int32"}, "jobName": {"type": "string"}, "state": {"$ref": "#/components/schemas/ATPrimitivesEnumsJobRunState"}, "result": {"$ref": "#/components/schemas/ATPrimitivesEnumsJobResultType"}, "triggered": {"type": "string", "format": "date-time"}, "started": {"type": "string", "format": "date-time", "nullable": true}, "finished": {"type": "string", "format": "date-time", "nullable": true}, "authorId": {"type": "integer", "format": "int32", "nullable": true}}}, "ATPrimitivesEnumsJobRunState": {"type": "integer", "description": "", "x-enumNames": ["Starting", "Running", "Finished"], "enum": [0, 1, 2]}, "ATPrimitivesEnumsJobResultType": {"type": "integer", "description": "", "x-enumNames": ["None", "Success", "SuccessWithWarning", "PartialSuccess", "Failure"], "enum": [0, 1, 2, 3, 4]}, "ATApiServiceEndpointsAdministrationModelsGetEntityRequestOfJobRunId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsGetEntitiesResponse": {"type": "object", "additionalProperties": false, "properties": {"value": {"type": "array", "items": {"type": "object", "additionalProperties": {}}}, "count": {"type": "integer", "format": "int64", "nullable": true}}}, "ATApiServiceEndpointsAdministrationModelsGetEntitiesRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobTriggerDto"}]}}}, "ATApiServiceEndpointsAdministrationModelsJobTriggerDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "parameters": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "disabled": {"type": "boolean"}, "jobId": {"type": "integer", "format": "int32"}, "runParameters": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsAdministrationModelsGetEntityRequestOfJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsAdministrationModelsPostJobRequest": {"type": "object", "additionalProperties": false, "properties": {"job": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobDto"}}}, "ATApiServiceEndpointsAdministrationModelsPostJobTriggerRequest": {"type": "object", "additionalProperties": false, "properties": {"jobTrigger": {"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobTriggerDto"}}}, "ATApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId": {"type": "object", "additionalProperties": false, "properties": {"model": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobDto"}]}}}, "ATApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false, "properties": {"model": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsAdministrationModelsJobTriggerDto"}]}}}, "ATApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse": {"type": "object", "additionalProperties": false, "properties": {"hasPermissionEnum": {"type": "boolean"}, "hasPermissionSmartEnum": {"type": "boolean"}}}}, "securitySchemes": {"JWTBearerAuth": {"type": "http", "description": "Enter a JWT token to authorize the requests...", "scheme": "Bearer", "bearerFormat": "JWT"}}}}