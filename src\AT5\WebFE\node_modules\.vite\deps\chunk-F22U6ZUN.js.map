{"version": 3, "sources": ["../../@mui/x-data-grid/esm/hooks/features/headerFiltering/useGridHeaderFiltering.js", "../../@mui/x-data-grid/esm/internals/demo/TailwindDemoContainer.js", "../../@mui/x-data-grid-pro/esm/components/GridColumnHeaders.js", "../../@mui/x-data-grid-pro/esm/hooks/features/columnHeaders/useGridColumnHeaders.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/composeClasses/composeClasses.js", "../../@mui/x-data-grid-pro/esm/hooks/utils/useGridRootProps.js", "../../@mui/x-data-grid-pro/esm/components/GridProColumnMenu.js", "../../@mui/x-data-grid-pro/esm/components/GridColumnMenuPinningItem.js", "../../@mui/x-data-grid-pro/esm/hooks/utils/useGridApiContext.js", "../../@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenu.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/capitalize/capitalize.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/HTMLElementType/HTMLElementType.js", "../../@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterCell.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useForkRef/useForkRef.js", "../../@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenuContainer.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/refType/refType.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useId/useId.js", "../../@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterClearButton.js", "../../@mui/x-data-grid-pro/esm/components/GridDetailPanels.js", "../../@mui/x-data-grid-pro/esm/hooks/utils/useGridPrivateApiContext.js", "../../@mui/x-data-grid-pro/esm/hooks/features/detailPanel/gridDetailPanelToggleColDef.js", "../../@mui/x-data-grid-pro/esm/components/GridDetailPanelToggleCell.js", "../../@mui/x-data-grid-pro/esm/hooks/features/detailPanel/gridDetailPanelSelector.js", "../../@mui/x-data-grid-pro/esm/components/GridDetailPanel.js", "../../@mui/x-internals/esm/useResizeObserver/useResizeObserver.js", "../../@mui/x-data-grid-pro/esm/components/GridPinnedRows.js", "../../@mui/x-data-grid-pro/esm/material/icons.js", "../../@mui/x-data-grid-pro/esm/material/index.js", "../../@mui/x-data-grid-pro/esm/constants/dataGridProDefaultSlotsComponents.js", "../../@mui/x-data-grid-pro/esm/hooks/utils/useGridAriaAttributes.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rows/useGridRowAriaAttributes.js", "../../@mui/x-data-grid-pro/esm/hooks/features/columnPinning/useGridColumnPinning.js", "../../@mui/x-data-grid-pro/esm/hooks/features/columnPinning/useGridColumnPinningPreProcessors.js", "../../@mui/x-data-grid-pro/esm/hooks/features/columnReorder/useGridColumnReorder.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/ownerDocument/ownerDocument.js", "../../@mui/x-data-grid-pro/esm/hooks/features/columnReorder/columnReorderSelector.js", "../../@mui/x-data-grid-pro/esm/hooks/features/serverSideTreeData/useGridDataSourceTreeDataPreProcessors.js", "../../@mui/x-data-grid-pro/esm/hooks/features/treeData/gridTreeDataGroupColDef.js", "../../@mui/x-data-grid-pro/esm/hooks/features/serverSideTreeData/utils.js", "../../@mui/x-data-grid-pro/esm/components/GridDataSourceTreeDataGroupingCell.js", "../../@mui/x-data-grid-pro/esm/hooks/features/dataSource/gridDataSourceSelector.js", "../../@mui/x-data-grid-pro/esm/utils/tree/utils.js", "../../@mui/x-data-grid-pro/esm/utils/tree/insertDataRowInTree.js", "../../@mui/x-data-grid-pro/esm/utils/tree/createRowTree.js", "../../@mui/x-data-grid-pro/esm/hooks/features/treeData/gridTreeDataUtils.js", "../../@mui/x-data-grid-pro/esm/utils/tree/removeDataRowFromTree.js", "../../@mui/x-data-grid-pro/esm/utils/tree/updateRowTree.js", "../../@mui/x-data-grid-pro/esm/hooks/features/detailPanel/useGridDetailPanel.js", "../../@mui/x-data-grid-pro/esm/hooks/features/detailPanel/useGridDetailPanelPreProcessors.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js", "../../@mui/x-data-grid-pro/esm/hooks/features/infiniteLoader/useGridInfiniteLoader.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rowReorder/useGridRowReorder.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useOnMount/useOnMount.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useTimeout/useTimeout.js", "../../@mui/x-data-grid-pro/esm/components/GridRowReorderCell.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rowReorder/gridRowReorderColDef.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rowReorder/useGridRowReorderPreProcessors.js", "../../@mui/x-data-grid-pro/esm/hooks/features/treeData/useGridTreeData.js", "../../@mui/x-data-grid-pro/esm/hooks/features/treeData/useGridTreeDataPreProcessors.js", "../../@mui/x-data-grid-pro/esm/components/GridTreeDataGroupingCell.js", "../../@mui/x-data-grid-pro/esm/utils/tree/sortRowTree.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rowPinning/useGridRowPinning.js", "../../@mui/x-data-grid-pro/esm/hooks/features/rowPinning/useGridRowPinningPreProcessors.js", "../../@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/useGridLazyLoader.js", "../../@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/utils.js", "../../@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/useGridLazyLoaderPreProcessors.js", "../../@mui/x-data-grid-pro/esm/hooks/features/serverSideLazyLoader/useGridDataSourceLazyLoader.js", "../../@mui/x-data-grid-pro/node_modules/@mui/utils/esm/debounce/debounce.js", "../../@mui/x-data-grid-pro/esm/hooks/features/serverSideLazyLoader/useGridInfiniteLoadingIntersection.js", "../../@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourcePro.js", "../../@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourceBasePro.js", "../../@mui/x-data-grid-pro/esm/hooks/features/dataSource/utils.js", "../../@mui/x-data-grid-pro/esm/internals/propValidation.js", "../../@mui/x-data-grid-pro/esm/internals/index.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/index.js\";\nimport { gridColumnLookupSelector, gridColumnVisibilityModelSelector, gridColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nexport const headerFilteringStateInitializer = (state, props) => _extends({}, state, {\n  headerFiltering: {\n    enabled: props.headerFilters ?? false,\n    editing: null,\n    menuOpen: null\n  }\n});\nexport const useGridHeaderFiltering = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridHeaderFiltering');\n  const setHeaderFilterState = React.useCallback(headerFilterState => {\n    apiRef.current.setState(state => {\n      // Safety check to avoid MIT users from using it\n      // This hook should ultimately be moved to the Pro package\n      if (props.signature === 'DataGrid') {\n        return state;\n      }\n      return _extends({}, state, {\n        headerFiltering: {\n          enabled: props.headerFilters ?? false,\n          editing: headerFilterState.editing ?? null,\n          menuOpen: headerFilterState.menuOpen ?? null\n        }\n      });\n    });\n  }, [apiRef, props.signature, props.headerFilters]);\n  const startHeaderFilterEditMode = React.useCallback(field => {\n    logger.debug(`Starting edit mode on header filter for field: ${field}`);\n    apiRef.current.setHeaderFilterState({\n      editing: field\n    });\n  }, [apiRef, logger]);\n  const stopHeaderFilterEditMode = React.useCallback(() => {\n    logger.debug(`Stopping edit mode on header filter`);\n    apiRef.current.setHeaderFilterState({\n      editing: null\n    });\n  }, [apiRef, logger]);\n  const showHeaderFilterMenu = React.useCallback(field => {\n    logger.debug(`Opening header filter menu for field: ${field}`);\n    apiRef.current.setHeaderFilterState({\n      menuOpen: field\n    });\n  }, [apiRef, logger]);\n  const hideHeaderFilterMenu = React.useCallback(() => {\n    logger.debug(`Hiding header filter menu for active field`);\n    let fieldToFocus = apiRef.current.state.headerFiltering.menuOpen;\n    if (fieldToFocus) {\n      const columnLookup = gridColumnLookupSelector(apiRef);\n      const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n      const orderedFields = gridColumnFieldsSelector(apiRef);\n\n      // If the column was removed from the grid, we need to find the closest visible field\n      if (!columnLookup[fieldToFocus]) {\n        fieldToFocus = orderedFields[0];\n      }\n\n      // If the field to focus is hidden, we need to find the closest visible field\n      if (columnVisibilityModel[fieldToFocus] === false) {\n        // contains visible column fields + the field that was just hidden\n        const visibleOrderedFields = orderedFields.filter(field => {\n          if (field === fieldToFocus) {\n            return true;\n          }\n          return columnVisibilityModel[field] !== false;\n        });\n        const fieldIndex = visibleOrderedFields.indexOf(fieldToFocus);\n        fieldToFocus = visibleOrderedFields[fieldIndex + 1] || visibleOrderedFields[fieldIndex - 1];\n      }\n      apiRef.current.setHeaderFilterState({\n        menuOpen: null\n      });\n      apiRef.current.setColumnHeaderFilterFocus(fieldToFocus);\n    }\n  }, [apiRef, logger]);\n  const headerFilterPrivateApi = {\n    setHeaderFilterState\n  };\n  const headerFilterApi = {\n    startHeaderFilterEditMode,\n    stopHeaderFilterEditMode,\n    showHeaderFilterMenu,\n    hideHeaderFilterMenu\n  };\n  useGridApiMethod(apiRef, headerFilterApi, 'public');\n  useGridApiMethod(apiRef, headerFilterPrivateApi, 'private');\n\n  /*\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      apiRef.current.setHeaderFilterState({\n        enabled: props.headerFilters ?? false\n      });\n    }\n  }, [apiRef, props.headerFilters]);\n};", "'use client';\n\nimport * as React from 'react';\nimport Box from '@mui/material/Box';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * WARNING: This is an internal component used in documentation to inject the Tailwind script.\n * Please do not use it in your application.\n */\nexport function TailwindDemoContainer(props) {\n  const {\n    children,\n    documentBody\n  } = props;\n  const [isLoaded, setIsLoaded] = React.useState(false);\n  React.useEffect(() => {\n    const body = documentBody ?? document.body;\n    const script = document.createElement('script');\n    script.src = 'https://unpkg.com/@tailwindcss/browser@4';\n    let mounted = true;\n    const cleanup = () => {\n      mounted = false;\n      script.remove();\n      const head = body?.ownerDocument?.head;\n      if (!head) {\n        return;\n      }\n      const styles = head.querySelectorAll('style:not([data-emotion])');\n      styles.forEach(style => {\n        const styleText = style.textContent?.substring(0, 100);\n        const isTailwindStylesheet = styleText?.includes('tailwind');\n        if (isTailwindStylesheet) {\n          style.remove();\n        }\n      });\n    };\n    script.onload = () => {\n      if (!mounted) {\n        cleanup();\n        return;\n      }\n      setIsLoaded(true);\n    };\n    body.appendChild(script);\n    return cleanup;\n  }, [documentBody]);\n  return isLoaded ? children : /*#__PURE__*/_jsx(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsx(CircularProgress, {})\n  });\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"style\", \"className\", \"visibleColumns\", \"sortColumnLookup\", \"filterColumnLookup\", \"columnHeaderTabIndexState\", \"columnGroupHeaderTabIndexState\", \"columnHeaderFocus\", \"columnGroupHeaderFocus\", \"headerGroupingMaxDepth\", \"columnMenuState\", \"columnVisibility\", \"columnGroupsHeaderStructure\", \"hasOtherElementInTabSequence\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/material/styles';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars, GridBaseColumnHeaders } from '@mui/x-data-grid/internals';\nimport { useGridColumnHeadersPro } from \"../hooks/features/columnHeaders/useGridColumnHeaders.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Filler = styled('div')({\n  flex: 1,\n  backgroundColor: vars.header.background.base\n});\nconst GridColumnHeaders = forwardRef(function GridColumnHeaders(props, ref) {\n  const {\n      className,\n      visibleColumns,\n      sortColumnLookup,\n      filterColumnLookup,\n      columnHeaderTabIndexState,\n      columnGroupHeaderTabIndexState,\n      columnHeaderFocus,\n      columnGroupHeaderFocus,\n      headerGroupingMaxDepth,\n      columnMenuState,\n      columnVisibility,\n      columnGroupsHeaderStructure,\n      hasOtherElementInTabSequence\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getInnerProps,\n    getColumnHeadersRow,\n    getColumnFiltersRow,\n    getColumnGroupHeadersRows\n  } = useGridColumnHeadersPro({\n    visibleColumns,\n    sortColumnLookup,\n    filterColumnLookup,\n    columnHeaderTabIndexState,\n    hasOtherElementInTabSequence,\n    columnGroupHeaderTabIndexState,\n    columnHeaderFocus,\n    columnGroupHeaderFocus,\n    headerGroupingMaxDepth,\n    columnMenuState,\n    columnVisibility,\n    columnGroupsHeaderStructure\n  });\n  return /*#__PURE__*/_jsxs(GridBaseColumnHeaders, _extends({\n    className: className\n  }, other, getInnerProps(), {\n    ref: ref,\n    children: [getColumnGroupHeadersRows(), getColumnHeadersRow(), getColumnFiltersRow(), /*#__PURE__*/_jsx(Filler, {})]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridColumnHeaders.displayName = \"GridColumnHeaders\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaders.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnGroupHeaderFocus: PropTypes.shape({\n    depth: PropTypes.number.isRequired,\n    field: PropTypes.string.isRequired\n  }),\n  columnGroupHeaderTabIndexState: PropTypes.shape({\n    depth: PropTypes.number.isRequired,\n    field: PropTypes.string.isRequired\n  }),\n  columnGroupsHeaderStructure: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.shape({\n    columnFields: PropTypes.arrayOf(PropTypes.string).isRequired,\n    groupId: PropTypes.string\n  }))).isRequired,\n  columnHeaderFocus: PropTypes.shape({\n    field: PropTypes.string.isRequired\n  }),\n  columnHeaderTabIndexState: PropTypes.shape({\n    field: PropTypes.string.isRequired\n  }),\n  columnMenuState: PropTypes.shape({\n    field: PropTypes.string,\n    open: PropTypes.bool.isRequired\n  }).isRequired,\n  columnVisibility: PropTypes.object.isRequired,\n  filterColumnLookup: PropTypes.object.isRequired,\n  hasOtherElementInTabSequence: PropTypes.bool.isRequired,\n  headerGroupingMaxDepth: PropTypes.number.isRequired,\n  sortColumnLookup: PropTypes.object.isRequired,\n  visibleColumns: PropTypes.arrayOf(PropTypes.object).isRequired\n} : void 0;\nexport { GridColumnHeaders };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getColumnsToRender\", \"getPinnedCellOffset\", \"renderContext\", \"leftRenderContext\", \"rightRenderContext\", \"pinnedColumns\", \"visibleColumns\", \"columnPositions\"];\nimport * as React from 'react';\nimport { gridFocusColumnHeaderFilterSelector, useGridSelector, gridFilterModelSelector, gridTabIndexColumnHeaderFilterSelector, getDataGridUtilityClass } from '@mui/x-data-grid';\nimport { gridColumnsTotalWidthSelector, gridHasFillerSelector, gridHeaderFilterHeightSelector, gridVerticalScrollbarWidthSelector, useGridColumnHeaders as useGridColumnHeadersCommunity, useGridPrivateApiContext, getGridFilter, GridColumnHeaderRow, shouldCellShowLeftBorder, shouldCellShowRightBorder, PinnedColumnPosition } from '@mui/x-data-grid/internals';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  return React.useMemo(() => {\n    const slots = {\n      headerFilterRow: ['headerFilterRow']\n    };\n    return composeClasses(slots, getDataGridUtilityClass, classes);\n  }, [classes]);\n};\nexport const useGridColumnHeadersPro = props => {\n  const apiRef = useGridPrivateApiContext();\n  const {\n    headerGroupingMaxDepth,\n    hasOtherElementInTabSequence\n  } = props;\n  const columnHeaderFilterTabIndexState = useGridSelector(apiRef, gridTabIndexColumnHeaderFilterSelector);\n  const _useGridColumnHeaders = useGridColumnHeadersCommunity(_extends({}, props, {\n      hasOtherElementInTabSequence: hasOtherElementInTabSequence || columnHeaderFilterTabIndexState !== null\n    })),\n    {\n      getColumnsToRender,\n      getPinnedCellOffset,\n      renderContext,\n      leftRenderContext,\n      rightRenderContext,\n      pinnedColumns,\n      visibleColumns,\n      columnPositions\n    } = _useGridColumnHeaders,\n    otherProps = _objectWithoutPropertiesLoose(_useGridColumnHeaders, _excluded);\n  const headerFiltersRef = React.useRef(null);\n  apiRef.current.register('private', {\n    headerFiltersElementRef: headerFiltersRef\n  });\n  const headerFilterMenuRef = React.useRef(null);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const disableHeaderFiltering = !rootProps.headerFilters;\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const gridHasFiller = useGridSelector(apiRef, gridHasFillerSelector);\n  const headerFilterHeight = useGridSelector(apiRef, gridHeaderFilterHeightSelector);\n  const scrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);\n  const columnHeaderFilterFocus = useGridSelector(apiRef, gridFocusColumnHeaderFilterSelector);\n  const filterItemsCache = React.useRef(Object.create(null)).current;\n  const getFilterItem = React.useCallback(colDef => {\n    const filterModelItem = filterModel?.items.find(it => it.field === colDef.field && it.operator !== 'isAnyOf');\n    if (filterModelItem != null) {\n      // there's a valid `filterModelItem` for this column\n      return filterModelItem;\n    }\n    const defaultCachedItem = filterItemsCache[colDef.field];\n    if (defaultCachedItem != null) {\n      // there's a cached `defaultItem` for this column\n      return defaultCachedItem;\n    }\n    // there's no cached `defaultItem` for this column, let's generate one and cache it\n    const defaultItem = getGridFilter(colDef);\n    filterItemsCache[colDef.field] = defaultItem;\n    return defaultItem;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [filterModel]);\n  const getColumnFilters = params => {\n    const {\n      renderedColumns,\n      firstColumnToRender\n    } = getColumnsToRender(params);\n    const filters = [];\n    for (let i = 0; i < renderedColumns.length; i += 1) {\n      const colDef = renderedColumns[i];\n      const columnIndex = firstColumnToRender + i;\n      const hasFocus = columnHeaderFilterFocus?.field === colDef.field;\n      const isFirstColumn = columnIndex === 0;\n      const tabIndexField = columnHeaderFilterTabIndexState?.field;\n      const tabIndex = tabIndexField === colDef.field || isFirstColumn && !props.hasOtherElementInTabSequence ? 0 : -1;\n      const headerClassName = typeof colDef.headerClassName === 'function' ? colDef.headerClassName({\n        field: colDef.field,\n        colDef\n      }) : colDef.headerClassName;\n      const item = getFilterItem(colDef);\n      const pinnedPosition = params?.position;\n      const pinnedOffset = getPinnedCellOffset(pinnedPosition, colDef.computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth);\n      const indexInSection = i;\n      const sectionLength = renderedColumns.length;\n      const showLeftBorder = shouldCellShowLeftBorder(pinnedPosition, indexInSection);\n      const showRightBorder = shouldCellShowRightBorder(pinnedPosition, indexInSection, sectionLength, rootProps.showColumnVerticalBorder, gridHasFiller);\n      filters.push(/*#__PURE__*/_jsx(rootProps.slots.headerFilterCell, _extends({\n        colIndex: columnIndex,\n        height: headerFilterHeight,\n        width: colDef.computedWidth,\n        colDef: colDef,\n        hasFocus: hasFocus,\n        tabIndex: tabIndex,\n        headerFilterMenuRef: headerFilterMenuRef,\n        headerClassName: headerClassName,\n        \"data-field\": colDef.field,\n        item: item,\n        pinnedPosition: pinnedPosition,\n        pinnedOffset: pinnedOffset,\n        showLeftBorder: showLeftBorder,\n        showRightBorder: showRightBorder\n      }, rootProps.slotProps?.headerFilterCell), `${colDef.field}-filter`));\n    }\n    return otherProps.getFillers(params, filters, 0, true);\n  };\n  const getColumnFiltersRow = () => {\n    if (disableHeaderFiltering) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxs(GridColumnHeaderRow, {\n      ref: headerFiltersRef,\n      className: classes.headerFilterRow,\n      role: \"row\",\n      \"aria-rowindex\": headerGroupingMaxDepth + 2,\n      ownerState: rootProps,\n      children: [leftRenderContext && getColumnFilters({\n        position: PinnedColumnPosition.LEFT,\n        renderContext: leftRenderContext,\n        maxLastColumn: leftRenderContext.lastColumnIndex\n      }), getColumnFilters({\n        renderContext,\n        maxLastColumn: visibleColumns.length - pinnedColumns.right.length\n      }), rightRenderContext && getColumnFilters({\n        position: PinnedColumnPosition.RIGHT,\n        renderContext: rightRenderContext,\n        maxLastColumn: rightRenderContext.lastColumnIndex\n      })]\n    });\n  };\n  if (process.env.NODE_ENV !== \"production\") getColumnFiltersRow.displayName = \"getColumnFiltersRow\";\n  return _extends({}, otherProps, {\n    getColumnFiltersRow\n  });\n};", "/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0\n   ---\n   These rules are preventing the performance optimizations below.\n */\n\n/**\n * Compose classes from multiple sources.\n *\n * @example\n * ```tsx\n * const slots = {\n *  root: ['root', 'primary'],\n *  label: ['label'],\n * };\n *\n * const getUtilityClass = (slot) => `MuiButton-${slot}`;\n *\n * const classes = {\n *   root: 'my-root-class',\n * };\n *\n * const output = composeClasses(slots, getUtilityClass, classes);\n * // {\n * //   root: 'MuiButton-root MuiButton-primary my-root-class',\n * //   label: 'MuiButton-label',\n * // }\n * ```\n *\n * @param slots a list of classes for each possible slot\n * @param getUtilityClass a function to resolve the class based on the slot name\n * @param classes the input classes from props\n * @returns the resolved classes for all slots\n */\nexport default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  for (const slotName in slots) {\n    const slot = slots[slotName];\n    let buffer = '';\n    let start = true;\n    for (let i = 0; i < slot.length; i += 1) {\n      const value = slot[i];\n      if (value) {\n        buffer += (start === true ? '' : ' ') + getUtilityClass(value);\n        start = false;\n        if (classes && classes[value]) {\n          buffer += ' ' + classes[value];\n        }\n      }\n    }\n    output[slotName] = buffer;\n  }\n  return output;\n}", "import { useGridRootProps as useCommunityGridRootProps } from '@mui/x-data-grid';\nexport const useGridRootProps = useCommunityGridRootProps;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridGenericColumnMenu, GRID_COLUMN_MENU_SLOTS, GRID_COLUMN_MENU_SLOT_PROPS } from '@mui/x-data-grid';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridColumnMenuPinningItem } from \"./GridColumnMenuPinningItem.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GRID_COLUMN_MENU_SLOTS_PRO = _extends({}, GRID_COLUMN_MENU_SLOTS, {\n  columnMenuPinningItem: GridColumnMenuPinningItem\n});\nexport const GRID_COLUMN_MENU_SLOT_PROPS_PRO = _extends({}, GRID_COLUMN_MENU_SLOT_PROPS, {\n  columnMenuPinningItem: {\n    displayOrder: 15\n  }\n});\nexport const GridProColumnMenu = forwardRef(function GridProColumnMenu(props, ref) {\n  return /*#__PURE__*/_jsx(GridGenericColumnMenu, _extends({}, props, {\n    defaultSlots: GRID_COLUMN_MENU_SLOTS_PRO,\n    defaultSlotProps: GRID_COLUMN_MENU_SLOT_PROPS_PRO,\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridProColumnMenu.displayName = \"GridProColumnMenu\";", "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport PropTypes from 'prop-types';\nimport { GridPinnedColumnPosition } from '@mui/x-data-grid';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuPinningItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const isRtl = useRtl();\n  const pinColumn = React.useCallback(side => event => {\n    apiRef.current.pinColumn(colDef.field, side);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick]);\n  const unpinColumn = event => {\n    apiRef.current.unpinColumn(colDef.field);\n    onClick(event);\n  };\n  const pinToLeftMenuItem = /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: pinColumn(GridPinnedColumnPosition.LEFT),\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuPinLeftIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('pinToLeft')\n  });\n  const pinToRightMenuItem = /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: pinColumn(GridPinnedColumnPosition.RIGHT),\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuPinRightIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('pinToRight')\n  });\n  if (!colDef) {\n    return null;\n  }\n  const side = apiRef.current.isColumnPinned(colDef.field);\n  if (side) {\n    const otherSide = side === GridPinnedColumnPosition.RIGHT ? GridPinnedColumnPosition.LEFT : GridPinnedColumnPosition.RIGHT;\n    const label = otherSide === GridPinnedColumnPosition.RIGHT ? 'pinToRight' : 'pinToLeft';\n    const Icon = side === GridPinnedColumnPosition.RIGHT ? rootProps.slots.columnMenuPinLeftIcon : rootProps.slots.columnMenuPinRightIcon;\n    return /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n        onClick: pinColumn(otherSide),\n        iconStart: /*#__PURE__*/_jsx(Icon, {\n          fontSize: \"small\"\n        }),\n        children: apiRef.current.getLocaleText(label)\n      }), /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n        onClick: unpinColumn,\n        iconStart: \"\",\n        children: apiRef.current.getLocaleText('unpin')\n      })]\n    });\n  }\n  if (isRtl) {\n    return /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [pinToRightMenuItem, pinToLeftMenuItem]\n    });\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [pinToLeftMenuItem, pinToRightMenuItem]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuPinningItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuPinningItem };", "import { useGridApiContext as useCommunityGridApiContext } from '@mui/x-data-grid';\nexport const useGridApiContext = useCommunityGridApiContext;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport capitalize from '@mui/utils/capitalize';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useGridRootProps, useGridApiContext, GridMenu } from '@mui/x-data-grid';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridHeaderFilterMenu({\n  open,\n  field,\n  target,\n  applyFilterChanges,\n  operators,\n  item,\n  id,\n  labelledBy,\n  showClearItem,\n  clearFilterItem\n}) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const hideMenu = React.useCallback(() => {\n    apiRef.current.hideHeaderFilterMenu();\n  }, [apiRef]);\n  if (!target) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridMenu, {\n    position: \"bottom-end\",\n    open: open,\n    target: target,\n    onClose: hideMenu,\n    children: /*#__PURE__*/_jsxs(rootProps.slots.baseMenuList, {\n      \"aria-labelledby\": labelledBy,\n      id: id,\n      children: [showClearItem && [/*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n        iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuClearIcon, {\n          fontSize: \"small\"\n        }),\n        onClick: () => {\n          clearFilterItem();\n          hideMenu();\n        },\n        children: apiRef.current.getLocaleText('headerFilterClear')\n      }, \"filter-menu-clear-filter\"), /*#__PURE__*/_jsx(rootProps.slots.baseDivider, {}, \"filter-menu-divider\")], operators.map(op => {\n        const selected = op.value === item.operator;\n        const label = op?.headerLabel ?? apiRef.current.getLocaleText(`headerFilterOperator${capitalize(op.value)}`);\n        return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n          iconStart: selected ? /*#__PURE__*/_jsx(rootProps.slots.menuItemCheckIcon, {\n            fontSize: \"small\"\n          }) : /*#__PURE__*/_jsx(\"span\", {}),\n          onClick: () => {\n            applyFilterChanges(_extends({}, item, {\n              operator: op.value\n            }));\n            hideMenu();\n          },\n          autoFocus: selected ? open : false,\n          children: label\n        }, `${field}-${op.value}`);\n      })]\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridHeaderFilterMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  applyFilterChanges: PropTypes.func.isRequired,\n  clearFilterItem: PropTypes.func.isRequired,\n  field: PropTypes.string.isRequired,\n  id: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  labelledBy: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  open: PropTypes.bool.isRequired,\n  operators: PropTypes.arrayOf(PropTypes.shape({\n    getApplyFilterFn: PropTypes.func.isRequired,\n    getValueAsString: PropTypes.func,\n    headerLabel: PropTypes.string,\n    InputComponent: PropTypes.elementType,\n    InputComponentProps: PropTypes.shape({\n      apiRef: PropTypes.shape({\n        current: PropTypes.object.isRequired\n      }),\n      applyValue: PropTypes.func,\n      className: PropTypes.string,\n      clearButton: PropTypes.node,\n      disabled: PropTypes.bool,\n      focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n        current: PropTypes.any.isRequired\n      })]),\n      headerFilterMenu: PropTypes.node,\n      inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n        current: (props, propName) => {\n          if (props[propName] == null) {\n            return null;\n          }\n          if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n            return new Error(`Expected prop '${propName}' to be of type Element`);\n          }\n          return null;\n        }\n      })]),\n      isFilterActive: PropTypes.bool,\n      item: PropTypes.shape({\n        field: PropTypes.string.isRequired,\n        id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n        operator: PropTypes.string.isRequired,\n        value: PropTypes.any\n      }),\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      slotProps: PropTypes.object,\n      tabIndex: PropTypes.number\n    }),\n    label: PropTypes.string,\n    requiresFilterValue: PropTypes.bool,\n    value: PropTypes.string.isRequired\n  })).isRequired,\n  showClearItem: PropTypes.bool.isRequired,\n  target: HTMLElementType\n} : void 0;\nexport { GridHeaderFilterMenu };", "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n// It should to be noted that this function isn't equivalent to `text-transform: capitalize`.\n//\n// A strict capitalization should uppercase the first letter of each word in the sentence.\n// We only handle the first word.\nexport default function capitalize(string) {\n  if (typeof string !== 'string') {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `capitalize(string)` expects a string argument.' : _formatErrorMessage(7));\n  }\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}", "export default function HTMLElementType(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null) {\n    return null;\n  }\n  if (propValue && propValue.nodeType !== 1) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an HTMLElement.`);\n  }\n  return null;\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"colIndex\", \"height\", \"hasFocus\", \"width\", \"headerClassName\", \"colDef\", \"item\", \"headerFilterMenuRef\", \"InputComponentProps\", \"showClearIcon\", \"pinnedPosition\", \"pinnedOffset\", \"style\", \"showLeftBorder\", \"showRightBorder\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport useForkRef from '@mui/utils/useForkRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { gridVisibleColumnFieldsSelector, getDataGridUtilityClass, useGridSelector, GridFilterInputValue, GridFilterInputDate, GridFilterInputBoolean, GridFilterInputSingleSelect, gridFilterModelSelector, gridFilterableColumnLookupSelector, gridClasses } from '@mui/x-data-grid';\nimport { PinnedColumnPosition, useGridPrivateApiContext, gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector, isNavigationKey, attachPinnedStyle, vars } from '@mui/x-data-grid/internals';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { inputBaseClasses } from '@mui/material/InputBase';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { GridHeaderFilterMenuContainer } from \"./GridHeaderFilterMenuContainer.js\";\nimport { GridHeaderFilterClearButton } from \"./GridHeaderFilterClearButton.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst StyledInputComponent = styled(GridFilterInputValue, {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderFilterInput'\n})({\n  flex: 1,\n  marginRight: vars.spacing(0.5),\n  marginBottom: vars.spacing(-0.25),\n  '& input[type=\"number\"], & input[type=\"date\"], & input[type=\"datetime-local\"]': {\n    '&[value=\"\"]:not(:focus)': {\n      color: 'transparent'\n    }\n  },\n  [`& .${inputBaseClasses.input}`]: {\n    fontSize: '14px'\n  },\n  [`.${gridClasses['root--densityCompact']} & .${inputBaseClasses.input}`]: {\n    paddingTop: vars.spacing(0.5),\n    paddingBottom: vars.spacing(0.5),\n    height: 23\n  }\n});\nconst OperatorLabel = styled('span', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderFilterOperatorLabel'\n})({\n  flex: 1,\n  marginRight: vars.spacing(0.5),\n  color: vars.colors.foreground.muted,\n  whiteSpace: 'nowrap',\n  textOverflow: 'ellipsis',\n  overflow: 'hidden'\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    colDef,\n    classes,\n    showRightBorder,\n    showLeftBorder,\n    pinnedPosition\n  } = ownerState;\n  const slots = {\n    root: ['columnHeader', 'columnHeader--filter', colDef.headerAlign === 'left' && 'columnHeader--alignLeft', colDef.headerAlign === 'center' && 'columnHeader--alignCenter', colDef.headerAlign === 'right' && 'columnHeader--alignRight', 'withBorderColor', showRightBorder && 'columnHeader--withRightBorder', showLeftBorder && 'columnHeader--withLeftBorder', pinnedPosition === PinnedColumnPosition.LEFT && 'columnHeader--pinnedLeft', pinnedPosition === PinnedColumnPosition.RIGHT && 'columnHeader--pinnedRight'],\n    input: ['columnHeaderFilterInput'],\n    operatorLabel: ['columnHeaderFilterOperatorLabel']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst DEFAULT_INPUT_COMPONENTS = {\n  string: GridFilterInputValue,\n  number: GridFilterInputValue,\n  date: GridFilterInputDate,\n  dateTime: GridFilterInputDate,\n  boolean: GridFilterInputBoolean,\n  singleSelect: GridFilterInputSingleSelect,\n  actions: null,\n  custom: null\n};\nconst GridHeaderFilterCell = forwardRef((props, ref) => {\n  const {\n      colIndex,\n      height,\n      hasFocus,\n      width,\n      headerClassName,\n      colDef,\n      item,\n      headerFilterMenuRef,\n      InputComponentProps,\n      showClearIcon = false,\n      pinnedPosition,\n      pinnedOffset,\n      style: styleProp,\n      showLeftBorder,\n      showRightBorder\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const isRtl = useRtl();\n  const columnFields = useGridSelector(apiRef, gridVisibleColumnFieldsSelector);\n  const rootProps = useGridRootProps();\n  const cellRef = React.useRef(null);\n  const handleRef = useForkRef(ref, cellRef);\n  const inputRef = React.useRef(null);\n  const buttonRef = React.useRef(null);\n  const editingField = useGridSelector(apiRef, gridHeaderFilteringEditFieldSelector);\n  const isEditing = editingField === colDef.field;\n  const menuOpenField = useGridSelector(apiRef, gridHeaderFilteringMenuSelector);\n  const isMenuOpen = menuOpenField === colDef.field;\n\n  // TODO: Support for `isAnyOf` operator\n  const filterOperators = React.useMemo(() => {\n    if (!colDef.filterOperators) {\n      return [];\n    }\n    return colDef.filterOperators.filter(operator => operator.value !== 'isAnyOf');\n  }, [colDef.filterOperators]);\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const filterableColumnsLookup = useGridSelector(apiRef, gridFilterableColumnLookupSelector);\n  const isFilterReadOnly = React.useMemo(() => {\n    if (!filterModel?.items.length) {\n      return false;\n    }\n    const filterModelItem = filterModel.items.find(it => it.field === colDef.field);\n    return filterModelItem ? !filterableColumnsLookup[filterModelItem.field] : false;\n  }, [colDef.field, filterModel, filterableColumnsLookup]);\n  const currentOperator = React.useMemo(() => filterOperators.find(operator => operator.value === item.operator) ?? filterOperators[0], [item.operator, filterOperators]);\n  const InputComponent = colDef.filterable || isFilterReadOnly ? currentOperator.InputComponent ?? DEFAULT_INPUT_COMPONENTS[colDef.type] : null;\n  const clearFilterItem = React.useCallback(() => {\n    apiRef.current.deleteFilterItem(item);\n  }, [apiRef, item]);\n  let headerFilterComponent;\n  if (colDef.renderHeaderFilter) {\n    headerFilterComponent = colDef.renderHeaderFilter(_extends({}, props, {\n      inputRef\n    }));\n  }\n  React.useLayoutEffect(() => {\n    if (hasFocus && !isMenuOpen) {\n      let focusableElement = cellRef.current.querySelector('[tabindex=\"0\"]');\n      if (isEditing && InputComponent) {\n        focusableElement = inputRef.current;\n      }\n      const elementToFocus = focusableElement || cellRef.current;\n      elementToFocus?.focus();\n      if (apiRef.current.columnHeadersContainerRef.current) {\n        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;\n      }\n    }\n  }, [InputComponent, apiRef, hasFocus, isEditing, isMenuOpen]);\n  const onKeyDown = React.useCallback(event => {\n    if (isMenuOpen || isNavigationKey(event.key) || isFilterReadOnly) {\n      return;\n    }\n    switch (event.key) {\n      case 'Escape':\n        if (isEditing) {\n          apiRef.current.stopHeaderFilterEditMode();\n        }\n        break;\n      case 'Enter':\n        if (isEditing) {\n          if (!event.defaultPrevented) {\n            apiRef.current.stopHeaderFilterEditMode();\n            break;\n          }\n        }\n        if (event.metaKey || event.ctrlKey) {\n          headerFilterMenuRef.current = buttonRef.current;\n          apiRef.current.showHeaderFilterMenu(colDef.field);\n          break;\n        }\n        apiRef.current.startHeaderFilterEditMode(colDef.field);\n        break;\n      case 'Tab':\n        {\n          if (isEditing) {\n            const fieldToFocus = columnFields[colIndex + (event.shiftKey ? -1 : 1)] ?? null;\n            if (fieldToFocus) {\n              apiRef.current.startHeaderFilterEditMode(fieldToFocus);\n              apiRef.current.setColumnHeaderFilterFocus(fieldToFocus, event);\n            }\n          }\n          break;\n        }\n      default:\n        if (isEditing || event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) {\n          break;\n        }\n        apiRef.current.startHeaderFilterEditMode(colDef.field);\n        break;\n    }\n  }, [apiRef, colDef.field, colIndex, columnFields, headerFilterMenuRef, isEditing, isFilterReadOnly, isMenuOpen]);\n  const publish = React.useCallback((eventName, propHandler) => event => {\n    apiRef.current.publishEvent(eventName, apiRef.current.getColumnHeaderParams(colDef.field), event);\n    if (propHandler) {\n      propHandler(event);\n    }\n  }, [apiRef, colDef.field]);\n  const onMouseDown = React.useCallback(event => {\n    if (!hasFocus) {\n      if (inputRef.current?.contains?.(event.target)) {\n        inputRef.current.focus();\n      }\n      apiRef.current.setColumnHeaderFilterFocus(colDef.field, event);\n    }\n  }, [apiRef, colDef.field, hasFocus]);\n  const mouseEventsHandlers = React.useMemo(() => ({\n    onKeyDown: publish('headerFilterKeyDown', onKeyDown),\n    onClick: publish('headerFilterClick'),\n    onMouseDown: publish('headerFilterMouseDown', onMouseDown),\n    onBlur: publish('headerFilterBlur')\n  }), [onMouseDown, onKeyDown, publish]);\n  const ownerState = _extends({}, rootProps, {\n    pinnedPosition,\n    colDef,\n    showLeftBorder,\n    showRightBorder\n  });\n  const classes = useUtilityClasses(ownerState);\n  const label = currentOperator.headerLabel ?? apiRef.current.getLocaleText(`headerFilterOperator${capitalize(item.operator)}`);\n  const isNoInputOperator = currentOperator.requiresFilterValue === false;\n  const isApplied = item?.value !== undefined || isNoInputOperator;\n  const isFilterActive = isApplied || hasFocus;\n  const headerFilterMenu = /*#__PURE__*/_jsx(GridHeaderFilterMenuContainer, {\n    operators: filterOperators,\n    item: item,\n    field: colDef.field,\n    disabled: isFilterReadOnly,\n    applyFilterChanges: apiRef.current.upsertFilterItem,\n    headerFilterMenuRef: headerFilterMenuRef,\n    buttonRef: buttonRef,\n    showClearItem: !showClearIcon && isApplied,\n    clearFilterItem: clearFilterItem\n  });\n  const clearButton = showClearIcon && isApplied ? /*#__PURE__*/_jsx(GridHeaderFilterClearButton, {\n    onClick: clearFilterItem,\n    disabled: isFilterReadOnly\n  }) : null;\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    className: clsx(classes.root, headerClassName),\n    style: attachPinnedStyle(_extends({\n      height,\n      width\n    }, styleProp), isRtl, pinnedPosition, pinnedOffset),\n    role: \"columnheader\",\n    \"aria-colindex\": colIndex + 1,\n    \"aria-label\": headerFilterComponent == null ? colDef.headerName ?? colDef.field : undefined\n  }, other, mouseEventsHandlers, {\n    ref: handleRef,\n    children: [headerFilterComponent, headerFilterComponent === undefined ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [isNoInputOperator ? /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [/*#__PURE__*/_jsx(OperatorLabel, {\n          className: classes.operatorLabel,\n          children: label\n        }), clearButton, headerFilterMenu]\n      }) : null, InputComponent && !isNoInputOperator ? /*#__PURE__*/_jsx(StyledInputComponent, _extends({\n        as: InputComponent,\n        className: classes.input,\n        apiRef: apiRef,\n        item: item,\n        inputRef: inputRef,\n        applyValue: apiRef.current.upsertFilterItem,\n        onFocus: () => apiRef.current.startHeaderFilterEditMode(colDef.field),\n        onBlur: event => {\n          apiRef.current.stopHeaderFilterEditMode();\n          // Blurring an input element should reset focus state only if `relatedTarget` is not the header filter cell\n          if (!event.relatedTarget?.className.includes('columnHeader')) {\n            apiRef.current.setState(state => _extends({}, state, {\n              focus: {\n                cell: null,\n                columnHeader: null,\n                columnHeaderFilter: null,\n                columnGroupHeader: null\n              }\n            }));\n          }\n        },\n        isFilterActive: isFilterActive,\n        headerFilterMenu: headerFilterMenu,\n        clearButton: clearButton,\n        disabled: isFilterReadOnly || isNoInputOperator,\n        tabIndex: -1,\n        slotProps: {\n          root: {\n            size: 'small',\n            label: capitalize(label),\n            placeholder: ''\n          }\n        }\n      }, isNoInputOperator ? {\n        value: ''\n      } : {}, currentOperator?.InputComponentProps, InputComponentProps)) : null]\n    }) : null]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridHeaderFilterCell.displayName = \"GridHeaderFilterCell\";\nprocess.env.NODE_ENV !== \"production\" ? GridHeaderFilterCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  colIndex: PropTypes.number.isRequired,\n  hasFocus: PropTypes.bool,\n  /**\n   * Class name added to the column header cell.\n   */\n  headerClassName: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  headerFilterMenuRef: PropTypes.shape({\n    current: PropTypes.object\n  }).isRequired,\n  height: PropTypes.number.isRequired,\n  InputComponentProps: PropTypes.shape({\n    apiRef: PropTypes.shape({\n      current: PropTypes.object.isRequired\n    }),\n    applyValue: PropTypes.func,\n    className: PropTypes.string,\n    clearButton: PropTypes.node,\n    disabled: PropTypes.bool,\n    focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      current: PropTypes.any.isRequired\n    })]),\n    headerFilterMenu: PropTypes.node,\n    inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      current: (props, propName) => {\n        if (props[propName] == null) {\n          return null;\n        }\n        if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n          return new Error(`Expected prop '${propName}' to be of type Element`);\n        }\n        return null;\n      }\n    })]),\n    isFilterActive: PropTypes.bool,\n    item: PropTypes.shape({\n      field: PropTypes.string.isRequired,\n      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n      operator: PropTypes.string.isRequired,\n      value: PropTypes.any\n    }),\n    onBlur: PropTypes.func,\n    onFocus: PropTypes.func,\n    slotProps: PropTypes.object,\n    tabIndex: PropTypes.number\n  }),\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  pinnedOffset: PropTypes.number,\n  pinnedPosition: PropTypes.oneOf([0, 1, 2, 3]),\n  showClearIcon: PropTypes.bool,\n  showLeftBorder: PropTypes.bool.isRequired,\n  showRightBorder: PropTypes.bool.isRequired,\n  sortIndex: PropTypes.number,\n  style: PropTypes.object,\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  width: PropTypes.number.isRequired\n} : void 0;\nconst Memoized = fastMemo(GridHeaderFilterCell);\nexport { Memoized as GridHeaderFilterCell };", "'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"operators\", \"item\", \"field\", \"buttonRef\", \"headerFilterMenuRef\", \"disabled\", \"showClearItem\", \"clearFilterItem\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridApiContext, useGridSelector } from '@mui/x-data-grid';\nimport refType from '@mui/utils/refType';\nimport useId from '@mui/utils/useId';\nimport { gridHeaderFilteringMenuSelector } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridHeaderFilterMenuContainer(props) {\n  const {\n      operators,\n      item,\n      field,\n      buttonRef,\n      headerFilterMenuRef,\n      disabled = false,\n      showClearItem,\n      clearFilterItem\n    } = props,\n    others = _objectWithoutPropertiesLoose(props, _excluded);\n  const buttonId = useId();\n  const menuId = useId();\n  const rootProps = useGridRootProps();\n  const apiRef = useGridApiContext();\n  const menuOpenField = useGridSelector(apiRef, gridHeaderFilteringMenuSelector);\n  const open = Boolean(menuOpenField === field && headerFilterMenuRef.current);\n  const handleClick = event => {\n    headerFilterMenuRef.current = event.currentTarget;\n    apiRef.current.showHeaderFilterMenu(field);\n  };\n  if (!rootProps.slots.headerFilterMenu) {\n    return null;\n  }\n  const label = apiRef.current.getLocaleText('filterPanelOperator');\n  const labelString = label ? String(label) : undefined;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n      id: buttonId,\n      ref: buttonRef,\n      \"aria-label\": labelString,\n      title: labelString,\n      \"aria-controls\": menuId,\n      \"aria-expanded\": open ? 'true' : undefined,\n      \"aria-haspopup\": \"true\",\n      tabIndex: -1,\n      size: \"small\",\n      onClick: handleClick,\n      disabled: disabled\n    }, rootProps.slotProps?.baseIconButton, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseBadge, {\n        color: \"primary\",\n        variant: \"dot\",\n        badgeContent: showClearItem ? 1 : 0,\n        children: /*#__PURE__*/_jsx(rootProps.slots.openFilterButtonIcon, {\n          fontSize: \"inherit\"\n        })\n      })\n    })), /*#__PURE__*/_jsx(rootProps.slots.headerFilterMenu, _extends({\n      field: field,\n      open: open,\n      item: item,\n      target: headerFilterMenuRef.current,\n      operators: operators,\n      labelledBy: buttonId,\n      id: menuId,\n      clearFilterItem: clearFilterItem,\n      showClearItem: showClearItem\n    }, others))]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridHeaderFilterMenuContainer.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  applyFilterChanges: PropTypes.func.isRequired,\n  buttonRef: refType,\n  clearFilterItem: PropTypes.func,\n  disabled: PropTypes.bool,\n  field: PropTypes.string.isRequired,\n  headerFilterMenuRef: PropTypes.shape({\n    current: PropTypes.object\n  }).isRequired,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  operators: PropTypes.arrayOf(PropTypes.shape({\n    getApplyFilterFn: PropTypes.func.isRequired,\n    getValueAsString: PropTypes.func,\n    headerLabel: PropTypes.string,\n    InputComponent: PropTypes.elementType,\n    InputComponentProps: PropTypes.shape({\n      apiRef: PropTypes.shape({\n        current: PropTypes.object.isRequired\n      }),\n      applyValue: PropTypes.func,\n      className: PropTypes.string,\n      clearButton: PropTypes.node,\n      disabled: PropTypes.bool,\n      focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n        current: PropTypes.any.isRequired\n      })]),\n      headerFilterMenu: PropTypes.node,\n      inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n        current: (props, propName) => {\n          if (props[propName] == null) {\n            return null;\n          }\n          if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n            return new Error(`Expected prop '${propName}' to be of type Element`);\n          }\n          return null;\n        }\n      })]),\n      isFilterActive: PropTypes.bool,\n      item: PropTypes.shape({\n        field: PropTypes.string.isRequired,\n        id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n        operator: PropTypes.string.isRequired,\n        value: PropTypes.any\n      }),\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      slotProps: PropTypes.object,\n      tabIndex: PropTypes.number\n    }),\n    label: PropTypes.string,\n    requiresFilterValue: PropTypes.bool,\n    value: PropTypes.string.isRequired\n  })).isRequired,\n  showClearItem: PropTypes.bool\n} : void 0;\nexport { GridHeaderFilterMenuContainer };", "import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;", "'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\n\n// TODO React 17: Remove `useGlobalId` once React 17 support is removed\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseId = safeReact.useId;\n\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  // React.useId() is only available from React 17.0.0.\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride ?? reactId;\n  }\n\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridHeaderFilterClearButton(props) {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n    tabIndex: -1,\n    \"aria-label\": \"Clear filter\",\n    size: \"small\"\n  }, rootProps.slotProps?.baseIconButton, props, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuClearIcon, {\n      fontSize: \"inherit\"\n    })\n  }));\n}\nexport { GridHeaderFilterClearButton };", "'use client';\n\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass, useGridSelector } from '@mui/x-data-grid';\nimport { useGridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridDetailPanelExpandedRowsContentCacheSelector, gridDetailPanelExpandedRowIdsSelector } from \"../hooks/features/detailPanel/index.js\";\nimport { GridDetailPanel } from \"./GridDetailPanel.js\";\nimport { gridDetailPanelRawHeightCacheSelector } from \"../hooks/features/detailPanel/gridDetailPanelSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    detailPanel: ['detailPanel']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nexport function GridDetailPanels(props) {\n  const rootProps = useGridRootProps();\n  if (!rootProps.getDetailPanelContent) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(GridDetailPanelsImpl, props);\n}\nfunction GridDetailPanelsImpl({\n  virtualScroller\n}) {\n  const apiRef = useGridPrivateApiContext();\n  const classes = useUtilityClasses();\n  const {\n    setPanels\n  } = virtualScroller;\n  const expandedRowIds = useGridSelector(apiRef, gridDetailPanelExpandedRowIdsSelector);\n  const detailPanelsContent = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);\n  const detailPanelsHeights = useGridSelector(apiRef, gridDetailPanelRawHeightCacheSelector);\n  const getDetailPanel = React.useCallback(rowId => {\n    const content = detailPanelsContent[rowId];\n\n    // Check if the id exists in the current page\n    const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(rowId);\n    const exists = rowIndex !== undefined;\n    if (! /*#__PURE__*/React.isValidElement(content) || !exists) {\n      return null;\n    }\n    const heightCache = detailPanelsHeights[rowId];\n    const height = heightCache.autoHeight ? 'auto' : heightCache.height;\n    return /*#__PURE__*/_jsx(GridDetailPanel, {\n      rowId: rowId,\n      height: height,\n      className: classes.detailPanel,\n      children: content\n    }, `panel-${rowId}`);\n  }, [apiRef, classes.detailPanel, detailPanelsHeights, detailPanelsContent]);\n  React.useEffect(() => {\n    const map = new Map();\n    for (const rowId of expandedRowIds) {\n      map.set(rowId, getDetailPanel(rowId));\n    }\n    setPanels(map);\n  }, [expandedRowIds, setPanels, getDetailPanel]);\n  return null;\n}", "import { useGridPrivateApiContext as useCommunityGridPrivateApiContext } from '@mui/x-data-grid/internals';\nexport const useGridPrivateApiContext = useCommunityGridPrivateApiContext;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GRID_STRING_COL_DEF, gridRowIdSelector } from '@mui/x-data-grid';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from '@mui/x-data-grid/internals';\nimport { GridDetailPanelToggleCell } from \"../../../components/GridDetailPanelToggleCell.js\";\nimport { gridDetailPanelExpandedRowIdsSelector } from \"./gridDetailPanelSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport { GRID_DETAIL_PANEL_TOGGLE_FIELD };\nexport const GRID_DETAIL_PANEL_TOGGLE_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'custom',\n  field: GRID_DETAIL_PANEL_TOGGLE_FIELD,\n  editable: false,\n  sortable: false,\n  filterable: false,\n  resizable: false,\n  // @ts-ignore\n  aggregable: false,\n  disableColumnMenu: true,\n  disableReorder: true,\n  disableExport: true,\n  align: 'left',\n  width: 40,\n  valueGetter: (value, row, column, apiRef) => {\n    const rowId = gridRowIdSelector(apiRef, row);\n    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(apiRef);\n    return expandedRowIds.has(rowId);\n  },\n  rowSpanValueGetter: (_, row, __, apiRef) => gridRowIdSelector(apiRef, row),\n  renderCell: params => /*#__PURE__*/_jsx(GridDetailPanelToggleCell, _extends({}, params)),\n  renderHeader: ({\n    colDef\n  }) => /*#__PURE__*/_jsx(\"div\", {\n    \"aria-label\": colDef.headerName\n  })\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass, useGridSelector } from '@mui/x-data-grid';\nimport { createSelector } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { gridDetailPanelExpandedRowIdsSelector, gridDetailPanelExpandedRowsContentCacheSelector } from \"../hooks/features/detailPanel/gridDetailPanelSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isExpanded\n  } = ownerState;\n  const slots = {\n    root: ['detailPanelToggleCell', isExpanded && 'detailPanelToggleCell--expanded']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst isExpandedSelector = createSelector(gridDetailPanelExpandedRowIdsSelector, (expandedRowIds, rowId) => {\n  return expandedRowIds.has(rowId);\n});\nfunction GridDetailPanelToggleCell(props) {\n  const {\n    id,\n    row,\n    api\n  } = props;\n  const rowId = api.getRowId(row);\n  const isExpanded = useGridSelector({\n    current: api\n  }, isExpandedSelector, rowId);\n  const rootProps = useGridRootProps();\n  const apiRef = useGridApiContext();\n  const ownerState = {\n    classes: rootProps.classes,\n    isExpanded\n  };\n  const classes = useUtilityClasses(ownerState);\n  const contentCache = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);\n  const hasContent = /*#__PURE__*/React.isValidElement(contentCache[id]);\n  const Icon = isExpanded ? rootProps.slots.detailPanelCollapseIcon : rootProps.slots.detailPanelExpandIcon;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n    size: \"small\",\n    tabIndex: -1,\n    disabled: !hasContent,\n    className: classes.root,\n    \"aria-expanded\": isExpanded,\n    \"aria-label\": isExpanded ? apiRef.current.getLocaleText('collapseDetailPanel') : apiRef.current.getLocaleText('expandDetailPanel')\n  }, rootProps.slotProps?.baseIconButton, {\n    children: /*#__PURE__*/_jsx(Icon, {\n      fontSize: \"inherit\"\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridDetailPanelToggleCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridDetailPanelToggleCell };", "import { createSelector, createRootSelector, createSelectorMemoized } from '@mui/x-data-grid/internals';\nexport const gridDetailPanelStateSelector = createRootSelector(state => state.detailPanel);\nexport const gridDetailPanelExpandedRowIdsSelector = createSelector(gridDetailPanelStateSelector, detailPanelState => detailPanelState.expandedRowIds);\nexport const gridDetailPanelExpandedRowsContentCacheSelector = createSelector(gridDetailPanelStateSelector, detailPanelState => detailPanelState.contentCache);\nexport const gridDetailPanelRawHeightCacheSelector = createSelectorMemoized(gridDetailPanelStateSelector, detailPanelState => detailPanelState.heightCache);", "'use client';\n\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport { gridRowNodeSelector } from '@mui/x-data-grid';\nimport { vars } from '@mui/x-data-grid/internals';\nimport { useResizeObserver } from '@mui/x-internals/useResizeObserver';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { useGridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DetailPanel = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'DetailPanel'\n})({\n  width: 'calc(var(--DataGrid-rowWidth) - var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))',\n  backgroundColor: vars.colors.background.base,\n  overflow: 'auto'\n});\nfunction GridDetailPanel(props) {\n  const {\n    rowId,\n    height,\n    className,\n    children\n  } = props;\n  const apiRef = useGridPrivateApiContext();\n  const ref = React.useRef(null);\n  const rootProps = useGridRootProps();\n  const ownerState = rootProps;\n  const hasAutoHeight = height === 'auto';\n  const rowNode = gridRowNodeSelector(apiRef, rowId);\n  React.useLayoutEffect(() => {\n    if (hasAutoHeight && typeof ResizeObserver === 'undefined') {\n      // Fallback for IE\n      apiRef.current.storeDetailPanelHeight(rowId, ref.current.clientHeight);\n    }\n  }, [apiRef, hasAutoHeight, rowId]);\n  useResizeObserver(ref, entries => {\n    const [entry] = entries;\n    const observedHeight = entry.borderBoxSize && entry.borderBoxSize.length > 0 ? entry.borderBoxSize[0].blockSize : entry.contentRect.height;\n    apiRef.current.storeDetailPanelHeight(rowId, observedHeight);\n  }, hasAutoHeight);\n  if (rowNode?.type === 'skeletonRow') {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(DetailPanel, {\n    ref: ref,\n    ownerState: ownerState,\n    role: \"presentation\",\n    style: {\n      height\n    },\n    className: className,\n    children: children\n  });\n}\nexport { GridDetailPanel };", "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nconst isDevEnvironment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nconst noop = () => {};\nexport function useResizeObserver(ref, fn, enabled) {\n  const fnRef = React.useRef(null);\n  fnRef.current = fn;\n  useEnhancedEffect(() => {\n    if (enabled === false || typeof ResizeObserver === 'undefined') {\n      return noop;\n    }\n    let frameID = 0;\n    const target = ref.current;\n    const observer = new ResizeObserver(entries => {\n      // See https://github.com/mui/mui-x/issues/8733\n      // In dev, we avoid the React warning by moving the task to the next frame.\n      // In prod, we want the task to run in the same frame as to avoid tear.\n      if (isDevEnvironment) {\n        frameID = requestAnimationFrame(() => {\n          fnRef.current(entries);\n        });\n      } else {\n        fnRef.current(entries);\n      }\n    });\n    if (target) {\n      observer.observe(target);\n    }\n    return () => {\n      if (frameID) {\n        cancelAnimationFrame(frameID);\n      }\n      observer.disconnect();\n    };\n  }, [ref, enabled]);\n}", "import * as React from 'react';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass, gridClasses, gridRowTreeSelector, useGridSelector } from '@mui/x-data-grid';\nimport { gridPinnedRowsSelector, useGridPrivateApiContext } from '@mui/x-data-grid/internals';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['pinnedRows']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nexport function GridPinnedRows({\n  position,\n  virtualScroller\n}) {\n  const classes = useUtilityClasses();\n  const apiRef = useGridPrivateApiContext();\n  const pinnedRowsData = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const rows = pinnedRowsData[position];\n  const {\n    getRows\n  } = virtualScroller;\n  const pinnedRenderContext = React.useMemo(() => ({\n    firstRowIndex: 0,\n    lastRowIndex: rows.length,\n    firstColumnIndex: -1,\n    lastColumnIndex: -1\n  }), [rows]);\n  if (rows.length === 0) {\n    return null;\n  }\n  const pinnedRows = getRows({\n    position,\n    rows,\n    renderContext: pinnedRenderContext\n  }, gridRowTreeSelector(apiRef));\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: clsx(classes.root, gridClasses[`pinnedRows--${position}`]),\n    role: \"presentation\",\n    children: pinnedRows\n  });\n}", "import * as React from 'react';\nimport { createSvgIcon } from '@mui/x-data-grid/internals';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridPushPinRightIcon = createSvgIcon(/*#__PURE__*/_jsx(\"g\", {\n  transform: \"rotate(-30 15 10)\",\n  children: /*#__PURE__*/_jsx(\"path\", {\n    d: \"M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z\",\n    fillRule: \"evenodd\"\n  })\n}), 'PushPinRight');\nexport const GridPushPinLeftIcon = createSvgIcon(/*#__PURE__*/_jsx(\"g\", {\n  transform: \"rotate(30 8 12)\",\n  children: /*#__PURE__*/_jsx(\"path\", {\n    d: \"M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z\",\n    fillRule: \"evenodd\"\n  })\n}), 'PushPinLeft');", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GridPushPinRightIcon, GridPushPinLeftIcon } from \"./icons.js\";\nconst iconSlots = {\n  columnMenuPinRightIcon: GridPushPinRightIcon,\n  columnMenuPinLeftIcon: GridPushPinLeftIcon\n};\nconst materialSlots = _extends({}, iconSlots);\nexport default materialSlots;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from '@mui/x-data-grid/internals';\nimport { GridProColumnMenu } from \"../components/GridProColumnMenu.js\";\nimport { GridColumnHeaders } from \"../components/GridColumnHeaders.js\";\nimport { GridHeaderFilterMenu } from \"../components/headerFiltering/GridHeaderFilterMenu.js\";\nimport { GridHeaderFilterCell } from \"../components/headerFiltering/GridHeaderFilterCell.js\";\nimport { GridDetailPanels } from \"../components/GridDetailPanels.js\";\nimport { GridPinnedRows } from \"../components/GridPinnedRows.js\";\nimport materialSlots from \"../material/index.js\";\nexport const DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS = _extends({}, DATA_GRID_DEFAULT_SLOTS_COMPONENTS, materialSlots, {\n  columnMenu: GridProColumnMenu,\n  columnHeaders: GridColumnHeaders,\n  detailPanels: GridDetailPanels,\n  headerFilterCell: GridHeaderFilterCell,\n  headerFilterMenu: GridHeaderFilterMenu,\n  pinnedRows: GridPinnedRows\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useGridAriaAttributes as useGridAriaAttributesCommunity } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"./useGridRootProps.js\";\nexport const useGridAriaAttributesPro = () => {\n  const ariaAttributesCommunity = useGridAriaAttributesCommunity();\n  const rootProps = useGridRootProps();\n  const ariaAttributesPro = rootProps.treeData ? {\n    role: 'treegrid'\n  } : {};\n  return _extends({}, ariaAttributesCommunity, ariaAttributesPro);\n};", "import * as React from 'react';\nimport { useGridSelector, gridFilteredTopLevelRowCountSelector, GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { useGridRowAriaAttributes as useGridRowAriaAttributesCommunity, gridFilteredChildrenCountLookupSelector, gridExpandedSortedRowTreeLevelPositionLookupSelector } from '@mui/x-data-grid/internals';\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nexport const useGridRowAriaAttributesPro = addTreeDataAttributes => {\n  const apiRef = useGridPrivateApiContext();\n  const props = useGridRootProps();\n  const getRowAriaAttributesCommunity = useGridRowAriaAttributesCommunity();\n  const filteredTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const filteredChildrenCountLookup = useGridSelector(apiRef, gridFilteredChildrenCountLookupSelector);\n  const sortedVisibleRowPositionsLookup = useGridSelector(apiRef, gridExpandedSortedRowTreeLevelPositionLookupSelector);\n  return React.useCallback((rowNode, index) => {\n    const ariaAttributes = getRowAriaAttributesCommunity(rowNode, index);\n    if (!rowNode || !(props.treeData || addTreeDataAttributes)) {\n      return ariaAttributes;\n    }\n\n    // pinned and footer rows are not part of the rowgroup and should not get the set specific aria attributes\n    if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n      return ariaAttributes;\n    }\n    ariaAttributes['aria-level'] = rowNode.depth + 1;\n    const filteredChildrenCount = filteredChildrenCountLookup[rowNode.id] ?? 0;\n    // aria-expanded should only be added to the rows that contain children\n    if (rowNode.type === 'group' && filteredChildrenCount > 0) {\n      ariaAttributes['aria-expanded'] = Boolean(rowNode.childrenExpanded);\n    }\n\n    // if the parent is null, set size and position cannot be determined\n    if (rowNode.parent !== null) {\n      ariaAttributes['aria-setsize'] = rowNode.parent === GRID_ROOT_GROUP_ID ? filteredTopLevelRowCount : filteredChildrenCountLookup[rowNode.parent];\n      ariaAttributes['aria-posinset'] = sortedVisibleRowPositionsLookup[rowNode.id];\n    }\n    return ariaAttributes;\n  }, [props.treeData, addTreeDataAttributes, filteredTopLevelRowCount, filteredChildrenCountLookup, sortedVisibleRowPositionsLookup, getRowAriaAttributesCommunity]);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridSelector, gridVisibleColumnDefinitionsSelector, gridColumnsTotalWidthSelector, gridColumnPositionsSelector, useGridApiMethod, useGridEvent, GridPinnedColumnPosition, gridColumnFieldsSelector } from '@mui/x-data-grid';\nimport { useGridRegisterPipeProcessor, gridPinnedColumnsSelector, gridVisiblePinnedColumnDefinitionsSelector } from '@mui/x-data-grid/internals';\nexport const columnPinningStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.columnPinning = {\n    orderedFieldsBeforePinningColumns: null\n  };\n  let model;\n  if (props.pinnedColumns) {\n    model = props.pinnedColumns;\n  } else if (props.initialState?.pinnedColumns) {\n    model = props.initialState.pinnedColumns;\n  } else {\n    model = {};\n  }\n  return _extends({}, state, {\n    pinnedColumns: model\n  });\n};\nexport const useGridColumnPinning = (apiRef, props) => {\n  const pinnedColumns = useGridSelector(apiRef, gridPinnedColumnsSelector);\n\n  /**\n   * PRE-PROCESSING\n   */\n  const calculateScrollLeft = React.useCallback((initialValue, params) => {\n    const visiblePinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);\n    if (!params.colIndex || visiblePinnedColumns.left.length === 0 && visiblePinnedColumns.right.length === 0) {\n      return initialValue;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const columnsTotalWidth = gridColumnsTotalWidthSelector(apiRef);\n    const columnPositions = gridColumnPositionsSelector(apiRef);\n    const clientWidth = apiRef.current.virtualScrollerRef.current.clientWidth;\n\n    // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n    const scrollLeft = Math.abs(apiRef.current.virtualScrollerRef.current.scrollLeft);\n    const offsetWidth = visibleColumns[params.colIndex].computedWidth;\n    const offsetLeft = columnPositions[params.colIndex];\n    const leftPinnedColumnsWidth = columnPositions[visiblePinnedColumns.left.length];\n    const rightPinnedColumnsWidth = columnsTotalWidth - columnPositions[columnPositions.length - visiblePinnedColumns.right.length];\n    const elementBottom = offsetLeft + offsetWidth;\n    if (elementBottom - (clientWidth - rightPinnedColumnsWidth) > scrollLeft) {\n      const left = elementBottom - (clientWidth - rightPinnedColumnsWidth);\n      return _extends({}, initialValue, {\n        left\n      });\n    }\n    if (offsetLeft < scrollLeft + leftPinnedColumnsWidth) {\n      const left = offsetLeft - leftPinnedColumnsWidth;\n      return _extends({}, initialValue, {\n        left\n      });\n    }\n    return initialValue;\n  }, [apiRef]);\n  const addColumnMenuItems = React.useCallback((columnMenuItems, colDef) => {\n    if (props.disableColumnPinning) {\n      return columnMenuItems;\n    }\n    if (colDef.pinnable === false) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuPinningItem'];\n  }, [props.disableColumnPinning]);\n  const checkIfCanBeReordered = React.useCallback((initialValue, {\n    targetIndex\n  }) => {\n    const visiblePinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);\n    if (visiblePinnedColumns.left.length === 0 && visiblePinnedColumns.right.length === 0) {\n      return initialValue;\n    }\n    if (visiblePinnedColumns.left.length > 0 && targetIndex < visiblePinnedColumns.left.length) {\n      return false;\n    }\n    if (visiblePinnedColumns.right.length > 0) {\n      const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n      const firstRightPinnedColumnIndex = visibleColumns.length - visiblePinnedColumns.right.length;\n      return targetIndex >= firstRightPinnedColumnIndex ? false : initialValue;\n    }\n    return initialValue;\n  }, [apiRef]);\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const pinnedColumnsToExport = gridPinnedColumnsSelector(apiRef);\n    const shouldExportPinnedColumns =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.pinnedColumns != null ||\n    // Always export if the model has been initialized\n    props.initialState?.pinnedColumns != null ||\n    // Export if the model is not empty\n    (pinnedColumnsToExport.left ?? []).length > 0 || (pinnedColumnsToExport.right ?? []).length > 0;\n    if (!shouldExportPinnedColumns) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pinnedColumns: pinnedColumnsToExport\n    });\n  }, [apiRef, props.pinnedColumns, props.initialState?.pinnedColumns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const newPinnedColumns = context.stateToRestore.pinnedColumns;\n    if (newPinnedColumns != null) {\n      setState(apiRef, newPinnedColumns);\n    }\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'scrollToIndexes', calculateScrollLeft);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'canBeReordered', checkIfCanBeReordered);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  apiRef.current.registerControlState({\n    stateId: 'pinnedColumns',\n    propModel: props.pinnedColumns,\n    propOnChange: props.onPinnedColumnsChange,\n    stateSelector: gridPinnedColumnsSelector,\n    changeEvent: 'pinnedColumnsChange'\n  });\n  const pinColumn = React.useCallback((field, side) => {\n    if (apiRef.current.isColumnPinned(field) === side) {\n      return;\n    }\n    const otherSide = side === GridPinnedColumnPosition.RIGHT ? GridPinnedColumnPosition.LEFT : GridPinnedColumnPosition.RIGHT;\n    const newPinnedColumns = {\n      [side]: [...(pinnedColumns[side] || []), field],\n      [otherSide]: (pinnedColumns[otherSide] || []).filter(column => column !== field)\n    };\n    apiRef.current.setPinnedColumns(newPinnedColumns);\n  }, [apiRef, pinnedColumns]);\n  const unpinColumn = React.useCallback(field => {\n    apiRef.current.setPinnedColumns({\n      left: (pinnedColumns.left || []).filter(column => column !== field),\n      right: (pinnedColumns.right || []).filter(column => column !== field)\n    });\n  }, [apiRef, pinnedColumns.left, pinnedColumns.right]);\n  const getPinnedColumns = React.useCallback(() => {\n    return gridPinnedColumnsSelector(apiRef);\n  }, [apiRef]);\n  const setPinnedColumns = React.useCallback(newPinnedColumns => {\n    setState(apiRef, newPinnedColumns);\n    apiRef.current.requestPipeProcessorsApplication('hydrateColumns');\n  }, [apiRef]);\n  const isColumnPinned = React.useCallback(field => {\n    const leftPinnedColumns = pinnedColumns.left || [];\n    if (leftPinnedColumns.includes(field)) {\n      return GridPinnedColumnPosition.LEFT;\n    }\n    const rightPinnedColumns = pinnedColumns.right || [];\n    if (rightPinnedColumns.includes(field)) {\n      return GridPinnedColumnPosition.RIGHT;\n    }\n    return false;\n  }, [pinnedColumns.left, pinnedColumns.right]);\n  const columnPinningApi = {\n    pinColumn,\n    unpinColumn,\n    getPinnedColumns,\n    setPinnedColumns,\n    isColumnPinned\n  };\n  useGridApiMethod(apiRef, columnPinningApi, 'public');\n  const handleColumnOrderChange = params => {\n    if (!apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns) {\n      return;\n    }\n    const {\n      column,\n      targetIndex,\n      oldIndex\n    } = params;\n    const delta = targetIndex > oldIndex ? 1 : -1;\n    const latestColumnFields = gridColumnFieldsSelector(apiRef);\n\n    /**\n     * When a column X is reordered to somewhere else, the position where this column X is dropped\n     * on must be moved to left or right to make room for it. The ^^^ below represents the column\n     * which gave space to receive X.\n     *\n     * | X | B | C | D | -> | B | C | D | X | (for example X moved to after D, so delta=1)\n     *              ^^^              ^^^\n     *\n     * | A | B | C | X | -> | X | A | B | C | (for example X moved before A, so delta=-1)\n     *  ^^^                      ^^^\n     *\n     * If column P is pinned, it will not move to provide space. However, it will jump to the next\n     * non-pinned column.\n     *\n     * | X | B | P | D | -> | B | D | P | X | (for example X moved to after D, with P pinned)\n     *              ^^^          ^^^\n     */\n    const siblingField = latestColumnFields[targetIndex - delta];\n    const newOrderedFieldsBeforePinningColumns = [...apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns];\n\n    // The index to start swapping fields\n    let i = newOrderedFieldsBeforePinningColumns.findIndex(currentColumn => currentColumn === column.field);\n    // The index of the field to swap with\n    let j = i + delta;\n\n    // When to stop swapping fields.\n    // We stop one field before because the swap is done with i + 1 (if delta=1)\n    const stop = newOrderedFieldsBeforePinningColumns.findIndex(currentColumn => currentColumn === siblingField);\n    while (delta > 0 ? i < stop : i > stop) {\n      // If the field to swap with is a pinned column, jump to the next\n      while (apiRef.current.isColumnPinned(newOrderedFieldsBeforePinningColumns[j])) {\n        j += delta;\n      }\n      const temp = newOrderedFieldsBeforePinningColumns[i];\n      newOrderedFieldsBeforePinningColumns[i] = newOrderedFieldsBeforePinningColumns[j];\n      newOrderedFieldsBeforePinningColumns[j] = temp;\n      i = j;\n      j = i + delta;\n    }\n    apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = newOrderedFieldsBeforePinningColumns;\n  };\n  useGridEvent(apiRef, 'columnOrderChange', handleColumnOrderChange);\n  React.useEffect(() => {\n    if (props.pinnedColumns) {\n      apiRef.current.setPinnedColumns(props.pinnedColumns);\n    }\n  }, [apiRef, props.pinnedColumns]);\n};\nfunction setState(apiRef, model) {\n  apiRef.current.setState(state => _extends({}, state, {\n    pinnedColumns: model\n  }));\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRegisterPipeProcessor, gridExistingPinnedColumnSelector } from '@mui/x-data-grid/internals';\nexport const useGridColumnPinningPreProcessors = (apiRef, props) => {\n  const {\n    disableColumnPinning\n  } = props;\n  const prevAllPinnedColumns = React.useRef([]);\n  const reorderPinnedColumns = React.useCallback(columnsState => {\n    if (columnsState.orderedFields.length === 0 || disableColumnPinning) {\n      return columnsState;\n    }\n\n    // HACK: This is a hack needed because the pipe processors aren't pure enough. What\n    // they should be is `gridState -> gridState` transformers, but they only transform a slice\n    // of the state, not the full state. So if they need access to other parts of the state (like\n    // the `state.columns.orderedFields` in this case), they might lag behind because the selectors\n    // are selecting the old state in `apiRef`, not the state being computed in the current pipe processor.\n    const savedState = apiRef.current.state;\n    apiRef.current.state = _extends({}, savedState, {\n      columns: columnsState\n    });\n    const pinnedColumns = gridExistingPinnedColumnSelector(apiRef);\n    apiRef.current.state = savedState;\n    // HACK: Ends here //\n\n    const leftPinnedColumns = pinnedColumns.left;\n    const rightPinnedColumns = pinnedColumns.right;\n    let newOrderedFields;\n    const allPinnedColumns = [...leftPinnedColumns, ...rightPinnedColumns];\n    const {\n      orderedFieldsBeforePinningColumns\n    } = apiRef.current.caches.columnPinning;\n    if (orderedFieldsBeforePinningColumns) {\n      newOrderedFields = new Array(columnsState.orderedFields.length).fill(null);\n      const newOrderedFieldsBeforePinningColumns = [...newOrderedFields];\n\n      // Contains the fields not added to the orderedFields array yet\n      const remainingFields = [...columnsState.orderedFields];\n\n      // First, we check if the column was unpinned since the last processing.\n      // If yes and it still exists, we move it back to the same position it was before pinning\n      prevAllPinnedColumns.current.forEach(field => {\n        if (!allPinnedColumns.includes(field) && columnsState.lookup[field]) {\n          // Get the position before pinning\n          const index = orderedFieldsBeforePinningColumns.indexOf(field);\n          newOrderedFields[index] = field;\n          newOrderedFieldsBeforePinningColumns[index] = field;\n          // This field was already consumed so we prevent from being added again\n          remainingFields.splice(remainingFields.indexOf(field), 1);\n        }\n      });\n\n      // For columns still pinned, we keep stored their original positions\n      allPinnedColumns.forEach(field => {\n        let index = orderedFieldsBeforePinningColumns.indexOf(field);\n        // If index = -1, the pinned field didn't exist in the last processing, it's possibly being added now\n        // If index >= newOrderedFieldsBeforePinningColumns.length, then one or more columns were removed\n        // In both cases, use the position from the columns array\n        // TODO: detect removed columns and decrease the positions after it\n        if (index === -1 || index >= newOrderedFieldsBeforePinningColumns.length) {\n          index = columnsState.orderedFields.indexOf(field);\n        }\n\n        // The fallback above may make the column to be inserted in a position already occupied\n        // In this case, put it in any empty slot available\n        if (newOrderedFieldsBeforePinningColumns[index] !== null) {\n          index = 0;\n          while (newOrderedFieldsBeforePinningColumns[index] !== null) {\n            index += 1;\n          }\n        }\n        newOrderedFields[index] = field;\n        newOrderedFieldsBeforePinningColumns[index] = field;\n        // This field was already consumed so we prevent from being added again\n        remainingFields.splice(remainingFields.indexOf(field), 1);\n      });\n\n      // The fields remaining are those that're neither pinnned nor were unpinned\n      // For these, we spread them across both arrays making sure to not override existing values\n      let i = 0;\n      remainingFields.forEach(field => {\n        while (newOrderedFieldsBeforePinningColumns[i] !== null) {\n          i += 1;\n        }\n        newOrderedFieldsBeforePinningColumns[i] = field;\n        newOrderedFields[i] = field;\n      });\n      apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = newOrderedFieldsBeforePinningColumns;\n    } else {\n      newOrderedFields = [...columnsState.orderedFields];\n      apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = [...columnsState.orderedFields];\n    }\n    prevAllPinnedColumns.current = allPinnedColumns;\n    const centerColumns = newOrderedFields.filter(field => {\n      return !leftPinnedColumns.includes(field) && !rightPinnedColumns.includes(field);\n    });\n    return _extends({}, columnsState, {\n      orderedFields: [...leftPinnedColumns, ...centerColumns, ...rightPinnedColumns]\n    });\n  }, [apiRef, disableColumnPinning]);\n  useGridRegisterPipeProcessor(apiRef, 'hydrateColumns', reorderPinnedColumns);\n  const isColumnPinned = React.useCallback((initialValue, field) => apiRef.current.isColumnPinned(field), [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'isColumnPinned', isColumnPinned);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridEvent, getDataGridUtilityClass, useGridLogger, useGridEventPriority } from '@mui/x-data-grid';\nimport { gridColumnReorderDragColSelector } from \"./columnReorderSelector.js\";\nconst CURSOR_MOVE_DIRECTION_LEFT = 'left';\nconst CURSOR_MOVE_DIRECTION_RIGHT = 'right';\nconst getCursorMoveDirectionX = (currentCoordinates, nextCoordinates) => {\n  return currentCoordinates.x <= nextCoordinates.x ? CURSOR_MOVE_DIRECTION_RIGHT : CURSOR_MOVE_DIRECTION_LEFT;\n};\nconst hasCursorPositionChanged = (currentCoordinates, nextCoordinates) => currentCoordinates.x !== nextCoordinates.x || currentCoordinates.y !== nextCoordinates.y;\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    columnHeaderDragging: ['columnHeader--dragging']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nexport const columnReorderStateInitializer = state => _extends({}, state, {\n  columnReorder: {\n    dragCol: ''\n  }\n});\n\n/**\n * @requires useGridColumns (method)\n */\nexport const useGridColumnReorder = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridColumnReorder');\n  const dragColNode = React.useRef(null);\n  const cursorPosition = React.useRef({\n    x: 0,\n    y: 0\n  });\n  const originColumnIndex = React.useRef(null);\n  const forbiddenIndexes = React.useRef({});\n  const removeDnDStylesTimeout = React.useRef(undefined);\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const isRtl = useRtl();\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(removeDnDStylesTimeout.current);\n    };\n  }, []);\n  const handleDragEnd = React.useCallback((params, event) => {\n    const dragColField = gridColumnReorderDragColSelector(apiRef);\n    if (props.disableColumnReorder || !dragColField) {\n      return;\n    }\n    logger.debug('End dragging col');\n    event.preventDefault();\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    clearTimeout(removeDnDStylesTimeout.current);\n\n    // For more information check here https://github.com/mui/mui-x/issues/14678\n    if (dragColNode.current.classList.contains(classes.columnHeaderDragging)) {\n      dragColNode.current.classList.remove(classes.columnHeaderDragging);\n    }\n    dragColNode.current = null;\n\n    // Check if the column was dropped outside the grid.\n    if (event.dataTransfer.dropEffect === 'none' && !props.keepColumnPositionIfDraggedOutside) {\n      // Accessing params.field may contain the wrong field as header elements are reused\n      apiRef.current.setColumnIndex(dragColField, originColumnIndex.current);\n      originColumnIndex.current = null;\n    } else {\n      // Emit the columnOrderChange event only once when the reordering stops.\n      const columnOrderChangeParams = {\n        column: apiRef.current.getColumn(dragColField),\n        targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(dragColField),\n        oldIndex: originColumnIndex.current\n      };\n      apiRef.current.publishEvent('columnOrderChange', columnOrderChangeParams);\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      columnReorder: _extends({}, state.columnReorder, {\n        dragCol: ''\n      })\n    }));\n  }, [apiRef, props.disableColumnReorder, props.keepColumnPositionIfDraggedOutside, logger, classes.columnHeaderDragging]);\n  const handleDragStart = React.useCallback((params, event) => {\n    if (props.disableColumnReorder || params.colDef.disableReorder) {\n      return;\n    }\n    logger.debug(`Start dragging col ${params.field}`);\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    dragColNode.current = event.currentTarget;\n    dragColNode.current.classList.add(classes.columnHeaderDragging);\n    const handleDragEndEvent = dragEndEvent => {\n      dragColNode.current.removeEventListener('dragend', handleDragEndEvent);\n      apiRef.current.publishEvent('columnHeaderDragEndNative', params, dragEndEvent);\n    };\n    dragColNode.current.addEventListener('dragend', handleDragEndEvent);\n    if (event.dataTransfer) {\n      event.dataTransfer.effectAllowed = 'move';\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      columnReorder: _extends({}, state.columnReorder, {\n        dragCol: params.field\n      })\n    }));\n    removeDnDStylesTimeout.current = setTimeout(() => {\n      dragColNode.current.classList.remove(classes.columnHeaderDragging);\n    });\n    originColumnIndex.current = apiRef.current.getColumnIndex(params.field, false);\n    const draggingColumnGroupPath = apiRef.current.getColumnGroupPath(params.field);\n    const columnIndex = originColumnIndex.current;\n    const allColumns = apiRef.current.getAllColumns();\n    const groupsLookup = apiRef.current.getAllGroupDetails();\n    const getGroupPathFromColumnIndex = colIndex => {\n      const field = allColumns[colIndex].field;\n      return apiRef.current.getColumnGroupPath(field);\n    };\n\n    // The limitingGroupId is the id of the group from which the dragged column should not escape\n    let limitingGroupId = null;\n    draggingColumnGroupPath.forEach(groupId => {\n      if (!groupsLookup[groupId]?.freeReordering) {\n        // Only consider group that are made of more than one column\n        if (columnIndex > 0 && getGroupPathFromColumnIndex(columnIndex - 1).includes(groupId)) {\n          limitingGroupId = groupId;\n        } else if (columnIndex + 1 < allColumns.length && getGroupPathFromColumnIndex(columnIndex + 1).includes(groupId)) {\n          limitingGroupId = groupId;\n        }\n      }\n    });\n    forbiddenIndexes.current = {};\n    for (let indexToForbid = 0; indexToForbid < allColumns.length; indexToForbid += 1) {\n      const leftIndex = indexToForbid <= columnIndex ? indexToForbid - 1 : indexToForbid;\n      const rightIndex = indexToForbid < columnIndex ? indexToForbid : indexToForbid + 1;\n      if (limitingGroupId !== null) {\n        // verify this indexToForbid will be linked to the limiting group. Otherwise forbid it\n        let allowIndex = false;\n        if (leftIndex >= 0 && getGroupPathFromColumnIndex(leftIndex).includes(limitingGroupId)) {\n          allowIndex = true;\n        } else if (rightIndex < allColumns.length && getGroupPathFromColumnIndex(rightIndex).includes(limitingGroupId)) {\n          allowIndex = true;\n        }\n        if (!allowIndex) {\n          forbiddenIndexes.current[indexToForbid] = true;\n        }\n      }\n\n      // Verify we are not splitting another group\n      if (leftIndex >= 0 && rightIndex < allColumns.length) {\n        getGroupPathFromColumnIndex(rightIndex).forEach(groupId => {\n          if (getGroupPathFromColumnIndex(leftIndex).includes(groupId)) {\n            if (!draggingColumnGroupPath.includes(groupId)) {\n              // moving here split the group groupId in two distincts chunks\n              if (!groupsLookup[groupId]?.freeReordering) {\n                forbiddenIndexes.current[indexToForbid] = true;\n              }\n            }\n          }\n        });\n      }\n    }\n  }, [props.disableColumnReorder, classes.columnHeaderDragging, logger, apiRef]);\n  const handleDragEnter = React.useCallback((params, event) => {\n    event.preventDefault();\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n  }, []);\n  const handleDragOver = React.useCallback((params, event) => {\n    const dragColField = gridColumnReorderDragColSelector(apiRef);\n    if (!dragColField) {\n      return;\n    }\n    logger.debug(`Dragging over col ${params.field}`);\n    event.preventDefault();\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    const coordinates = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (params.field !== dragColField && hasCursorPositionChanged(cursorPosition.current, coordinates)) {\n      const targetColIndex = apiRef.current.getColumnIndex(params.field, false);\n      const targetColVisibleIndex = apiRef.current.getColumnIndex(params.field, true);\n      const targetCol = apiRef.current.getColumn(params.field);\n      const dragColIndex = apiRef.current.getColumnIndex(dragColField, false);\n      const visibleColumns = apiRef.current.getVisibleColumns();\n      const allColumns = apiRef.current.getAllColumns();\n      const cursorMoveDirectionX = getCursorMoveDirectionX(cursorPosition.current, coordinates);\n      const hasMovedLeft = cursorMoveDirectionX === CURSOR_MOVE_DIRECTION_LEFT && (isRtl ? dragColIndex < targetColIndex : targetColIndex < dragColIndex);\n      const hasMovedRight = cursorMoveDirectionX === CURSOR_MOVE_DIRECTION_RIGHT && (isRtl ? targetColIndex < dragColIndex : dragColIndex < targetColIndex);\n      if (hasMovedLeft || hasMovedRight) {\n        let canBeReordered;\n        let indexOffsetInHiddenColumns = 0;\n        if (!targetCol.disableReorder) {\n          canBeReordered = true;\n        } else if (hasMovedLeft) {\n          canBeReordered = targetColVisibleIndex > 0 && !visibleColumns[targetColVisibleIndex - 1].disableReorder;\n        } else {\n          canBeReordered = targetColVisibleIndex < visibleColumns.length - 1 && !visibleColumns[targetColVisibleIndex + 1].disableReorder;\n        }\n        if (forbiddenIndexes.current[targetColIndex]) {\n          let nextVisibleColumnField;\n          let indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;\n          if (hasMovedLeft) {\n            nextVisibleColumnField = targetColVisibleIndex > 0 ? visibleColumns[targetColVisibleIndex - 1].field : null;\n            while (indexWithOffset > 0 && allColumns[indexWithOffset].field !== nextVisibleColumnField && forbiddenIndexes.current[indexWithOffset]) {\n              indexOffsetInHiddenColumns -= 1;\n              indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;\n            }\n          } else {\n            nextVisibleColumnField = targetColVisibleIndex + 1 < visibleColumns.length ? visibleColumns[targetColVisibleIndex + 1].field : null;\n            while (indexWithOffset < allColumns.length - 1 && allColumns[indexWithOffset].field !== nextVisibleColumnField && forbiddenIndexes.current[indexWithOffset]) {\n              indexOffsetInHiddenColumns += 1;\n              indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;\n            }\n          }\n          if (forbiddenIndexes.current[indexWithOffset] || allColumns[indexWithOffset].field === nextVisibleColumnField) {\n            // If we ended up on a visible column, or a forbidden one, we cannot do the reorder\n            canBeReordered = false;\n          }\n        }\n        const canBeReorderedProcessed = apiRef.current.unstable_applyPipeProcessors('canBeReordered', canBeReordered, {\n          targetIndex: targetColVisibleIndex\n        });\n        if (canBeReorderedProcessed) {\n          apiRef.current.setColumnIndex(dragColField, targetColIndex + indexOffsetInHiddenColumns);\n        }\n      }\n      cursorPosition.current = coordinates;\n    }\n  }, [apiRef, logger, isRtl]);\n  React.useEffect(() => {\n    if (!props.keepColumnPositionIfDraggedOutside) {\n      return () => {};\n    }\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    const listener = event => {\n      if (event.dataTransfer) {\n        // keep the drop effect if we are keeping the column position if dragged outside\n        // https://github.com/mui/mui-x/issues/19183#issuecomment-3202307783\n        event.preventDefault();\n        event.dataTransfer.dropEffect = 'move';\n      }\n    };\n    doc.addEventListener('dragover', listener);\n    return () => {\n      doc.removeEventListener('dragover', listener);\n    };\n  }, [apiRef, props.keepColumnPositionIfDraggedOutside]);\n  useGridEvent(apiRef, 'columnHeaderDragStart', handleDragStart);\n  useGridEvent(apiRef, 'columnHeaderDragEnter', handleDragEnter);\n  useGridEvent(apiRef, 'columnHeaderDragOver', handleDragOver);\n  useGridEvent(apiRef, 'columnHeaderDragEndNative', handleDragEnd);\n  useGridEvent(apiRef, 'cellDragEnter', handleDragEnter);\n  useGridEvent(apiRef, 'cellDragOver', handleDragOver);\n  useGridEventPriority(apiRef, 'columnOrderChange', props.onColumnOrderChange);\n};", "export default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}", "import { createSelector, createRootSelector } from '@mui/x-data-grid/internals';\nexport const gridColumnReorderSelector = createRootSelector(state => state.columnReorder);\nexport const gridColumnReorderDragColSelector = createSelector(gridColumnReorderSelector, columnReorder => columnReorder.dragCol);", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"hideDescendantCount\"];\nimport * as React from 'react';\nimport { gridRowTreeSelector, useFirstRender } from '@mui/x-data-grid';\nimport { GridStrategyGroup, useGridRegisterPipeProcessor, useGridRegisterStrategyProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_TREE_DATA_GROUPING_COL_DEF, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES } from \"../treeData/gridTreeDataGroupColDef.js\";\nimport { getParentPath, skipFiltering, skipSorting } from \"./utils.js\";\nimport { GridDataSourceTreeDataGroupingCell } from \"../../../components/GridDataSourceTreeDataGroupingCell.js\";\nimport { createRowTree } from \"../../../utils/tree/createRowTree.js\";\nimport { updateRowTree } from \"../../../utils/tree/updateRowTree.js\";\nimport { getVisibleRowsLookup } from \"../../../utils/tree/utils.js\";\nimport { TreeDataStrategy } from \"../treeData/gridTreeDataUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const useGridDataSourceTreeDataPreProcessors = (privateApiRef, props) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.RowTree, TreeDataStrategy.DataSource, props.treeData && props.dataSource ? () => true : () => false);\n  }, [privateApiRef, props.treeData, props.dataSource]);\n  const getGroupingColDef = React.useCallback(() => {\n    const groupingColDefProp = props.groupingColDef;\n    let colDefOverride;\n    if (typeof groupingColDefProp === 'function') {\n      const params = {\n        groupingName: TreeDataStrategy.DataSource,\n        fields: []\n      };\n      colDefOverride = groupingColDefProp(params);\n    } else {\n      colDefOverride = groupingColDefProp;\n    }\n    const _ref = colDefOverride ?? {},\n      {\n        hideDescendantCount\n      } = _ref,\n      colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const commonProperties = _extends({}, GRID_TREE_DATA_GROUPING_COL_DEF, {\n      renderCell: params => /*#__PURE__*/_jsx(GridDataSourceTreeDataGroupingCell, _extends({}, params, {\n        hideDescendantCount: hideDescendantCount\n      })),\n      headerName: privateApiRef.current.getLocaleText('treeDataGroupingHeaderName')\n    });\n    return _extends({}, commonProperties, colDefOverrideProperties, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES);\n  }, [privateApiRef, props.groupingColDef]);\n  const updateGroupingColumn = React.useCallback(columnsState => {\n    if (!props.dataSource) {\n      return columnsState;\n    }\n    const groupingColDefField = GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES.field;\n    const shouldHaveGroupingColumn = props.treeData;\n    const prevGroupingColumn = columnsState.lookup[groupingColDefField];\n    if (shouldHaveGroupingColumn) {\n      const newGroupingColumn = getGroupingColDef();\n      if (prevGroupingColumn) {\n        newGroupingColumn.width = prevGroupingColumn.width;\n        newGroupingColumn.flex = prevGroupingColumn.flex;\n      }\n      columnsState.lookup[groupingColDefField] = newGroupingColumn;\n      if (prevGroupingColumn == null) {\n        columnsState.orderedFields = [groupingColDefField, ...columnsState.orderedFields];\n      }\n    } else if (!shouldHaveGroupingColumn && prevGroupingColumn) {\n      delete columnsState.lookup[groupingColDefField];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== groupingColDefField);\n    }\n    return columnsState;\n  }, [props.treeData, props.dataSource, getGroupingColDef]);\n  const createRowTreeForTreeData = React.useCallback(params => {\n    const getGroupKey = props.dataSource?.getGroupKey;\n    if (!getGroupKey) {\n      throw new Error('MUI X: No `getGroupKey` method provided with the dataSource.');\n    }\n    const getChildrenCount = props.dataSource?.getChildrenCount;\n    if (!getChildrenCount) {\n      throw new Error('MUI X: No `getChildrenCount` method provided with the dataSource.');\n    }\n    const getRowTreeBuilderNode = rowId => {\n      const parentPath = params.updates.groupKeys ?? getParentPath(rowId, params);\n      const count = getChildrenCount(params.dataRowIdToModelLookup[rowId]);\n      return {\n        id: rowId,\n        path: [...parentPath, getGroupKey(params.dataRowIdToModelLookup[rowId])].map(key => ({\n          key,\n          field: null\n        })),\n        serverChildrenCount: count\n      };\n    };\n    const onDuplicatePath = (firstId, secondId, path) => {\n      throw new Error(['MUI X: The values returned by `getGroupKey` for all the sibling rows should be unique.', `The rows with id #${firstId} and #${secondId} have the same.`, `Path: ${JSON.stringify(path.map(step => step.key))}.`].join('\\n'));\n    };\n    if (params.updates.type === 'full') {\n      return createRowTree({\n        previousTree: params.previousTree,\n        nodes: params.updates.rows.map(getRowTreeBuilderNode),\n        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,\n        isGroupExpandedByDefault: props.isGroupExpandedByDefault,\n        groupingName: TreeDataStrategy.DataSource,\n        onDuplicatePath\n      });\n    }\n    return updateRowTree({\n      nodes: {\n        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),\n        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),\n        removed: params.updates.actions.remove\n      },\n      previousTree: params.previousTree,\n      previousGroupsToFetch: params.previousGroupsToFetch,\n      previousTreeDepth: params.previousTreeDepths,\n      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,\n      isGroupExpandedByDefault: props.isGroupExpandedByDefault,\n      groupingName: TreeDataStrategy.DataSource\n    });\n  }, [props.dataSource, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);\n  const filterRows = React.useCallback(() => {\n    const rowTree = gridRowTreeSelector(privateApiRef);\n    return skipFiltering(rowTree);\n  }, [privateApiRef]);\n  const sortRows = React.useCallback(() => {\n    const rowTree = gridRowTreeSelector(privateApiRef);\n    return skipSorting(rowTree);\n  }, [privateApiRef]);\n  useGridRegisterPipeProcessor(privateApiRef, 'hydrateColumns', updateGroupingColumn);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, 'rowTreeCreation', createRowTreeForTreeData);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, 'filtering', filterRows);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, 'sorting', sortRows);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    setStrategyAvailability();\n  });\n\n  /**\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (!isFirstRender.current) {\n      setStrategyAvailability();\n    } else {\n      isFirstRender.current = false;\n    }\n  }, [setStrategyAvailability]);\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF, gridRowIdSelector, gridRowNodeSelector } from '@mui/x-data-grid';\nimport { GRID_TREE_DATA_GROUPING_FIELD } from '@mui/x-data-grid/internals';\n\n/**\n * TODO: Add sorting and filtering on the value and the filteredDescendantCount\n */\nexport const GRID_TREE_DATA_GROUPING_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'custom',\n  sortable: false,\n  filterable: false,\n  disableColumnMenu: true,\n  disableReorder: true,\n  align: 'left',\n  width: 200,\n  valueGetter: (value, row, column, apiRef) => {\n    const rowId = gridRowIdSelector(apiRef, row);\n    const rowNode = gridRowNodeSelector(apiRef, rowId);\n    return rowNode?.type === 'group' || rowNode?.type === 'leaf' ? rowNode.groupingKey : undefined;\n  }\n});\nexport { GRID_TREE_DATA_GROUPING_FIELD };\nexport const GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES = {\n  field: GRID_TREE_DATA_GROUPING_FIELD,\n  editable: false,\n  groupable: false\n};", "import { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { defaultGridFilterLookup, getTreeNodeDescendants } from '@mui/x-data-grid/internals';\nexport function skipFiltering(rowTree) {\n  const filteredChildrenCountLookup = {};\n  const nodes = Object.values(rowTree);\n  for (let i = 0; i < nodes.length; i += 1) {\n    const node = nodes[i];\n    filteredChildrenCountLookup[node.id] = node.serverChildrenCount ?? 0;\n  }\n  return {\n    filteredRowsLookup: defaultGridFilterLookup.filteredRowsLookup,\n    filteredChildrenCountLookup,\n    filteredDescendantCountLookup: defaultGridFilterLookup.filteredDescendantCountLookup\n  };\n}\nexport function skipSorting(rowTree) {\n  return getTreeNodeDescendants(rowTree, GRID_ROOT_GROUP_ID, false);\n}\n\n/**\n * Retrieves the parent path for a row from the previous tree state.\n * Used during full tree updates to maintain correct hierarchy.\n */\nexport function getParentPath(rowId, treeCreationParams) {\n  if (treeCreationParams.updates.type !== 'full' || !treeCreationParams.previousTree?.[rowId] || treeCreationParams.previousTree[rowId].depth < 1 || !('path' in treeCreationParams.previousTree[rowId])) {\n    return [];\n  }\n  return treeCreationParams.previousTree[rowId].path || [];\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass, useGridSelector } from '@mui/x-data-grid';\nimport { vars, gridRowSelector } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { useGridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { gridDataSourceErrorSelector, gridDataSourceLoadingIdSelector } from \"../hooks/features/dataSource/gridDataSourceSelector.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['treeDataGroupingCell'],\n    toggle: ['treeDataGroupingCellToggle'],\n    loadingContainer: ['treeDataGroupingCellLoadingContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridTreeDataGroupingCellIcon(props) {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const {\n    rowNode,\n    id,\n    field,\n    descendantCount\n  } = props;\n  const isDataLoading = useGridSelector(apiRef, gridDataSourceLoadingIdSelector, id);\n  const error = useGridSelector(apiRef, gridDataSourceErrorSelector, id);\n  const handleClick = event => {\n    if (!rowNode.childrenExpanded) {\n      // always fetch/get from cache the children when the node is expanded\n      apiRef.current.dataSource.fetchRows(id);\n    } else {\n      // Collapse the node and remove child rows from the grid\n      apiRef.current.setRowChildrenExpansion(id, false);\n      apiRef.current.removeChildrenRows(id);\n    }\n    apiRef.current.setCellFocus(id, field);\n    event.stopPropagation(); // TODO remove event.stopPropagation\n  };\n  const Icon = rowNode.childrenExpanded ? rootProps.slots.treeDataCollapseIcon : rootProps.slots.treeDataExpandIcon;\n  if (isDataLoading) {\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: classes.loadingContainer,\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseCircularProgress, {\n        size: \"1rem\",\n        color: \"inherit\"\n      })\n    });\n  }\n  return descendantCount === -1 || descendantCount > 0 ? /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n    size: \"small\",\n    onClick: handleClick,\n    tabIndex: -1,\n    \"aria-label\": rowNode.childrenExpanded ? apiRef.current.getLocaleText('treeDataCollapse') : apiRef.current.getLocaleText('treeDataExpand')\n  }, rootProps?.slotProps?.baseIconButton, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, {\n      title: error?.message ?? null,\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseBadge, {\n        variant: \"dot\",\n        color: \"error\",\n        invisible: !error,\n        children: /*#__PURE__*/_jsx(Icon, {\n          fontSize: \"inherit\"\n        })\n      })\n    })\n  })) : null;\n}\nexport function GridDataSourceTreeDataGroupingCell(props) {\n  const {\n    id,\n    field,\n    formattedValue,\n    rowNode,\n    hideDescendantCount,\n    offsetMultiplier = 2\n  } = props;\n  const rootProps = useGridRootProps();\n  const apiRef = useGridPrivateApiContext();\n  const row = useGridSelector(apiRef, gridRowSelector, id);\n  const classes = useUtilityClasses(rootProps);\n  let descendantCount = 0;\n  if (row) {\n    descendantCount = rootProps.dataSource?.getChildrenCount?.(row) ?? 0;\n  }\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: classes.root,\n    style: {\n      marginLeft: vars.spacing(rowNode.depth * offsetMultiplier)\n    },\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: classes.toggle,\n      children: /*#__PURE__*/_jsx(GridTreeDataGroupingCellIcon, {\n        id: id,\n        field: field,\n        rowNode: rowNode,\n        row: row,\n        descendantCount: descendantCount\n      })\n    }), /*#__PURE__*/_jsxs(\"span\", {\n      children: [formattedValue === undefined ? rowNode.groupingKey : formattedValue, !hideDescendantCount && descendantCount > 0 ? ` (${descendantCount})` : '']\n    })]\n  });\n}", "import { createSelector, createRootSelector } from '@mui/x-data-grid/internals';\nexport const gridDataSourceStateSelector = createRootSelector(state => state.dataSource);\nexport const gridDataSourceLoadingSelector = createSelector(gridDataSourceStateSelector, dataSource => dataSource.loading);\nexport const gridDataSourceLoadingIdSelector = createSelector(gridDataSourceStateSelector, (dataSource, id) => dataSource.loading[id] ?? false);\nexport const gridDataSourceErrorsSelector = createSelector(gridDataSourceStateSelector, dataSource => dataSource.errors);\nexport const gridDataSourceErrorSelector = createSelector(gridDataSourceStateSelector, (dataSource, id) => dataSource.errors[id]);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nexport const getGroupRowIdFromPath = path => {\n  const pathStr = path.map(groupingCriteria => `${groupingCriteria.field}/${groupingCriteria.key}`).join('-');\n  return `auto-generated-row-${pathStr}`;\n};\nexport const getNodePathInTree = ({\n  id,\n  tree\n}) => {\n  const path = [];\n  let node = tree[id];\n  while (node.id !== GRID_ROOT_GROUP_ID) {\n    path.push({\n      field: node.type === 'leaf' ? null : node.groupingField,\n      key: node.groupingKey\n    });\n    node = tree[node.parent];\n  }\n  path.reverse();\n  return path;\n};\nexport const checkGroupChildrenExpansion = (node, defaultGroupingExpansionDepth, isGroupExpandedByDefault) => {\n  let childrenExpanded;\n  if (node.id === GRID_ROOT_GROUP_ID) {\n    childrenExpanded = true;\n  } else if (isGroupExpandedByDefault) {\n    childrenExpanded = isGroupExpandedByDefault(node);\n  } else {\n    childrenExpanded = defaultGroupingExpansionDepth === -1 || defaultGroupingExpansionDepth > node.depth;\n  }\n  return childrenExpanded;\n};\nexport const updateGroupDefaultExpansion = (node, defaultGroupingExpansionDepth, isGroupExpandedByDefault) => {\n  const childrenExpanded = checkGroupChildrenExpansion(node, defaultGroupingExpansionDepth, isGroupExpandedByDefault);\n  node.childrenExpanded = childrenExpanded;\n  return node;\n};\n\n/**\n * Insert a node in the tree\n */\nexport const insertNodeInTree = (node, tree, treeDepths, previousTree) => {\n  // 1. Insert node in the tree.\n  tree[node.id] = node;\n\n  // 2. Increment the `treeDepths` object for the node's depth.\n  treeDepths[node.depth] = (treeDepths[node.depth] ?? 0) + 1;\n\n  // 3. Register the new node in its parent.\n  const parentNode = tree[node.parent];\n  if (node.type === 'group' || node.type === 'leaf') {\n    // For groups and leaves,\n    // Register the node from its parents `children` and `childrenFromPath` properties.\n    const groupingFieldName = node.groupingField ?? '__no_field__';\n    const groupingKeyName = node.groupingKey ?? '__no_key__';\n    const groupingField = parentNode.childrenFromPath?.[groupingFieldName];\n    if (previousTree !== null && previousTree[parentNode.id] === tree[parentNode.id]) {\n      parentNode.children = [...parentNode.children, node.id];\n    } else {\n      parentNode.children.push(node.id);\n    }\n    if (groupingField == null) {\n      parentNode.childrenFromPath[groupingFieldName] = {\n        [groupingKeyName.toString()]: node.id\n      };\n    } else {\n      groupingField[groupingKeyName.toString()] = node.id;\n    }\n  } else if (node.type === 'footer') {\n    // For footers,\n    // Register the node from its parent `footerId` property.\n    parentNode.footerId = node.id;\n  }\n};\n\n/**\n * Removes a node from the tree\n */\nexport const removeNodeFromTree = ({\n  node,\n  tree,\n  treeDepths\n}) => {\n  // 1. Remove node from the tree.\n  delete tree[node.id];\n\n  // 2. Decrement the `treeDepths` object for the node's depth.\n  const nodeDepth = node.depth;\n  const currentNodeCount = treeDepths[nodeDepth];\n  if (currentNodeCount === 1) {\n    delete treeDepths[nodeDepth];\n  } else {\n    treeDepths[nodeDepth] = currentNodeCount - 1;\n  }\n\n  // 3. Unregister the new node in its parent.\n  const parentNode = tree[node.parent];\n  // For footers,\n  // Unregister the node from its parent `footerId` property.\n  if (node.type === 'footer') {\n    tree[parentNode.id] = _extends({}, parentNode, {\n      footerId: null\n    });\n  }\n  // For groups and leaves,\n  // Unregister the node from its parents `children` and `childrenFromPath` properties.\n  else {\n    const groupingField = node.groupingField ?? '__no_field__';\n    const groupingKey = node.groupingKey ?? '__no_key__';\n\n    // TODO rows v6: Can we avoid this linear complexity ?\n    const children = parentNode.children.filter(childId => childId !== node.id);\n    const childrenFromPath = parentNode.childrenFromPath;\n    delete childrenFromPath[groupingField][groupingKey.toString()];\n    tree[parentNode.id] = _extends({}, parentNode, {\n      children,\n      childrenFromPath\n    });\n  }\n};\n\n/**\n * Updates the `id` and `isAutoGenerated` properties of a group node.\n */\nexport const updateGroupNodeIdAndAutoGenerated = ({\n  node,\n  updatedNode,\n  previousTree,\n  tree,\n  treeDepths\n}) => {\n  // 1. Set the new parent for all children from the old group\n  node.children.forEach(childId => {\n    tree[childId] = _extends({}, tree[childId], {\n      parent: updatedNode.id\n    });\n  });\n\n  // 2. Remove the old group from the tree\n  removeNodeFromTree({\n    node,\n    tree,\n    treeDepths\n  });\n\n  // 3. Add the new group in the tree\n  const groupNode = _extends({}, node, updatedNode);\n  insertNodeInTree(groupNode, tree, treeDepths, previousTree);\n};\nexport const createUpdatedGroupsManager = () => ({\n  value: {},\n  addAction(groupId, action) {\n    if (!this.value[groupId]) {\n      this.value[groupId] = {};\n    }\n    this.value[groupId][action] = true;\n  }\n});\nexport const getVisibleRowsLookup = ({\n  tree,\n  filteredRowsLookup\n}) => {\n  if (!filteredRowsLookup) {\n    return {};\n  }\n  const visibleRowsLookup = {};\n  const handleTreeNode = (node, areAncestorsExpanded) => {\n    const isPassingFiltering = filteredRowsLookup[node.id] !== false;\n    if (node.type === 'group') {\n      node.children.forEach(childId => {\n        const childNode = tree[childId];\n        handleTreeNode(childNode, areAncestorsExpanded && !!node.childrenExpanded);\n      });\n    }\n    const isVisible = isPassingFiltering && areAncestorsExpanded;\n    if (!isVisible) {\n      visibleRowsLookup[node.id] = isVisible;\n    }\n\n    // TODO rows v6: Should we keep storing the visibility status of footer independently or rely on the group visibility in the selector ?\n    if (node.type === 'group' && node.footerId != null) {\n      const isFooterVisible = isPassingFiltering && areAncestorsExpanded && !!node.childrenExpanded;\n      if (!isFooterVisible) {\n        visibleRowsLookup[node.footerId] = isFooterVisible;\n      }\n    }\n  };\n  const nodes = Object.values(tree);\n  for (let i = 0; i < nodes.length; i += 1) {\n    const node = nodes[i];\n    if (node.depth === 0) {\n      handleTreeNode(node, true);\n    }\n  }\n  return visibleRowsLookup;\n};", "import { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { updateGroupDefaultExpansion, checkGroupChildrenExpansion, getGroupRowIdFromPath, insertNodeInTree, updateGroupNodeIdAndAutoGenerated } from \"./utils.js\";\n/**\n * Inserts a data row in a tree.\n * For each steps of its path:\n * - if a node exists with the same partial path, it will register this node as the ancestor of the data row.\n * - if not, it will create an auto-generated node and register it as ancestor of the data row.\n */\nexport const insertDataRowInTree = ({\n  id,\n  path,\n  updatedGroupsManager,\n  previousTree,\n  tree,\n  treeDepths,\n  onDuplicatePath,\n  isGroupExpandedByDefault,\n  defaultGroupingExpansionDepth,\n  serverChildrenCount,\n  groupsToFetch\n}) => {\n  let parentNodeId = GRID_ROOT_GROUP_ID;\n  for (let depth = 0; depth < path.length; depth += 1) {\n    const {\n      key,\n      field\n    } = path[depth];\n    const fieldWithDefaultValue = field ?? '__no_field__';\n    const keyWithDefaultValue = key ?? '__no_key__';\n    const existingNodeIdWithPartialPath = tree[parentNodeId].childrenFromPath?.[fieldWithDefaultValue]?.[keyWithDefaultValue.toString()];\n\n    // When we reach the last step of the path,\n    // We need to create a node for the row passed to `insertNodeInTree`\n    if (depth === path.length - 1) {\n      // If no node matches the full path,\n      // We create a leaf node for the data row.\n      if (existingNodeIdWithPartialPath == null) {\n        let node;\n        if (serverChildrenCount !== undefined && serverChildrenCount !== 0) {\n          node = {\n            type: 'group',\n            id,\n            parent: parentNodeId,\n            path: path.map(step => step.key),\n            depth,\n            isAutoGenerated: false,\n            groupingKey: key,\n            groupingField: field,\n            children: [],\n            childrenFromPath: {},\n            childrenExpanded: false,\n            serverChildrenCount\n          };\n          const shouldFetchChildren = checkGroupChildrenExpansion(node, defaultGroupingExpansionDepth, isGroupExpandedByDefault);\n          if (shouldFetchChildren) {\n            groupsToFetch?.add(id);\n          }\n        } else {\n          node = {\n            type: 'leaf',\n            id,\n            depth,\n            parent: parentNodeId,\n            groupingKey: key\n          };\n        }\n        updatedGroupsManager?.addAction(parentNodeId, 'insertChildren');\n        insertNodeInTree(node, tree, treeDepths, previousTree);\n      } else {\n        const existingNodeWithPartialPath = tree[existingNodeIdWithPartialPath];\n\n        // If we already have an auto-generated group matching the partial path,\n        // We replace it with the node from of data row passed to `insertNodeInTree`\n        if (existingNodeWithPartialPath.type === 'group' && existingNodeWithPartialPath.isAutoGenerated) {\n          updatedGroupsManager?.addAction(parentNodeId, 'removeChildren');\n          updatedGroupsManager?.addAction(parentNodeId, 'insertChildren');\n          updateGroupNodeIdAndAutoGenerated({\n            tree,\n            previousTree,\n            treeDepths,\n            node: existingNodeWithPartialPath,\n            updatedNode: {\n              id,\n              isAutoGenerated: false\n            }\n          });\n        } else {\n          // If we have another row matching the partial path, then there is a duplicate in the dataset.\n          // We warn the user and skip the current row.\n          onDuplicatePath?.(existingNodeIdWithPartialPath, id, path);\n        }\n      }\n    }\n    // For the intermediary steps of the path,\n    // We need to make sure that there is a node matching the partial path.\n    //\n    // If no node matches the partial path,\n    // We create an auto-generated group node.\n    else if (existingNodeIdWithPartialPath == null) {\n      const nodeId = getGroupRowIdFromPath(path.slice(0, depth + 1));\n      const autoGeneratedGroupNode = {\n        type: 'group',\n        id: nodeId,\n        parent: parentNodeId,\n        depth,\n        isAutoGenerated: true,\n        groupingKey: key,\n        groupingField: field,\n        children: [],\n        childrenFromPath: {},\n        childrenExpanded: false\n      };\n      updatedGroupsManager?.addAction(parentNodeId, 'insertChildren');\n      insertNodeInTree(updateGroupDefaultExpansion(autoGeneratedGroupNode, defaultGroupingExpansionDepth, isGroupExpandedByDefault), tree, treeDepths, previousTree);\n      parentNodeId = nodeId;\n    }\n    // For the intermediary steps of the path\n    // If a node matches the partial path, we use it as parent for the next step\n    else {\n      const currentGroupNode = tree[existingNodeIdWithPartialPath];\n\n      // If the node matching the partial path is not a group, we turn it into a group\n      if (currentGroupNode.type !== 'group') {\n        const groupNode = {\n          type: 'group',\n          id: currentGroupNode.id,\n          parent: currentGroupNode.parent,\n          depth: currentGroupNode.depth,\n          isAutoGenerated: false,\n          groupingKey: key,\n          groupingField: field,\n          children: [],\n          childrenFromPath: {},\n          childrenExpanded: false\n        };\n        tree[existingNodeIdWithPartialPath] = updateGroupDefaultExpansion(groupNode, defaultGroupingExpansionDepth, isGroupExpandedByDefault);\n      }\n      parentNodeId = currentGroupNode.id;\n    }\n  }\n};", "import { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { buildRootGroup } from '@mui/x-data-grid/internals';\nimport { insertDataRowInTree } from \"./insertDataRowInTree.js\";\n/**\n * Transform a list of rows into a tree structure where each row references its parent and children.\n */\nexport const createRowTree = params => {\n  const dataRowIds = [];\n  const tree = {\n    [GRID_ROOT_GROUP_ID]: buildRootGroup()\n  };\n  const treeDepths = {};\n  const groupsToFetch = new Set();\n  for (let i = 0; i < params.nodes.length; i += 1) {\n    const node = params.nodes[i];\n    dataRowIds.push(node.id);\n    insertDataRowInTree({\n      tree,\n      previousTree: params.previousTree,\n      id: node.id,\n      path: node.path,\n      serverChildrenCount: node.serverChildrenCount,\n      onDuplicatePath: params.onDuplicatePath,\n      treeDepths,\n      isGroupExpandedByDefault: params.isGroupExpandedByDefault,\n      defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,\n      groupsToFetch\n    });\n  }\n  return {\n    tree,\n    treeDepths,\n    groupingName: params.groupingName,\n    dataRowIds,\n    groupsToFetch: Array.from(groupsToFetch)\n  };\n};", "import { passFilterLogic } from '@mui/x-data-grid/internals';\nexport let TreeDataStrategy = /*#__PURE__*/function (TreeDataStrategy) {\n  TreeDataStrategy[\"Default\"] = \"tree-data\";\n  TreeDataStrategy[\"DataSource\"] = \"tree-data-source\";\n  return TreeDataStrategy;\n}({});\n\n/**\n * A node is visible if one of the following criteria is met:\n * - One of its children is passing the filter\n * - It is passing the filter\n */\nexport const filterRowTreeFromTreeData = params => {\n  const {\n    apiRef,\n    rowTree,\n    disableChildrenFiltering,\n    isRowMatchingFilters\n  } = params;\n  const filteredRowsLookup = {};\n  const filteredChildrenCountLookup = {};\n  const filteredDescendantCountLookup = {};\n  const filterCache = {};\n  const filterResults = {\n    passingFilterItems: null,\n    passingQuickFilterValues: null\n  };\n  const filterTreeNode = (node, isParentMatchingFilters, areAncestorsExpanded) => {\n    const shouldSkipFilters = disableChildrenFiltering && node.depth > 0;\n    let isMatchingFilters;\n    if (shouldSkipFilters) {\n      isMatchingFilters = null;\n    } else if (!isRowMatchingFilters || node.type === 'footer') {\n      isMatchingFilters = true;\n    } else {\n      const row = apiRef.current.getRow(node.id);\n      isRowMatchingFilters(row, undefined, filterResults);\n      isMatchingFilters = passFilterLogic([filterResults.passingFilterItems], [filterResults.passingQuickFilterValues], params.filterModel, params.apiRef, filterCache);\n    }\n    let filteredChildrenCount = 0;\n    let filteredDescendantCount = 0;\n    if (node.type === 'group') {\n      node.children.forEach(childId => {\n        const childNode = rowTree[childId];\n        const childSubTreeSize = filterTreeNode(childNode, isMatchingFilters ?? isParentMatchingFilters, areAncestorsExpanded && !!node.childrenExpanded);\n        filteredDescendantCount += childSubTreeSize;\n        if (childSubTreeSize > 0) {\n          filteredChildrenCount += 1;\n        }\n      });\n    }\n    let shouldPassFilters;\n    switch (isMatchingFilters) {\n      case true:\n        {\n          shouldPassFilters = true;\n          break;\n        }\n      case false:\n        {\n          shouldPassFilters = filteredDescendantCount > 0;\n          break;\n        }\n      default:\n        {\n          shouldPassFilters = isParentMatchingFilters;\n          break;\n        }\n    }\n    if (!shouldPassFilters) {\n      filteredRowsLookup[node.id] = false;\n    }\n    if (!shouldPassFilters) {\n      return 0;\n    }\n    filteredChildrenCountLookup[node.id] = filteredChildrenCount;\n    filteredDescendantCountLookup[node.id] = filteredDescendantCount;\n    if (node.type === 'footer') {\n      return filteredDescendantCount;\n    }\n    return filteredDescendantCount + 1;\n  };\n  const nodes = Object.values(rowTree);\n  for (let i = 0; i < nodes.length; i += 1) {\n    const node = nodes[i];\n    if (node.depth === 0) {\n      filterTreeNode(node, true, true);\n    }\n  }\n  return {\n    filteredRowsLookup,\n    filteredChildrenCountLookup,\n    filteredDescendantCountLookup\n  };\n};", "import { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { getNodePathInTree, getGroupRowIdFromPath, removeNodeFromTree, updateGroupNodeIdAndAutoGenerated } from \"./utils.js\";\nimport { TreeDataStrategy } from \"../../hooks/features/treeData/gridTreeDataUtils.js\";\nimport { RowGroupingStrategy } from \"../../internals/index.js\";\nconst removeNode = ({\n  node,\n  tree,\n  treeDepths\n}) => {\n  removeNodeFromTree({\n    node,\n    tree,\n    treeDepths\n  });\n  if (node.type === 'group' && node.footerId != null) {\n    removeNodeFromTree({\n      node: tree[node.footerId],\n      tree,\n      treeDepths\n    });\n  }\n};\nconst removeNodeAndCleanParent = ({\n  node,\n  tree,\n  treeDepths,\n  updatedGroupsManager\n}) => {\n  removeNode({\n    node,\n    tree,\n    treeDepths\n  });\n  const parentNode = tree[node.parent];\n  updatedGroupsManager?.addAction(parentNode.id, 'removeChildren');\n  const shouldDeleteGroup = parentNode.id !== GRID_ROOT_GROUP_ID && parentNode.children.length === 0;\n  if (shouldDeleteGroup) {\n    if (parentNode.isAutoGenerated) {\n      removeNodeAndCleanParent({\n        node: parentNode,\n        tree,\n        treeDepths\n      });\n    } else {\n      tree[parentNode.id] = {\n        type: 'leaf',\n        id: parentNode.id,\n        depth: parentNode.depth,\n        parent: parentNode.parent,\n        groupingKey: parentNode.groupingKey\n      };\n    }\n  }\n};\nconst replaceDataGroupWithAutoGeneratedGroup = ({\n  node,\n  tree,\n  treeDepths,\n  updatedGroupsManager\n}) => {\n  updatedGroupsManager?.addAction(node.parent, 'removeChildren');\n  updatedGroupsManager?.addAction(node.parent, 'insertChildren');\n  updateGroupNodeIdAndAutoGenerated({\n    previousTree: null,\n    tree,\n    treeDepths,\n    node,\n    updatedNode: {\n      id: getGroupRowIdFromPath(getNodePathInTree({\n        id: node.id,\n        tree\n      })),\n      isAutoGenerated: true\n    }\n  });\n};\n\n/**\n * Removed a data row from the tree.\n * If the node is a non-empty group, replace it with an auto-generated group to be able to keep displaying its children.\n * If not, remove it and recursively clean its parent with the following rules:\n * - An empty auto-generated should be removed from the tree\n * - An empty non-auto-generated should be turned into a leaf\n */\nexport const removeDataRowFromTree = ({\n  id,\n  tree,\n  treeDepths,\n  updatedGroupsManager,\n  groupingName\n}) => {\n  const node = tree[id];\n  if (node.type === 'group' && node.children.length > 0) {\n    replaceDataGroupWithAutoGeneratedGroup({\n      node,\n      tree,\n      treeDepths,\n      updatedGroupsManager\n    });\n  } else if (groupingName === TreeDataStrategy.Default || groupingName === RowGroupingStrategy.Default) {\n    removeNodeAndCleanParent({\n      node,\n      tree,\n      treeDepths,\n      updatedGroupsManager\n    });\n  } else {\n    removeNode({\n      node,\n      tree,\n      treeDepths\n    });\n  }\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { getTreeNodeDescendants } from '@mui/x-data-grid/internals';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { insertDataRowInTree } from \"./insertDataRowInTree.js\";\nimport { removeDataRowFromTree } from \"./removeDataRowFromTree.js\";\nimport { createUpdatedGroupsManager, getNodePathInTree } from \"./utils.js\";\nexport const updateRowTree = params => {\n  const tree = _extends({}, params.previousTree);\n  const treeDepths = _extends({}, params.previousTreeDepth);\n  const updatedGroupsManager = createUpdatedGroupsManager();\n  const groupsToFetch = params.previousGroupsToFetch ? new Set([...params.previousGroupsToFetch]) : new Set([]);\n  for (let i = 0; i < params.nodes.inserted.length; i += 1) {\n    const {\n      id,\n      path,\n      serverChildrenCount\n    } = params.nodes.inserted[i];\n    insertDataRowInTree({\n      previousTree: params.previousTree,\n      tree,\n      treeDepths,\n      updatedGroupsManager,\n      id,\n      path,\n      serverChildrenCount,\n      onDuplicatePath: params.onDuplicatePath,\n      isGroupExpandedByDefault: params.isGroupExpandedByDefault,\n      defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,\n      groupsToFetch\n    });\n  }\n  for (let i = 0; i < params.nodes.removed.length; i += 1) {\n    const nodeId = params.nodes.removed[i];\n    removeDataRowFromTree({\n      tree,\n      treeDepths,\n      updatedGroupsManager,\n      id: nodeId,\n      groupingName: params.groupingName\n    });\n  }\n  for (let i = 0; i < params.nodes.modified.length; i += 1) {\n    const {\n      id,\n      path,\n      serverChildrenCount\n    } = params.nodes.modified[i];\n    const pathInPreviousTree = getNodePathInTree({\n      tree,\n      id\n    });\n    const isInSameGroup = isDeepEqual(pathInPreviousTree, path);\n    if (!isInSameGroup) {\n      removeDataRowFromTree({\n        tree,\n        treeDepths,\n        updatedGroupsManager,\n        id,\n        groupingName: params.groupingName\n      });\n      insertDataRowInTree({\n        previousTree: params.previousTree,\n        tree,\n        treeDepths,\n        updatedGroupsManager,\n        id,\n        path,\n        serverChildrenCount,\n        onDuplicatePath: params.onDuplicatePath,\n        isGroupExpandedByDefault: params.isGroupExpandedByDefault,\n        defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,\n        groupsToFetch\n      });\n    } else {\n      updatedGroupsManager?.addAction(tree[id].parent, 'modifyChildren');\n    }\n  }\n\n  // TODO rows v6: Avoid walking the whole tree, we should be able to generate the new list only using slices.\n  const dataRowIds = getTreeNodeDescendants(tree, GRID_ROOT_GROUP_ID, true);\n  return {\n    tree,\n    treeDepths,\n    groupingName: params.groupingName,\n    dataRowIds,\n    updatedGroupsManager,\n    groupsToFetch: Array.from(groupsToFetch)\n  };\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridSelector, useGridEvent, useGridApiMethod, gridDataRowIdsSelector } from '@mui/x-data-grid';\nimport { useGridRegisterPipeProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"./gridDetailPanelToggleColDef.js\";\nimport { gridDetailPanelExpandedRowIdsSelector, gridDetailPanelExpandedRowsContentCacheSelector, gridDetailPanelRawHeightCacheSelector } from \"./gridDetailPanelSelector.js\";\nconst emptySet = new Set();\nexport const detailPanelStateInitializer = (state, props) => {\n  return _extends({}, state, {\n    detailPanel: {\n      heightCache: {},\n      expandedRowIds: props.detailPanelExpandedRowIds ?? props.initialState?.detailPanel?.expandedRowIds ?? emptySet\n    }\n  });\n};\nfunction cacheContentAndHeight(apiRef, getDetailPanelContent, getDetailPanelHeight, previousHeightCache) {\n  if (typeof getDetailPanelContent !== 'function') {\n    return {};\n  }\n\n  // TODO change to lazy approach using a Proxy\n  // only call getDetailPanelContent when asked for an id\n  const rowIds = gridDataRowIdsSelector(apiRef);\n  const contentCache = {};\n  const heightCache = {};\n  for (let i = 0; i < rowIds.length; i += 1) {\n    const id = rowIds[i];\n    const params = apiRef.current.getRowParams(id);\n    const content = getDetailPanelContent(params);\n    contentCache[id] = content;\n    if (content == null) {\n      continue;\n    }\n    const height = getDetailPanelHeight(params);\n    const autoHeight = height === 'auto';\n    heightCache[id] = {\n      autoHeight,\n      height: autoHeight ? previousHeightCache[id]?.height : height\n    };\n  }\n  return {\n    contentCache,\n    heightCache\n  };\n}\nexport const useGridDetailPanel = (apiRef, props) => {\n  const contentCache = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);\n  const handleCellClick = React.useCallback((params, event) => {\n    if (params.field !== GRID_DETAIL_PANEL_TOGGLE_FIELD || props.getDetailPanelContent == null) {\n      return;\n    }\n    const content = contentCache[params.id];\n    if (! /*#__PURE__*/React.isValidElement(content)) {\n      return;\n    }\n\n    // Ignore if the user didn't click specifically in the \"i\" button\n    if (event.target === event.currentTarget) {\n      return;\n    }\n    apiRef.current.toggleDetailPanel(params.id);\n  }, [apiRef, contentCache, props.getDetailPanelContent]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (props.getDetailPanelContent == null) {\n      return;\n    }\n    if (params.field === GRID_DETAIL_PANEL_TOGGLE_FIELD && event.key === ' ') {\n      apiRef.current.toggleDetailPanel(params.id);\n    }\n  }, [apiRef, props.getDetailPanelContent]);\n  useGridEvent(apiRef, 'cellClick', handleCellClick);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n  apiRef.current.registerControlState({\n    stateId: 'detailPanels',\n    propModel: props.detailPanelExpandedRowIds,\n    propOnChange: props.onDetailPanelExpandedRowIdsChange,\n    stateSelector: gridDetailPanelExpandedRowIdsSelector,\n    changeEvent: 'detailPanelsExpandedRowIdsChange'\n  });\n  const toggleDetailPanel = React.useCallback(id => {\n    if (props.getDetailPanelContent == null) {\n      return;\n    }\n    const content = contentCache[id];\n    if (! /*#__PURE__*/React.isValidElement(content)) {\n      return;\n    }\n    const ids = apiRef.current.getExpandedDetailPanels();\n    const newIds = new Set(ids);\n    if (ids.has(id)) {\n      newIds.delete(id);\n    } else {\n      newIds.add(id);\n    }\n    apiRef.current.setExpandedDetailPanels(newIds);\n  }, [apiRef, contentCache, props.getDetailPanelContent]);\n  const getExpandedDetailPanels = React.useCallback(() => gridDetailPanelExpandedRowIdsSelector(apiRef), [apiRef]);\n  const setExpandedDetailPanels = React.useCallback(ids => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        detailPanel: _extends({}, state.detailPanel, {\n          expandedRowIds: ids\n        })\n      });\n    });\n    apiRef.current.requestPipeProcessorsApplication('rowHeight');\n  }, [apiRef]);\n  const storeDetailPanelHeight = React.useCallback((id, height) => {\n    const heightCache = gridDetailPanelRawHeightCacheSelector(apiRef);\n    if (!heightCache[id] || heightCache[id].height === height) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        detailPanel: _extends({}, state.detailPanel, {\n          heightCache: _extends({}, heightCache, {\n            [id]: _extends({}, heightCache[id], {\n              height\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.requestPipeProcessorsApplication('rowHeight');\n  }, [apiRef]);\n  const detailPanelPubicApi = {\n    toggleDetailPanel,\n    getExpandedDetailPanels,\n    setExpandedDetailPanels\n  };\n  const detailPanelPrivateApi = {\n    storeDetailPanelHeight\n  };\n  useGridApiMethod(apiRef, detailPanelPubicApi, 'public');\n  useGridApiMethod(apiRef, detailPanelPrivateApi, 'private');\n  React.useEffect(() => {\n    if (props.detailPanelExpandedRowIds) {\n      const currentModel = gridDetailPanelExpandedRowIdsSelector(apiRef);\n      if (currentModel !== props.detailPanelExpandedRowIds) {\n        apiRef.current.setExpandedDetailPanels(props.detailPanelExpandedRowIds);\n      }\n    }\n  }, [apiRef, props.detailPanelExpandedRowIds]);\n  const updateCaches = React.useCallback(() => {\n    if (!props.getDetailPanelContent) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        detailPanel: _extends({}, state.detailPanel, cacheContentAndHeight(apiRef, props.getDetailPanelContent, props.getDetailPanelHeight, state.detailPanel.heightCache))\n      });\n    });\n  }, [apiRef, props.getDetailPanelContent, props.getDetailPanelHeight]);\n  useGridEvent(apiRef, 'sortedRowsSet', updateCaches);\n  const previousGetDetailPanelContentProp = React.useRef(undefined);\n  const previousGetDetailPanelHeightProp = React.useRef(undefined);\n  const updateCachesIfNeeded = React.useCallback(() => {\n    if (props.getDetailPanelContent === previousGetDetailPanelContentProp.current && props.getDetailPanelHeight === previousGetDetailPanelHeightProp.current) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        detailPanel: _extends({}, state.detailPanel, cacheContentAndHeight(apiRef, props.getDetailPanelContent, props.getDetailPanelHeight, state.detailPanel.heightCache))\n      });\n    });\n    previousGetDetailPanelContentProp.current = props.getDetailPanelContent;\n    previousGetDetailPanelHeightProp.current = props.getDetailPanelHeight;\n  }, [apiRef, props.getDetailPanelContent, props.getDetailPanelHeight]);\n  const addDetailHeight = React.useCallback((initialValue, row) => {\n    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(apiRef);\n    if (!expandedRowIds || !expandedRowIds.has(row.id)) {\n      initialValue.detail = 0;\n      return initialValue;\n    }\n    updateCachesIfNeeded();\n    const heightCache = gridDetailPanelRawHeightCacheSelector(apiRef);\n    initialValue.detail = heightCache[row.id]?.height ?? 0; // Fallback to zero because the cache might not be ready yet (for example page was changed)\n    return initialValue;\n  }, [apiRef, updateCachesIfNeeded]);\n  const enabled = props.getDetailPanelContent !== undefined;\n  useGridRegisterPipeProcessor(apiRef, 'rowHeight', addDetailHeight, enabled);\n  const isFirstRender = React.useRef(true);\n  if (isFirstRender.current) {\n    updateCachesIfNeeded();\n  }\n  React.useEffect(() => {\n    if (!isFirstRender.current) {\n      updateCachesIfNeeded();\n    }\n    isFirstRender.current = false;\n  }, [apiRef, updateCachesIfNeeded]);\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridClasses } from '@mui/x-data-grid';\nimport { useGridRegisterPipeProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD, GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from \"./gridDetailPanelToggleColDef.js\";\nimport { gridDetailPanelExpandedRowIdsSelector } from \"./gridDetailPanelSelector.js\";\nexport const useGridDetailPanelPreProcessors = (privateApiRef, props) => {\n  const addToggleColumn = React.useCallback(columnsState => {\n    const detailPanelToggleColumn = _extends({}, GRID_DETAIL_PANEL_TOGGLE_COL_DEF, {\n      headerName: privateApiRef.current.getLocaleText('detailPanelToggle')\n    });\n    const shouldHaveToggleColumn = !!props.getDetailPanelContent;\n    const hasToggleColumn = columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] != null;\n    if (shouldHaveToggleColumn && !hasToggleColumn) {\n      columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] = detailPanelToggleColumn;\n      columnsState.orderedFields = [GRID_DETAIL_PANEL_TOGGLE_FIELD, ...columnsState.orderedFields];\n    } else if (!shouldHaveToggleColumn && hasToggleColumn) {\n      delete columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== GRID_DETAIL_PANEL_TOGGLE_FIELD);\n    } else if (shouldHaveToggleColumn && hasToggleColumn) {\n      columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] = _extends({}, detailPanelToggleColumn, columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD]);\n      // If the column is not in the columns array (not a custom detail panel toggle column), move it to the beginning of the column order\n      if (!props.columns.some(col => col.field === GRID_DETAIL_PANEL_TOGGLE_FIELD)) {\n        columnsState.orderedFields = [GRID_DETAIL_PANEL_TOGGLE_FIELD, ...columnsState.orderedFields.filter(field => field !== GRID_DETAIL_PANEL_TOGGLE_FIELD)];\n      }\n    }\n    return columnsState;\n  }, [privateApiRef, props.columns, props.getDetailPanelContent]);\n  const addExpandedClassToRow = React.useCallback((classes, id) => {\n    if (props.getDetailPanelContent == null) {\n      return classes;\n    }\n    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(privateApiRef);\n    if (!expandedRowIds.has(id)) {\n      return classes;\n    }\n    return [...classes, gridClasses['row--detailPanelExpanded']];\n  }, [privateApiRef, props.getDetailPanelContent]);\n  useGridRegisterPipeProcessor(privateApiRef, 'hydrateColumns', addToggleColumn);\n  useGridRegisterPipeProcessor(privateApiRef, 'rowClassName', addExpandedClassToRow);\n};", "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from \"../useEnhancedEffect/index.js\";\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-440013892\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;", "'use client';\n\nimport * as React from 'react';\n\n/**\n * A version of `React.useLayoutEffect` that does not show a warning when server-side rendering.\n * This is useful for effects that are only needed for client-side rendering but not for SSR.\n *\n * Before you use this hook, make sure to read https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * and confirm it doesn't apply to your use-case.\n */\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nexport default useEnhancedEffect;", "import { useGridSelector, useGridEventPriority, gridVisibleColumnDefinitionsSelector, useGridEvent } from '@mui/x-data-grid';\nimport { useGridVisibleRows, runIf } from '@mui/x-data-grid/internals';\nimport useEventCallback from '@mui/utils/useEventCallback';\n/**\n * @requires useGridColumns (state)\n * @requires useGridDimensions (method) - can be after\n * @requires useGridScroll (method\n */\nexport const useGridInfiniteLoader = (apiRef, props) => {\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const isEnabled = props.rowsLoadingMode === 'client' && !!props.onRowsScrollEnd;\n  const handleLoadMoreRows = useEventCallback(() => {\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const rowScrollEndParams = {\n      visibleColumns,\n      viewportPageSize,\n      visibleRowsCount: currentPage.rows.length\n    };\n    apiRef.current.publishEvent('rowsScrollEnd', rowScrollEndParams);\n  });\n  useGridEventPriority(apiRef, 'rowsScrollEnd', props.onRowsScrollEnd);\n  useGridEvent(apiRef, 'rowsScrollEndIntersection', runIf(isEnabled, handleLoadMoreRows));\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useTimeout from '@mui/utils/useTimeout';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridLogger, useGridEvent, getDataGridUtilityClass, useGridSelector, gridSortModelSelector, useGridEventPriority, gridRowNodeSelector, gridRowMaximumTreeDepthSelector, useGridApiMethod } from '@mui/x-data-grid';\nimport { gridEditRowsStateSelector, useGridRegisterPipeProcessor, gridExpandedSortedRowIndexLookupSelector } from '@mui/x-data-grid/internals';\nimport { GRID_REORDER_COL_DEF } from \"./gridRowReorderColDef.js\";\nconst EMPTY_REORDER_STATE = {\n  previousTargetId: null,\n  dragDirection: null,\n  previousDropPosition: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    rowDragging: ['row--dragging'],\n    rowDropAbove: ['row--dropAbove'],\n    rowDropBelow: ['row--dropBelow'],\n    rowBeingDragged: ['row--beingDragged']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nexport const rowReorderStateInitializer = state => _extends({}, state, {\n  rowReorder: {\n    isActive: false\n  }\n});\n\n/**\n * Hook for row reordering (Pro package)\n * @requires useGridRows (method)\n */\nexport const useGridRowReorder = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridRowReorder');\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const dragRowNode = React.useRef(null);\n  const originRowIndex = React.useRef(null);\n  const removeDnDStylesTimeout = React.useRef(undefined);\n  const previousDropIndicatorRef = React.useRef(null);\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const [dragRowId, setDragRowId] = React.useState('');\n  const sortedRowIndexLookup = useGridSelector(apiRef, gridExpandedSortedRowIndexLookupSelector);\n  const timeoutRowId = React.useRef('');\n  const timeout = useTimeout();\n  const previousReorderState = React.useRef(EMPTY_REORDER_STATE);\n  const dropTarget = React.useRef({\n    targetRowId: null,\n    targetRowIndex: null,\n    dropPosition: null\n  });\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(removeDnDStylesTimeout.current);\n    };\n  }, []);\n\n  // TODO: remove sortModel check once row reorder is sorting compatible\n  // remove treeData check once row reorder is treeData compatible\n  const isRowReorderDisabled = React.useMemo(() => {\n    return !props.rowReordering || !!sortModel.length || props.treeData;\n  }, [props.rowReordering, sortModel, props.treeData]);\n  const applyDropIndicator = React.useCallback((targetRowId, position) => {\n    // Remove existing drop indicator from previous target\n    if (previousDropIndicatorRef.current) {\n      previousDropIndicatorRef.current.classList.remove(classes.rowDropAbove, classes.rowDropBelow);\n      previousDropIndicatorRef.current = null;\n    }\n\n    // Apply new drop indicator\n    if (targetRowId !== undefined && position !== null) {\n      const targetRow = apiRef.current.rootElementRef?.current?.querySelector(`[data-id=\"${targetRowId}\"]`);\n      if (targetRow) {\n        targetRow.classList.add(position === 'above' ? classes.rowDropAbove : classes.rowDropBelow);\n        previousDropIndicatorRef.current = targetRow;\n      }\n    }\n  }, [apiRef, classes]);\n  const applyDraggedState = React.useCallback((rowId, isDragged) => {\n    if (rowId) {\n      const draggedRow = apiRef.current.rootElementRef?.current?.querySelector(`[data-id=\"${rowId}\"]`);\n      if (draggedRow) {\n        if (isDragged) {\n          draggedRow.classList.add(classes.rowBeingDragged);\n        } else {\n          draggedRow.classList.remove(classes.rowBeingDragged);\n        }\n      }\n    }\n  }, [apiRef, classes.rowBeingDragged]);\n  const applyRowAnimation = React.useCallback(callback => {\n    const rootElement = apiRef.current.rootElementRef?.current;\n    if (!rootElement) {\n      return;\n    }\n    const visibleRows = rootElement.querySelectorAll('[data-id]');\n    if (!visibleRows.length) {\n      return;\n    }\n    const rowsArray = Array.from(visibleRows);\n    const initialPositions = new Map();\n    rowsArray.forEach(row => {\n      const rowId = row.getAttribute('data-id');\n      if (rowId) {\n        initialPositions.set(rowId, row.getBoundingClientRect());\n      }\n    });\n    callback();\n\n    // Use `requestAnimationFrame` to ensure DOM has updated\n    requestAnimationFrame(() => {\n      const newRows = rootElement.querySelectorAll('[data-id]');\n      const animations = [];\n      newRows.forEach(row => {\n        const rowId = row.getAttribute('data-id');\n        if (!rowId) {\n          return;\n        }\n        const prevRect = initialPositions.get(rowId);\n        if (!prevRect) {\n          return;\n        }\n        const currentRect = row.getBoundingClientRect();\n        const deltaY = prevRect.top - currentRect.top;\n        if (Math.abs(deltaY) > 1) {\n          const animation = row.animate([{\n            transform: `translateY(${deltaY}px)`\n          }, {\n            transform: 'translateY(0)'\n          }], {\n            duration: 200,\n            easing: 'ease-in-out',\n            fill: 'forwards'\n          });\n          animations.push(animation);\n        }\n      });\n      if (animations.length > 0) {\n        Promise.allSettled(animations.map(a => a.finished)).then(() => {});\n      }\n    });\n  }, [apiRef]);\n  const handleDragStart = React.useCallback((params, event) => {\n    // Call the gridEditRowsStateSelector directly to avoid infnite loop\n    const editRowsState = gridEditRowsStateSelector(apiRef);\n    event.dataTransfer.effectAllowed = 'copy';\n    if (isRowReorderDisabled || Object.keys(editRowsState).length !== 0) {\n      return;\n    }\n    if (timeoutRowId.current) {\n      timeout.clear();\n      timeoutRowId.current = '';\n    }\n    logger.debug(`Start dragging row ${params.id}`);\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    apiRef.current.setRowDragActive(true);\n    dragRowNode.current = event.currentTarget;\n    // Apply cell-level dragging class to the drag handle\n    dragRowNode.current.classList.add(classes.rowDragging);\n    setDragRowId(params.id);\n\n    // Apply the dragged state to the entire row\n    applyDraggedState(params.id, true);\n    removeDnDStylesTimeout.current = setTimeout(() => {\n      dragRowNode.current.classList.remove(classes.rowDragging);\n    });\n    originRowIndex.current = sortedRowIndexLookup[params.id];\n    apiRef.current.setCellFocus(params.id, GRID_REORDER_COL_DEF.field);\n  }, [apiRef, isRowReorderDisabled, logger, classes.rowDragging, applyDraggedState, sortedRowIndexLookup, timeout]);\n  const handleDragOver = React.useCallback((params, event) => {\n    if (dragRowId === '') {\n      return;\n    }\n    const targetNode = gridRowNodeSelector(apiRef, params.id);\n    const sourceNode = gridRowNodeSelector(apiRef, dragRowId);\n    if (!sourceNode || !targetNode || targetNode.type === 'footer' || targetNode.type === 'pinnedRow' || !event.target) {\n      return;\n    }\n\n    // Find the relative 'y' mouse position based on the event.target\n    const targetRect = event.target.getBoundingClientRect();\n    const relativeY = Math.floor(event.clientY - targetRect.top);\n    const midPoint = Math.floor(targetRect.height / 2);\n    logger.debug(`Dragging over row ${params.id}`);\n    event.preventDefault();\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    if (timeoutRowId.current && timeoutRowId.current !== params.id) {\n      timeout.clear();\n      timeoutRowId.current = '';\n    }\n    if (targetNode.type === 'group' && targetNode.depth < sourceNode.depth && !targetNode.childrenExpanded && !timeoutRowId.current) {\n      timeout.start(500, () => {\n        const rowNode = gridRowNodeSelector(apiRef, params.id);\n        // TODO: Handle `dataSource` case with https://github.com/mui/mui-x/issues/18947\n        apiRef.current.setRowChildrenExpansion(params.id, !rowNode.childrenExpanded);\n      });\n      timeoutRowId.current = params.id;\n      return;\n    }\n    const targetRowIndex = sortedRowIndexLookup[params.id];\n    const sourceRowIndex = sortedRowIndexLookup[dragRowId];\n\n    // Determine drop position based on relativeY position within the row\n    const dropPosition = relativeY < midPoint ? 'above' : 'below';\n    const currentReorderState = {\n      dragDirection: targetRowIndex < sourceRowIndex ? 'up' : 'down',\n      previousTargetId: params.id,\n      previousDropPosition: dropPosition\n    };\n\n    // Update visual indicator when dragging over a different row or position\n    if (previousReorderState.current.previousTargetId !== params.id || previousReorderState.current.previousDropPosition !== dropPosition) {\n      const isSameNode = targetRowIndex === sourceRowIndex;\n\n      // Check if this is an adjacent position\n      const isAdjacentPosition = dropPosition === 'above' && targetRowIndex === sourceRowIndex + 1 || dropPosition === 'below' && targetRowIndex === sourceRowIndex - 1;\n      const validatedIndex = apiRef.current.unstable_applyPipeProcessors('getRowReorderTargetIndex', -1, {\n        sourceRowId: dragRowId,\n        targetRowId: params.id,\n        dropPosition,\n        dragDirection: currentReorderState.dragDirection\n      });\n\n      // Show drop indicator for valid drops OR adjacent positions OR same node\n      if (validatedIndex !== -1 || isAdjacentPosition || isSameNode) {\n        dropTarget.current = {\n          targetRowId: params.id,\n          targetRowIndex,\n          dropPosition\n        };\n        applyDropIndicator(params.id, dropPosition);\n      } else {\n        // Clear indicators for invalid drops\n        dropTarget.current = {\n          targetRowId: null,\n          targetRowIndex: null,\n          dropPosition: null\n        };\n        applyDropIndicator(null, null);\n      }\n      previousReorderState.current = currentReorderState;\n    }\n\n    // Render the native 'copy' cursor for additional visual feedback\n    if (dropTarget.current.targetRowId === null) {\n      event.dataTransfer.dropEffect = 'none';\n    } else {\n      event.dataTransfer.dropEffect = 'copy';\n    }\n  }, [dragRowId, apiRef, logger, timeout, sortedRowIndexLookup, applyDropIndicator]);\n  const handleDragEnd = React.useCallback((_, event) => {\n    // Call the gridEditRowsStateSelector directly to avoid infnite loop\n    const editRowsState = gridEditRowsStateSelector(apiRef);\n    if (dragRowId === '' || isRowReorderDisabled || Object.keys(editRowsState).length !== 0) {\n      return;\n    }\n    if (timeoutRowId.current) {\n      timeout.clear();\n      timeoutRowId.current = '';\n    }\n    logger.debug('End dragging row');\n    event.preventDefault();\n    // Prevent drag events propagation.\n    // For more information check here https://github.com/mui/mui-x/issues/2680.\n    event.stopPropagation();\n    clearTimeout(removeDnDStylesTimeout.current);\n    dragRowNode.current = null;\n    const dragDirection = previousReorderState.current.dragDirection;\n    previousReorderState.current = EMPTY_REORDER_STATE;\n\n    // Clear visual indicators and dragged state\n    applyDropIndicator(null, null);\n    applyDraggedState(dragRowId, false);\n    apiRef.current.setRowDragActive(false);\n\n    // Check if the row was dropped outside the grid.\n    if (!event.dataTransfer || event.dataTransfer.dropEffect === 'none') {\n      // Reset drop target state\n      dropTarget.current = {\n        targetRowId: null,\n        targetRowIndex: null,\n        dropPosition: null\n      };\n      originRowIndex.current = null;\n      setDragRowId('');\n      return;\n    }\n    if (dropTarget.current.targetRowIndex !== null && dropTarget.current.targetRowId !== null) {\n      const sourceRowIndex = originRowIndex.current;\n      const targetRowIndex = dropTarget.current.targetRowIndex;\n      const validatedIndex = apiRef.current.unstable_applyPipeProcessors('getRowReorderTargetIndex', targetRowIndex, {\n        sourceRowId: dragRowId,\n        targetRowId: dropTarget.current.targetRowId,\n        dropPosition: dropTarget.current.dropPosition,\n        dragDirection: dragDirection\n      });\n      if (validatedIndex !== -1) {\n        applyRowAnimation(() => {\n          apiRef.current.setRowIndex(dragRowId, validatedIndex);\n\n          // Emit the rowOrderChange event only once when the reordering stops.\n          const rowOrderChangeParams = {\n            row: apiRef.current.getRow(dragRowId),\n            targetIndex: validatedIndex,\n            oldIndex: sourceRowIndex\n          };\n          apiRef.current.publishEvent('rowOrderChange', rowOrderChangeParams);\n        });\n      }\n    }\n\n    // Reset drop target state\n    dropTarget.current = {\n      targetRowId: null,\n      targetRowIndex: null,\n      dropPosition: null\n    };\n    setDragRowId('');\n  }, [apiRef, dragRowId, isRowReorderDisabled, logger, applyDropIndicator, applyDraggedState, timeout, applyRowAnimation]);\n  const getRowReorderTargetIndex = React.useCallback((initialValue, {\n    sourceRowId,\n    targetRowId,\n    dropPosition,\n    dragDirection\n  }) => {\n    if (gridRowMaximumTreeDepthSelector(apiRef) > 1) {\n      return initialValue;\n    }\n    const targetRowIndex = sortedRowIndexLookup[targetRowId];\n    const sourceRowIndex = sortedRowIndexLookup[sourceRowId];\n\n    // Check if this drop would result in no actual movement\n    const isAdjacentNode = dropPosition === 'above' && targetRowIndex === sourceRowIndex + 1 ||\n    // dragging to immediately below (above next row)\n    dropPosition === 'below' && targetRowIndex === sourceRowIndex - 1; // dragging to immediately above (below previous row)\n\n    if (isAdjacentNode || sourceRowIndex === targetRowIndex) {\n      // Return -1 to prevent actual movement (indicators handled separately)\n      return -1;\n    }\n    let finalTargetIndex;\n    if (dragDirection === 'up') {\n      finalTargetIndex = dropPosition === 'above' ? targetRowIndex : targetRowIndex + 1;\n    } else {\n      finalTargetIndex = dropPosition === 'above' ? targetRowIndex - 1 : targetRowIndex;\n    }\n    return finalTargetIndex;\n  }, [apiRef, sortedRowIndexLookup]);\n  useGridRegisterPipeProcessor(apiRef, 'getRowReorderTargetIndex', getRowReorderTargetIndex);\n  useGridEvent(apiRef, 'rowDragStart', handleDragStart);\n  useGridEvent(apiRef, 'rowDragOver', handleDragOver);\n  useGridEvent(apiRef, 'rowDragEnd', handleDragEnd);\n  useGridEvent(apiRef, 'cellDragOver', handleDragOver);\n  useGridEventPriority(apiRef, 'rowOrderChange', props.onRowOrderChange);\n  const setRowDragActive = React.useCallback(isActive => {\n    apiRef.current.setState(state => _extends({}, state, {\n      rowReorder: _extends({}, state.rowReorder, {\n        isActive\n      })\n    }));\n  }, [apiRef]);\n  useGridApiMethod(apiRef, {\n    setRowDragActive\n  }, 'private');\n};", "'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}", "'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridSortModelSelector, useGridApiContext, useGridSelector, getDataGridUtilityClass, gridClasses } from '@mui/x-data-grid';\nimport { gridEditRowsStateSelector, isEventTargetInPortal, vars } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    isDraggable,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['rowReorderCell', isDraggable && 'rowReorderCell--draggable'],\n    placeholder: ['rowReorderCellPlaceholder'],\n    icon: ['rowReorderIcon']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst RowReorderIcon = styled('svg', {\n  name: 'MuiDataGrid',\n  slot: 'RowReorderIcon'\n})({\n  color: vars.colors.foreground.muted\n});\nfunction GridRowReorderCell(params) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const editRowsState = useGridSelector(apiRef, gridEditRowsStateSelector);\n  const cellValue =\n  // eslint-disable-next-line no-underscore-dangle\n  params.row.__reorder__ || (params.rowNode.type === 'group' ? params.rowNode.groupingKey ?? params.id : params.id);\n  const cellRef = React.useRef(null);\n  const listenerNodeRef = React.useRef(null);\n\n  // TODO: remove sortModel and treeData checks once row reorder is compatible\n  const isDraggable = React.useMemo(() => !!rootProps.rowReordering && !sortModel.length && !rootProps.treeData && Object.keys(editRowsState).length === 0, [rootProps.rowReordering, sortModel, rootProps.treeData, editRowsState]);\n  const ownerState = {\n    isDraggable,\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const publish = React.useCallback((eventName, propHandler) => event => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(params.id)) {\n      return;\n    }\n    apiRef.current.publishEvent(eventName, apiRef.current.getRowParams(params.id), event);\n    if (propHandler) {\n      propHandler(event);\n    }\n  }, [apiRef, params.id]);\n  const handleMouseDown = React.useCallback(() => {\n    // Prevent text selection as it will block all the drag events. More context: https://github.com/mui/mui-x/issues/16303\n    apiRef.current.rootElementRef?.current?.classList.add(gridClasses['root--disableUserSelection']);\n  }, [apiRef]);\n  const handleMouseUp = React.useCallback(() => {\n    apiRef.current.rootElementRef?.current?.classList.remove(gridClasses['root--disableUserSelection']);\n  }, [apiRef]);\n  const handleDragEnd = React.useCallback(event => {\n    handleMouseUp();\n    if (apiRef.current.getRow(params.id)) {\n      apiRef.current.publishEvent('rowDragEnd', apiRef.current.getRowParams(params.id), event);\n    }\n    listenerNodeRef.current.removeEventListener('dragend', handleDragEnd);\n    listenerNodeRef.current = null;\n  }, [apiRef, params.id, handleMouseUp]);\n  const handleDragStart = React.useCallback(event => {\n    if (!cellRef.current) {\n      return;\n    }\n    publish('rowDragStart')(event);\n    cellRef.current.addEventListener('dragend', handleDragEnd);\n    // cache the node to remove the listener when the drag ends\n    listenerNodeRef.current = cellRef.current;\n  }, [publish, handleDragEnd]);\n  const draggableEventHandlers = isDraggable ? {\n    onDragStart: handleDragStart,\n    onDragOver: publish('rowDragOver'),\n    onMouseDown: handleMouseDown,\n    onMouseUp: handleMouseUp\n  } : null;\n  if (params.rowNode.type === 'footer') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    ref: cellRef,\n    className: classes.root,\n    draggable: isDraggable\n  }, draggableEventHandlers, {\n    children: [/*#__PURE__*/_jsx(RowReorderIcon, {\n      as: rootProps.slots.rowReorderIcon,\n      ownerState: ownerState,\n      className: classes.icon,\n      fontSize: \"small\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: classes.placeholder,\n      children: cellValue\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridRowReorderCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridRowReorderCell };\nexport const renderRowReorderCell = params => {\n  if (params.rowNode.type === 'footer' || params.rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridRowReorderCell, _extends({}, params));\n};\nif (process.env.NODE_ENV !== \"production\") renderRowReorderCell.displayName = \"renderRowReorderCell\";", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridRowIdSelector, GRID_STRING_COL_DEF } from '@mui/x-data-grid';\nimport { renderRowReorderCell } from \"../../../components/GridRowReorderCell.js\";\nexport const GRID_REORDER_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'custom',\n  field: '__reorder__',\n  sortable: false,\n  filterable: false,\n  width: 50,\n  align: 'center',\n  headerAlign: 'center',\n  disableColumnMenu: true,\n  disableExport: true,\n  disableReorder: true,\n  resizable: false,\n  // @ts-ignore\n  aggregable: false,\n  renderHeader: () => ' ',\n  renderCell: renderRowReorderCell,\n  rowSpanValueGetter: (_, row, __, apiRef) => gridRowIdSelector(apiRef, row)\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass } from '@mui/x-data-grid';\nimport { useGridRegisterPipeProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_REORDER_COL_DEF } from \"./gridRowReorderColDef.js\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  return React.useMemo(() => {\n    const slots = {\n      rowReorderCellContainer: ['rowReorderCellContainer'],\n      columnHeaderReorder: ['columnHeaderReorder']\n    };\n    return composeClasses(slots, getDataGridUtilityClass, classes);\n  }, [classes]);\n};\nexport const useGridRowReorderPreProcessors = (privateApiRef, props) => {\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const updateReorderColumn = React.useCallback(columnsState => {\n    const reorderColumn = _extends({}, GRID_REORDER_COL_DEF, {\n      cellClassName: classes.rowReorderCellContainer,\n      headerClassName: classes.columnHeaderReorder,\n      headerName: privateApiRef.current.getLocaleText('rowReorderingHeaderName')\n    });\n    const shouldHaveReorderColumn = props.rowReordering;\n    const hasReorderColumn = columnsState.lookup[reorderColumn.field] != null;\n    if (shouldHaveReorderColumn && !hasReorderColumn) {\n      columnsState.lookup[reorderColumn.field] = reorderColumn;\n      columnsState.orderedFields = [reorderColumn.field, ...columnsState.orderedFields];\n    } else if (!shouldHaveReorderColumn && hasReorderColumn) {\n      delete columnsState.lookup[reorderColumn.field];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== reorderColumn.field);\n    } else if (shouldHaveReorderColumn && hasReorderColumn) {\n      columnsState.lookup[reorderColumn.field] = _extends({}, reorderColumn, columnsState.lookup[reorderColumn.field]);\n      // If the column is not in the columns array (not a custom reorder column), move it to the beginning of the column order\n      if (!props.columns.some(col => col.field === GRID_REORDER_COL_DEF.field)) {\n        columnsState.orderedFields = [reorderColumn.field, ...columnsState.orderedFields.filter(field => field !== reorderColumn.field)];\n      }\n    }\n    return columnsState;\n  }, [privateApiRef, classes, props.columns, props.rowReordering]);\n  useGridRegisterPipeProcessor(privateApiRef, 'hydrateColumns', updateReorderColumn);\n};", "import * as React from 'react';\nimport { useGridEvent } from '@mui/x-data-grid';\nimport { GRID_TREE_DATA_GROUPING_FIELD } from \"./gridTreeDataGroupColDef.js\";\nexport const useGridTreeData = (apiRef, props) => {\n  /**\n   * EVENTS\n   */\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.colDef.field === GRID_TREE_DATA_GROUPING_FIELD && (event.key === ' ' || event.key === 'Enter') && !event.shiftKey) {\n      if (params.rowNode.type !== 'group') {\n        return;\n      }\n      if (props.dataSource && !params.rowNode.childrenExpanded) {\n        apiRef.current.dataSource.fetchRows(params.id);\n        return;\n      }\n      apiRef.current.setRowChildrenExpansion(params.id, !params.rowNode.childrenExpanded);\n    }\n  }, [apiRef, props.dataSource]);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"hideDescendantCount\"];\nimport * as React from 'react';\nimport { gridRowTreeSelector, useFirstRender } from '@mui/x-data-grid';\nimport { GridStrategyGroup, useGridRegisterPipeProcessor, useGridRegisterStrategyProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_TREE_DATA_GROUPING_COL_DEF, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES } from \"./gridTreeDataGroupColDef.js\";\nimport { filterRowTreeFromTreeData, TreeDataStrategy } from \"./gridTreeDataUtils.js\";\nimport { GridTreeDataGroupingCell } from \"../../../components/index.js\";\nimport { createRowTree } from \"../../../utils/tree/createRowTree.js\";\nimport { sortRowTree } from \"../../../utils/tree/sortRowTree.js\";\nimport { updateRowTree } from \"../../../utils/tree/updateRowTree.js\";\nimport { getVisibleRowsLookup } from \"../../../utils/tree/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const useGridTreeDataPreProcessors = (privateApiRef, props) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.RowTree, TreeDataStrategy.Default, props.treeData && !props.dataSource ? () => true : () => false);\n  }, [privateApiRef, props.treeData, props.dataSource]);\n  const getGroupingColDef = React.useCallback(() => {\n    const groupingColDefProp = props.groupingColDef;\n    let colDefOverride;\n    if (typeof groupingColDefProp === 'function') {\n      const params = {\n        groupingName: TreeDataStrategy.Default,\n        fields: []\n      };\n      colDefOverride = groupingColDefProp(params);\n    } else {\n      colDefOverride = groupingColDefProp;\n    }\n    const _ref = colDefOverride ?? {},\n      {\n        hideDescendantCount\n      } = _ref,\n      colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const commonProperties = _extends({}, GRID_TREE_DATA_GROUPING_COL_DEF, {\n      renderCell: params => /*#__PURE__*/_jsx(GridTreeDataGroupingCell, _extends({}, params, {\n        hideDescendantCount: hideDescendantCount\n      })),\n      headerName: privateApiRef.current.getLocaleText('treeDataGroupingHeaderName')\n    });\n    return _extends({}, commonProperties, colDefOverrideProperties, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES);\n  }, [privateApiRef, props.groupingColDef]);\n  const updateGroupingColumn = React.useCallback(columnsState => {\n    if (props.dataSource) {\n      return columnsState;\n    }\n    const groupingColDefField = GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES.field;\n    const shouldHaveGroupingColumn = props.treeData;\n    const prevGroupingColumn = columnsState.lookup[groupingColDefField];\n    if (shouldHaveGroupingColumn) {\n      const newGroupingColumn = getGroupingColDef();\n      if (prevGroupingColumn) {\n        newGroupingColumn.width = prevGroupingColumn.width;\n        newGroupingColumn.flex = prevGroupingColumn.flex;\n      }\n      columnsState.lookup[groupingColDefField] = newGroupingColumn;\n      if (prevGroupingColumn == null) {\n        columnsState.orderedFields = [groupingColDefField, ...columnsState.orderedFields];\n      }\n    } else if (!shouldHaveGroupingColumn && prevGroupingColumn) {\n      delete columnsState.lookup[groupingColDefField];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== groupingColDefField);\n    }\n    return columnsState;\n  }, [props.treeData, props.dataSource, getGroupingColDef]);\n  const createRowTreeForTreeData = React.useCallback(params => {\n    if (!props.getTreeDataPath) {\n      throw new Error('MUI X: No getTreeDataPath given.');\n    }\n    const getRowTreeBuilderNode = rowId => ({\n      id: rowId,\n      path: props.getTreeDataPath(params.dataRowIdToModelLookup[rowId]).map(key => ({\n        key,\n        field: null\n      }))\n    });\n    const onDuplicatePath = (firstId, secondId, path) => {\n      throw new Error(['MUI X: The path returned by `getTreeDataPath` should be unique.', `The rows with id #${firstId} and #${secondId} have the same.`, `Path: ${JSON.stringify(path.map(step => step.key))}.`].join('\\n'));\n    };\n    if (params.updates.type === 'full') {\n      return createRowTree({\n        previousTree: params.previousTree,\n        nodes: params.updates.rows.map(getRowTreeBuilderNode),\n        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,\n        isGroupExpandedByDefault: props.isGroupExpandedByDefault,\n        groupingName: TreeDataStrategy.Default,\n        onDuplicatePath\n      });\n    }\n    return updateRowTree({\n      nodes: {\n        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),\n        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),\n        removed: params.updates.actions.remove\n      },\n      previousTree: params.previousTree,\n      previousTreeDepth: params.previousTreeDepths,\n      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,\n      isGroupExpandedByDefault: props.isGroupExpandedByDefault,\n      groupingName: TreeDataStrategy.Default\n    });\n  }, [props.getTreeDataPath, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);\n  const filterRows = React.useCallback(params => {\n    const rowTree = gridRowTreeSelector(privateApiRef);\n    return filterRowTreeFromTreeData({\n      rowTree,\n      isRowMatchingFilters: params.isRowMatchingFilters,\n      disableChildrenFiltering: props.disableChildrenFiltering,\n      filterModel: params.filterModel,\n      apiRef: privateApiRef\n    });\n  }, [privateApiRef, props.disableChildrenFiltering]);\n  const sortRows = React.useCallback(params => {\n    const rowTree = gridRowTreeSelector(privateApiRef);\n    return sortRowTree({\n      rowTree,\n      sortRowList: params.sortRowList,\n      disableChildrenSorting: props.disableChildrenSorting,\n      shouldRenderGroupBelowLeaves: false\n    });\n  }, [privateApiRef, props.disableChildrenSorting]);\n  useGridRegisterPipeProcessor(privateApiRef, 'hydrateColumns', updateGroupingColumn);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, 'rowTreeCreation', createRowTreeForTreeData);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, 'filtering', filterRows);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, 'sorting', sortRows);\n  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    setStrategyAvailability();\n  });\n\n  /**\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (!isFirstRender.current) {\n      setStrategyAvailability();\n    } else {\n      isFirstRender.current = false;\n    }\n  }, [setStrategyAvailability]);\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridSelector, gridFilteredDescendantCountLookupSelector, getDataGridUtilityClass } from '@mui/x-data-grid';\nimport { vars } from '@mui/x-data-grid/internals';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['treeDataGroupingCell'],\n    toggle: ['treeDataGroupingCellToggle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridTreeDataGroupingCell(props) {\n  const {\n    id,\n    field,\n    formattedValue,\n    rowNode,\n    hideDescendantCount,\n    offsetMultiplier = 2\n  } = props;\n  const rootProps = useGridRootProps();\n  const apiRef = useGridApiContext();\n  const classes = useUtilityClasses(rootProps);\n  const filteredDescendantCountLookup = useGridSelector(apiRef, gridFilteredDescendantCountLookupSelector);\n  const filteredDescendantCount = filteredDescendantCountLookup[rowNode.id] ?? 0;\n  const Icon = rowNode.childrenExpanded ? rootProps.slots.treeDataCollapseIcon : rootProps.slots.treeDataExpandIcon;\n  const handleClick = event => {\n    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);\n    apiRef.current.setCellFocus(id, field);\n    event.stopPropagation(); // TODO remove event.stopPropagation\n  };\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: classes.root,\n    style: {\n      marginLeft: vars.spacing(rowNode.depth * offsetMultiplier)\n    },\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: classes.toggle,\n      children: filteredDescendantCount > 0 && /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n        size: \"small\",\n        onClick: handleClick,\n        tabIndex: -1,\n        \"aria-label\": rowNode.childrenExpanded ? apiRef.current.getLocaleText('treeDataCollapse') : apiRef.current.getLocaleText('treeDataExpand')\n      }, rootProps?.slotProps?.baseIconButton, {\n        children: /*#__PURE__*/_jsx(Icon, {\n          fontSize: \"inherit\"\n        })\n      }))\n    }), /*#__PURE__*/_jsxs(\"span\", {\n      children: [formattedValue === undefined ? rowNode.groupingKey : formattedValue, !hideDescendantCount && filteredDescendantCount > 0 ? ` (${filteredDescendantCount})` : '']\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridTreeDataGroupingCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  hideDescendantCount: PropTypes.bool,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The cell offset multiplier used for calculating cell offset (`rowNode.depth * offsetMultiplier` px).\n   * @default 2\n   */\n  offsetMultiplier: PropTypes.number,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridTreeDataGroupingCell };", "import { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\n// Single-linked list node\nclass Node {\n  constructor(data, next) {\n    this.next = next;\n    this.data = data;\n  }\n  insertAfter(list) {\n    if (!list.first || !list.last) {\n      return;\n    }\n    const next = this.next;\n    this.next = list.first;\n    list.last.next = next;\n  }\n}\n\n// Single-linked list container\nclass List {\n  constructor(first, last) {\n    this.first = first;\n    this.last = last;\n  }\n  data() {\n    const array = [];\n    this.forEach(node => {\n      array.push(node.data);\n    });\n    return array;\n  }\n  forEach(fn) {\n    let current = this.first;\n    while (current !== null) {\n      fn(current);\n      current = current.next;\n    }\n  }\n  static from(array) {\n    if (array.length === 0) {\n      return new List(null, null);\n    }\n    let index = 0;\n    const first = new Node(array[index], null);\n    let current = first;\n    while (index + 1 < array.length) {\n      index += 1;\n      const node = new Node(array[index], null);\n      current.next = node;\n      current = node;\n    }\n    return new List(first, current);\n  }\n}\nexport const sortRowTree = params => {\n  const {\n    rowTree,\n    disableChildrenSorting,\n    sortRowList,\n    shouldRenderGroupBelowLeaves\n  } = params;\n  const sortedGroupedByParentRows = new Map();\n  const sortGroup = node => {\n    const shouldSortGroup = !!sortRowList && (!disableChildrenSorting || node.depth === -1);\n    let sortedRowIds;\n    if (shouldSortGroup) {\n      for (let i = 0; i < node.children.length; i += 1) {\n        const childNode = rowTree[node.children[i]];\n        if (childNode.type === 'group') {\n          sortGroup(childNode);\n        }\n      }\n      sortedRowIds = sortRowList(node.children.map(childId => rowTree[childId]));\n    } else if (shouldRenderGroupBelowLeaves) {\n      const childrenLeaves = [];\n      const childrenGroups = [];\n      for (let i = 0; i < node.children.length; i += 1) {\n        const childId = node.children[i];\n        const childNode = rowTree[childId];\n        if (childNode.type === 'group') {\n          sortGroup(childNode);\n          childrenGroups.push(childId);\n        } else if (childNode.type === 'leaf') {\n          childrenLeaves.push(childId);\n        }\n      }\n      sortedRowIds = [...childrenLeaves, ...childrenGroups];\n    } else {\n      for (let i = 0; i < node.children.length; i += 1) {\n        const childNode = rowTree[node.children[i]];\n        if (childNode.type === 'group') {\n          sortGroup(childNode);\n        }\n      }\n      sortedRowIds = [...node.children];\n    }\n    if (node.footerId != null) {\n      sortedRowIds.push(node.footerId);\n    }\n    sortedGroupedByParentRows.set(node.id, sortedRowIds);\n  };\n  sortGroup(rowTree[GRID_ROOT_GROUP_ID]);\n  const rootList = List.from(sortedGroupedByParentRows.get(GRID_ROOT_GROUP_ID));\n  rootList.forEach(node => {\n    const children = sortedGroupedByParentRows.get(node.data);\n    if (children?.length) {\n      node.insertAfter(List.from(children));\n    }\n  });\n  return rootList.data();\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '@mui/x-data-grid';\nimport { getRowIdFromRowModel } from '@mui/x-data-grid/internals';\nfunction createPinnedRowsInternalCache(pinnedRows, getRowId) {\n  const cache = {\n    topIds: [],\n    bottomIds: [],\n    idLookup: {}\n  };\n  pinnedRows?.top?.forEach(rowModel => {\n    const id = getRowIdFromRowModel(rowModel, getRowId);\n    cache.topIds.push(id);\n    cache.idLookup[id] = rowModel;\n  });\n  pinnedRows?.bottom?.forEach(rowModel => {\n    const id = getRowIdFromRowModel(rowModel, getRowId);\n    cache.bottomIds.push(id);\n    cache.idLookup[id] = rowModel;\n  });\n  return cache;\n}\nexport const rowPinningStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.pinnedRows = createPinnedRowsInternalCache(props.pinnedRows, props.getRowId);\n  return _extends({}, state, {\n    rows: _extends({}, state.rows, {\n      additionalRowGroups: _extends({}, state.rows?.additionalRowGroups, {\n        pinnedRows: {\n          top: [],\n          bottom: []\n        }\n      })\n    })\n  });\n};\nexport const useGridRowPinning = (apiRef, props) => {\n  const setPinnedRows = React.useCallback(newPinnedRows => {\n    apiRef.current.caches.pinnedRows = createPinnedRowsInternalCache(newPinnedRows, props.getRowId);\n    apiRef.current.requestPipeProcessorsApplication('hydrateRows');\n  }, [apiRef, props.getRowId]);\n  useGridApiMethod(apiRef, {\n    unstable_setPinnedRows: setPinnedRows\n  }, 'public');\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    apiRef.current.unstable_setPinnedRows(props.pinnedRows);\n  }, [apiRef, props.pinnedRows]);\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRegisterPipeProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nimport { insertNodeInTree } from \"../../../utils/tree/utils.js\";\nexport function addPinnedRow({\n  groupingParams,\n  rowModel,\n  rowId,\n  position,\n  apiRef,\n  isAutoGenerated\n}) {\n  const dataRowIdToModelLookup = _extends({}, groupingParams.dataRowIdToModelLookup);\n  const tree = _extends({}, groupingParams.tree);\n  const treeDepths = _extends({}, groupingParams.treeDepths);\n\n  // TODO: warn if id is already present in `props.rows`\n\n  const node = {\n    type: 'pinnedRow',\n    id: rowId,\n    depth: 0,\n    parent: GRID_ROOT_GROUP_ID,\n    isAutoGenerated\n  };\n  insertNodeInTree(node, tree, treeDepths, null);\n  if (!isAutoGenerated) {\n    dataRowIdToModelLookup[rowId] = rowModel;\n  }\n  // Do not push it to ids list so that pagination is not affected by pinned rows\n\n  apiRef.current.caches.rows.dataRowIdToModelLookup[rowId] = _extends({}, rowModel);\n  const previousPinnedRows = groupingParams.additionalRowGroups?.pinnedRows || {};\n  const newPinnedRow = {\n    id: rowId,\n    model: rowModel\n  };\n  if (groupingParams.additionalRowGroups?.pinnedRows?.[position]?.includes(newPinnedRow)) {\n    return _extends({}, groupingParams, {\n      dataRowIdToModelLookup,\n      tree,\n      treeDepths\n    });\n  }\n  return _extends({}, groupingParams, {\n    dataRowIdToModelLookup,\n    tree,\n    treeDepths,\n    additionalRowGroups: _extends({}, groupingParams.additionalRowGroups, {\n      pinnedRows: _extends({}, previousPinnedRows, {\n        [position]: [...(previousPinnedRows[position] || []), newPinnedRow]\n      })\n    })\n  });\n}\nexport const useGridRowPinningPreProcessors = apiRef => {\n  const prevPinnedRowsCacheRef = React.useRef(null);\n  const addPinnedRows = React.useCallback(groupingParams => {\n    const pinnedRowsCache = apiRef.current.caches.pinnedRows || {};\n    const prevPinnedRowsCache = prevPinnedRowsCacheRef.current;\n    prevPinnedRowsCacheRef.current = pinnedRowsCache;\n    let newGroupingParams = _extends({}, groupingParams, {\n      additionalRowGroups: _extends({}, groupingParams.additionalRowGroups, {\n        // reset pinned rows state\n        pinnedRows: {}\n      })\n    });\n    if (prevPinnedRowsCache) {\n      const pinnedRowCleanup = rowId => {\n        const node = newGroupingParams.tree[rowId];\n        if (node?.type === 'pinnedRow') {\n          delete newGroupingParams.tree[rowId];\n          delete newGroupingParams.dataRowIdToModelLookup[rowId];\n          delete apiRef.current.caches.rows.dataRowIdToModelLookup[rowId];\n        }\n      };\n      prevPinnedRowsCache.topIds?.forEach(pinnedRowCleanup);\n      prevPinnedRowsCache.bottomIds?.forEach(pinnedRowCleanup);\n    }\n    pinnedRowsCache.topIds?.forEach(rowId => {\n      newGroupingParams = addPinnedRow({\n        groupingParams: newGroupingParams,\n        rowModel: pinnedRowsCache.idLookup[rowId],\n        rowId,\n        position: 'top',\n        apiRef,\n        isAutoGenerated: false\n      });\n    });\n    pinnedRowsCache.bottomIds?.forEach(rowId => {\n      newGroupingParams = addPinnedRow({\n        groupingParams: newGroupingParams,\n        rowModel: pinnedRowsCache.idLookup[rowId],\n        rowId,\n        position: 'bottom',\n        apiRef,\n        isAutoGenerated: false\n      });\n    });\n\n    // If row with the same `id` is present both in `rows` and `pinnedRows` - remove it from the root group children\n    if (pinnedRowsCache.bottomIds?.length || pinnedRowsCache.topIds?.length) {\n      const shouldKeepRow = rowId => {\n        if (newGroupingParams.tree[rowId] && newGroupingParams.tree[rowId].type === 'pinnedRow') {\n          return false;\n        }\n        return true;\n      };\n      const rootGroupNode = newGroupingParams.tree[GRID_ROOT_GROUP_ID];\n      newGroupingParams.tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroupNode, {\n        children: rootGroupNode.children.filter(shouldKeepRow)\n      });\n      newGroupingParams.dataRowIds = newGroupingParams.dataRowIds.filter(shouldKeepRow);\n    }\n    return newGroupingParams;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'hydrateRows', addPinnedRows);\n};", "import * as React from 'react';\nimport { useGridEvent, useGridSelector, gridSortModelSelector, gridFilterModelSelector, gridRenderContextSelector, useGridEventPriority } from '@mui/x-data-grid';\nimport { getVisibleRows } from '@mui/x-data-grid/internals';\nimport { findSkeletonRowsSection } from \"./utils.js\";\n\n/**\n * @requires useGridRows (state)\n * @requires useGridPagination (state)\n * @requires useGridDimensions (method) - can be after\n * @requires useGridScroll (method\n */\nexport const useGridLazyLoader = (privateApiRef, props) => {\n  const sortModel = useGridSelector(privateApiRef, gridSortModelSelector);\n  const filterModel = useGridSelector(privateApiRef, gridFilterModelSelector);\n  const renderedRowsIntervalCache = React.useRef({\n    firstRowToRender: 0,\n    lastRowToRender: 0\n  });\n  const isDisabled = props.rowsLoadingMode !== 'server';\n  const handleRenderedRowsIntervalChange = React.useCallback(params => {\n    if (isDisabled) {\n      return;\n    }\n    const fetchRowsParams = {\n      firstRowToRender: params.firstRowIndex,\n      lastRowToRender: params.lastRowIndex,\n      sortModel,\n      filterModel\n    };\n    if (renderedRowsIntervalCache.current.firstRowToRender === params.firstRowIndex && renderedRowsIntervalCache.current.lastRowToRender === params.lastRowIndex) {\n      return;\n    }\n    renderedRowsIntervalCache.current = {\n      firstRowToRender: params.firstRowIndex,\n      lastRowToRender: params.lastRowIndex\n    };\n    if (sortModel.length === 0 && filterModel.items.length === 0) {\n      const currentVisibleRows = getVisibleRows(privateApiRef, {\n        pagination: props.pagination,\n        paginationMode: props.paginationMode\n      });\n      const skeletonRowsSection = findSkeletonRowsSection({\n        apiRef: privateApiRef,\n        visibleRows: currentVisibleRows.rows,\n        range: {\n          firstRowIndex: params.firstRowIndex,\n          lastRowIndex: params.lastRowIndex\n        }\n      });\n      if (!skeletonRowsSection) {\n        return;\n      }\n      fetchRowsParams.firstRowToRender = skeletonRowsSection.firstRowIndex;\n      fetchRowsParams.lastRowToRender = skeletonRowsSection.lastRowIndex;\n    }\n    privateApiRef.current.publishEvent('fetchRows', fetchRowsParams);\n  }, [privateApiRef, isDisabled, props.pagination, props.paginationMode, sortModel, filterModel]);\n  const handleGridSortModelChange = React.useCallback(newSortModel => {\n    if (isDisabled) {\n      return;\n    }\n    privateApiRef.current.requestPipeProcessorsApplication('hydrateRows');\n    const renderContext = gridRenderContextSelector(privateApiRef);\n    const fetchRowsParams = {\n      firstRowToRender: renderContext.firstRowIndex,\n      lastRowToRender: renderContext.lastRowIndex,\n      sortModel: newSortModel,\n      filterModel\n    };\n    privateApiRef.current.publishEvent('fetchRows', fetchRowsParams);\n  }, [privateApiRef, isDisabled, filterModel]);\n  const handleGridFilterModelChange = React.useCallback(newFilterModel => {\n    if (isDisabled) {\n      return;\n    }\n    privateApiRef.current.requestPipeProcessorsApplication('hydrateRows');\n    const renderContext = gridRenderContextSelector(privateApiRef);\n    const fetchRowsParams = {\n      firstRowToRender: renderContext.firstRowIndex,\n      lastRowToRender: renderContext.lastRowIndex,\n      sortModel,\n      filterModel: newFilterModel\n    };\n    privateApiRef.current.publishEvent('fetchRows', fetchRowsParams);\n  }, [privateApiRef, isDisabled, sortModel]);\n  useGridEvent(privateApiRef, 'renderedRowsIntervalChange', handleRenderedRowsIntervalChange);\n  useGridEvent(privateApiRef, 'sortModelChange', handleGridSortModelChange);\n  useGridEvent(privateApiRef, 'filterModelChange', handleGridFilterModelChange);\n  useGridEventPriority(privateApiRef, 'fetchRows', props.onFetchRows);\n};", "import { gridRowNodeSelector } from '@mui/x-data-grid';\nexport const findSkeletonRowsSection = ({\n  apiRef,\n  visibleRows,\n  range\n}) => {\n  let {\n    firstRowIndex,\n    lastRowIndex\n  } = range;\n  const visibleRowsSection = visibleRows.slice(range.firstRowIndex, range.lastRowIndex);\n  let startIndex = 0;\n  let endIndex = visibleRowsSection.length - 1;\n  let isSkeletonSectionFound = false;\n  while (!isSkeletonSectionFound && firstRowIndex < lastRowIndex) {\n    const isStartingWithASkeletonRow = gridRowNodeSelector(apiRef, visibleRowsSection[startIndex].id)?.type === 'skeletonRow';\n    const isEndingWithASkeletonRow = gridRowNodeSelector(apiRef, visibleRowsSection[endIndex].id)?.type === 'skeletonRow';\n    if (isStartingWithASkeletonRow && isEndingWithASkeletonRow) {\n      isSkeletonSectionFound = true;\n    }\n    if (!isStartingWithASkeletonRow) {\n      startIndex += 1;\n      firstRowIndex += 1;\n    }\n    if (!isEndingWithASkeletonRow) {\n      endIndex -= 1;\n      lastRowIndex -= 1;\n    }\n  }\n  return isSkeletonSectionFound ? {\n    firstRowIndex,\n    lastRowIndex\n  } : undefined;\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRegisterPipeProcessor } from '@mui/x-data-grid/internals';\nimport { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nexport const GRID_SKELETON_ROW_ROOT_ID = 'auto-generated-skeleton-row-root';\nconst getSkeletonRowId = index => `${GRID_SKELETON_ROW_ROOT_ID}-${index}`;\nexport const useGridLazyLoaderPreProcessors = (privateApiRef, props) => {\n  const addSkeletonRows = React.useCallback(groupingParams => {\n    const rootGroup = groupingParams.tree[GRID_ROOT_GROUP_ID];\n    if (props.rowsLoadingMode !== 'server' || !props.rowCount || rootGroup.children.length >= props.rowCount) {\n      return groupingParams;\n    }\n    const tree = _extends({}, groupingParams.tree);\n    const rootGroupChildren = [...rootGroup.children];\n    for (let i = 0; i < props.rowCount - rootGroup.children.length; i += 1) {\n      const skeletonId = getSkeletonRowId(i);\n      rootGroupChildren.push(skeletonId);\n      const skeletonRowNode = {\n        type: 'skeletonRow',\n        id: skeletonId,\n        parent: GRID_ROOT_GROUP_ID,\n        depth: 0\n      };\n      tree[skeletonId] = skeletonRowNode;\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n    return _extends({}, groupingParams, {\n      tree\n    });\n  }, [props.rowCount, props.rowsLoadingMode]);\n  useGridRegisterPipeProcessor(privateApiRef, 'hydrateRows', addSkeletonRows);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { throttle } from '@mui/x-internals/throttle';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport debounce from '@mui/utils/debounce';\nimport { useGridEvent, gridSortModelSelector, gridFilterModelSelector, GRID_ROOT_GROUP_ID, gridPaginationModelSelector, gridFilteredSortedRowIdsSelector, gridRowIdSelector } from '@mui/x-data-grid';\nimport { getVisibleRows, gridRenderContextSelector, GridStrategyGroup, useGridRegisterStrategyProcessor, runIf, DataSourceRowsUpdateStrategy } from '@mui/x-data-grid/internals';\nimport { findSkeletonRowsSection } from \"../lazyLoader/utils.js\";\nimport { GRID_SKELETON_ROW_ROOT_ID } from \"../lazyLoader/useGridLazyLoaderPreProcessors.js\";\nvar LoadingTrigger = /*#__PURE__*/function (LoadingTrigger) {\n  LoadingTrigger[LoadingTrigger[\"VIEWPORT\"] = 0] = \"VIEWPORT\";\n  LoadingTrigger[LoadingTrigger[\"SCROLL_END\"] = 1] = \"SCROLL_END\";\n  return LoadingTrigger;\n}(LoadingTrigger || {});\nconst INTERVAL_CACHE_INITIAL_STATE = {\n  firstRowToRender: 0,\n  lastRowToRender: 0\n};\nconst getSkeletonRowId = index => `${GRID_SKELETON_ROW_ROOT_ID}-${index}`;\n\n/**\n * @requires useGridRows (state)\n * @requires useGridPagination (state)\n * @requires useGridScroll (method\n */\nexport const useGridDataSourceLazyLoader = (privateApiRef, props) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.LazyLoading, props.dataSource && props.lazyLoading ? () => true : () => false);\n  }, [privateApiRef, props.lazyLoading, props.dataSource]);\n  const [lazyLoadingRowsUpdateStrategyActive, setLazyLoadingRowsUpdateStrategyActive] = React.useState(false);\n  const renderedRowsIntervalCache = React.useRef(INTERVAL_CACHE_INITIAL_STATE);\n  const previousLastRowIndex = React.useRef(0);\n  const loadingTrigger = React.useRef(null);\n  const rowsStale = React.useRef(false);\n  const draggedRowId = React.useRef(null);\n  const fetchRows = React.useCallback(params => {\n    privateApiRef.current.dataSource.fetchRows(GRID_ROOT_GROUP_ID, params);\n  }, [privateApiRef]);\n  const debouncedFetchRows = React.useMemo(() => debounce(fetchRows, 0), [fetchRows]);\n\n  // Adjust the render context range to fit the pagination model's page size\n  // First row index should be decreased to the start of the page, end row index should be increased to the end of the page\n  const adjustRowParams = React.useCallback(params => {\n    if (typeof params.start !== 'number') {\n      return params;\n    }\n    const paginationModel = gridPaginationModelSelector(privateApiRef);\n    return _extends({}, params, {\n      start: params.start - params.start % paginationModel.pageSize,\n      end: params.end + paginationModel.pageSize - params.end % paginationModel.pageSize - 1\n    });\n  }, [privateApiRef]);\n  const resetGrid = React.useCallback(() => {\n    privateApiRef.current.setLoading(true);\n    privateApiRef.current.dataSource.cache.clear();\n    rowsStale.current = true;\n    previousLastRowIndex.current = 0;\n    const paginationModel = gridPaginationModelSelector(privateApiRef);\n    const sortModel = gridSortModelSelector(privateApiRef);\n    const filterModel = gridFilterModelSelector(privateApiRef);\n    const getRowsParams = {\n      start: 0,\n      end: paginationModel.pageSize - 1,\n      sortModel,\n      filterModel\n    };\n    fetchRows(getRowsParams);\n  }, [privateApiRef, fetchRows]);\n  const ensureValidRowCount = React.useCallback((previousLoadingTrigger, newLoadingTrigger) => {\n    // switching from lazy loading to infinite loading should always reset the grid\n    // since there is no guarantee that the new data will be placed correctly\n    // there might be some skeleton rows in between the data or the data has changed (row count became unknown)\n    if (previousLoadingTrigger === LoadingTrigger.VIEWPORT && newLoadingTrigger === LoadingTrigger.SCROLL_END) {\n      resetGrid();\n      return;\n    }\n\n    // switching from infinite loading to lazy loading should reset the grid only if the known row count\n    // is smaller than the amount of rows rendered\n    const tree = privateApiRef.current.state.rows.tree;\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const pageRowCount = privateApiRef.current.state.pagination.rowCount;\n    const rootChildrenCount = rootGroupChildren.length;\n    if (rootChildrenCount > pageRowCount) {\n      resetGrid();\n    }\n  }, [privateApiRef, resetGrid]);\n  const addSkeletonRows = React.useCallback(() => {\n    const tree = privateApiRef.current.state.rows.tree;\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const pageRowCount = privateApiRef.current.state.pagination.rowCount;\n    const rootChildrenCount = rootGroupChildren.length;\n\n    /**\n     * Do nothing if\n     * - children count is 0\n     */\n    if (rootChildrenCount === 0) {\n      return;\n    }\n    const pageToSkip = adjustRowParams({\n      start: renderedRowsIntervalCache.current.firstRowToRender,\n      end: renderedRowsIntervalCache.current.lastRowToRender\n    });\n    let hasChanged = false;\n    const isInitialPage = renderedRowsIntervalCache.current.firstRowToRender === 0 && renderedRowsIntervalCache.current.lastRowToRender === 0;\n    for (let i = 0; i < rootChildrenCount; i += 1) {\n      if (isInitialPage) {\n        break;\n      }\n      // replace the rows not in the viewport with skeleton rows\n      if (pageToSkip.start <= i && i <= pageToSkip.end || tree[rootGroupChildren[i]]?.type === 'skeletonRow' ||\n      // ignore rows that are already skeleton rows\n      tree[rootGroupChildren[i]]?.id === draggedRowId.current // ignore row that is being dragged (https://github.com/mui/mui-x/issues/17854)\n      ) {\n        continue;\n      }\n      const rowId = tree[rootGroupChildren[i]].id; // keep the id, so that row related state is maintained\n      const skeletonRowNode = {\n        type: 'skeletonRow',\n        id: rowId,\n        parent: GRID_ROOT_GROUP_ID,\n        depth: 0\n      };\n      tree[rowId] = skeletonRowNode;\n      hasChanged = true;\n    }\n\n    // Should only happen with VIEWPORT loading trigger\n    if (loadingTrigger.current === LoadingTrigger.VIEWPORT) {\n      // fill the grid with skeleton rows\n      for (let i = 0; i < pageRowCount - rootChildrenCount; i += 1) {\n        const skeletonId = getSkeletonRowId(i + rootChildrenCount); // to avoid duplicate keys on rebuild\n        rootGroupChildren.push(skeletonId);\n        const skeletonRowNode = {\n          type: 'skeletonRow',\n          id: skeletonId,\n          parent: GRID_ROOT_GROUP_ID,\n          depth: 0\n        };\n        tree[skeletonId] = skeletonRowNode;\n        hasChanged = true;\n      }\n    }\n    if (!hasChanged) {\n      return;\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n    privateApiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        tree\n      })\n    }), 'addSkeletonRows');\n  }, [privateApiRef, adjustRowParams]);\n  const updateLoadingTrigger = React.useCallback(rowCount => {\n    const newLoadingTrigger = rowCount === -1 ? LoadingTrigger.SCROLL_END : LoadingTrigger.VIEWPORT;\n    if (loadingTrigger.current !== null) {\n      ensureValidRowCount(loadingTrigger.current, newLoadingTrigger);\n    }\n    if (loadingTrigger.current !== newLoadingTrigger) {\n      loadingTrigger.current = newLoadingTrigger;\n    }\n  }, [ensureValidRowCount]);\n  const handleDataUpdate = React.useCallback(params => {\n    if ('error' in params) {\n      return;\n    }\n    const {\n      response,\n      fetchParams\n    } = params;\n    const pageRowCount = privateApiRef.current.state.pagination.rowCount;\n    const tree = privateApiRef.current.state.rows.tree;\n    const dataRowIdToModelLookup = privateApiRef.current.state.rows.dataRowIdToModelLookup;\n    if (response.rowCount !== undefined || pageRowCount === undefined) {\n      privateApiRef.current.setRowCount(response.rowCount === undefined ? -1 : response.rowCount);\n    }\n\n    // scroll to the top if the rows are stale and the new request is for the first page\n    if (rowsStale.current && params.fetchParams.start === 0) {\n      privateApiRef.current.scroll({\n        top: 0\n      });\n      // the rows can safely be replaced. skeleton rows will be added later\n      privateApiRef.current.setRows(response.rows);\n    } else {\n      const rootGroup = tree[GRID_ROOT_GROUP_ID];\n      const rootGroupChildren = [...rootGroup.children];\n      const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(privateApiRef);\n      const startingIndex = typeof fetchParams.start === 'string' ? Math.max(filteredSortedRowIds.indexOf(fetchParams.start), 0) : fetchParams.start;\n\n      // Check for duplicate rows\n      let duplicateRowCount = 0;\n      response.rows.forEach(row => {\n        const rowId = gridRowIdSelector(privateApiRef, row);\n        if (tree[rowId] || dataRowIdToModelLookup[rowId]) {\n          const index = rootGroupChildren.indexOf(rowId);\n          if (index !== -1) {\n            const skeletonId = getSkeletonRowId(index);\n            rootGroupChildren[index] = skeletonId;\n            tree[skeletonId] = {\n              type: 'skeletonRow',\n              id: skeletonId,\n              parent: GRID_ROOT_GROUP_ID,\n              depth: 0\n            };\n          }\n          delete tree[rowId];\n          delete dataRowIdToModelLookup[rowId];\n          duplicateRowCount += 1;\n        }\n      });\n      if (duplicateRowCount > 0) {\n        tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n          children: rootGroupChildren\n        });\n        privateApiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            tree,\n            dataRowIdToModelLookup\n          })\n        }));\n      }\n      privateApiRef.current.unstable_replaceRows(startingIndex, response.rows);\n    }\n    rowsStale.current = false;\n    if (loadingTrigger.current === null) {\n      updateLoadingTrigger(privateApiRef.current.state.pagination.rowCount);\n    }\n    addSkeletonRows();\n    privateApiRef.current.setLoading(false);\n    privateApiRef.current.unstable_applyPipeProcessors('processDataSourceRows', {\n      params: params.fetchParams,\n      response\n    }, false);\n    privateApiRef.current.requestPipeProcessorsApplication('hydrateRows');\n  }, [privateApiRef, updateLoadingTrigger, addSkeletonRows]);\n  const handleRowCountChange = React.useCallback(() => {\n    if (rowsStale.current || loadingTrigger.current === null) {\n      return;\n    }\n    updateLoadingTrigger(privateApiRef.current.state.pagination.rowCount);\n    addSkeletonRows();\n    privateApiRef.current.requestPipeProcessorsApplication('hydrateRows');\n  }, [privateApiRef, updateLoadingTrigger, addSkeletonRows]);\n  const handleIntersection = useEventCallback(() => {\n    if (rowsStale.current || loadingTrigger.current !== LoadingTrigger.SCROLL_END) {\n      return;\n    }\n    const renderContext = gridRenderContextSelector(privateApiRef);\n    if (previousLastRowIndex.current >= renderContext.lastRowIndex) {\n      return;\n    }\n    previousLastRowIndex.current = renderContext.lastRowIndex;\n    const paginationModel = gridPaginationModelSelector(privateApiRef);\n    const sortModel = gridSortModelSelector(privateApiRef);\n    const filterModel = gridFilterModelSelector(privateApiRef);\n    const getRowsParams = {\n      start: renderContext.lastRowIndex,\n      end: renderContext.lastRowIndex + paginationModel.pageSize - 1,\n      sortModel,\n      filterModel\n    };\n    privateApiRef.current.setLoading(true);\n    fetchRows(adjustRowParams(getRowsParams));\n  });\n  const handleRenderedRowsIntervalChange = React.useCallback(params => {\n    if (rowsStale.current) {\n      return;\n    }\n    const sortModel = gridSortModelSelector(privateApiRef);\n    const filterModel = gridFilterModelSelector(privateApiRef);\n    const getRowsParams = {\n      start: params.firstRowIndex,\n      end: params.lastRowIndex - 1,\n      sortModel,\n      filterModel\n    };\n    if (renderedRowsIntervalCache.current.firstRowToRender === params.firstRowIndex && renderedRowsIntervalCache.current.lastRowToRender === params.lastRowIndex) {\n      return;\n    }\n    renderedRowsIntervalCache.current = {\n      firstRowToRender: params.firstRowIndex,\n      lastRowToRender: params.lastRowIndex\n    };\n    const currentVisibleRows = getVisibleRows(privateApiRef);\n    const skeletonRowsSection = findSkeletonRowsSection({\n      apiRef: privateApiRef,\n      visibleRows: currentVisibleRows.rows,\n      range: {\n        firstRowIndex: params.firstRowIndex,\n        lastRowIndex: params.lastRowIndex - 1\n      }\n    });\n    if (!skeletonRowsSection) {\n      return;\n    }\n    getRowsParams.start = skeletonRowsSection.firstRowIndex;\n    getRowsParams.end = skeletonRowsSection.lastRowIndex;\n    fetchRows(adjustRowParams(getRowsParams));\n  }, [privateApiRef, adjustRowParams, fetchRows]);\n  const throttledHandleRenderedRowsIntervalChange = React.useMemo(() => throttle(handleRenderedRowsIntervalChange, props.lazyLoadingRequestThrottleMs), [props.lazyLoadingRequestThrottleMs, handleRenderedRowsIntervalChange]);\n  React.useEffect(() => {\n    return () => {\n      throttledHandleRenderedRowsIntervalChange.clear();\n    };\n  }, [throttledHandleRenderedRowsIntervalChange]);\n  const handleGridSortModelChange = React.useCallback(newSortModel => {\n    rowsStale.current = true;\n    throttledHandleRenderedRowsIntervalChange.clear();\n    previousLastRowIndex.current = 0;\n    const paginationModel = gridPaginationModelSelector(privateApiRef);\n    const filterModel = gridFilterModelSelector(privateApiRef);\n    const getRowsParams = {\n      start: 0,\n      end: paginationModel.pageSize - 1,\n      sortModel: newSortModel,\n      filterModel\n    };\n    privateApiRef.current.setLoading(true);\n    debouncedFetchRows(getRowsParams);\n  }, [privateApiRef, debouncedFetchRows, throttledHandleRenderedRowsIntervalChange]);\n  const handleGridFilterModelChange = React.useCallback(newFilterModel => {\n    rowsStale.current = true;\n    throttledHandleRenderedRowsIntervalChange.clear();\n    previousLastRowIndex.current = 0;\n    const paginationModel = gridPaginationModelSelector(privateApiRef);\n    const sortModel = gridSortModelSelector(privateApiRef);\n    const getRowsParams = {\n      start: 0,\n      end: paginationModel.pageSize - 1,\n      sortModel,\n      filterModel: newFilterModel\n    };\n    privateApiRef.current.setLoading(true);\n    debouncedFetchRows(getRowsParams);\n  }, [privateApiRef, debouncedFetchRows, throttledHandleRenderedRowsIntervalChange]);\n  const handleDragStart = React.useCallback(row => {\n    draggedRowId.current = row.id;\n  }, []);\n  const handleDragEnd = React.useCallback(() => {\n    draggedRowId.current = null;\n  }, []);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    setLazyLoadingRowsUpdateStrategyActive(privateApiRef.current.getActiveStrategy(GridStrategyGroup.DataSource) === DataSourceRowsUpdateStrategy.LazyLoading);\n  }, [privateApiRef]);\n  useGridRegisterStrategyProcessor(privateApiRef, DataSourceRowsUpdateStrategy.LazyLoading, 'dataSourceRowsUpdate', handleDataUpdate);\n  useGridEvent(privateApiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n  useGridEvent(privateApiRef, 'rowCountChange', runIf(lazyLoadingRowsUpdateStrategyActive, handleRowCountChange));\n  useGridEvent(privateApiRef, 'rowsScrollEndIntersection', runIf(lazyLoadingRowsUpdateStrategyActive, handleIntersection));\n  useGridEvent(privateApiRef, 'renderedRowsIntervalChange', runIf(lazyLoadingRowsUpdateStrategyActive, throttledHandleRenderedRowsIntervalChange));\n  useGridEvent(privateApiRef, 'sortModelChange', runIf(lazyLoadingRowsUpdateStrategyActive, handleGridSortModelChange));\n  useGridEvent(privateApiRef, 'filterModelChange', runIf(lazyLoadingRowsUpdateStrategyActive, handleGridFilterModelChange));\n  useGridEvent(privateApiRef, 'rowDragStart', runIf(lazyLoadingRowsUpdateStrategyActive, handleDragStart));\n  useGridEvent(privateApiRef, 'rowDragEnd', runIf(lazyLoadingRowsUpdateStrategyActive, handleDragEnd));\n  React.useEffect(() => {\n    setStrategyAvailability();\n  }, [setStrategyAvailability]);\n};", "// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func, wait = 166) {\n  let timeout;\n  function debounced(...args) {\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}", "'use client';\n\nimport * as React from 'react';\nimport { useGridSelector, useGridApiMethod, gridDimensionsSelector } from '@mui/x-data-grid';\nimport { useTimeout, gridHorizontalScrollbarHeightSelector } from '@mui/x-data-grid/internals';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { styled } from '@mui/system';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InfiniteLoadingTriggerElement = styled('div')({\n  position: 'sticky',\n  left: 0,\n  width: 0,\n  height: 0\n});\n\n/**\n * @requires useGridDimensions (method) - can be after\n */\nexport const useGridInfiniteLoadingIntersection = (apiRef, props) => {\n  const isReady = useGridSelector(apiRef, gridDimensionsSelector).isReady;\n  const observer = React.useRef(null);\n  const updateTargetTimeout = useTimeout();\n  const triggerElement = React.useRef(null);\n  const isEnabledClientSide = props.rowsLoadingMode === 'client' && !!props.onRowsScrollEnd;\n  const isEnabledServerSide = props.dataSource && props.lazyLoading;\n  const isEnabled = isEnabledClientSide || isEnabledServerSide;\n  const handleIntersectionChange = useEventCallback(([entry]) => {\n    const currentRatio = entry.intersectionRatio;\n    const isIntersecting = entry.isIntersecting;\n    if (isIntersecting && currentRatio === 1) {\n      observer.current?.disconnect();\n      // do not observe this node anymore\n      triggerElement.current = null;\n      apiRef.current.publishEvent('rowsScrollEndIntersection');\n    }\n  });\n  React.useEffect(() => {\n    const virtualScroller = apiRef.current.virtualScrollerRef.current;\n    if (!isEnabled || !isReady || !virtualScroller) {\n      return;\n    }\n    observer.current?.disconnect();\n    const horizontalScrollbarHeight = gridHorizontalScrollbarHeightSelector(apiRef);\n    const marginBottom = props.scrollEndThreshold - horizontalScrollbarHeight;\n    observer.current = new IntersectionObserver(handleIntersectionChange, {\n      threshold: 1,\n      root: virtualScroller,\n      rootMargin: `0px 0px ${marginBottom}px 0px`\n    });\n    if (triggerElement.current) {\n      observer.current.observe(triggerElement.current);\n    }\n  }, [apiRef, isReady, handleIntersectionChange, isEnabled, props.scrollEndThreshold]);\n  const updateTarget = node => {\n    if (triggerElement.current !== node) {\n      observer.current?.disconnect();\n      triggerElement.current = node;\n      if (triggerElement.current) {\n        observer.current?.observe(triggerElement.current);\n      }\n    }\n  };\n  const triggerRef = React.useCallback(node => {\n    // Prevent the infite loading working in combination with lazy loading\n    if (!isEnabled) {\n      return;\n    }\n\n    // If the user scrolls through the grid too fast it might happen that the observer is connected to the trigger element\n    // that will be intersecting the root inside the same render cycle (but not intersecting at the time of the connection).\n    // This will cause the observer to not call the callback with `isIntersecting` set to `true`.\n    // https://www.w3.org/TR/intersection-observer/#event-loop\n    // Delaying the connection to the next cycle helps since the observer will always call the callback the first time it is connected.\n    // https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserver/observe\n    // Related to\n    // https://github.com/mui/mui-x/issues/14116\n    updateTargetTimeout.start(0, () => updateTarget(node));\n  }, [isEnabled, updateTargetTimeout]);\n  const getInfiniteLoadingTriggerElement = React.useCallback(({\n    lastRowId\n  }) => {\n    if (!isEnabled) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(InfiniteLoadingTriggerElement, {\n      ref: triggerRef\n      // Force rerender on last row change to start observing the new trigger\n      ,\n\n      role: \"presentation\"\n    }, `trigger-${lastRowId}`);\n  }, [isEnabled, triggerRef]);\n  const infiniteLoaderPrivateApi = {\n    getInfiniteLoadingTriggerElement\n  };\n  useGridApiMethod(apiRef, infiniteLoaderPrivateApi, 'private');\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridEvent as addEventHandler, useGridApiMethod } from '@mui/x-data-grid';\nimport { useGridRegisterStrategyProcessor } from '@mui/x-data-grid/internals';\nimport { INITIAL_STATE, useGridDataSourceBasePro } from \"./useGridDataSourceBasePro.js\";\nfunction getKeyPro(params) {\n  return JSON.stringify([params.filterModel, params.sortModel, params.groupKeys, params.start, params.end]);\n}\nexport const dataSourceStateInitializer = state => {\n  return _extends({}, state, {\n    dataSource: INITIAL_STATE\n  });\n};\nconst options = {\n  cacheOptions: {\n    getKey: getKeyPro\n  }\n};\nexport const useGridDataSourcePro = (apiRef, props) => {\n  const {\n    api,\n    strategyProcessor,\n    events,\n    setStrategyAvailability\n  } = useGridDataSourceBasePro(apiRef, props, options);\n  useGridApiMethod(apiRef, api.public, 'public');\n  useGridApiMethod(apiRef, api.private, 'private');\n  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);\n  Object.entries(events).forEach(([event, handler]) => {\n    addEventHandler(apiRef, event, handler);\n  });\n  React.useEffect(() => {\n    setStrategyAvailability();\n  }, [setStrategyAvailability]);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { useGridSelector, GridGetRowsError, gridRowIdSelector, gridRowNodeSelector, gridRowTreeSelector } from '@mui/x-data-grid';\nimport { gridRowGroupsToFetchSelector, useGridDataSourceBase, CacheChunkManager, gridGetRowsParamsSelector, DataSourceRowsUpdateStrategy, GridStrategyGroup } from '@mui/x-data-grid/internals';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { NestedDataManager, RequestStatus, getGroupKeys } from \"./utils.js\";\nimport { gridDataSourceErrorsSelector } from \"./gridDataSourceSelector.js\";\nexport const INITIAL_STATE = {\n  loading: {},\n  errors: {}\n};\nexport const useGridDataSourceBasePro = (apiRef, props, options = {}) => {\n  const groupsToAutoFetch = useGridSelector(apiRef, gridRowGroupsToFetchSelector);\n  const nestedDataManager = useLazyRef(() => new NestedDataManager(apiRef)).current;\n  const scheduledGroups = React.useRef(0);\n  const clearDataSourceState = React.useCallback(() => {\n    nestedDataManager.clear();\n    scheduledGroups.current = 0;\n    const dataSourceState = apiRef.current.state.dataSource;\n    if (dataSourceState !== INITIAL_STATE) {\n      apiRef.current.resetDataSourceState();\n    }\n    return null;\n  }, [apiRef, nestedDataManager]);\n  const handleEditRow = React.useCallback((params, updatedRow) => {\n    const groupKeys = getGroupKeys(gridRowTreeSelector(apiRef), params.rowId);\n    apiRef.current.updateNestedRows([updatedRow], groupKeys);\n    if (updatedRow && !isDeepEqual(updatedRow, params.previousRow)) {\n      // Reset the outdated cache, only if the row is _actually_ updated\n      apiRef.current.dataSource.cache.clear();\n    }\n  }, [apiRef]);\n  const {\n    api,\n    debouncedFetchRows,\n    strategyProcessor,\n    events,\n    cacheChunkManager,\n    cache\n  } = useGridDataSourceBase(apiRef, props, _extends({\n    fetchRowChildren: nestedDataManager.queue,\n    clearDataSourceState,\n    handleEditRow\n  }, options));\n  const setStrategyAvailability = React.useCallback(() => {\n    apiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.Default, props.dataSource && !props.lazyLoading ? () => true : () => false);\n  }, [apiRef, props.dataSource, props.lazyLoading]);\n  const onDataSourceErrorProp = props.onDataSourceError;\n  const fetchRowChildren = React.useCallback(async id => {\n    const pipedParams = apiRef.current.unstable_applyPipeProcessors('getRowsParams', {});\n    if (!props.treeData && (pipedParams.groupFields?.length ?? 0) === 0) {\n      nestedDataManager.clearPendingRequest(id);\n      return;\n    }\n    const getRows = props.dataSource?.getRows;\n    if (!getRows) {\n      nestedDataManager.clearPendingRequest(id);\n      return;\n    }\n    const rowNode = apiRef.current.getRowNode(id);\n    if (!rowNode) {\n      nestedDataManager.clearPendingRequest(id);\n      return;\n    }\n    const fetchParams = _extends({}, gridGetRowsParamsSelector(apiRef), pipedParams, {\n      groupKeys: rowNode.path\n    });\n    const cacheKeys = cacheChunkManager.getCacheKeys(fetchParams);\n    const responses = cacheKeys.map(cacheKey => cache.get(cacheKey));\n    const cachedData = responses.some(response => response === undefined) ? undefined : CacheChunkManager.mergeResponses(responses);\n    if (cachedData !== undefined) {\n      const rows = cachedData.rows;\n      nestedDataManager.setRequestSettled(id);\n      apiRef.current.updateNestedRows(rows, rowNode.path);\n      if (cachedData.rowCount !== undefined) {\n        apiRef.current.setRowCount(cachedData.rowCount);\n      }\n      apiRef.current.setRowChildrenExpansion(id, true);\n      apiRef.current.dataSource.setChildrenLoading(id, false);\n      return;\n    }\n    const existingError = gridDataSourceErrorsSelector(apiRef)[id] ?? null;\n    if (existingError) {\n      apiRef.current.dataSource.setChildrenFetchError(id, null);\n    }\n    try {\n      const getRowsResponse = await getRows(fetchParams);\n      if (!apiRef.current.getRowNode(id)) {\n        // The row has been removed from the grid\n        nestedDataManager.clearPendingRequest(id);\n        return;\n      }\n      if (nestedDataManager.getRequestStatus(id) === RequestStatus.UNKNOWN) {\n        apiRef.current.dataSource.setChildrenLoading(id, false);\n        return;\n      }\n      nestedDataManager.setRequestSettled(id);\n      const cacheResponses = cacheChunkManager.splitResponse(fetchParams, getRowsResponse);\n      cacheResponses.forEach((response, key) => {\n        cache.set(key, response);\n      });\n      if (getRowsResponse.rowCount !== undefined) {\n        apiRef.current.setRowCount(getRowsResponse.rowCount);\n      }\n      // Remove existing outdated rows before setting the new ones\n      const rowsToDelete = [];\n      getRowsResponse.rows.forEach(row => {\n        const rowId = gridRowIdSelector(apiRef, row);\n        const treeNode = gridRowNodeSelector(apiRef, rowId);\n        if (treeNode) {\n          rowsToDelete.push({\n            id: rowId,\n            _action: 'delete'\n          });\n        }\n      });\n      if (rowsToDelete.length > 0) {\n        // TODO: Make this happen in a single pass by modifying the pre-processing of the rows\n        apiRef.current.updateNestedRows(rowsToDelete, rowNode.path);\n      }\n      apiRef.current.updateNestedRows(getRowsResponse.rows, rowNode.path);\n      apiRef.current.setRowChildrenExpansion(id, true);\n    } catch (error) {\n      const childrenFetchError = error;\n      apiRef.current.dataSource.setChildrenFetchError(id, childrenFetchError);\n      if (typeof onDataSourceErrorProp === 'function') {\n        onDataSourceErrorProp(new GridGetRowsError({\n          message: childrenFetchError.message,\n          params: fetchParams,\n          cause: childrenFetchError\n        }));\n      } else if (process.env.NODE_ENV !== 'production') {\n        warnOnce(['MUI X: A call to `dataSource.getRows()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n      }\n    } finally {\n      apiRef.current.dataSource.setChildrenLoading(id, false);\n      nestedDataManager.setRequestSettled(id);\n    }\n  }, [nestedDataManager, cacheChunkManager, cache, onDataSourceErrorProp, apiRef, props.treeData, props.dataSource?.getRows]);\n  const setChildrenLoading = React.useCallback((parentId, isLoading) => {\n    apiRef.current.setState(state => {\n      if (!state.dataSource.loading[parentId] && isLoading === false) {\n        return state;\n      }\n      const newLoadingState = _extends({}, state.dataSource.loading);\n      if (isLoading === false) {\n        delete newLoadingState[parentId];\n      } else {\n        newLoadingState[parentId] = isLoading;\n      }\n      return _extends({}, state, {\n        dataSource: _extends({}, state.dataSource, {\n          loading: newLoadingState\n        })\n      });\n    });\n  }, [apiRef]);\n  const setChildrenFetchError = React.useCallback((parentId, error) => {\n    apiRef.current.setState(state => {\n      const newErrorsState = _extends({}, state.dataSource.errors);\n      if (error === null && newErrorsState[parentId] !== undefined) {\n        delete newErrorsState[parentId];\n      } else {\n        newErrorsState[parentId] = error;\n      }\n      return _extends({}, state, {\n        dataSource: _extends({}, state.dataSource, {\n          errors: newErrorsState\n        })\n      });\n    });\n  }, [apiRef]);\n  const resetDataSourceState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        dataSource: INITIAL_STATE\n      });\n    });\n  }, [apiRef]);\n  const removeChildrenRows = React.useCallback(parentId => {\n    const rowNode = gridRowNodeSelector(apiRef, parentId);\n    if (!rowNode || rowNode.type !== 'group' || rowNode.children.length === 0) {\n      return;\n    }\n    const removedRows = [];\n    const traverse = nodeId => {\n      const node = gridRowNodeSelector(apiRef, nodeId);\n      if (!node) {\n        return;\n      }\n      if (node.type === 'group' && node.children.length > 0) {\n        node.children.forEach(traverse);\n      }\n      removedRows.push({\n        id: nodeId,\n        _action: 'delete'\n      });\n    };\n    rowNode.children.forEach(traverse);\n    if (removedRows.length > 0) {\n      apiRef.current.updateNestedRows(removedRows, rowNode.path);\n    }\n  }, [apiRef]);\n  const dataSourceApi = {\n    dataSource: _extends({}, api.public.dataSource, {\n      setChildrenLoading,\n      setChildrenFetchError\n    })\n  };\n  const dataSourcePrivateApi = {\n    fetchRowChildren,\n    resetDataSourceState,\n    removeChildrenRows\n  };\n  React.useEffect(() => {\n    if (groupsToAutoFetch && groupsToAutoFetch.length && scheduledGroups.current < groupsToAutoFetch.length) {\n      const groupsToSchedule = groupsToAutoFetch.slice(scheduledGroups.current);\n      nestedDataManager.queue(groupsToSchedule);\n      scheduledGroups.current = groupsToAutoFetch.length;\n    }\n  }, [apiRef, nestedDataManager, groupsToAutoFetch]);\n  return {\n    api: {\n      public: dataSourceApi,\n      private: dataSourcePrivateApi\n    },\n    debouncedFetchRows,\n    strategyProcessor,\n    events,\n    setStrategyAvailability,\n    cacheChunkManager,\n    cache\n  };\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_ROOT_GROUP_ID } from '@mui/x-data-grid';\nconst MAX_CONCURRENT_REQUESTS = Infinity;\nexport let RequestStatus = /*#__PURE__*/function (RequestStatus) {\n  RequestStatus[RequestStatus[\"QUEUED\"] = 0] = \"QUEUED\";\n  RequestStatus[RequestStatus[\"PENDING\"] = 1] = \"PENDING\";\n  RequestStatus[RequestStatus[\"SETTLED\"] = 2] = \"SETTLED\";\n  RequestStatus[RequestStatus[\"UNKNOWN\"] = 3] = \"UNKNOWN\";\n  return RequestStatus;\n}({});\n\n/**\n * Fetches row children from the server with option to limit the number of concurrent requests\n * Determines the status of a request based on the enum `RequestStatus`\n * Uses `GridRowId` to uniquely identify a request\n */\nexport class NestedDataManager {\n  pendingRequests = (() => new Set())();\n  queuedRequests = (() => new Set())();\n  settledRequests = (() => new Set())();\n  constructor(privateApiRef, maxConcurrentRequests = MAX_CONCURRENT_REQUESTS) {\n    this.api = privateApiRef.current;\n    this.maxConcurrentRequests = maxConcurrentRequests;\n  }\n  processQueue = async () => {\n    if (this.queuedRequests.size === 0 || this.pendingRequests.size >= this.maxConcurrentRequests) {\n      return;\n    }\n    const loopLength = Math.min(this.maxConcurrentRequests - this.pendingRequests.size, this.queuedRequests.size);\n    if (loopLength === 0) {\n      return;\n    }\n    const fetchQueue = Array.from(this.queuedRequests);\n    for (let i = 0; i < loopLength; i += 1) {\n      const id = fetchQueue[i];\n      this.queuedRequests.delete(id);\n      this.pendingRequests.add(id);\n      this.api.fetchRowChildren(id);\n    }\n  };\n  queue = async ids => {\n    const loadingIds = {};\n    ids.forEach(id => {\n      this.queuedRequests.add(id);\n      loadingIds[id] = true;\n    });\n    this.api.setState(state => _extends({}, state, {\n      dataSource: _extends({}, state.dataSource, {\n        loading: _extends({}, state.dataSource.loading, loadingIds)\n      })\n    }));\n    this.processQueue();\n  };\n  setRequestSettled = id => {\n    this.pendingRequests.delete(id);\n    this.settledRequests.add(id);\n    this.processQueue();\n  };\n  clear = () => {\n    this.queuedRequests.clear();\n    Array.from(this.pendingRequests).forEach(id => this.clearPendingRequest(id));\n  };\n  clearPendingRequest = id => {\n    this.api.dataSource.setChildrenLoading(id, false);\n    this.pendingRequests.delete(id);\n    this.processQueue();\n  };\n  getRequestStatus = id => {\n    if (this.pendingRequests.has(id)) {\n      return RequestStatus.PENDING;\n    }\n    if (this.queuedRequests.has(id)) {\n      return RequestStatus.QUEUED;\n    }\n    if (this.settledRequests.has(id)) {\n      return RequestStatus.SETTLED;\n    }\n    return RequestStatus.UNKNOWN;\n  };\n  getActiveRequestsCount = () => this.pendingRequests.size + this.queuedRequests.size;\n}\nexport const getGroupKeys = (tree, rowId) => {\n  const rowNode = tree[rowId];\n  let currentNodeId = rowNode.parent;\n  const groupKeys = [];\n  while (currentNodeId && currentNodeId !== GRID_ROOT_GROUP_ID) {\n    const currentNode = tree[currentNodeId];\n    groupKeys.push(currentNode.groupingKey ?? '');\n    currentNodeId = currentNode.parent;\n  }\n  return groupKeys.reverse();\n};", "import { GridSignature, isNumber, propValidatorsDataGrid } from '@mui/x-data-grid/internals';\nexport const propValidatorsDataGridPro = [...propValidatorsDataGrid, props => props.pagination && props.hideFooterRowCount && 'MUI X: The `hideFooterRowCount` prop has no effect when the pagination is enabled.' || undefined, props => props.treeData && props.filterMode === 'server' && !props.dataSource && 'MUI X: The `filterMode=\"server\"` prop is not available when the `treeData` is enabled.' || undefined, props => !props.pagination && props.checkboxSelectionVisibleOnly && 'MUI X: The `checkboxSelectionVisibleOnly` prop has no effect when the pagination is not enabled.' || undefined, props => props.signature !== GridSignature.DataGrid && props.paginationMode === 'client' && props.rowsLoadingMode !== 'server' && isNumber(props.rowCount) && 'MUI X: Usage of the `rowCount` prop with client side pagination (`paginationMode=\"client\"`) has no effect. `rowCount` is only meant to be used with `paginationMode=\"server\"`.' || undefined, props => props.signature !== GridSignature.DataGrid && (props.rowsLoadingMode === 'server' || props.onRowsScrollEnd) && props.lazyLoading && 'MUI X: Usage of the client side lazy loading (`rowsLoadingMode=\"server\"` or `onRowsScrollEnd=...`) cannot be used together with server side lazy loading `lazyLoading=\"true\"`.' || undefined];", "export * from '@mui/x-data-grid/internals';\nexport { GridColumnHeaders } from \"../components/GridColumnHeaders.js\";\nexport { DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS } from \"../constants/dataGridProDefaultSlotsComponents.js\";\n\n/*\n * x-data-grid-pro internals that are overriding the x-data-grid internals\n */\nexport { useGridColumnHeadersPro } from \"../hooks/features/columnHeaders/useGridColumnHeaders.js\";\nexport { useGridAriaAttributesPro } from \"../hooks/utils/useGridAriaAttributes.js\";\nexport { useGridRowAriaAttributesPro } from \"../hooks/features/rows/useGridRowAriaAttributes.js\";\nexport { useGridColumnPinning, columnPinningStateInitializer } from \"../hooks/features/columnPinning/useGridColumnPinning.js\";\nexport { useGridColumnPinningPreProcessors } from \"../hooks/features/columnPinning/useGridColumnPinningPreProcessors.js\";\nexport { useGridColumnReorder, columnReorderStateInitializer } from \"../hooks/features/columnReorder/useGridColumnReorder.js\";\nexport { useGridDataSourceTreeDataPreProcessors } from \"../hooks/features/serverSideTreeData/useGridDataSourceTreeDataPreProcessors.js\";\nexport { useGridDetailPanel, detailPanelStateInitializer } from \"../hooks/features/detailPanel/useGridDetailPanel.js\";\nexport { useGridDetailPanelPreProcessors } from \"../hooks/features/detailPanel/useGridDetailPanelPreProcessors.js\";\nexport { useGridInfiniteLoader } from \"../hooks/features/infiniteLoader/useGridInfiniteLoader.js\";\nexport { useGridRowReorder, rowReorderStateInitializer } from \"../hooks/features/rowReorder/useGridRowReorder.js\";\nexport { useGridRowReorderPreProcessors } from \"../hooks/features/rowReorder/useGridRowReorderPreProcessors.js\";\nexport { useGridTreeData } from \"../hooks/features/treeData/useGridTreeData.js\";\nexport { useGridTreeDataPreProcessors } from \"../hooks/features/treeData/useGridTreeDataPreProcessors.js\";\nexport { useGridRowPinning, rowPinningStateInitializer } from \"../hooks/features/rowPinning/useGridRowPinning.js\";\nexport { useGridRowPinningPreProcessors, addPinnedRow } from \"../hooks/features/rowPinning/useGridRowPinningPreProcessors.js\";\nexport { useGridLazyLoader } from \"../hooks/features/lazyLoader/useGridLazyLoader.js\";\nexport { useGridLazyLoaderPreProcessors } from \"../hooks/features/lazyLoader/useGridLazyLoaderPreProcessors.js\";\nexport { useGridDataSourceLazyLoader } from \"../hooks/features/serverSideLazyLoader/useGridDataSourceLazyLoader.js\";\nexport { useGridInfiniteLoadingIntersection } from \"../hooks/features/serverSideLazyLoader/useGridInfiniteLoadingIntersection.js\";\nexport { dataSourceStateInitializer } from \"../hooks/features/dataSource/useGridDataSourcePro.js\";\nexport { useGridDataSourceBasePro } from \"../hooks/features/dataSource/useGridDataSourceBasePro.js\";\nexport { gridDataSourceErrorSelector, gridDataSourceLoadingIdSelector } from \"../hooks/features/dataSource/gridDataSourceSelector.js\";\nexport { getGroupKeys } from \"../hooks/features/dataSource/utils.js\";\nexport { createRowTree } from \"../utils/tree/createRowTree.js\";\nexport { updateRowTree } from \"../utils/tree/updateRowTree.js\";\nexport { sortRowTree } from \"../utils/tree/sortRowTree.js\";\nexport { insertNodeInTree, removeNodeFromTree, getVisibleRowsLookup } from \"../utils/tree/utils.js\";\nexport { skipSorting, skipFiltering, getParentPath } from \"../hooks/features/serverSideTreeData/utils.js\";\nexport let RowGroupingStrategy = /*#__PURE__*/function (RowGroupingStrategy) {\n  RowGroupingStrategy[\"Default\"] = \"grouping-columns\";\n  RowGroupingStrategy[\"DataSource\"] = \"grouping-columns-data-source\";\n  return RowGroupingStrategy;\n}({});\nexport * from \"./propValidation.js\";"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,YAAuB;AAIhB,IAAM,kCAAkC,CAAC,OAAO,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,EACnF,iBAAiB;AAAA,IACf,SAAS,MAAM,iBAAiB;AAAA,IAChC,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF,CAAC;AACM,IAAM,yBAAyB,CAAC,QAAQ,UAAU;AACvD,QAAM,SAAS,cAAc,QAAQ,wBAAwB;AAC7D,QAAM,uBAA6B,kBAAY,uBAAqB;AAClE,WAAO,QAAQ,SAAS,WAAS;AAG/B,UAAI,MAAM,cAAc,YAAY;AAClC,eAAO;AAAA,MACT;AACA,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,iBAAiB;AAAA,UACf,SAAS,MAAM,iBAAiB;AAAA,UAChC,SAAS,kBAAkB,WAAW;AAAA,UACtC,UAAU,kBAAkB,YAAY;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,WAAW,MAAM,aAAa,CAAC;AACjD,QAAM,4BAAkC,kBAAY,WAAS;AAC3D,WAAO,MAAM,kDAAkD,KAAK,EAAE;AACtE,WAAO,QAAQ,qBAAqB;AAAA,MAClC,SAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,CAAC;AACnB,QAAM,2BAAiC,kBAAY,MAAM;AACvD,WAAO,MAAM,qCAAqC;AAClD,WAAO,QAAQ,qBAAqB;AAAA,MAClC,SAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,CAAC;AACnB,QAAM,uBAA6B,kBAAY,WAAS;AACtD,WAAO,MAAM,yCAAyC,KAAK,EAAE;AAC7D,WAAO,QAAQ,qBAAqB;AAAA,MAClC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,CAAC;AACnB,QAAM,uBAA6B,kBAAY,MAAM;AACnD,WAAO,MAAM,4CAA4C;AACzD,QAAI,eAAe,OAAO,QAAQ,MAAM,gBAAgB;AACxD,QAAI,cAAc;AAChB,YAAM,eAAe,yBAAyB,MAAM;AACpD,YAAM,wBAAwB,kCAAkC,MAAM;AACtE,YAAM,gBAAgB,yBAAyB,MAAM;AAGrD,UAAI,CAAC,aAAa,YAAY,GAAG;AAC/B,uBAAe,cAAc,CAAC;AAAA,MAChC;AAGA,UAAI,sBAAsB,YAAY,MAAM,OAAO;AAEjD,cAAM,uBAAuB,cAAc,OAAO,WAAS;AACzD,cAAI,UAAU,cAAc;AAC1B,mBAAO;AAAA,UACT;AACA,iBAAO,sBAAsB,KAAK,MAAM;AAAA,QAC1C,CAAC;AACD,cAAM,aAAa,qBAAqB,QAAQ,YAAY;AAC5D,uBAAe,qBAAqB,aAAa,CAAC,KAAK,qBAAqB,aAAa,CAAC;AAAA,MAC5F;AACA,aAAO,QAAQ,qBAAqB;AAAA,QAClC,UAAU;AAAA,MACZ,CAAC;AACD,aAAO,QAAQ,2BAA2B,YAAY;AAAA,IACxD;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,CAAC;AACnB,QAAM,yBAAyB;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,mBAAiB,QAAQ,iBAAiB,QAAQ;AAClD,mBAAiB,QAAQ,wBAAwB,SAAS;AAK1D,QAAM,gBAAsB,aAAO,IAAI;AACvC,EAAM,gBAAU,MAAM;AACpB,QAAI,cAAc,SAAS;AACzB,oBAAc,UAAU;AAAA,IAC1B,OAAO;AACL,aAAO,QAAQ,qBAAqB;AAAA,QAClC,SAAS,MAAM,iBAAiB;AAAA,MAClC,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,aAAa,CAAC;AAClC;;;ACxGA,IAAAA,SAAuB;AAGvB,yBAA4B;AAKrB,SAAS,sBAAsB,OAAO;AAC3C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAS,KAAK;AACpD,EAAM,iBAAU,MAAM;AACpB,UAAM,OAAO,gBAAgB,SAAS;AACtC,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,QAAI,UAAU;AACd,UAAM,UAAU,MAAM;AArB1B;AAsBM,gBAAU;AACV,aAAO,OAAO;AACd,YAAM,QAAO,kCAAM,kBAAN,mBAAqB;AAClC,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,YAAM,SAAS,KAAK,iBAAiB,2BAA2B;AAChE,aAAO,QAAQ,WAAS;AA7B9B,YAAAC;AA8BQ,cAAM,aAAYA,MAAA,MAAM,gBAAN,gBAAAA,IAAmB,UAAU,GAAG;AAClD,cAAM,uBAAuB,uCAAW,SAAS;AACjD,YAAI,sBAAsB;AACxB,gBAAM,OAAO;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,SAAS,MAAM;AACpB,UAAI,CAAC,SAAS;AACZ,gBAAQ;AACR;AAAA,MACF;AACA,kBAAY,IAAI;AAAA,IAClB;AACA,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,CAAC;AACjB,SAAO,WAAW,eAAwB,mBAAAC,KAAK,aAAK;AAAA,IAClD,IAAI;AAAA,MACF,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,IACA,cAAuB,mBAAAA,KAAK,0BAAkB,CAAC,CAAC;AAAA,EAClD,CAAC;AACH;;;ACrDA,IAAAC,SAAuB;AACvB,wBAAsB;;;ACDtB,IAAAC,SAAuB;;;AC8BR,SAAR,eAAgC,OAAO,iBAAiB,UAAU,QAAW;AAClF,QAAM,SAAS,CAAC;AAChB,aAAW,YAAY,OAAO;AAC5B,UAAM,OAAO,MAAM,QAAQ;AAC3B,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,OAAO;AACT,mBAAW,UAAU,OAAO,KAAK,OAAO,gBAAgB,KAAK;AAC7D,gBAAQ;AACR,YAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,oBAAU,MAAM,QAAQ,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,SAAO;AACT;;;ACnDO,IAAMC,oBAAmB;;;AFOhC,IAAAC,sBAA2C;AAN3C,IAAM,YAAY,CAAC,sBAAsB,uBAAuB,iBAAiB,qBAAqB,sBAAsB,iBAAiB,kBAAkB,iBAAiB;AAOhL,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAa,eAAQ,MAAM;AACzB,UAAM,QAAQ;AAAA,MACZ,iBAAiB,CAAC,iBAAiB;AAAA,IACrC;AACA,WAAO,eAAe,OAAO,yBAAyB,OAAO;AAAA,EAC/D,GAAG,CAAC,OAAO,CAAC;AACd;AACO,IAAM,0BAA0B,WAAS;AAC9C,QAAM,SAAS,yBAAyB;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kCAAkC,gBAAgB,QAAQ,sCAAsC;AACtG,QAAM,wBAAwB,qBAA8B,SAAS,CAAC,GAAG,OAAO;AAAA,IAC5E,8BAA8B,gCAAgC,oCAAoC;AAAA,EACpG,CAAC,CAAC,GACF;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,uBACJ,aAAa,8BAA8B,uBAAuB,SAAS;AAC7E,QAAM,mBAAyB,cAAO,IAAI;AAC1C,SAAO,QAAQ,SAAS,WAAW;AAAA,IACjC,yBAAyB;AAAA,EAC3B,CAAC;AACD,QAAM,sBAA4B,cAAO,IAAI;AAC7C,QAAM,YAAYC,kBAAiB;AACnC,QAAM,UAAU,kBAAkB,SAAS;AAC3C,QAAM,yBAAyB,CAAC,UAAU;AAC1C,QAAM,cAAc,gBAAgB,QAAQ,uBAAuB;AACnE,QAAM,oBAAoB,gBAAgB,QAAQ,6BAA6B;AAC/E,QAAM,gBAAgB,gBAAgB,QAAQ,qBAAqB;AACnE,QAAM,qBAAqB,gBAAgB,QAAQ,8BAA8B;AACjF,QAAM,iBAAiB,gBAAgB,QAAQ,kCAAkC;AACjF,QAAM,0BAA0B,gBAAgB,QAAQ,mCAAmC;AAC3F,QAAM,mBAAyB,cAAO,uBAAO,OAAO,IAAI,CAAC,EAAE;AAC3D,QAAM,gBAAsB;AAAA,IAAY,YAAU;AAChD,YAAM,kBAAkB,2CAAa,MAAM,KAAK,QAAM,GAAG,UAAU,OAAO,SAAS,GAAG,aAAa;AACnG,UAAI,mBAAmB,MAAM;AAE3B,eAAO;AAAA,MACT;AACA,YAAM,oBAAoB,iBAAiB,OAAO,KAAK;AACvD,UAAI,qBAAqB,MAAM;AAE7B,eAAO;AAAA,MACT;AAEA,YAAM,cAAc,cAAc,MAAM;AACxC,uBAAiB,OAAO,KAAK,IAAI;AACjC,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,CAAC,WAAW;AAAA,EAAC;AACb,QAAM,mBAAmB,YAAU;AA1ErC;AA2EI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,mBAAmB,MAAM;AAC7B,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK,GAAG;AAClD,YAAM,SAAS,gBAAgB,CAAC;AAChC,YAAM,cAAc,sBAAsB;AAC1C,YAAM,YAAW,mEAAyB,WAAU,OAAO;AAC3D,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,gBAAgB,mFAAiC;AACvD,YAAM,WAAW,kBAAkB,OAAO,SAAS,iBAAiB,CAAC,MAAM,+BAA+B,IAAI;AAC9G,YAAM,kBAAkB,OAAO,OAAO,oBAAoB,aAAa,OAAO,gBAAgB;AAAA,QAC5F,OAAO,OAAO;AAAA,QACd;AAAA,MACF,CAAC,IAAI,OAAO;AACZ,YAAM,OAAO,cAAc,MAAM;AACjC,YAAM,iBAAiB,iCAAQ;AAC/B,YAAM,eAAe,oBAAoB,gBAAgB,OAAO,eAAe,aAAa,iBAAiB,mBAAmB,cAAc;AAC9I,YAAM,iBAAiB;AACvB,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,iBAAiB,yBAAyB,gBAAgB,cAAc;AAC9E,YAAM,kBAAkB,0BAA0B,gBAAgB,gBAAgB,eAAe,UAAU,0BAA0B,aAAa;AAClJ,cAAQ,SAAkB,oBAAAC,KAAK,UAAU,MAAM,kBAAkB,SAAS;AAAA,QACxE,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO,OAAO;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,OAAO;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAG,eAAU,cAAV,mBAAqB,gBAAgB,GAAG,GAAG,OAAO,KAAK,SAAS,CAAC;AAAA,IACtE;AACA,WAAO,WAAW,WAAW,QAAQ,SAAS,GAAG,IAAI;AAAA,EACvD;AACA,QAAM,sBAAsB,MAAM;AAChC,QAAI,wBAAwB;AAC1B,aAAO;AAAA,IACT;AACA,eAAoB,oBAAAC,MAAM,qBAAqB;AAAA,MAC7C,KAAK;AAAA,MACL,WAAW,QAAQ;AAAA,MACnB,MAAM;AAAA,MACN,iBAAiB,yBAAyB;AAAA,MAC1C,YAAY;AAAA,MACZ,UAAU,CAAC,qBAAqB,iBAAiB;AAAA,QAC/C,UAAU,qBAAqB;AAAA,QAC/B,eAAe;AAAA,QACf,eAAe,kBAAkB;AAAA,MACnC,CAAC,GAAG,iBAAiB;AAAA,QACnB;AAAA,QACA,eAAe,eAAe,SAAS,cAAc,MAAM;AAAA,MAC7D,CAAC,GAAG,sBAAsB,iBAAiB;AAAA,QACzC,UAAU,qBAAqB;AAAA,QAC/B,eAAe;AAAA,QACf,eAAe,mBAAmB;AAAA,MACpC,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AACA,MAAI,KAAuC,qBAAoB,cAAc;AAC7E,SAAO,SAAS,CAAC,GAAG,YAAY;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;;;ADxIA,IAAAC,sBAA2C;AAP3C,IAAMC,aAAY,CAAC,SAAS,aAAa,kBAAkB,oBAAoB,sBAAsB,6BAA6B,kCAAkC,qBAAqB,0BAA0B,0BAA0B,mBAAmB,oBAAoB,+BAA+B,8BAA8B;AAQjV,IAAM,SAAS,eAAO,KAAK,EAAE;AAAA,EAC3B,MAAM;AAAA,EACN,iBAAiB,KAAK,OAAO,WAAW;AAC1C,CAAC;AACD,IAAM,oBAAoB,WAAW,SAASC,mBAAkB,OAAO,KAAK;AAC1E,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,wBAAwB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAE,MAAM,uBAAuB,SAAS;AAAA,IACxD;AAAA,EACF,GAAG,OAAO,cAAc,GAAG;AAAA,IACzB;AAAA,IACA,UAAU,CAAC,0BAA0B,GAAG,oBAAoB,GAAG,oBAAoB,OAAgB,oBAAAC,KAAK,QAAQ,CAAC,CAAC,CAAC;AAAA,EACrH,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,mBAAkB,cAAc;AAC3E,OAAwC,kBAAkB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpE,wBAAwB,kBAAAC,QAAU,MAAM;AAAA,IACtC,OAAO,kBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,kBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC;AAAA,EACD,gCAAgC,kBAAAA,QAAU,MAAM;AAAA,IAC9C,OAAO,kBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,kBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC;AAAA,EACD,6BAA6B,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IAC/E,cAAc,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,EAAE;AAAA,IAClD,SAAS,kBAAAA,QAAU;AAAA,EACrB,CAAC,CAAC,CAAC,EAAE;AAAA,EACL,mBAAmB,kBAAAA,QAAU,MAAM;AAAA,IACjC,OAAO,kBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC;AAAA,EACD,2BAA2B,kBAAAA,QAAU,MAAM;AAAA,IACzC,OAAO,kBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC;AAAA,EACD,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU,KAAK;AAAA,EACvB,CAAC,EAAE;AAAA,EACH,kBAAkB,kBAAAA,QAAU,OAAO;AAAA,EACnC,oBAAoB,kBAAAA,QAAU,OAAO;AAAA,EACrC,8BAA8B,kBAAAA,QAAU,KAAK;AAAA,EAC7C,wBAAwB,kBAAAA,QAAU,OAAO;AAAA,EACzC,kBAAkB,kBAAAA,QAAU,OAAO;AAAA,EACnC,gBAAgB,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,EAAE;AACtD,IAAI;;;AI1FJ,IAAAC,SAAuB;;;ACDvB,IAAAC,SAAuB;AAEvB,IAAAC,qBAAsB;;;ACDf,IAAMC,qBAAoB;;;ADKjC,IAAAC,sBAA2C;AAC3C,SAAS,0BAA0B,OAAO;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAASC,mBAAkB;AACjC,QAAM,YAAYC,kBAAiB;AACnC,QAAM,QAAQ,OAAO;AACrB,QAAM,YAAkB,mBAAY,CAAAC,UAAQ,WAAS;AACnD,WAAO,QAAQ,UAAU,OAAO,OAAOA,KAAI;AAC3C,YAAQ,KAAK;AAAA,EACf,GAAG,CAAC,QAAQ,OAAO,OAAO,OAAO,CAAC;AAClC,QAAM,cAAc,WAAS;AAC3B,WAAO,QAAQ,YAAY,OAAO,KAAK;AACvC,YAAQ,KAAK;AAAA,EACf;AACA,QAAM,wBAAiC,oBAAAC,KAAK,UAAU,MAAM,cAAc;AAAA,IACxE,SAAS,UAAU,yBAAyB,IAAI;AAAA,IAChD,eAAwB,oBAAAA,KAAK,UAAU,MAAM,uBAAuB;AAAA,MAClE,UAAU;AAAA,IACZ,CAAC;AAAA,IACD,UAAU,OAAO,QAAQ,cAAc,WAAW;AAAA,EACpD,CAAC;AACD,QAAM,yBAAkC,oBAAAA,KAAK,UAAU,MAAM,cAAc;AAAA,IACzE,SAAS,UAAU,yBAAyB,KAAK;AAAA,IACjD,eAAwB,oBAAAA,KAAK,UAAU,MAAM,wBAAwB;AAAA,MACnE,UAAU;AAAA,IACZ,CAAC;AAAA,IACD,UAAU,OAAO,QAAQ,cAAc,YAAY;AAAA,EACrD,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,OAAO,OAAO,QAAQ,eAAe,OAAO,KAAK;AACvD,MAAI,MAAM;AACR,UAAM,YAAY,SAAS,yBAAyB,QAAQ,yBAAyB,OAAO,yBAAyB;AACrH,UAAM,QAAQ,cAAc,yBAAyB,QAAQ,eAAe;AAC5E,UAAM,OAAO,SAAS,yBAAyB,QAAQ,UAAU,MAAM,wBAAwB,UAAU,MAAM;AAC/G,eAAoB,oBAAAC,MAAY,iBAAU;AAAA,MACxC,UAAU,KAAc,oBAAAD,KAAK,UAAU,MAAM,cAAc;AAAA,QACzD,SAAS,UAAU,SAAS;AAAA,QAC5B,eAAwB,oBAAAA,KAAK,MAAM;AAAA,UACjC,UAAU;AAAA,QACZ,CAAC;AAAA,QACD,UAAU,OAAO,QAAQ,cAAc,KAAK;AAAA,MAC9C,CAAC,OAAgB,oBAAAA,KAAK,UAAU,MAAM,cAAc;AAAA,QAClD,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU,OAAO,QAAQ,cAAc,OAAO;AAAA,MAChD,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AACA,MAAI,OAAO;AACT,eAAoB,oBAAAC,MAAY,iBAAU;AAAA,MACxC,UAAU,CAAC,oBAAoB,iBAAiB;AAAA,IAClD,CAAC;AAAA,EACH;AACA,aAAoB,oBAAAA,MAAY,iBAAU;AAAA,IACxC,UAAU,CAAC,mBAAmB,kBAAkB;AAAA,EAClD,CAAC;AACH;AACA,OAAwC,0BAA0B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5E,QAAQ,mBAAAC,QAAU,OAAO;AAAA,EACzB,SAAS,mBAAAA,QAAU,KAAK;AAC1B,IAAI;;;ADtEJ,IAAAC,sBAA4B;AACrB,IAAM,6BAA6B,SAAS,CAAC,GAAG,wBAAwB;AAAA,EAC7E,uBAAuB;AACzB,CAAC;AACM,IAAM,kCAAkC,SAAS,CAAC,GAAG,6BAA6B;AAAA,EACvF,uBAAuB;AAAA,IACrB,cAAc;AAAA,EAChB;AACF,CAAC;AACM,IAAM,oBAAoB,WAAW,SAASC,mBAAkB,OAAO,KAAK;AACjF,aAAoB,oBAAAC,KAAK,uBAAuB,SAAS,CAAC,GAAG,OAAO;AAAA,IAClE,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,mBAAkB,cAAc;;;AGpB3E,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACGP,SAAR,WAA4B,QAAQ;AACzC,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,MAAM,OAAwC,yDAAyD,sBAAoB,CAAC,CAAC;AAAA,EACzI;AACA,SAAO,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AACxD;;;ACVe,SAAR,gBAAiC,OAAO,UAAU,eAAe,UAAU,cAAc;AAC9F,MAAI,OAAuC;AACzC,WAAO;AAAA,EACT;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,eAAe,gBAAgB;AACrC,MAAI,aAAa,MAAM;AACrB,WAAO;AAAA,EACT;AACA,MAAI,aAAa,UAAU,aAAa,GAAG;AACzC,WAAO,IAAI,MAAM,WAAW,QAAQ,MAAM,YAAY,oBAAoB,aAAa,8BAAmC;AAAA,EAC5H;AACA,SAAO;AACT;;;AFPA,IAAAC,sBAA2C;AAC3C,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,SAAS,kBAAkB;AACjC,QAAM,YAAY,iBAAiB;AACnC,QAAM,WAAiB,mBAAY,MAAM;AACvC,WAAO,QAAQ,qBAAqB;AAAA,EACtC,GAAG,CAAC,MAAM,CAAC;AACX,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,aAAoB,oBAAAC,KAAK,UAAU;AAAA,IACjC,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,cAAuB,oBAAAC,MAAM,UAAU,MAAM,cAAc;AAAA,MACzD,mBAAmB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,iBAAiB,KAAc,oBAAAD,KAAK,UAAU,MAAM,cAAc;AAAA,QAC3E,eAAwB,oBAAAA,KAAK,UAAU,MAAM,qBAAqB;AAAA,UAChE,UAAU;AAAA,QACZ,CAAC;AAAA,QACD,SAAS,MAAM;AACb,0BAAgB;AAChB,mBAAS;AAAA,QACX;AAAA,QACA,UAAU,OAAO,QAAQ,cAAc,mBAAmB;AAAA,MAC5D,GAAG,0BAA0B,OAAgB,oBAAAA,KAAK,UAAU,MAAM,aAAa,CAAC,GAAG,qBAAqB,CAAC,GAAG,UAAU,IAAI,QAAM;AAC9H,cAAM,WAAW,GAAG,UAAU,KAAK;AACnC,cAAM,SAAQ,yBAAI,gBAAe,OAAO,QAAQ,cAAc,uBAAuB,WAAW,GAAG,KAAK,CAAC,EAAE;AAC3G,mBAAoB,oBAAAA,KAAK,UAAU,MAAM,cAAc;AAAA,UACrD,WAAW,eAAwB,oBAAAA,KAAK,UAAU,MAAM,mBAAmB;AAAA,YACzE,UAAU;AAAA,UACZ,CAAC,QAAiB,oBAAAA,KAAK,QAAQ,CAAC,CAAC;AAAA,UACjC,SAAS,MAAM;AACb,+BAAmB,SAAS,CAAC,GAAG,MAAM;AAAA,cACpC,UAAU,GAAG;AAAA,YACf,CAAC,CAAC;AACF,qBAAS;AAAA,UACX;AAAA,UACA,WAAW,WAAW,OAAO;AAAA,UAC7B,UAAU;AAAA,QACZ,GAAG,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MAC3B,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC;AACH;AACA,OAAwC,qBAAqB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvE,oBAAoB,mBAAAE,QAAU,KAAK;AAAA,EACnC,iBAAiB,mBAAAA,QAAU,KAAK;AAAA,EAChC,OAAO,mBAAAA,QAAU,OAAO;AAAA,EACxB,IAAI,mBAAAA,QAAgD;AAAA,EACpD,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC,EAAE;AAAA,EACH,YAAY,mBAAAA,QAAgD;AAAA,EAC5D,MAAM,mBAAAA,QAAU,KAAK;AAAA,EACrB,WAAW,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IAC3C,kBAAkB,mBAAAA,QAAU,KAAK;AAAA,IACjC,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,aAAa,mBAAAA,QAAU;AAAA,IACvB,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,qBAAqB,mBAAAA,QAAU,MAAM;AAAA,MACnC,QAAQ,mBAAAA,QAAU,MAAM;AAAA,QACtB,SAAS,mBAAAA,QAAU,OAAO;AAAA,MAC5B,CAAC;AAAA,MACD,YAAY,mBAAAA,QAAU;AAAA,MACtB,WAAW,mBAAAA,QAAU;AAAA,MACrB,aAAa,mBAAAA,QAAU;AAAA,MACvB,UAAU,mBAAAA,QAAU;AAAA,MACpB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,QACpE,SAAS,mBAAAA,QAAU,IAAI;AAAA,MACzB,CAAC,CAAC,CAAC;AAAA,MACH,kBAAkB,mBAAAA,QAAU;AAAA,MAC5B,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,QAC7D,SAAS,CAAC,OAAO,aAAa;AAC5B,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,MAAM,QAAQ,MAAM,YAAY,MAAM,QAAQ,EAAE,aAAa,GAAG;AACzE,mBAAO,IAAI,MAAM,kBAAkB,QAAQ,yBAAyB;AAAA,UACtE;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC,CAAC;AAAA,MACH,gBAAgB,mBAAAA,QAAU;AAAA,MAC1B,MAAM,mBAAAA,QAAU,MAAM;AAAA,QACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,QACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,QAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,QAC3B,OAAO,mBAAAA,QAAU;AAAA,MACnB,CAAC;AAAA,MACD,QAAQ,mBAAAA,QAAU;AAAA,MAClB,SAAS,mBAAAA,QAAU;AAAA,MACnB,WAAW,mBAAAA,QAAU;AAAA,MACrB,UAAU,mBAAAA,QAAU;AAAA,IACtB,CAAC;AAAA,IACD,OAAO,mBAAAA,QAAU;AAAA,IACjB,qBAAqB,mBAAAA,QAAU;AAAA,IAC/B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA,EACJ,eAAe,mBAAAA,QAAU,KAAK;AAAA,EAC9B,QAAQ;AACV,IAAI;;;AG1HJ,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACJtB,IAAAC,SAAuB;AAiBR,SAAR,cAA+B,MAAM;AAC1C,QAAM,aAAmB,cAAO,MAAS;AACzC,QAAM,YAAkB,mBAAY,cAAY;AAC9C,UAAM,WAAW,KAAK,IAAI,SAAO;AAC/B,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,cAAc;AACpB,cAAM,aAAa,YAAY,QAAQ;AACvC,eAAO,OAAO,eAAe,aAAa,aAAa,MAAM;AAC3D,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF;AACA,UAAI,UAAU;AACd,aAAO,MAAM;AACX,YAAI,UAAU;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,eAAS,QAAQ,gBAAc,0CAAc;AAAA,IAC/C;AAAA,EAEF,GAAG,IAAI;AACP,SAAa,eAAQ,MAAM;AACzB,QAAI,KAAK,MAAM,SAAO,OAAO,IAAI,GAAG;AAClC,aAAO;AAAA,IACT;AACA,WAAO,WAAS;AACd,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAQ;AACnB,mBAAW,UAAU;AAAA,MACvB;AACA,UAAI,SAAS,MAAM;AACjB,mBAAW,UAAU,UAAU,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EAGF,GAAG,IAAI;AACT;;;ACxDA,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACJtB,IAAAC,qBAAsB;AACtB,IAAM,UAAU,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AACtE,IAAO,kBAAQ;;;ACAf,IAAAC,SAAuB;AACvB,IAAI,WAAW;AAGf,SAAS,YAAY,YAAY;AAC/B,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,UAAU;AAC3D,QAAM,KAAK,cAAc;AACzB,EAAM,iBAAU,MAAM;AACpB,QAAI,aAAa,MAAM;AAKrB,kBAAY;AACZ,mBAAa,OAAO,QAAQ,EAAE;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,SAAO;AACT;AAGA,IAAM,YAAY;AAAA,EAChB,GAAGA;AACL;AACA,IAAM,kBAAkB,UAAU;AAQnB,SAAR,MAAuB,YAAY;AAExC,MAAI,oBAAoB,QAAW;AACjC,UAAM,UAAU,gBAAgB;AAChC,WAAO,cAAc;AAAA,EACvB;AAIA,SAAO,YAAY,UAAU;AAC/B;;;AFlCA,IAAAC,sBAA2C;AAR3C,IAAMC,aAAY,CAAC,aAAa,QAAQ,SAAS,aAAa,uBAAuB,YAAY,iBAAiB,iBAAiB;AASnI,SAAS,8BAA8B,OAAO;AAX9C;AAYE,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,OACJ,SAAS,8BAA8B,OAAOA,UAAS;AACzD,QAAM,WAAW,MAAM;AACvB,QAAM,SAAS,MAAM;AACrB,QAAM,YAAYC,kBAAiB;AACnC,QAAM,SAAS,kBAAkB;AACjC,QAAM,gBAAgB,gBAAgB,QAAQ,+BAA+B;AAC7E,QAAM,OAAO,QAAQ,kBAAkB,SAAS,oBAAoB,OAAO;AAC3E,QAAM,cAAc,WAAS;AAC3B,wBAAoB,UAAU,MAAM;AACpC,WAAO,QAAQ,qBAAqB,KAAK;AAAA,EAC3C;AACA,MAAI,CAAC,UAAU,MAAM,kBAAkB;AACrC,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,QAAQ,cAAc,qBAAqB;AAChE,QAAM,cAAc,QAAQ,OAAO,KAAK,IAAI;AAC5C,aAAoB,oBAAAC,MAAY,kBAAU;AAAA,IACxC,UAAU,KAAc,oBAAAC,KAAK,UAAU,MAAM,gBAAgB,SAAS;AAAA,MACpE,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,cAAc;AAAA,MACd,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,iBAAiB,OAAO,SAAS;AAAA,MACjC,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT;AAAA,IACF,IAAG,eAAU,cAAV,mBAAqB,gBAAgB;AAAA,MACtC,cAAuB,oBAAAA,KAAK,UAAU,MAAM,WAAW;AAAA,QACrD,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc,gBAAgB,IAAI;AAAA,QAClC,cAAuB,oBAAAA,KAAK,UAAU,MAAM,sBAAsB;AAAA,UAChE,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,CAAC,OAAgB,oBAAAA,KAAK,UAAU,MAAM,kBAAkB,SAAS;AAAA,MAChE;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,oBAAoB;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,IACF,GAAG,MAAM,CAAC,CAAC;AAAA,EACb,CAAC;AACH;AACA,OAAwC,8BAA8B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhF,oBAAoB,mBAAAC,QAAU,KAAK;AAAA,EACnC,WAAW;AAAA,EACX,iBAAiB,mBAAAA,QAAU;AAAA,EAC3B,UAAU,mBAAAA,QAAU;AAAA,EACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,EACxB,qBAAqB,mBAAAA,QAAU,MAAM;AAAA,IACnC,SAAS,mBAAAA,QAAU;AAAA,EACrB,CAAC,EAAE;AAAA,EACH,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC,EAAE;AAAA,EACH,WAAW,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IAC3C,kBAAkB,mBAAAA,QAAU,KAAK;AAAA,IACjC,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,aAAa,mBAAAA,QAAU;AAAA,IACvB,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,qBAAqB,mBAAAA,QAAU,MAAM;AAAA,MACnC,QAAQ,mBAAAA,QAAU,MAAM;AAAA,QACtB,SAAS,mBAAAA,QAAU,OAAO;AAAA,MAC5B,CAAC;AAAA,MACD,YAAY,mBAAAA,QAAU;AAAA,MACtB,WAAW,mBAAAA,QAAU;AAAA,MACrB,aAAa,mBAAAA,QAAU;AAAA,MACvB,UAAU,mBAAAA,QAAU;AAAA,MACpB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,QACpE,SAAS,mBAAAA,QAAU,IAAI;AAAA,MACzB,CAAC,CAAC,CAAC;AAAA,MACH,kBAAkB,mBAAAA,QAAU;AAAA,MAC5B,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,QAC7D,SAAS,CAAC,OAAO,aAAa;AAC5B,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,MAAM,QAAQ,MAAM,YAAY,MAAM,QAAQ,EAAE,aAAa,GAAG;AACzE,mBAAO,IAAI,MAAM,kBAAkB,QAAQ,yBAAyB;AAAA,UACtE;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC,CAAC;AAAA,MACH,gBAAgB,mBAAAA,QAAU;AAAA,MAC1B,MAAM,mBAAAA,QAAU,MAAM;AAAA,QACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,QACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,QAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,QAC3B,OAAO,mBAAAA,QAAU;AAAA,MACnB,CAAC;AAAA,MACD,QAAQ,mBAAAA,QAAU;AAAA,MAClB,SAAS,mBAAAA,QAAU;AAAA,MACnB,WAAW,mBAAAA,QAAU;AAAA,MACrB,UAAU,mBAAAA,QAAU;AAAA,IACtB,CAAC;AAAA,IACD,OAAO,mBAAAA,QAAU;AAAA,IACjB,qBAAqB,mBAAAA,QAAU;AAAA,IAC/B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA,EACJ,eAAe,mBAAAA,QAAU;AAC3B,IAAI;;;AGxIJ,IAAAC,UAAuB;AAEvB,IAAAC,sBAA4B;AAC5B,SAAS,4BAA4B,OAAO;AAJ5C;AAKE,QAAM,YAAYC,kBAAiB;AACnC,aAAoB,oBAAAC,KAAK,UAAU,MAAM,gBAAgB,SAAS;AAAA,IAChE,UAAU;AAAA,IACV,cAAc;AAAA,IACd,MAAM;AAAA,EACR,IAAG,eAAU,cAAV,mBAAqB,gBAAgB,OAAO;AAAA,IAC7C,cAAuB,oBAAAA,KAAK,UAAU,MAAM,qBAAqB;AAAA,MAC/D,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;;;ALMA,IAAAC,sBAA2C;AAjB3C,IAAMC,aAAY,CAAC,YAAY,UAAU,YAAY,SAAS,mBAAmB,UAAU,QAAQ,uBAAuB,uBAAuB,iBAAiB,kBAAkB,gBAAgB,SAAS,kBAAkB,iBAAiB;AAkBhP,IAAM,uBAAuB,eAAO,sBAAsB;AAAA,EACxD,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,MAAM;AAAA,EACN,aAAa,KAAK,QAAQ,GAAG;AAAA,EAC7B,cAAc,KAAK,QAAQ,KAAK;AAAA,EAChC,gFAAgF;AAAA,IAC9E,2BAA2B;AAAA,MACzB,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,CAAC,MAAM,yBAAiB,KAAK,EAAE,GAAG;AAAA,IAChC,UAAU;AAAA,EACZ;AAAA,EACA,CAAC,IAAI,YAAY,sBAAsB,CAAC,OAAO,yBAAiB,KAAK,EAAE,GAAG;AAAA,IACxE,YAAY,KAAK,QAAQ,GAAG;AAAA,IAC5B,eAAe,KAAK,QAAQ,GAAG;AAAA,IAC/B,QAAQ;AAAA,EACV;AACF,CAAC;AACD,IAAM,gBAAgB,eAAO,QAAQ;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,MAAM;AAAA,EACN,aAAa,KAAK,QAAQ,GAAG;AAAA,EAC7B,OAAO,KAAK,OAAO,WAAW;AAAA,EAC9B,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AACZ,CAAC;AACD,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,gBAAgB,wBAAwB,OAAO,gBAAgB,UAAU,2BAA2B,OAAO,gBAAgB,YAAY,6BAA6B,OAAO,gBAAgB,WAAW,4BAA4B,mBAAmB,mBAAmB,iCAAiC,kBAAkB,gCAAgC,mBAAmB,qBAAqB,QAAQ,4BAA4B,mBAAmB,qBAAqB,SAAS,2BAA2B;AAAA,IAC1f,OAAO,CAAC,yBAAyB;AAAA,IACjC,eAAe,CAAC,iCAAiC;AAAA,EACnD;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,2BAA2B;AAAA,EAC/B,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,cAAc;AAAA,EACd,SAAS;AAAA,EACT,QAAQ;AACV;AACA,IAAM,uBAAuB,WAAW,CAAC,OAAO,QAAQ;AACtD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,SAAS,yBAAyB;AACxC,QAAM,QAAQ,OAAO;AACrB,QAAM,eAAe,gBAAgB,QAAQ,+BAA+B;AAC5E,QAAM,YAAYE,kBAAiB;AACnC,QAAM,UAAgB,eAAO,IAAI;AACjC,QAAM,YAAY,WAAW,KAAK,OAAO;AACzC,QAAM,WAAiB,eAAO,IAAI;AAClC,QAAM,YAAkB,eAAO,IAAI;AACnC,QAAM,eAAe,gBAAgB,QAAQ,oCAAoC;AACjF,QAAM,YAAY,iBAAiB,OAAO;AAC1C,QAAM,gBAAgB,gBAAgB,QAAQ,+BAA+B;AAC7E,QAAM,aAAa,kBAAkB,OAAO;AAG5C,QAAM,kBAAwB,gBAAQ,MAAM;AAC1C,QAAI,CAAC,OAAO,iBAAiB;AAC3B,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,gBAAgB,OAAO,cAAY,SAAS,UAAU,SAAS;AAAA,EAC/E,GAAG,CAAC,OAAO,eAAe,CAAC;AAC3B,QAAM,cAAc,gBAAgB,QAAQ,uBAAuB;AACnE,QAAM,0BAA0B,gBAAgB,QAAQ,kCAAkC;AAC1F,QAAM,mBAAyB,gBAAQ,MAAM;AAC3C,QAAI,EAAC,2CAAa,MAAM,SAAQ;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,YAAY,MAAM,KAAK,QAAM,GAAG,UAAU,OAAO,KAAK;AAC9E,WAAO,kBAAkB,CAAC,wBAAwB,gBAAgB,KAAK,IAAI;AAAA,EAC7E,GAAG,CAAC,OAAO,OAAO,aAAa,uBAAuB,CAAC;AACvD,QAAM,kBAAwB,gBAAQ,MAAM,gBAAgB,KAAK,cAAY,SAAS,UAAU,KAAK,QAAQ,KAAK,gBAAgB,CAAC,GAAG,CAAC,KAAK,UAAU,eAAe,CAAC;AACtK,QAAM,iBAAiB,OAAO,cAAc,mBAAmB,gBAAgB,kBAAkB,yBAAyB,OAAO,IAAI,IAAI;AACzI,QAAM,kBAAwB,oBAAY,MAAM;AAC9C,WAAO,QAAQ,iBAAiB,IAAI;AAAA,EACtC,GAAG,CAAC,QAAQ,IAAI,CAAC;AACjB,MAAI;AACJ,MAAI,OAAO,oBAAoB;AAC7B,4BAAwB,OAAO,mBAAmB,SAAS,CAAC,GAAG,OAAO;AAAA,MACpE;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACA,EAAM,wBAAgB,MAAM;AAC1B,QAAI,YAAY,CAAC,YAAY;AAC3B,UAAI,mBAAmB,QAAQ,QAAQ,cAAc,gBAAgB;AACrE,UAAI,aAAa,gBAAgB;AAC/B,2BAAmB,SAAS;AAAA,MAC9B;AACA,YAAM,iBAAiB,oBAAoB,QAAQ;AACnD,uDAAgB;AAChB,UAAI,OAAO,QAAQ,0BAA0B,SAAS;AACpD,eAAO,QAAQ,0BAA0B,QAAQ,aAAa;AAAA,MAChE;AAAA,IACF;AAAA,EACF,GAAG,CAAC,gBAAgB,QAAQ,UAAU,WAAW,UAAU,CAAC;AAC5D,QAAM,YAAkB,oBAAY,WAAS;AAC3C,QAAI,cAAc,gBAAgB,MAAM,GAAG,KAAK,kBAAkB;AAChE;AAAA,IACF;AACA,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AACH,YAAI,WAAW;AACb,iBAAO,QAAQ,yBAAyB;AAAA,QAC1C;AACA;AAAA,MACF,KAAK;AACH,YAAI,WAAW;AACb,cAAI,CAAC,MAAM,kBAAkB;AAC3B,mBAAO,QAAQ,yBAAyB;AACxC;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM,WAAW,MAAM,SAAS;AAClC,8BAAoB,UAAU,UAAU;AACxC,iBAAO,QAAQ,qBAAqB,OAAO,KAAK;AAChD;AAAA,QACF;AACA,eAAO,QAAQ,0BAA0B,OAAO,KAAK;AACrD;AAAA,MACF,KAAK,OACH;AACE,YAAI,WAAW;AACb,gBAAM,eAAe,aAAa,YAAY,MAAM,WAAW,KAAK,EAAE,KAAK;AAC3E,cAAI,cAAc;AAChB,mBAAO,QAAQ,0BAA0B,YAAY;AACrD,mBAAO,QAAQ,2BAA2B,cAAc,KAAK;AAAA,UAC/D;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACF;AACE,YAAI,aAAa,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,MAAM,UAAU;AACjF;AAAA,QACF;AACA,eAAO,QAAQ,0BAA0B,OAAO,KAAK;AACrD;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,OAAO,UAAU,cAAc,qBAAqB,WAAW,kBAAkB,UAAU,CAAC;AAC/G,QAAM,UAAgB,oBAAY,CAAC,WAAW,gBAAgB,WAAS;AACrE,WAAO,QAAQ,aAAa,WAAW,OAAO,QAAQ,sBAAsB,OAAO,KAAK,GAAG,KAAK;AAChG,QAAI,aAAa;AACf,kBAAY,KAAK;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC;AACzB,QAAM,cAAoB,oBAAY,WAAS;AAxMjD;AAyMI,QAAI,CAAC,UAAU;AACb,WAAI,oBAAS,YAAT,mBAAkB,aAAlB,4BAA6B,MAAM,SAAS;AAC9C,iBAAS,QAAQ,MAAM;AAAA,MACzB;AACA,aAAO,QAAQ,2BAA2B,OAAO,OAAO,KAAK;AAAA,IAC/D;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,OAAO,QAAQ,CAAC;AACnC,QAAM,sBAA4B,gBAAQ,OAAO;AAAA,IAC/C,WAAW,QAAQ,uBAAuB,SAAS;AAAA,IACnD,SAAS,QAAQ,mBAAmB;AAAA,IACpC,aAAa,QAAQ,yBAAyB,WAAW;AAAA,IACzD,QAAQ,QAAQ,kBAAkB;AAAA,EACpC,IAAI,CAAC,aAAa,WAAW,OAAO,CAAC;AACrC,QAAM,aAAa,SAAS,CAAC,GAAG,WAAW;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAUD,mBAAkB,UAAU;AAC5C,QAAM,QAAQ,gBAAgB,eAAe,OAAO,QAAQ,cAAc,uBAAuB,WAAW,KAAK,QAAQ,CAAC,EAAE;AAC5H,QAAM,oBAAoB,gBAAgB,wBAAwB;AAClE,QAAM,aAAY,6BAAM,WAAU,UAAa;AAC/C,QAAM,iBAAiB,aAAa;AACpC,QAAM,uBAAgC,oBAAAE,KAAK,+BAA+B;AAAA,IACxE,WAAW;AAAA,IACX;AAAA,IACA,OAAO,OAAO;AAAA,IACd,UAAU;AAAA,IACV,oBAAoB,OAAO,QAAQ;AAAA,IACnC;AAAA,IACA;AAAA,IACA,eAAe,CAAC,iBAAiB;AAAA,IACjC;AAAA,EACF,CAAC;AACD,QAAM,cAAc,iBAAiB,gBAAyB,oBAAAA,KAAK,6BAA6B;AAAA,IAC9F,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,IAAI;AACL,aAAoB,oBAAAC,MAAM,OAAO,SAAS;AAAA,IACxC,WAAW,aAAK,QAAQ,MAAM,eAAe;AAAA,IAC7C,OAAO,kBAAkB,SAAS;AAAA,MAChC;AAAA,MACA;AAAA,IACF,GAAG,SAAS,GAAG,OAAO,gBAAgB,YAAY;AAAA,IAClD,MAAM;AAAA,IACN,iBAAiB,WAAW;AAAA,IAC5B,cAAc,yBAAyB,OAAO,OAAO,cAAc,OAAO,QAAQ;AAAA,EACpF,GAAG,OAAO,qBAAqB;AAAA,IAC7B,KAAK;AAAA,IACL,UAAU,CAAC,uBAAuB,0BAA0B,aAAyB,oBAAAA,MAAY,kBAAU;AAAA,MACzG,UAAU,CAAC,wBAAiC,oBAAAA,MAAY,kBAAU;AAAA,QAChE,UAAU,KAAc,oBAAAD,KAAK,eAAe;AAAA,UAC1C,WAAW,QAAQ;AAAA,UACnB,UAAU;AAAA,QACZ,CAAC,GAAG,aAAa,gBAAgB;AAAA,MACnC,CAAC,IAAI,MAAM,kBAAkB,CAAC,wBAAiC,oBAAAA,KAAK,sBAAsB,SAAS;AAAA,QACjG,IAAI;AAAA,QACJ,WAAW,QAAQ;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,OAAO,QAAQ;AAAA,QAC3B,SAAS,MAAM,OAAO,QAAQ,0BAA0B,OAAO,KAAK;AAAA,QACpE,QAAQ,WAAS;AAzQzB;AA0QU,iBAAO,QAAQ,yBAAyB;AAExC,cAAI,GAAC,WAAM,kBAAN,mBAAqB,UAAU,SAAS,kBAAiB;AAC5D,mBAAO,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,cACnD,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,cAAc;AAAA,gBACd,oBAAoB;AAAA,gBACpB,mBAAmB;AAAA,cACrB;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,oBAAoB;AAAA,QAC9B,UAAU;AAAA,QACV,WAAW;AAAA,UACT,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,WAAW,KAAK;AAAA,YACvB,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,GAAG,oBAAoB;AAAA,QACrB,OAAO;AAAA,MACT,IAAI,CAAC,GAAG,mDAAiB,qBAAqB,mBAAmB,CAAC,IAAI,IAAI;AAAA,IAC5E,CAAC,IAAI,IAAI;AAAA,EACX,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,sBAAqB,cAAc;AAC9E,OAAwC,qBAAqB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvE,QAAQ,mBAAAE,QAAU,OAAO;AAAA,EACzB,UAAU,mBAAAA,QAAU,OAAO;AAAA,EAC3B,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACvE,qBAAqB,mBAAAA,QAAU,MAAM;AAAA,IACnC,SAAS,mBAAAA,QAAU;AAAA,EACrB,CAAC,EAAE;AAAA,EACH,QAAQ,mBAAAA,QAAU,OAAO;AAAA,EACzB,qBAAqB,mBAAAA,QAAU,MAAM;AAAA,IACnC,QAAQ,mBAAAA,QAAU,MAAM;AAAA,MACtB,SAAS,mBAAAA,QAAU,OAAO;AAAA,IAC5B,CAAC;AAAA,IACD,YAAY,mBAAAA,QAAU;AAAA,IACtB,WAAW,mBAAAA,QAAU;AAAA,IACrB,aAAa,mBAAAA,QAAU;AAAA,IACvB,UAAU,mBAAAA,QAAU;AAAA,IACpB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,MACpE,SAAS,mBAAAA,QAAU,IAAI;AAAA,IACzB,CAAC,CAAC,CAAC;AAAA,IACH,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,MAC7D,SAAS,CAAC,OAAO,aAAa;AAC5B,YAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,MAAM,QAAQ,MAAM,YAAY,MAAM,QAAQ,EAAE,aAAa,GAAG;AACzE,iBAAO,IAAI,MAAM,kBAAkB,QAAQ,yBAAyB;AAAA,QACtE;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,IACH,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,MAAM,mBAAAA,QAAU,MAAM;AAAA,MACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,MACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,MAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,MAC3B,OAAO,mBAAAA,QAAU;AAAA,IACnB,CAAC;AAAA,IACD,QAAQ,mBAAAA,QAAU;AAAA,IAClB,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU;AAAA,IACrB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC;AAAA,EACD,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpB,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC,EAAE;AAAA,EACH,cAAc,mBAAAA,QAAU;AAAA,EACxB,gBAAgB,mBAAAA,QAAU,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,EAC5C,eAAe,mBAAAA,QAAU;AAAA,EACzB,gBAAgB,mBAAAA,QAAU,KAAK;AAAA,EAC/B,iBAAiB,mBAAAA,QAAU,KAAK;AAAA,EAChC,WAAW,mBAAAA,QAAU;AAAA,EACrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAAA,EACnC,OAAO,mBAAAA,QAAU,OAAO;AAC1B,IAAI;AACJ,IAAM,WAAW,SAAS,oBAAoB;;;AM3W9C,IAAAC,UAAuB;;;ACDhB,IAAMC,4BAA2B;;;ACAxC,IAAAC,UAAuB;;;ACAvB,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACDf,IAAM,+BAA+B,mBAAmB,WAAS,MAAM,WAAW;AAClF,IAAM,wCAAwC,eAAe,8BAA8B,sBAAoB,iBAAiB,cAAc;AAC9I,IAAM,kDAAkD,eAAe,8BAA8B,sBAAoB,iBAAiB,YAAY;AACtJ,IAAM,wCAAwC,uBAAuB,8BAA8B,sBAAoB,iBAAiB,WAAW;;;ADK1J,IAAAC,uBAA4B;AAC5B,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,yBAAyB,cAAc,iCAAiC;AAAA,EACjF;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,qBAAqB,eAAe,uCAAuC,CAAC,gBAAgB,UAAU;AAC1G,SAAO,eAAe,IAAI,KAAK;AACjC,CAAC;AACD,SAAS,0BAA0B,OAAO;AAvB1C;AAwBE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,IAAI,SAAS,GAAG;AAC9B,QAAM,aAAa,gBAAgB;AAAA,IACjC,SAAS;AAAA,EACX,GAAG,oBAAoB,KAAK;AAC5B,QAAM,YAAYC,kBAAiB;AACnC,QAAM,SAASC,mBAAkB;AACjC,QAAM,aAAa;AAAA,IACjB,SAAS,UAAU;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAUF,mBAAkB,UAAU;AAC5C,QAAM,eAAe,gBAAgB,QAAQ,+CAA+C;AAC5F,QAAM,aAAgC,uBAAe,aAAa,EAAE,CAAC;AACrE,QAAM,OAAO,aAAa,UAAU,MAAM,0BAA0B,UAAU,MAAM;AACpF,aAAoB,qBAAAG,KAAK,UAAU,MAAM,gBAAgB,SAAS;AAAA,IAChE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU,CAAC;AAAA,IACX,WAAW,QAAQ;AAAA,IACnB,iBAAiB;AAAA,IACjB,cAAc,aAAa,OAAO,QAAQ,cAAc,qBAAqB,IAAI,OAAO,QAAQ,cAAc,mBAAmB;AAAA,EACnI,IAAG,eAAU,cAAV,mBAAqB,gBAAgB;AAAA,IACtC,cAAuB,qBAAAA,KAAK,MAAM;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AACA,OAAwC,0BAA0B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ5E,KAAK,mBAAAC,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAItB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI5C,QAAQ,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpE,SAAS,mBAAAA,QAAU,MAAM;AAAA,MACvB,OAAO,mBAAAA,QAAU,KAAK;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,EAIH,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA,EAIzB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI9D,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,KAAK,mBAAAA,QAAU,IAAI;AAAA;AAAA;AAAA;AAAA,EAInB,SAAS,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ADlHJ,IAAAC,uBAA4B;AAErB,IAAM,mCAAmC,SAAS,CAAC,GAAG,qBAAqB;AAAA,EAChF,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa,CAAC,OAAO,KAAK,QAAQ,WAAW;AAC3C,UAAM,QAAQ,kBAAkB,QAAQ,GAAG;AAC3C,UAAM,iBAAiB,sCAAsC,MAAM;AACnE,WAAO,eAAe,IAAI,KAAK;AAAA,EACjC;AAAA,EACA,oBAAoB,CAAC,GAAG,KAAK,IAAI,WAAW,kBAAkB,QAAQ,GAAG;AAAA,EACzE,YAAY,gBAAuB,qBAAAC,KAAK,2BAA2B,SAAS,CAAC,GAAG,MAAM,CAAC;AAAA,EACvF,cAAc,CAAC;AAAA,IACb;AAAA,EACF,UAAmB,qBAAAA,KAAK,OAAO;AAAA,IAC7B,cAAc,OAAO;AAAA,EACvB,CAAC;AACH,CAAC;;;AGhCD,IAAAC,UAAuB;;;ACFvB,IAAAC,UAAuB;AAEvB,IAAM,mBAAmB;AACzB,IAAM,OAAO,MAAM;AAAC;AACb,SAAS,kBAAkB,KAAK,IAAI,SAAS;AAClD,QAAM,QAAc,eAAO,IAAI;AAC/B,QAAM,UAAU;AAChB,4BAAkB,MAAM;AACtB,QAAI,YAAY,SAAS,OAAO,mBAAmB,aAAa;AAC9D,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACd,UAAM,SAAS,IAAI;AACnB,UAAM,WAAW,IAAI,eAAe,aAAW;AAI7C,UAAI,kBAAkB;AACpB,kBAAU,sBAAsB,MAAM;AACpC,gBAAM,QAAQ,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,OAAO;AACL,cAAM,QAAQ,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AACD,QAAI,QAAQ;AACV,eAAS,QAAQ,MAAM;AAAA,IACzB;AACA,WAAO,MAAM;AACX,UAAI,SAAS;AACX,6BAAqB,OAAO;AAAA,MAC9B;AACA,eAAS,WAAW;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,KAAK,OAAO,CAAC;AACnB;;;AD1BA,IAAAC,uBAA4B;AAC5B,IAAM,cAAc,eAAO,OAAO;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,iBAAiB,KAAK,OAAO,WAAW;AAAA,EACxC,UAAU;AACZ,CAAC;AACD,SAAS,gBAAgB,OAAO;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAASC,0BAAyB;AACxC,QAAM,MAAY,eAAO,IAAI;AAC7B,QAAM,YAAYC,kBAAiB;AACnC,QAAM,aAAa;AACnB,QAAM,gBAAgB,WAAW;AACjC,QAAM,UAAU,oBAAoB,QAAQ,KAAK;AACjD,EAAM,wBAAgB,MAAM;AAC1B,QAAI,iBAAiB,OAAO,mBAAmB,aAAa;AAE1D,aAAO,QAAQ,uBAAuB,OAAO,IAAI,QAAQ,YAAY;AAAA,IACvE;AAAA,EACF,GAAG,CAAC,QAAQ,eAAe,KAAK,CAAC;AACjC,oBAAkB,KAAK,aAAW;AAChC,UAAM,CAAC,KAAK,IAAI;AAChB,UAAM,iBAAiB,MAAM,iBAAiB,MAAM,cAAc,SAAS,IAAI,MAAM,cAAc,CAAC,EAAE,YAAY,MAAM,YAAY;AACpI,WAAO,QAAQ,uBAAuB,OAAO,cAAc;AAAA,EAC7D,GAAG,aAAa;AAChB,OAAI,mCAAS,UAAS,eAAe;AACnC,WAAO;AAAA,EACT;AACA,aAAoB,qBAAAC,KAAK,aAAa;AAAA,IACpC;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AL7CA,IAAAC,uBAA4B;AAC5B,IAAMC,qBAAoB,MAAM;AAC9B,QAAM,QAAQ;AAAA,IACZ,aAAa,CAAC,aAAa;AAAA,EAC7B;AACA,SAAO,eAAe,OAAO,yBAAyB,CAAC,CAAC;AAC1D;AACO,SAAS,iBAAiB,OAAO;AACtC,QAAM,YAAYC,kBAAiB;AACnC,MAAI,CAAC,UAAU,uBAAuB;AACpC,WAAO;AAAA,EACT;AACA,SAA0B,sBAAc,sBAAsB,KAAK;AACrE;AACA,SAAS,qBAAqB;AAAA,EAC5B;AACF,GAAG;AACD,QAAM,SAASC,0BAAyB;AACxC,QAAM,UAAUF,mBAAkB;AAClC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,gBAAgB,QAAQ,qCAAqC;AACpF,QAAM,sBAAsB,gBAAgB,QAAQ,+CAA+C;AACnG,QAAM,sBAAsB,gBAAgB,QAAQ,qCAAqC;AACzF,QAAM,iBAAuB,oBAAY,WAAS;AAChD,UAAM,UAAU,oBAAoB,KAAK;AAGzC,UAAM,WAAW,OAAO,QAAQ,iCAAiC,KAAK;AACtE,UAAM,SAAS,aAAa;AAC5B,QAAI,CAAqB,uBAAe,OAAO,KAAK,CAAC,QAAQ;AAC3D,aAAO;AAAA,IACT;AACA,UAAM,cAAc,oBAAoB,KAAK;AAC7C,UAAM,SAAS,YAAY,aAAa,SAAS,YAAY;AAC7D,eAAoB,qBAAAG,KAAK,iBAAiB;AAAA,MACxC;AAAA,MACA;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,UAAU;AAAA,IACZ,GAAG,SAAS,KAAK,EAAE;AAAA,EACrB,GAAG,CAAC,QAAQ,QAAQ,aAAa,qBAAqB,mBAAmB,CAAC;AAC1E,EAAM,kBAAU,MAAM;AACpB,UAAM,MAAM,oBAAI,IAAI;AACpB,eAAW,SAAS,gBAAgB;AAClC,UAAI,IAAI,OAAO,eAAe,KAAK,CAAC;AAAA,IACtC;AACA,cAAU,GAAG;AAAA,EACf,GAAG,CAAC,gBAAgB,WAAW,cAAc,CAAC;AAC9C,SAAO;AACT;;;AO7DA,IAAAC,UAAuB;AAKvB,IAAAC,uBAA4B;AAC5B,IAAMC,qBAAoB,MAAM;AAC9B,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,YAAY;AAAA,EACrB;AACA,SAAO,eAAe,OAAO,yBAAyB,CAAC,CAAC;AAC1D;AACO,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AACF,GAAG;AACD,QAAM,UAAUA,mBAAkB;AAClC,QAAM,SAAS,yBAAyB;AACxC,QAAM,iBAAiB,gBAAgB,QAAQ,sBAAsB;AACrE,QAAM,OAAO,eAAe,QAAQ;AACpC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,sBAA4B,gBAAQ,OAAO;AAAA,IAC/C,eAAe;AAAA,IACf,cAAc,KAAK;AAAA,IACnB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EACnB,IAAI,CAAC,IAAI,CAAC;AACV,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,QAAQ;AAAA,IACzB;AAAA,IACA;AAAA,IACA,eAAe;AAAA,EACjB,GAAG,oBAAoB,MAAM,CAAC;AAC9B,aAAoB,qBAAAC,KAAK,OAAO;AAAA,IAC9B,WAAW,aAAK,QAAQ,MAAM,YAAY,eAAe,QAAQ,EAAE,CAAC;AAAA,IACpE,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AACH;;;AC1CA,IAAAC,UAAuB;AAEvB,IAAAC,uBAA4B;AACrB,IAAM,uBAAuB,kBAA2B,qBAAAC,KAAK,KAAK;AAAA,EACvE,WAAW;AAAA,EACX,cAAuB,qBAAAA,KAAK,QAAQ;AAAA,IAClC,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH,CAAC,GAAG,cAAc;AACX,IAAM,sBAAsB,kBAA2B,qBAAAA,KAAK,KAAK;AAAA,EACtE,WAAW;AAAA,EACX,cAAuB,qBAAAA,KAAK,QAAQ;AAAA,IAClC,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH,CAAC,GAAG,aAAa;;;ACdjB,IAAM,YAAY;AAAA,EAChB,wBAAwB;AAAA,EACxB,uBAAuB;AACzB;AACA,IAAM,gBAAgB,SAAS,CAAC,GAAG,SAAS;AAC5C,IAAO,mBAAQ;;;ACER,IAAM,yCAAyC,SAAS,CAAC,GAAG,oCAAoC,kBAAe;AAAA,EACpH,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,YAAY;AACd,CAAC;;;ACbM,IAAM,2BAA2B,MAAM;AAC5C,QAAM,0BAA0B,sBAA+B;AAC/D,QAAM,YAAYC,kBAAiB;AACnC,QAAM,oBAAoB,UAAU,WAAW;AAAA,IAC7C,MAAM;AAAA,EACR,IAAI,CAAC;AACL,SAAO,SAAS,CAAC,GAAG,yBAAyB,iBAAiB;AAChE;;;ACVA,IAAAC,UAAuB;AAKhB,IAAM,8BAA8B,2BAAyB;AAClE,QAAM,SAASC,0BAAyB;AACxC,QAAM,QAAQC,kBAAiB;AAC/B,QAAM,gCAAgC,yBAAkC;AACxE,QAAM,2BAA2B,gBAAgB,QAAQ,oCAAoC;AAC7F,QAAM,8BAA8B,gBAAgB,QAAQ,uCAAuC;AACnG,QAAM,kCAAkC,gBAAgB,QAAQ,oDAAoD;AACpH,SAAa,oBAAY,CAAC,SAAS,UAAU;AAC3C,UAAM,iBAAiB,8BAA8B,SAAS,KAAK;AACnE,QAAI,CAAC,WAAW,EAAE,MAAM,YAAY,wBAAwB;AAC1D,aAAO;AAAA,IACT;AAGA,QAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,aAAa;AAC7D,aAAO;AAAA,IACT;AACA,mBAAe,YAAY,IAAI,QAAQ,QAAQ;AAC/C,UAAM,wBAAwB,4BAA4B,QAAQ,EAAE,KAAK;AAEzE,QAAI,QAAQ,SAAS,WAAW,wBAAwB,GAAG;AACzD,qBAAe,eAAe,IAAI,QAAQ,QAAQ,gBAAgB;AAAA,IACpE;AAGA,QAAI,QAAQ,WAAW,MAAM;AAC3B,qBAAe,cAAc,IAAI,QAAQ,WAAW,qBAAqB,2BAA2B,4BAA4B,QAAQ,MAAM;AAC9I,qBAAe,eAAe,IAAI,gCAAgC,QAAQ,EAAE;AAAA,IAC9E;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,UAAU,uBAAuB,0BAA0B,6BAA6B,iCAAiC,6BAA6B,CAAC;AACnK;;;ACjCA,IAAAC,UAAuB;AAGhB,IAAM,gCAAgC,CAAC,OAAO,OAAO,WAAW;AANvE;AAOE,SAAO,QAAQ,OAAO,gBAAgB;AAAA,IACpC,mCAAmC;AAAA,EACrC;AACA,MAAI;AACJ,MAAI,MAAM,eAAe;AACvB,YAAQ,MAAM;AAAA,EAChB,YAAW,WAAM,iBAAN,mBAAoB,eAAe;AAC5C,YAAQ,MAAM,aAAa;AAAA,EAC7B,OAAO;AACL,YAAQ,CAAC;AAAA,EACX;AACA,SAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACzB,eAAe;AAAA,EACjB,CAAC;AACH;AACO,IAAM,uBAAuB,CAAC,QAAQ,UAAU;AAtBvD;AAuBE,QAAM,gBAAgB,gBAAgB,QAAQ,yBAAyB;AAKvE,QAAM,sBAA4B,oBAAY,CAAC,cAAc,WAAW;AACtE,UAAM,uBAAuB,2CAA2C,MAAM;AAC9E,QAAI,CAAC,OAAO,YAAY,qBAAqB,KAAK,WAAW,KAAK,qBAAqB,MAAM,WAAW,GAAG;AACzG,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,qCAAqC,MAAM;AAClE,UAAM,oBAAoB,8BAA8B,MAAM;AAC9D,UAAM,kBAAkB,4BAA4B,MAAM;AAC1D,UAAM,cAAc,OAAO,QAAQ,mBAAmB,QAAQ;AAG9D,UAAM,aAAa,KAAK,IAAI,OAAO,QAAQ,mBAAmB,QAAQ,UAAU;AAChF,UAAM,cAAc,eAAe,OAAO,QAAQ,EAAE;AACpD,UAAM,aAAa,gBAAgB,OAAO,QAAQ;AAClD,UAAM,yBAAyB,gBAAgB,qBAAqB,KAAK,MAAM;AAC/E,UAAM,0BAA0B,oBAAoB,gBAAgB,gBAAgB,SAAS,qBAAqB,MAAM,MAAM;AAC9H,UAAM,gBAAgB,aAAa;AACnC,QAAI,iBAAiB,cAAc,2BAA2B,YAAY;AACxE,YAAM,OAAO,iBAAiB,cAAc;AAC5C,aAAO,SAAS,CAAC,GAAG,cAAc;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,aAAa,aAAa,wBAAwB;AACpD,YAAM,OAAO,aAAa;AAC1B,aAAO,SAAS,CAAC,GAAG,cAAc;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,qBAA2B,oBAAY,CAAC,iBAAiB,WAAW;AACxE,QAAI,MAAM,sBAAsB;AAC9B,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,OAAO;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,CAAC,GAAG,iBAAiB,uBAAuB;AAAA,EACrD,GAAG,CAAC,MAAM,oBAAoB,CAAC;AAC/B,QAAM,wBAA8B,oBAAY,CAAC,cAAc;AAAA,IAC7D;AAAA,EACF,MAAM;AACJ,UAAM,uBAAuB,2CAA2C,MAAM;AAC9E,QAAI,qBAAqB,KAAK,WAAW,KAAK,qBAAqB,MAAM,WAAW,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,KAAK,SAAS,KAAK,cAAc,qBAAqB,KAAK,QAAQ;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,MAAM,SAAS,GAAG;AACzC,YAAM,iBAAiB,qCAAqC,MAAM;AAClE,YAAM,8BAA8B,eAAe,SAAS,qBAAqB,MAAM;AACvF,aAAO,eAAe,8BAA8B,QAAQ;AAAA,IAC9D;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,2BAAiC,oBAAY,CAAC,WAAW,YAAY;AArF7E,QAAAC;AAsFI,UAAM,wBAAwB,0BAA0B,MAAM;AAC9D,UAAM;AAAA;AAAA,MAEN,CAAC,QAAQ;AAAA,MAET,MAAM,iBAAiB;AAAA,QAEvBA,MAAA,MAAM,iBAAN,gBAAAA,IAAoB,kBAAiB;AAAA,OAEpC,sBAAsB,QAAQ,CAAC,GAAG,SAAS,MAAM,sBAAsB,SAAS,CAAC,GAAG,SAAS;AAAA;AAC9F,QAAI,CAAC,2BAA2B;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,SAAS,CAAC,GAAG,WAAW;AAAA,MAC7B,eAAe;AAAA,IACjB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,gBAAe,WAAM,iBAAN,mBAAoB,aAAa,CAAC;AACnE,QAAM,4BAAkC,oBAAY,CAAC,QAAQ,YAAY;AACvE,UAAM,mBAAmB,QAAQ,eAAe;AAChD,QAAI,oBAAoB,MAAM;AAC5B,eAAS,QAAQ,gBAAgB;AAAA,IACnC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,CAAC;AACX,+BAA6B,QAAQ,mBAAmB,mBAAmB;AAC3E,+BAA6B,QAAQ,cAAc,kBAAkB;AACrE,+BAA6B,QAAQ,kBAAkB,qBAAqB;AAC5E,+BAA6B,QAAQ,eAAe,wBAAwB;AAC5E,+BAA6B,QAAQ,gBAAgB,yBAAyB;AAC9E,SAAO,QAAQ,qBAAqB;AAAA,IAClC,SAAS;AAAA,IACT,WAAW,MAAM;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,eAAe;AAAA,IACf,aAAa;AAAA,EACf,CAAC;AACD,QAAM,YAAkB,oBAAY,CAAC,OAAO,SAAS;AACnD,QAAI,OAAO,QAAQ,eAAe,KAAK,MAAM,MAAM;AACjD;AAAA,IACF;AACA,UAAM,YAAY,SAAS,yBAAyB,QAAQ,yBAAyB,OAAO,yBAAyB;AACrH,UAAM,mBAAmB;AAAA,MACvB,CAAC,IAAI,GAAG,CAAC,GAAI,cAAc,IAAI,KAAK,CAAC,GAAI,KAAK;AAAA,MAC9C,CAAC,SAAS,IAAI,cAAc,SAAS,KAAK,CAAC,GAAG,OAAO,YAAU,WAAW,KAAK;AAAA,IACjF;AACA,WAAO,QAAQ,iBAAiB,gBAAgB;AAAA,EAClD,GAAG,CAAC,QAAQ,aAAa,CAAC;AAC1B,QAAM,cAAoB,oBAAY,WAAS;AAC7C,WAAO,QAAQ,iBAAiB;AAAA,MAC9B,OAAO,cAAc,QAAQ,CAAC,GAAG,OAAO,YAAU,WAAW,KAAK;AAAA,MAClE,QAAQ,cAAc,SAAS,CAAC,GAAG,OAAO,YAAU,WAAW,KAAK;AAAA,IACtE,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,cAAc,MAAM,cAAc,KAAK,CAAC;AACpD,QAAM,mBAAyB,oBAAY,MAAM;AAC/C,WAAO,0BAA0B,MAAM;AAAA,EACzC,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,mBAAyB,oBAAY,sBAAoB;AAC7D,aAAS,QAAQ,gBAAgB;AACjC,WAAO,QAAQ,iCAAiC,gBAAgB;AAAA,EAClE,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,iBAAuB,oBAAY,WAAS;AAChD,UAAM,oBAAoB,cAAc,QAAQ,CAAC;AACjD,QAAI,kBAAkB,SAAS,KAAK,GAAG;AACrC,aAAO,yBAAyB;AAAA,IAClC;AACA,UAAM,qBAAqB,cAAc,SAAS,CAAC;AACnD,QAAI,mBAAmB,SAAS,KAAK,GAAG;AACtC,aAAO,yBAAyB;AAAA,IAClC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,cAAc,MAAM,cAAc,KAAK,CAAC;AAC5C,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,mBAAiB,QAAQ,kBAAkB,QAAQ;AACnD,QAAM,0BAA0B,YAAU;AACxC,QAAI,CAAC,OAAO,QAAQ,OAAO,cAAc,mCAAmC;AAC1E;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,cAAc,WAAW,IAAI;AAC3C,UAAM,qBAAqB,yBAAyB,MAAM;AAmB1D,UAAM,eAAe,mBAAmB,cAAc,KAAK;AAC3D,UAAM,uCAAuC,CAAC,GAAG,OAAO,QAAQ,OAAO,cAAc,iCAAiC;AAGtH,QAAI,IAAI,qCAAqC,UAAU,mBAAiB,kBAAkB,OAAO,KAAK;AAEtG,QAAI,IAAI,IAAI;AAIZ,UAAM,OAAO,qCAAqC,UAAU,mBAAiB,kBAAkB,YAAY;AAC3G,WAAO,QAAQ,IAAI,IAAI,OAAO,IAAI,MAAM;AAEtC,aAAO,OAAO,QAAQ,eAAe,qCAAqC,CAAC,CAAC,GAAG;AAC7E,aAAK;AAAA,MACP;AACA,YAAM,OAAO,qCAAqC,CAAC;AACnD,2CAAqC,CAAC,IAAI,qCAAqC,CAAC;AAChF,2CAAqC,CAAC,IAAI;AAC1C,UAAI;AACJ,UAAI,IAAI;AAAA,IACV;AACA,WAAO,QAAQ,OAAO,cAAc,oCAAoC;AAAA,EAC1E;AACA,eAAa,QAAQ,qBAAqB,uBAAuB;AACjE,EAAM,kBAAU,MAAM;AACpB,QAAI,MAAM,eAAe;AACvB,aAAO,QAAQ,iBAAiB,MAAM,aAAa;AAAA,IACrD;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,aAAa,CAAC;AAClC;AACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,SAAO,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,IACnD,eAAe;AAAA,EACjB,CAAC,CAAC;AACJ;;;ACpOA,IAAAC,UAAuB;AAEhB,IAAM,oCAAoC,CAAC,QAAQ,UAAU;AAClE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,uBAA6B,eAAO,CAAC,CAAC;AAC5C,QAAM,uBAA6B,oBAAY,kBAAgB;AAC7D,QAAI,aAAa,cAAc,WAAW,KAAK,sBAAsB;AACnE,aAAO;AAAA,IACT;AAOA,UAAM,aAAa,OAAO,QAAQ;AAClC,WAAO,QAAQ,QAAQ,SAAS,CAAC,GAAG,YAAY;AAAA,MAC9C,SAAS;AAAA,IACX,CAAC;AACD,UAAM,gBAAgB,iCAAiC,MAAM;AAC7D,WAAO,QAAQ,QAAQ;AAGvB,UAAM,oBAAoB,cAAc;AACxC,UAAM,qBAAqB,cAAc;AACzC,QAAI;AACJ,UAAM,mBAAmB,CAAC,GAAG,mBAAmB,GAAG,kBAAkB;AACrE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,QAAQ,OAAO;AAC1B,QAAI,mCAAmC;AACrC,yBAAmB,IAAI,MAAM,aAAa,cAAc,MAAM,EAAE,KAAK,IAAI;AACzE,YAAM,uCAAuC,CAAC,GAAG,gBAAgB;AAGjE,YAAM,kBAAkB,CAAC,GAAG,aAAa,aAAa;AAItD,2BAAqB,QAAQ,QAAQ,WAAS;AAC5C,YAAI,CAAC,iBAAiB,SAAS,KAAK,KAAK,aAAa,OAAO,KAAK,GAAG;AAEnE,gBAAM,QAAQ,kCAAkC,QAAQ,KAAK;AAC7D,2BAAiB,KAAK,IAAI;AAC1B,+CAAqC,KAAK,IAAI;AAE9C,0BAAgB,OAAO,gBAAgB,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC1D;AAAA,MACF,CAAC;AAGD,uBAAiB,QAAQ,WAAS;AAChC,YAAI,QAAQ,kCAAkC,QAAQ,KAAK;AAK3D,YAAI,UAAU,MAAM,SAAS,qCAAqC,QAAQ;AACxE,kBAAQ,aAAa,cAAc,QAAQ,KAAK;AAAA,QAClD;AAIA,YAAI,qCAAqC,KAAK,MAAM,MAAM;AACxD,kBAAQ;AACR,iBAAO,qCAAqC,KAAK,MAAM,MAAM;AAC3D,qBAAS;AAAA,UACX;AAAA,QACF;AACA,yBAAiB,KAAK,IAAI;AAC1B,6CAAqC,KAAK,IAAI;AAE9C,wBAAgB,OAAO,gBAAgB,QAAQ,KAAK,GAAG,CAAC;AAAA,MAC1D,CAAC;AAID,UAAI,IAAI;AACR,sBAAgB,QAAQ,WAAS;AAC/B,eAAO,qCAAqC,CAAC,MAAM,MAAM;AACvD,eAAK;AAAA,QACP;AACA,6CAAqC,CAAC,IAAI;AAC1C,yBAAiB,CAAC,IAAI;AAAA,MACxB,CAAC;AACD,aAAO,QAAQ,OAAO,cAAc,oCAAoC;AAAA,IAC1E,OAAO;AACL,yBAAmB,CAAC,GAAG,aAAa,aAAa;AACjD,aAAO,QAAQ,OAAO,cAAc,oCAAoC,CAAC,GAAG,aAAa,aAAa;AAAA,IACxG;AACA,yBAAqB,UAAU;AAC/B,UAAM,gBAAgB,iBAAiB,OAAO,WAAS;AACrD,aAAO,CAAC,kBAAkB,SAAS,KAAK,KAAK,CAAC,mBAAmB,SAAS,KAAK;AAAA,IACjF,CAAC;AACD,WAAO,SAAS,CAAC,GAAG,cAAc;AAAA,MAChC,eAAe,CAAC,GAAG,mBAAmB,GAAG,eAAe,GAAG,kBAAkB;AAAA,IAC/E,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,oBAAoB,CAAC;AACjC,+BAA6B,QAAQ,kBAAkB,oBAAoB;AAC3E,QAAM,iBAAuB,oBAAY,CAAC,cAAc,UAAU,OAAO,QAAQ,eAAe,KAAK,GAAG,CAAC,MAAM,CAAC;AAChH,+BAA6B,QAAQ,kBAAkB,cAAc;AACvE;;;ACrGA,IAAAC,UAAuB;;;ACHR,SAAR,cAA+B,MAAM;AAC1C,SAAO,QAAQ,KAAK,iBAAiB;AACvC;;;ACDO,IAAM,4BAA4B,mBAAmB,WAAS,MAAM,aAAa;AACjF,IAAM,mCAAmC,eAAe,2BAA2B,mBAAiB,cAAc,OAAO;;;AFOhI,IAAM,6BAA6B;AACnC,IAAM,8BAA8B;AACpC,IAAM,0BAA0B,CAAC,oBAAoB,oBAAoB;AACvE,SAAO,mBAAmB,KAAK,gBAAgB,IAAI,8BAA8B;AACnF;AACA,IAAM,2BAA2B,CAAC,oBAAoB,oBAAoB,mBAAmB,MAAM,gBAAgB,KAAK,mBAAmB,MAAM,gBAAgB;AACjK,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,sBAAsB,CAAC,wBAAwB;AAAA,EACjD;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACO,IAAM,gCAAgC,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,EACxE,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AACF,CAAC;AAKM,IAAM,uBAAuB,CAAC,QAAQ,UAAU;AACrD,QAAM,SAAS,cAAc,QAAQ,sBAAsB;AAC3D,QAAM,cAAoB,eAAO,IAAI;AACrC,QAAM,iBAAuB,eAAO;AAAA,IAClC,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,QAAM,oBAA0B,eAAO,IAAI;AAC3C,QAAM,mBAAyB,eAAO,CAAC,CAAC;AACxC,QAAM,yBAA+B,eAAO,MAAS;AACrD,QAAM,aAAa;AAAA,IACjB,SAAS,MAAM;AAAA,EACjB;AACA,QAAM,UAAUA,mBAAkB,UAAU;AAC5C,QAAM,QAAQ,OAAO;AACrB,EAAM,kBAAU,MAAM;AACpB,WAAO,MAAM;AACX,mBAAa,uBAAuB,OAAO;AAAA,IAC7C;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,gBAAsB,oBAAY,CAAC,QAAQ,UAAU;AACzD,UAAM,eAAe,iCAAiC,MAAM;AAC5D,QAAI,MAAM,wBAAwB,CAAC,cAAc;AAC/C;AAAA,IACF;AACA,WAAO,MAAM,kBAAkB;AAC/B,UAAM,eAAe;AAGrB,UAAM,gBAAgB;AACtB,iBAAa,uBAAuB,OAAO;AAG3C,QAAI,YAAY,QAAQ,UAAU,SAAS,QAAQ,oBAAoB,GAAG;AACxE,kBAAY,QAAQ,UAAU,OAAO,QAAQ,oBAAoB;AAAA,IACnE;AACA,gBAAY,UAAU;AAGtB,QAAI,MAAM,aAAa,eAAe,UAAU,CAAC,MAAM,oCAAoC;AAEzF,aAAO,QAAQ,eAAe,cAAc,kBAAkB,OAAO;AACrE,wBAAkB,UAAU;AAAA,IAC9B,OAAO;AAEL,YAAM,0BAA0B;AAAA,QAC9B,QAAQ,OAAO,QAAQ,UAAU,YAAY;AAAA,QAC7C,aAAa,OAAO,QAAQ,uCAAuC,YAAY;AAAA,QAC/E,UAAU,kBAAkB;AAAA,MAC9B;AACA,aAAO,QAAQ,aAAa,qBAAqB,uBAAuB;AAAA,IAC1E;AACA,WAAO,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,MACnD,eAAe,SAAS,CAAC,GAAG,MAAM,eAAe;AAAA,QAC/C,SAAS;AAAA,MACX,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,QAAQ,MAAM,sBAAsB,MAAM,oCAAoC,QAAQ,QAAQ,oBAAoB,CAAC;AACvH,QAAM,kBAAwB,oBAAY,CAAC,QAAQ,UAAU;AAC3D,QAAI,MAAM,wBAAwB,OAAO,OAAO,gBAAgB;AAC9D;AAAA,IACF;AACA,WAAO,MAAM,sBAAsB,OAAO,KAAK,EAAE;AAGjD,UAAM,gBAAgB;AACtB,gBAAY,UAAU,MAAM;AAC5B,gBAAY,QAAQ,UAAU,IAAI,QAAQ,oBAAoB;AAC9D,UAAM,qBAAqB,kBAAgB;AACzC,kBAAY,QAAQ,oBAAoB,WAAW,kBAAkB;AACrE,aAAO,QAAQ,aAAa,6BAA6B,QAAQ,YAAY;AAAA,IAC/E;AACA,gBAAY,QAAQ,iBAAiB,WAAW,kBAAkB;AAClE,QAAI,MAAM,cAAc;AACtB,YAAM,aAAa,gBAAgB;AAAA,IACrC;AACA,WAAO,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,MACnD,eAAe,SAAS,CAAC,GAAG,MAAM,eAAe;AAAA,QAC/C,SAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH,CAAC,CAAC;AACF,2BAAuB,UAAU,WAAW,MAAM;AAChD,kBAAY,QAAQ,UAAU,OAAO,QAAQ,oBAAoB;AAAA,IACnE,CAAC;AACD,sBAAkB,UAAU,OAAO,QAAQ,eAAe,OAAO,OAAO,KAAK;AAC7E,UAAM,0BAA0B,OAAO,QAAQ,mBAAmB,OAAO,KAAK;AAC9E,UAAM,cAAc,kBAAkB;AACtC,UAAM,aAAa,OAAO,QAAQ,cAAc;AAChD,UAAM,eAAe,OAAO,QAAQ,mBAAmB;AACvD,UAAM,8BAA8B,cAAY;AAC9C,YAAM,QAAQ,WAAW,QAAQ,EAAE;AACnC,aAAO,OAAO,QAAQ,mBAAmB,KAAK;AAAA,IAChD;AAGA,QAAI,kBAAkB;AACtB,4BAAwB,QAAQ,aAAW;AAjI/C;AAkIM,UAAI,GAAC,kBAAa,OAAO,MAApB,mBAAuB,iBAAgB;AAE1C,YAAI,cAAc,KAAK,4BAA4B,cAAc,CAAC,EAAE,SAAS,OAAO,GAAG;AACrF,4BAAkB;AAAA,QACpB,WAAW,cAAc,IAAI,WAAW,UAAU,4BAA4B,cAAc,CAAC,EAAE,SAAS,OAAO,GAAG;AAChH,4BAAkB;AAAA,QACpB;AAAA,MACF;AAAA,IACF,CAAC;AACD,qBAAiB,UAAU,CAAC;AAC5B,aAAS,gBAAgB,GAAG,gBAAgB,WAAW,QAAQ,iBAAiB,GAAG;AACjF,YAAM,YAAY,iBAAiB,cAAc,gBAAgB,IAAI;AACrE,YAAM,aAAa,gBAAgB,cAAc,gBAAgB,gBAAgB;AACjF,UAAI,oBAAoB,MAAM;AAE5B,YAAI,aAAa;AACjB,YAAI,aAAa,KAAK,4BAA4B,SAAS,EAAE,SAAS,eAAe,GAAG;AACtF,uBAAa;AAAA,QACf,WAAW,aAAa,WAAW,UAAU,4BAA4B,UAAU,EAAE,SAAS,eAAe,GAAG;AAC9G,uBAAa;AAAA,QACf;AACA,YAAI,CAAC,YAAY;AACf,2BAAiB,QAAQ,aAAa,IAAI;AAAA,QAC5C;AAAA,MACF;AAGA,UAAI,aAAa,KAAK,aAAa,WAAW,QAAQ;AACpD,oCAA4B,UAAU,EAAE,QAAQ,aAAW;AA9JnE;AA+JU,cAAI,4BAA4B,SAAS,EAAE,SAAS,OAAO,GAAG;AAC5D,gBAAI,CAAC,wBAAwB,SAAS,OAAO,GAAG;AAE9C,kBAAI,GAAC,kBAAa,OAAO,MAApB,mBAAuB,iBAAgB;AAC1C,iCAAiB,QAAQ,aAAa,IAAI;AAAA,cAC5C;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,sBAAsB,QAAQ,sBAAsB,QAAQ,MAAM,CAAC;AAC7E,QAAM,kBAAwB,oBAAY,CAAC,QAAQ,UAAU;AAC3D,UAAM,eAAe;AAGrB,UAAM,gBAAgB;AAAA,EACxB,GAAG,CAAC,CAAC;AACL,QAAM,iBAAuB,oBAAY,CAAC,QAAQ,UAAU;AAC1D,UAAM,eAAe,iCAAiC,MAAM;AAC5D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AACA,WAAO,MAAM,qBAAqB,OAAO,KAAK,EAAE;AAChD,UAAM,eAAe;AAGrB,UAAM,gBAAgB;AACtB,UAAM,cAAc;AAAA,MAClB,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACX;AACA,QAAI,OAAO,UAAU,gBAAgB,yBAAyB,eAAe,SAAS,WAAW,GAAG;AAClG,YAAM,iBAAiB,OAAO,QAAQ,eAAe,OAAO,OAAO,KAAK;AACxE,YAAM,wBAAwB,OAAO,QAAQ,eAAe,OAAO,OAAO,IAAI;AAC9E,YAAM,YAAY,OAAO,QAAQ,UAAU,OAAO,KAAK;AACvD,YAAM,eAAe,OAAO,QAAQ,eAAe,cAAc,KAAK;AACtE,YAAM,iBAAiB,OAAO,QAAQ,kBAAkB;AACxD,YAAM,aAAa,OAAO,QAAQ,cAAc;AAChD,YAAM,uBAAuB,wBAAwB,eAAe,SAAS,WAAW;AACxF,YAAM,eAAe,yBAAyB,+BAA+B,QAAQ,eAAe,iBAAiB,iBAAiB;AACtI,YAAM,gBAAgB,yBAAyB,gCAAgC,QAAQ,iBAAiB,eAAe,eAAe;AACtI,UAAI,gBAAgB,eAAe;AACjC,YAAI;AACJ,YAAI,6BAA6B;AACjC,YAAI,CAAC,UAAU,gBAAgB;AAC7B,2BAAiB;AAAA,QACnB,WAAW,cAAc;AACvB,2BAAiB,wBAAwB,KAAK,CAAC,eAAe,wBAAwB,CAAC,EAAE;AAAA,QAC3F,OAAO;AACL,2BAAiB,wBAAwB,eAAe,SAAS,KAAK,CAAC,eAAe,wBAAwB,CAAC,EAAE;AAAA,QACnH;AACA,YAAI,iBAAiB,QAAQ,cAAc,GAAG;AAC5C,cAAI;AACJ,cAAI,kBAAkB,iBAAiB;AACvC,cAAI,cAAc;AAChB,qCAAyB,wBAAwB,IAAI,eAAe,wBAAwB,CAAC,EAAE,QAAQ;AACvG,mBAAO,kBAAkB,KAAK,WAAW,eAAe,EAAE,UAAU,0BAA0B,iBAAiB,QAAQ,eAAe,GAAG;AACvI,4CAA8B;AAC9B,gCAAkB,iBAAiB;AAAA,YACrC;AAAA,UACF,OAAO;AACL,qCAAyB,wBAAwB,IAAI,eAAe,SAAS,eAAe,wBAAwB,CAAC,EAAE,QAAQ;AAC/H,mBAAO,kBAAkB,WAAW,SAAS,KAAK,WAAW,eAAe,EAAE,UAAU,0BAA0B,iBAAiB,QAAQ,eAAe,GAAG;AAC3J,4CAA8B;AAC9B,gCAAkB,iBAAiB;AAAA,YACrC;AAAA,UACF;AACA,cAAI,iBAAiB,QAAQ,eAAe,KAAK,WAAW,eAAe,EAAE,UAAU,wBAAwB;AAE7G,6BAAiB;AAAA,UACnB;AAAA,QACF;AACA,cAAM,0BAA0B,OAAO,QAAQ,6BAA6B,kBAAkB,gBAAgB;AAAA,UAC5G,aAAa;AAAA,QACf,CAAC;AACD,YAAI,yBAAyB;AAC3B,iBAAO,QAAQ,eAAe,cAAc,iBAAiB,0BAA0B;AAAA,QACzF;AAAA,MACF;AACA,qBAAe,UAAU;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,QAAQ,QAAQ,KAAK,CAAC;AAC1B,EAAM,kBAAU,MAAM;AACpB,QAAI,CAAC,MAAM,oCAAoC;AAC7C,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AACA,UAAM,MAAM,cAAc,OAAO,QAAQ,eAAe,OAAO;AAC/D,UAAM,WAAW,WAAS;AACxB,UAAI,MAAM,cAAc;AAGtB,cAAM,eAAe;AACrB,cAAM,aAAa,aAAa;AAAA,MAClC;AAAA,IACF;AACA,QAAI,iBAAiB,YAAY,QAAQ;AACzC,WAAO,MAAM;AACX,UAAI,oBAAoB,YAAY,QAAQ;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,kCAAkC,CAAC;AACrD,eAAa,QAAQ,yBAAyB,eAAe;AAC7D,eAAa,QAAQ,yBAAyB,eAAe;AAC7D,eAAa,QAAQ,wBAAwB,cAAc;AAC3D,eAAa,QAAQ,6BAA6B,aAAa;AAC/D,eAAa,QAAQ,iBAAiB,eAAe;AACrD,eAAa,QAAQ,gBAAgB,cAAc;AACnD,uBAAqB,QAAQ,qBAAqB,MAAM,mBAAmB;AAC7E;;;AGtQA,IAAAC,UAAuB;;;ACEhB,IAAM,kCAAkC,SAAS,CAAC,GAAG,qBAAqB;AAAA,EAC/E,MAAM;AAAA,EACN,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa,CAAC,OAAO,KAAK,QAAQ,WAAW;AAC3C,UAAM,QAAQ,kBAAkB,QAAQ,GAAG;AAC3C,UAAM,UAAU,oBAAoB,QAAQ,KAAK;AACjD,YAAO,mCAAS,UAAS,YAAW,mCAAS,UAAS,SAAS,QAAQ,cAAc;AAAA,EACvF;AACF,CAAC;AAEM,IAAM,oDAAoD;AAAA,EAC/D,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AACb;;;ACxBO,SAAS,cAAc,SAAS;AACrC,QAAM,8BAA8B,CAAC;AACrC,QAAM,QAAQ,OAAO,OAAO,OAAO;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,UAAM,OAAO,MAAM,CAAC;AACpB,gCAA4B,KAAK,EAAE,IAAI,KAAK,uBAAuB;AAAA,EACrE;AACA,SAAO;AAAA,IACL,oBAAoB,wBAAwB;AAAA,IAC5C;AAAA,IACA,+BAA+B,wBAAwB;AAAA,EACzD;AACF;AACO,SAAS,YAAY,SAAS;AACnC,SAAO,uBAAuB,SAAS,oBAAoB,KAAK;AAClE;AAMO,SAAS,cAAc,OAAO,oBAAoB;AAvBzD;AAwBE,MAAI,mBAAmB,QAAQ,SAAS,UAAU,GAAC,wBAAmB,iBAAnB,mBAAkC,WAAU,mBAAmB,aAAa,KAAK,EAAE,QAAQ,KAAK,EAAE,UAAU,mBAAmB,aAAa,KAAK,IAAI;AACtM,WAAO,CAAC;AAAA,EACV;AACA,SAAO,mBAAmB,aAAa,KAAK,EAAE,QAAQ,CAAC;AACzD;;;AC3BA,IAAAC,UAAuB;;;ACAhB,IAAM,8BAA8B,mBAAmB,WAAS,MAAM,UAAU;AAChF,IAAM,gCAAgC,eAAe,6BAA6B,gBAAc,WAAW,OAAO;AAClH,IAAM,kCAAkC,eAAe,6BAA6B,CAAC,YAAY,OAAO,WAAW,QAAQ,EAAE,KAAK,KAAK;AACvI,IAAM,+BAA+B,eAAe,6BAA6B,gBAAc,WAAW,MAAM;AAChH,IAAM,8BAA8B,eAAe,6BAA6B,CAAC,YAAY,OAAO,WAAW,OAAO,EAAE,CAAC;;;ADGhI,IAAAC,uBAA2C;AAC3C,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,sBAAsB;AAAA,IAC7B,QAAQ,CAAC,4BAA4B;AAAA,IACrC,kBAAkB,CAAC,sCAAsC;AAAA,EAC3D;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,SAAS,6BAA6B,OAAO;AApB7C;AAqBE,QAAM,SAASC,0BAAyB;AACxC,QAAM,YAAYC,kBAAiB;AACnC,QAAM,UAAUF,mBAAkB,SAAS;AAC3C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,gBAAgB,QAAQ,iCAAiC,EAAE;AACjF,QAAM,QAAQ,gBAAgB,QAAQ,6BAA6B,EAAE;AACrE,QAAM,cAAc,WAAS;AAC3B,QAAI,CAAC,QAAQ,kBAAkB;AAE7B,aAAO,QAAQ,WAAW,UAAU,EAAE;AAAA,IACxC,OAAO;AAEL,aAAO,QAAQ,wBAAwB,IAAI,KAAK;AAChD,aAAO,QAAQ,mBAAmB,EAAE;AAAA,IACtC;AACA,WAAO,QAAQ,aAAa,IAAI,KAAK;AACrC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,OAAO,QAAQ,mBAAmB,UAAU,MAAM,uBAAuB,UAAU,MAAM;AAC/F,MAAI,eAAe;AACjB,eAAoB,qBAAAG,KAAK,OAAO;AAAA,MAC9B,WAAW,QAAQ;AAAA,MACnB,cAAuB,qBAAAA,KAAK,UAAU,MAAM,sBAAsB;AAAA,QAChE,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,oBAAoB,MAAM,kBAAkB,QAAiB,qBAAAA,KAAK,UAAU,MAAM,gBAAgB,SAAS;AAAA,IAChH,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,cAAc,QAAQ,mBAAmB,OAAO,QAAQ,cAAc,kBAAkB,IAAI,OAAO,QAAQ,cAAc,gBAAgB;AAAA,EAC3I,IAAG,4CAAW,cAAX,mBAAsB,gBAAgB;AAAA,IACvC,cAAuB,qBAAAA,KAAK,UAAU,MAAM,aAAa;AAAA,MACvD,QAAO,+BAAO,YAAW;AAAA,MACzB,cAAuB,qBAAAA,KAAK,UAAU,MAAM,WAAW;AAAA,QACrD,SAAS;AAAA,QACT,OAAO;AAAA,QACP,WAAW,CAAC;AAAA,QACZ,cAAuB,qBAAAA,KAAK,MAAM;AAAA,UAChC,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,CAAC,IAAI;AACR;AACO,SAAS,mCAAmC,OAAO;AAzE1D;AA0EE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,YAAYD,kBAAiB;AACnC,QAAM,SAASD,0BAAyB;AACxC,QAAM,MAAM,gBAAgB,QAAQ,iBAAiB,EAAE;AACvD,QAAM,UAAUD,mBAAkB,SAAS;AAC3C,MAAI,kBAAkB;AACtB,MAAI,KAAK;AACP,wBAAkB,qBAAU,eAAV,mBAAsB,qBAAtB,4BAAyC,SAAQ;AAAA,EACrE;AACA,aAAoB,qBAAAI,MAAM,OAAO;AAAA,IAC/B,WAAW,QAAQ;AAAA,IACnB,OAAO;AAAA,MACL,YAAY,KAAK,QAAQ,QAAQ,QAAQ,gBAAgB;AAAA,IAC3D;AAAA,IACA,UAAU,KAAc,qBAAAD,KAAK,OAAO;AAAA,MAClC,WAAW,QAAQ;AAAA,MACnB,cAAuB,qBAAAA,KAAK,8BAA8B;AAAA,QACxD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC,OAAgB,qBAAAC,MAAM,QAAQ;AAAA,MAC7B,UAAU,CAAC,mBAAmB,SAAY,QAAQ,cAAc,gBAAgB,CAAC,uBAAuB,kBAAkB,IAAI,KAAK,eAAe,MAAM,EAAE;AAAA,IAC5J,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;;;AE1GO,IAAM,wBAAwB,UAAQ;AAC3C,QAAM,UAAU,KAAK,IAAI,sBAAoB,GAAG,iBAAiB,KAAK,IAAI,iBAAiB,GAAG,EAAE,EAAE,KAAK,GAAG;AAC1G,SAAO,sBAAsB,OAAO;AACtC;AACO,IAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,CAAC;AACd,MAAI,OAAO,KAAK,EAAE;AAClB,SAAO,KAAK,OAAO,oBAAoB;AACrC,SAAK,KAAK;AAAA,MACR,OAAO,KAAK,SAAS,SAAS,OAAO,KAAK;AAAA,MAC1C,KAAK,KAAK;AAAA,IACZ,CAAC;AACD,WAAO,KAAK,KAAK,MAAM;AAAA,EACzB;AACA,OAAK,QAAQ;AACb,SAAO;AACT;AACO,IAAM,8BAA8B,CAAC,MAAM,+BAA+B,6BAA6B;AAC5G,MAAI;AACJ,MAAI,KAAK,OAAO,oBAAoB;AAClC,uBAAmB;AAAA,EACrB,WAAW,0BAA0B;AACnC,uBAAmB,yBAAyB,IAAI;AAAA,EAClD,OAAO;AACL,uBAAmB,kCAAkC,MAAM,gCAAgC,KAAK;AAAA,EAClG;AACA,SAAO;AACT;AACO,IAAM,8BAA8B,CAAC,MAAM,+BAA+B,6BAA6B;AAC5G,QAAM,mBAAmB,4BAA4B,MAAM,+BAA+B,wBAAwB;AAClH,OAAK,mBAAmB;AACxB,SAAO;AACT;AAKO,IAAM,mBAAmB,CAAC,MAAM,MAAM,YAAY,iBAAiB;AA1C1E;AA4CE,OAAK,KAAK,EAAE,IAAI;AAGhB,aAAW,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK;AAGzD,QAAM,aAAa,KAAK,KAAK,MAAM;AACnC,MAAI,KAAK,SAAS,WAAW,KAAK,SAAS,QAAQ;AAGjD,UAAM,oBAAoB,KAAK,iBAAiB;AAChD,UAAM,kBAAkB,KAAK,eAAe;AAC5C,UAAM,iBAAgB,gBAAW,qBAAX,mBAA8B;AACpD,QAAI,iBAAiB,QAAQ,aAAa,WAAW,EAAE,MAAM,KAAK,WAAW,EAAE,GAAG;AAChF,iBAAW,WAAW,CAAC,GAAG,WAAW,UAAU,KAAK,EAAE;AAAA,IACxD,OAAO;AACL,iBAAW,SAAS,KAAK,KAAK,EAAE;AAAA,IAClC;AACA,QAAI,iBAAiB,MAAM;AACzB,iBAAW,iBAAiB,iBAAiB,IAAI;AAAA,QAC/C,CAAC,gBAAgB,SAAS,CAAC,GAAG,KAAK;AAAA,MACrC;AAAA,IACF,OAAO;AACL,oBAAc,gBAAgB,SAAS,CAAC,IAAI,KAAK;AAAA,IACnD;AAAA,EACF,WAAW,KAAK,SAAS,UAAU;AAGjC,eAAW,WAAW,KAAK;AAAA,EAC7B;AACF;AAKO,IAAM,qBAAqB,CAAC;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AAEJ,SAAO,KAAK,KAAK,EAAE;AAGnB,QAAM,YAAY,KAAK;AACvB,QAAM,mBAAmB,WAAW,SAAS;AAC7C,MAAI,qBAAqB,GAAG;AAC1B,WAAO,WAAW,SAAS;AAAA,EAC7B,OAAO;AACL,eAAW,SAAS,IAAI,mBAAmB;AAAA,EAC7C;AAGA,QAAM,aAAa,KAAK,KAAK,MAAM;AAGnC,MAAI,KAAK,SAAS,UAAU;AAC1B,SAAK,WAAW,EAAE,IAAI,SAAS,CAAC,GAAG,YAAY;AAAA,MAC7C,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAGK;AACH,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,UAAM,cAAc,KAAK,eAAe;AAGxC,UAAM,WAAW,WAAW,SAAS,OAAO,aAAW,YAAY,KAAK,EAAE;AAC1E,UAAM,mBAAmB,WAAW;AACpC,WAAO,iBAAiB,aAAa,EAAE,YAAY,SAAS,CAAC;AAC7D,SAAK,WAAW,EAAE,IAAI,SAAS,CAAC,GAAG,YAAY;AAAA,MAC7C;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAKO,IAAM,oCAAoC,CAAC;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AAEJ,OAAK,SAAS,QAAQ,aAAW;AAC/B,SAAK,OAAO,IAAI,SAAS,CAAC,GAAG,KAAK,OAAO,GAAG;AAAA,MAC1C,QAAQ,YAAY;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AAGD,qBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAGD,QAAM,YAAY,SAAS,CAAC,GAAG,MAAM,WAAW;AAChD,mBAAiB,WAAW,MAAM,YAAY,YAAY;AAC5D;AACO,IAAM,6BAA6B,OAAO;AAAA,EAC/C,OAAO,CAAC;AAAA,EACR,UAAU,SAAS,QAAQ;AACzB,QAAI,CAAC,KAAK,MAAM,OAAO,GAAG;AACxB,WAAK,MAAM,OAAO,IAAI,CAAC;AAAA,IACzB;AACA,SAAK,MAAM,OAAO,EAAE,MAAM,IAAI;AAAA,EAChC;AACF;AACO,IAAM,uBAAuB,CAAC;AAAA,EACnC;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,oBAAoB;AACvB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,oBAAoB,CAAC;AAC3B,QAAM,iBAAiB,CAAC,MAAM,yBAAyB;AACrD,UAAM,qBAAqB,mBAAmB,KAAK,EAAE,MAAM;AAC3D,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,aAAW;AAC/B,cAAM,YAAY,KAAK,OAAO;AAC9B,uBAAe,WAAW,wBAAwB,CAAC,CAAC,KAAK,gBAAgB;AAAA,MAC3E,CAAC;AAAA,IACH;AACA,UAAM,YAAY,sBAAsB;AACxC,QAAI,CAAC,WAAW;AACd,wBAAkB,KAAK,EAAE,IAAI;AAAA,IAC/B;AAGA,QAAI,KAAK,SAAS,WAAW,KAAK,YAAY,MAAM;AAClD,YAAM,kBAAkB,sBAAsB,wBAAwB,CAAC,CAAC,KAAK;AAC7E,UAAI,CAAC,iBAAiB;AACpB,0BAAkB,KAAK,QAAQ,IAAI;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,OAAO,OAAO,IAAI;AAChC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,KAAK,UAAU,GAAG;AACpB,qBAAe,MAAM,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;;;AC5LO,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AApBN;AAqBE,MAAI,eAAe;AACnB,WAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS,GAAG;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,KAAK;AACd,UAAM,wBAAwB,SAAS;AACvC,UAAM,sBAAsB,OAAO;AACnC,UAAM,iCAAgC,gBAAK,YAAY,EAAE,qBAAnB,mBAAsC,2BAAtC,mBAA+D,oBAAoB,SAAS;AAIlI,QAAI,UAAU,KAAK,SAAS,GAAG;AAG7B,UAAI,iCAAiC,MAAM;AACzC,YAAI;AACJ,YAAI,wBAAwB,UAAa,wBAAwB,GAAG;AAClE,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,QAAQ;AAAA,YACR,MAAM,KAAK,IAAI,UAAQ,KAAK,GAAG;AAAA,YAC/B;AAAA,YACA,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU,CAAC;AAAA,YACX,kBAAkB,CAAC;AAAA,YACnB,kBAAkB;AAAA,YAClB;AAAA,UACF;AACA,gBAAM,sBAAsB,4BAA4B,MAAM,+BAA+B,wBAAwB;AACrH,cAAI,qBAAqB;AACvB,2DAAe,IAAI;AAAA,UACrB;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,aAAa;AAAA,UACf;AAAA,QACF;AACA,qEAAsB,UAAU,cAAc;AAC9C,yBAAiB,MAAM,MAAM,YAAY,YAAY;AAAA,MACvD,OAAO;AACL,cAAM,8BAA8B,KAAK,6BAA6B;AAItE,YAAI,4BAA4B,SAAS,WAAW,4BAA4B,iBAAiB;AAC/F,uEAAsB,UAAU,cAAc;AAC9C,uEAAsB,UAAU,cAAc;AAC9C,4CAAkC;AAAA,YAChC;AAAA,YACA;AAAA,YACA;AAAA,YACA,MAAM;AAAA,YACN,aAAa;AAAA,cACX;AAAA,cACA,iBAAiB;AAAA,YACnB;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AAGL,6DAAkB,+BAA+B,IAAI;AAAA,QACvD;AAAA,MACF;AAAA,IACF,WAMS,iCAAiC,MAAM;AAC9C,YAAM,SAAS,sBAAsB,KAAK,MAAM,GAAG,QAAQ,CAAC,CAAC;AAC7D,YAAM,yBAAyB;AAAA,QAC7B,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,QACA,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,UAAU,CAAC;AAAA,QACX,kBAAkB,CAAC;AAAA,QACnB,kBAAkB;AAAA,MACpB;AACA,mEAAsB,UAAU,cAAc;AAC9C,uBAAiB,4BAA4B,wBAAwB,+BAA+B,wBAAwB,GAAG,MAAM,YAAY,YAAY;AAC7J,qBAAe;AAAA,IACjB,OAGK;AACH,YAAM,mBAAmB,KAAK,6BAA6B;AAG3D,UAAI,iBAAiB,SAAS,SAAS;AACrC,cAAM,YAAY;AAAA,UAChB,MAAM;AAAA,UACN,IAAI,iBAAiB;AAAA,UACrB,QAAQ,iBAAiB;AAAA,UACzB,OAAO,iBAAiB;AAAA,UACxB,iBAAiB;AAAA,UACjB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU,CAAC;AAAA,UACX,kBAAkB,CAAC;AAAA,UACnB,kBAAkB;AAAA,QACpB;AACA,aAAK,6BAA6B,IAAI,4BAA4B,WAAW,+BAA+B,wBAAwB;AAAA,MACtI;AACA,qBAAe,iBAAiB;AAAA,IAClC;AAAA,EACF;AACF;;;ACtIO,IAAM,gBAAgB,YAAU;AACrC,QAAM,aAAa,CAAC;AACpB,QAAM,OAAO;AAAA,IACX,CAAC,kBAAkB,GAAG,eAAe;AAAA,EACvC;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK,GAAG;AAC/C,UAAM,OAAO,OAAO,MAAM,CAAC;AAC3B,eAAW,KAAK,KAAK,EAAE;AACvB,wBAAoB;AAAA,MAClB;AAAA,MACA,cAAc,OAAO;AAAA,MACrB,IAAI,KAAK;AAAA,MACT,MAAM,KAAK;AAAA,MACX,qBAAqB,KAAK;AAAA,MAC1B,iBAAiB,OAAO;AAAA,MACxB;AAAA,MACA,0BAA0B,OAAO;AAAA,MACjC,+BAA+B,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAc,OAAO;AAAA,IACrB;AAAA,IACA,eAAe,MAAM,KAAK,aAAa;AAAA,EACzC;AACF;;;ACnCO,IAAI,oBAAgC,SAAUC,mBAAkB;AACrE,EAAAA,kBAAiB,SAAS,IAAI;AAC9B,EAAAA,kBAAiB,YAAY,IAAI;AACjC,SAAOA;AACT,GAAE,CAAC,CAAC;AAOG,IAAM,4BAA4B,YAAU;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,qBAAqB,CAAC;AAC5B,QAAM,8BAA8B,CAAC;AACrC,QAAM,gCAAgC,CAAC;AACvC,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB;AAAA,IACpB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,EAC5B;AACA,QAAM,iBAAiB,CAAC,MAAM,yBAAyB,yBAAyB;AAC9E,UAAM,oBAAoB,4BAA4B,KAAK,QAAQ;AACnE,QAAI;AACJ,QAAI,mBAAmB;AACrB,0BAAoB;AAAA,IACtB,WAAW,CAAC,wBAAwB,KAAK,SAAS,UAAU;AAC1D,0BAAoB;AAAA,IACtB,OAAO;AACL,YAAM,MAAM,OAAO,QAAQ,OAAO,KAAK,EAAE;AACzC,2BAAqB,KAAK,QAAW,aAAa;AAClD,0BAAoB,gBAAgB,CAAC,cAAc,kBAAkB,GAAG,CAAC,cAAc,wBAAwB,GAAG,OAAO,aAAa,OAAO,QAAQ,WAAW;AAAA,IAClK;AACA,QAAI,wBAAwB;AAC5B,QAAI,0BAA0B;AAC9B,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,aAAW;AAC/B,cAAM,YAAY,QAAQ,OAAO;AACjC,cAAM,mBAAmB,eAAe,WAAW,qBAAqB,yBAAyB,wBAAwB,CAAC,CAAC,KAAK,gBAAgB;AAChJ,mCAA2B;AAC3B,YAAI,mBAAmB,GAAG;AACxB,mCAAyB;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI;AACJ,YAAQ,mBAAmB;AAAA,MACzB,KAAK,MACH;AACE,4BAAoB;AACpB;AAAA,MACF;AAAA,MACF,KAAK,OACH;AACE,4BAAoB,0BAA0B;AAC9C;AAAA,MACF;AAAA,MACF,SACE;AACE,4BAAoB;AACpB;AAAA,MACF;AAAA,IACJ;AACA,QAAI,CAAC,mBAAmB;AACtB,yBAAmB,KAAK,EAAE,IAAI;AAAA,IAChC;AACA,QAAI,CAAC,mBAAmB;AACtB,aAAO;AAAA,IACT;AACA,gCAA4B,KAAK,EAAE,IAAI;AACvC,kCAA8B,KAAK,EAAE,IAAI;AACzC,QAAI,KAAK,SAAS,UAAU;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,0BAA0B;AAAA,EACnC;AACA,QAAM,QAAQ,OAAO,OAAO,OAAO;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,KAAK,UAAU,GAAG;AACpB,qBAAe,MAAM,MAAM,IAAI;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC1FA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,qBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,KAAK,SAAS,WAAW,KAAK,YAAY,MAAM;AAClD,uBAAmB;AAAA,MACjB,MAAM,KAAK,KAAK,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,2BAA2B,CAAC;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,aAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa,KAAK,KAAK,MAAM;AACnC,+DAAsB,UAAU,WAAW,IAAI;AAC/C,QAAM,oBAAoB,WAAW,OAAO,sBAAsB,WAAW,SAAS,WAAW;AACjG,MAAI,mBAAmB;AACrB,QAAI,WAAW,iBAAiB;AAC9B,+BAAyB;AAAA,QACvB,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,EAAE,IAAI;AAAA,QACpB,MAAM;AAAA,QACN,IAAI,WAAW;AAAA,QACf,OAAO,WAAW;AAAA,QAClB,QAAQ,WAAW;AAAA,QACnB,aAAa,WAAW;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,yCAAyC,CAAC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,+DAAsB,UAAU,KAAK,QAAQ;AAC7C,+DAAsB,UAAU,KAAK,QAAQ;AAC7C,oCAAkC;AAAA,IAChC,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,MACX,IAAI,sBAAsB,kBAAkB;AAAA,QAC1C,IAAI,KAAK;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AAAA,MACF,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AASO,IAAM,wBAAwB,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,KAAK,EAAE;AACpB,MAAI,KAAK,SAAS,WAAW,KAAK,SAAS,SAAS,GAAG;AACrD,2CAAuC;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,WAAW,iBAAiB,iBAAiB,WAAW,iBAAiB,oBAAoB,SAAS;AACpG,6BAAyB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,eAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC1GO,IAAM,gBAAgB,YAAU;AACrC,QAAM,OAAO,SAAS,CAAC,GAAG,OAAO,YAAY;AAC7C,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO,iBAAiB;AACxD,QAAM,uBAAuB,2BAA2B;AACxD,QAAM,gBAAgB,OAAO,wBAAwB,oBAAI,IAAI,CAAC,GAAG,OAAO,qBAAqB,CAAC,IAAI,oBAAI,IAAI,CAAC,CAAC;AAC5G,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACxD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO,MAAM,SAAS,CAAC;AAC3B,wBAAoB;AAAA,MAClB,cAAc,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,OAAO;AAAA,MACxB,0BAA0B,OAAO;AAAA,MACjC,+BAA+B,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,QAAQ,KAAK,GAAG;AACvD,UAAM,SAAS,OAAO,MAAM,QAAQ,CAAC;AACrC,0BAAsB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,cAAc,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACxD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO,MAAM,SAAS,CAAC;AAC3B,UAAM,qBAAqB,kBAAkB;AAAA,MAC3C;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,YAAY,oBAAoB,IAAI;AAC1D,QAAI,CAAC,eAAe;AAClB,4BAAsB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,OAAO;AAAA,MACvB,CAAC;AACD,0BAAoB;AAAA,QAClB,cAAc,OAAO;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,iBAAiB,OAAO;AAAA,QACxB,0BAA0B,OAAO;AAAA,QACjC,+BAA+B,OAAO;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,mEAAsB,UAAU,KAAK,EAAE,EAAE,QAAQ;AAAA,IACnD;AAAA,EACF;AAGA,QAAM,aAAa,uBAAuB,MAAM,oBAAoB,IAAI;AACxE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAc,OAAO;AAAA,IACrB;AAAA,IACA;AAAA,IACA,eAAe,MAAM,KAAK,aAAa;AAAA,EACzC;AACF;;;AV1EA,IAAAC,uBAA4B;AAX5B,IAAMC,aAAY,CAAC,qBAAqB;AAYjC,IAAM,yCAAyC,CAAC,eAAe,UAAU;AAC9E,QAAM,0BAAgC,oBAAY,MAAM;AACtD,kBAAc,QAAQ,wBAAwB,kBAAkB,SAAS,iBAAiB,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,OAAO,MAAM,KAAK;AAAA,EACrK,GAAG,CAAC,eAAe,MAAM,UAAU,MAAM,UAAU,CAAC;AACpD,QAAM,oBAA0B,oBAAY,MAAM;AAChD,UAAM,qBAAqB,MAAM;AACjC,QAAI;AACJ,QAAI,OAAO,uBAAuB,YAAY;AAC5C,YAAM,SAAS;AAAA,QACb,cAAc,iBAAiB;AAAA,QAC/B,QAAQ,CAAC;AAAA,MACX;AACA,uBAAiB,mBAAmB,MAAM;AAAA,IAC5C,OAAO;AACL,uBAAiB;AAAA,IACnB;AACA,UAAM,OAAO,kBAAkB,CAAC,GAC9B;AAAA,MACE;AAAA,IACF,IAAI,MACJ,2BAA2B,8BAA8B,MAAMA,UAAS;AAC1E,UAAM,mBAAmB,SAAS,CAAC,GAAG,iCAAiC;AAAA,MACrE,YAAY,gBAAuB,qBAAAC,KAAK,oCAAoC,SAAS,CAAC,GAAG,QAAQ;AAAA,QAC/F;AAAA,MACF,CAAC,CAAC;AAAA,MACF,YAAY,cAAc,QAAQ,cAAc,4BAA4B;AAAA,IAC9E,CAAC;AACD,WAAO,SAAS,CAAC,GAAG,kBAAkB,0BAA0B,iDAAiD;AAAA,EACnH,GAAG,CAAC,eAAe,MAAM,cAAc,CAAC;AACxC,QAAM,uBAA6B,oBAAY,kBAAgB;AAC7D,QAAI,CAAC,MAAM,YAAY;AACrB,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,kDAAkD;AAC9E,UAAM,2BAA2B,MAAM;AACvC,UAAM,qBAAqB,aAAa,OAAO,mBAAmB;AAClE,QAAI,0BAA0B;AAC5B,YAAM,oBAAoB,kBAAkB;AAC5C,UAAI,oBAAoB;AACtB,0BAAkB,QAAQ,mBAAmB;AAC7C,0BAAkB,OAAO,mBAAmB;AAAA,MAC9C;AACA,mBAAa,OAAO,mBAAmB,IAAI;AAC3C,UAAI,sBAAsB,MAAM;AAC9B,qBAAa,gBAAgB,CAAC,qBAAqB,GAAG,aAAa,aAAa;AAAA,MAClF;AAAA,IACF,WAAW,CAAC,4BAA4B,oBAAoB;AAC1D,aAAO,aAAa,OAAO,mBAAmB;AAC9C,mBAAa,gBAAgB,aAAa,cAAc,OAAO,WAAS,UAAU,mBAAmB;AAAA,IACvG;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,UAAU,MAAM,YAAY,iBAAiB,CAAC;AACxD,QAAM,2BAAiC,oBAAY,YAAU;AApE/D;AAqEI,UAAM,eAAc,WAAM,eAAN,mBAAkB;AACtC,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,8DAA8D;AAAA,IAChF;AACA,UAAM,oBAAmB,WAAM,eAAN,mBAAkB;AAC3C,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,mEAAmE;AAAA,IACrF;AACA,UAAM,wBAAwB,WAAS;AACrC,YAAM,aAAa,OAAO,QAAQ,aAAa,cAAc,OAAO,MAAM;AAC1E,YAAM,QAAQ,iBAAiB,OAAO,uBAAuB,KAAK,CAAC;AACnE,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,MAAM,CAAC,GAAG,YAAY,YAAY,OAAO,uBAAuB,KAAK,CAAC,CAAC,EAAE,IAAI,UAAQ;AAAA,UACnF;AAAA,UACA,OAAO;AAAA,QACT,EAAE;AAAA,QACF,qBAAqB;AAAA,MACvB;AAAA,IACF;AACA,UAAM,kBAAkB,CAAC,SAAS,UAAU,SAAS;AACnD,YAAM,IAAI,MAAM,CAAC,0FAA0F,qBAAqB,OAAO,SAAS,QAAQ,mBAAmB,SAAS,KAAK,UAAU,KAAK,IAAI,UAAQ,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,IAC/O;AACA,QAAI,OAAO,QAAQ,SAAS,QAAQ;AAClC,aAAO,cAAc;AAAA,QACnB,cAAc,OAAO;AAAA,QACrB,OAAO,OAAO,QAAQ,KAAK,IAAI,qBAAqB;AAAA,QACpD,+BAA+B,MAAM;AAAA,QACrC,0BAA0B,MAAM;AAAA,QAChC,cAAc,iBAAiB;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,cAAc;AAAA,MACnB,OAAO;AAAA,QACL,UAAU,OAAO,QAAQ,QAAQ,OAAO,IAAI,qBAAqB;AAAA,QACjE,UAAU,OAAO,QAAQ,QAAQ,OAAO,IAAI,qBAAqB;AAAA,QACjE,SAAS,OAAO,QAAQ,QAAQ;AAAA,MAClC;AAAA,MACA,cAAc,OAAO;AAAA,MACrB,uBAAuB,OAAO;AAAA,MAC9B,mBAAmB,OAAO;AAAA,MAC1B,+BAA+B,MAAM;AAAA,MACrC,0BAA0B,MAAM;AAAA,MAChC,cAAc,iBAAiB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,YAAY,MAAM,+BAA+B,MAAM,wBAAwB,CAAC;AAC1F,QAAM,aAAmB,oBAAY,MAAM;AACzC,UAAM,UAAU,oBAAoB,aAAa;AACjD,WAAO,cAAc,OAAO;AAAA,EAC9B,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,WAAiB,oBAAY,MAAM;AACvC,UAAM,UAAU,oBAAoB,aAAa;AACjD,WAAO,YAAY,OAAO;AAAA,EAC5B,GAAG,CAAC,aAAa,CAAC;AAClB,+BAA6B,eAAe,kBAAkB,oBAAoB;AAClF,mCAAiC,eAAe,iBAAiB,YAAY,mBAAmB,wBAAwB;AACxH,mCAAiC,eAAe,iBAAiB,YAAY,aAAa,UAAU;AACpG,mCAAiC,eAAe,iBAAiB,YAAY,WAAW,QAAQ;AAChG,mCAAiC,eAAe,iBAAiB,YAAY,6BAA6B,oBAAoB;AAK9H,iBAAe,MAAM;AACnB,4BAAwB;AAAA,EAC1B,CAAC;AAKD,QAAM,gBAAsB,eAAO,IAAI;AACvC,EAAM,kBAAU,MAAM;AACpB,QAAI,CAAC,cAAc,SAAS;AAC1B,8BAAwB;AAAA,IAC1B,OAAO;AACL,oBAAc,UAAU;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,uBAAuB,CAAC;AAC9B;;;AWjJA,IAAAC,UAAuB;AAKvB,IAAM,WAAW,oBAAI,IAAI;AAClB,IAAM,8BAA8B,CAAC,OAAO,UAAU;AAT7D;AAUE,SAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACzB,aAAa;AAAA,MACX,aAAa,CAAC;AAAA,MACd,gBAAgB,MAAM,+BAA6B,iBAAM,iBAAN,mBAAoB,gBAApB,mBAAiC,mBAAkB;AAAA,IACxG;AAAA,EACF,CAAC;AACH;AACA,SAAS,sBAAsB,QAAQ,uBAAuB,sBAAsB,qBAAqB;AAjBzG;AAkBE,MAAI,OAAO,0BAA0B,YAAY;AAC/C,WAAO,CAAC;AAAA,EACV;AAIA,QAAM,SAAS,uBAAuB,MAAM;AAC5C,QAAM,eAAe,CAAC;AACtB,QAAM,cAAc,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAM,KAAK,OAAO,CAAC;AACnB,UAAM,SAAS,OAAO,QAAQ,aAAa,EAAE;AAC7C,UAAM,UAAU,sBAAsB,MAAM;AAC5C,iBAAa,EAAE,IAAI;AACnB,QAAI,WAAW,MAAM;AACnB;AAAA,IACF;AACA,UAAM,SAAS,qBAAqB,MAAM;AAC1C,UAAM,aAAa,WAAW;AAC9B,gBAAY,EAAE,IAAI;AAAA,MAChB;AAAA,MACA,QAAQ,cAAa,yBAAoB,EAAE,MAAtB,mBAAyB,SAAS;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACO,IAAM,qBAAqB,CAAC,QAAQ,UAAU;AACnD,QAAM,eAAe,gBAAgB,QAAQ,+CAA+C;AAC5F,QAAM,kBAAwB,oBAAY,CAAC,QAAQ,UAAU;AAC3D,QAAI,OAAO,UAAU,kCAAkC,MAAM,yBAAyB,MAAM;AAC1F;AAAA,IACF;AACA,UAAM,UAAU,aAAa,OAAO,EAAE;AACtC,QAAI,CAAqB,uBAAe,OAAO,GAAG;AAChD;AAAA,IACF;AAGA,QAAI,MAAM,WAAW,MAAM,eAAe;AACxC;AAAA,IACF;AACA,WAAO,QAAQ,kBAAkB,OAAO,EAAE;AAAA,EAC5C,GAAG,CAAC,QAAQ,cAAc,MAAM,qBAAqB,CAAC;AACtD,QAAM,oBAA0B,oBAAY,CAAC,QAAQ,UAAU;AAC7D,QAAI,MAAM,yBAAyB,MAAM;AACvC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,kCAAkC,MAAM,QAAQ,KAAK;AACxE,aAAO,QAAQ,kBAAkB,OAAO,EAAE;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,qBAAqB,CAAC;AACxC,eAAa,QAAQ,aAAa,eAAe;AACjD,eAAa,QAAQ,eAAe,iBAAiB;AACrD,SAAO,QAAQ,qBAAqB;AAAA,IAClC,SAAS;AAAA,IACT,WAAW,MAAM;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,eAAe;AAAA,IACf,aAAa;AAAA,EACf,CAAC;AACD,QAAM,oBAA0B,oBAAY,QAAM;AAChD,QAAI,MAAM,yBAAyB,MAAM;AACvC;AAAA,IACF;AACA,UAAM,UAAU,aAAa,EAAE;AAC/B,QAAI,CAAqB,uBAAe,OAAO,GAAG;AAChD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,QAAQ,wBAAwB;AACnD,UAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,QAAI,IAAI,IAAI,EAAE,GAAG;AACf,aAAO,OAAO,EAAE;AAAA,IAClB,OAAO;AACL,aAAO,IAAI,EAAE;AAAA,IACf;AACA,WAAO,QAAQ,wBAAwB,MAAM;AAAA,EAC/C,GAAG,CAAC,QAAQ,cAAc,MAAM,qBAAqB,CAAC;AACtD,QAAM,0BAAgC,oBAAY,MAAM,sCAAsC,MAAM,GAAG,CAAC,MAAM,CAAC;AAC/G,QAAM,0BAAgC,oBAAY,SAAO;AACvD,WAAO,QAAQ,SAAS,WAAS;AAC/B,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,aAAa,SAAS,CAAC,GAAG,MAAM,aAAa;AAAA,UAC3C,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,WAAO,QAAQ,iCAAiC,WAAW;AAAA,EAC7D,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,yBAA+B,oBAAY,CAAC,IAAI,WAAW;AAC/D,UAAM,cAAc,sCAAsC,MAAM;AAChE,QAAI,CAAC,YAAY,EAAE,KAAK,YAAY,EAAE,EAAE,WAAW,QAAQ;AACzD;AAAA,IACF;AACA,WAAO,QAAQ,SAAS,WAAS;AAC/B,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,aAAa,SAAS,CAAC,GAAG,MAAM,aAAa;AAAA,UAC3C,aAAa,SAAS,CAAC,GAAG,aAAa;AAAA,YACrC,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,YAAY,EAAE,GAAG;AAAA,cAClC;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,WAAO,QAAQ,iCAAiC,WAAW;AAAA,EAC7D,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,sBAAsB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,wBAAwB;AAAA,IAC5B;AAAA,EACF;AACA,mBAAiB,QAAQ,qBAAqB,QAAQ;AACtD,mBAAiB,QAAQ,uBAAuB,SAAS;AACzD,EAAM,kBAAU,MAAM;AACpB,QAAI,MAAM,2BAA2B;AACnC,YAAM,eAAe,sCAAsC,MAAM;AACjE,UAAI,iBAAiB,MAAM,2BAA2B;AACpD,eAAO,QAAQ,wBAAwB,MAAM,yBAAyB;AAAA,MACxE;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,yBAAyB,CAAC;AAC5C,QAAM,eAAqB,oBAAY,MAAM;AAC3C,QAAI,CAAC,MAAM,uBAAuB;AAChC;AAAA,IACF;AACA,WAAO,QAAQ,SAAS,WAAS;AAC/B,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,aAAa,SAAS,CAAC,GAAG,MAAM,aAAa,sBAAsB,QAAQ,MAAM,uBAAuB,MAAM,sBAAsB,MAAM,YAAY,WAAW,CAAC;AAAA,MACpK,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,MAAM,uBAAuB,MAAM,oBAAoB,CAAC;AACpE,eAAa,QAAQ,iBAAiB,YAAY;AAClD,QAAM,oCAA0C,eAAO,MAAS;AAChE,QAAM,mCAAyC,eAAO,MAAS;AAC/D,QAAM,uBAA6B,oBAAY,MAAM;AACnD,QAAI,MAAM,0BAA0B,kCAAkC,WAAW,MAAM,yBAAyB,iCAAiC,SAAS;AACxJ;AAAA,IACF;AACA,WAAO,QAAQ,SAAS,WAAS;AAC/B,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,aAAa,SAAS,CAAC,GAAG,MAAM,aAAa,sBAAsB,QAAQ,MAAM,uBAAuB,MAAM,sBAAsB,MAAM,YAAY,WAAW,CAAC;AAAA,MACpK,CAAC;AAAA,IACH,CAAC;AACD,sCAAkC,UAAU,MAAM;AAClD,qCAAiC,UAAU,MAAM;AAAA,EACnD,GAAG,CAAC,QAAQ,MAAM,uBAAuB,MAAM,oBAAoB,CAAC;AACpE,QAAM,kBAAwB,oBAAY,CAAC,cAAc,QAAQ;AA1KnE;AA2KI,UAAM,iBAAiB,sCAAsC,MAAM;AACnE,QAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI,IAAI,EAAE,GAAG;AAClD,mBAAa,SAAS;AACtB,aAAO;AAAA,IACT;AACA,yBAAqB;AACrB,UAAM,cAAc,sCAAsC,MAAM;AAChE,iBAAa,WAAS,iBAAY,IAAI,EAAE,MAAlB,mBAAqB,WAAU;AACrD,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,oBAAoB,CAAC;AACjC,QAAM,UAAU,MAAM,0BAA0B;AAChD,+BAA6B,QAAQ,aAAa,iBAAiB,OAAO;AAC1E,QAAM,gBAAsB,eAAO,IAAI;AACvC,MAAI,cAAc,SAAS;AACzB,yBAAqB;AAAA,EACvB;AACA,EAAM,kBAAU,MAAM;AACpB,QAAI,CAAC,cAAc,SAAS;AAC1B,2BAAqB;AAAA,IACvB;AACA,kBAAc,UAAU;AAAA,EAC1B,GAAG,CAAC,QAAQ,oBAAoB,CAAC;AACnC;;;AChMA,IAAAC,UAAuB;AAKhB,IAAM,kCAAkC,CAAC,eAAe,UAAU;AACvE,QAAM,kBAAwB,oBAAY,kBAAgB;AACxD,UAAM,0BAA0B,SAAS,CAAC,GAAG,kCAAkC;AAAA,MAC7E,YAAY,cAAc,QAAQ,cAAc,mBAAmB;AAAA,IACrE,CAAC;AACD,UAAM,yBAAyB,CAAC,CAAC,MAAM;AACvC,UAAM,kBAAkB,aAAa,OAAO,8BAA8B,KAAK;AAC/E,QAAI,0BAA0B,CAAC,iBAAiB;AAC9C,mBAAa,OAAO,8BAA8B,IAAI;AACtD,mBAAa,gBAAgB,CAAC,gCAAgC,GAAG,aAAa,aAAa;AAAA,IAC7F,WAAW,CAAC,0BAA0B,iBAAiB;AACrD,aAAO,aAAa,OAAO,8BAA8B;AACzD,mBAAa,gBAAgB,aAAa,cAAc,OAAO,WAAS,UAAU,8BAA8B;AAAA,IAClH,WAAW,0BAA0B,iBAAiB;AACpD,mBAAa,OAAO,8BAA8B,IAAI,SAAS,CAAC,GAAG,yBAAyB,aAAa,OAAO,8BAA8B,CAAC;AAE/I,UAAI,CAAC,MAAM,QAAQ,KAAK,SAAO,IAAI,UAAU,8BAA8B,GAAG;AAC5E,qBAAa,gBAAgB,CAAC,gCAAgC,GAAG,aAAa,cAAc,OAAO,WAAS,UAAU,8BAA8B,CAAC;AAAA,MACvJ;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,eAAe,MAAM,SAAS,MAAM,qBAAqB,CAAC;AAC9D,QAAM,wBAA8B,oBAAY,CAAC,SAAS,OAAO;AAC/D,QAAI,MAAM,yBAAyB,MAAM;AACvC,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,sCAAsC,aAAa;AAC1E,QAAI,CAAC,eAAe,IAAI,EAAE,GAAG;AAC3B,aAAO;AAAA,IACT;AACA,WAAO,CAAC,GAAG,SAAS,YAAY,0BAA0B,CAAC;AAAA,EAC7D,GAAG,CAAC,eAAe,MAAM,qBAAqB,CAAC;AAC/C,+BAA6B,eAAe,kBAAkB,eAAe;AAC7E,+BAA6B,eAAe,gBAAgB,qBAAqB;AACnF;;;ACtCA,IAAAC,UAAuB;;;ACAvB,IAAAC,UAAuB;AASvB,IAAM,oBAAoB,OAAO,WAAW,cAAoB,0BAAwB;AACxF,IAAOC,6BAAQ;;;ADFf,SAAS,iBAAiB,IAAI;AAC5B,QAAM,MAAY,eAAO,EAAE;AAC3B,EAAAC,2BAAkB,MAAM;AACtB,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAa,eAAO,IAAI;AAAA;AAAA,KAEvB,GAAG,IAAI,SAAS,GAAG,IAAI;AAAA,GAAC,EAAE;AAC7B;AACA,IAAO,2BAAQ;;;AEXR,IAAM,wBAAwB,CAAC,QAAQ,UAAU;AACtD,QAAM,iBAAiB,gBAAgB,QAAQ,oCAAoC;AACnF,QAAM,cAAc,mBAAmB,QAAQ,KAAK;AACpD,QAAM,YAAY,MAAM,oBAAoB,YAAY,CAAC,CAAC,MAAM;AAChE,QAAM,qBAAqB,yBAAiB,MAAM;AAChD,UAAM,mBAAmB,OAAO,QAAQ,oBAAoB;AAC5D,UAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA,kBAAkB,YAAY,KAAK;AAAA,IACrC;AACA,WAAO,QAAQ,aAAa,iBAAiB,kBAAkB;AAAA,EACjE,CAAC;AACD,uBAAqB,QAAQ,iBAAiB,MAAM,eAAe;AACnE,eAAa,QAAQ,6BAA6B,MAAM,WAAW,kBAAkB,CAAC;AACxF;;;ACpBA,IAAAC,UAAuB;;;ACDvB,IAAAC,UAAuB;AACvB,IAAM,gBAAgB,CAAC;AASR,SAAR,WAA4B,MAAM,SAAS;AAChD,QAAM,MAAY,eAAO,aAAa;AACtC,MAAI,IAAI,YAAY,eAAe;AACjC,QAAI,UAAU,KAAK,OAAO;AAAA,EAC5B;AACA,SAAO;AACT;;;AChBA,IAAAC,UAAuB;AACvB,IAAM,QAAQ,CAAC;AAKA,SAAR,WAA4B,IAAI;AAGrC,EAAM,kBAAU,IAAI,KAAK;AAE3B;;;ACTO,IAAM,UAAN,MAAM,SAAQ;AAAA,EAAd;AAIL,qCAAY;AAYZ,iCAAQ,MAAM;AACZ,UAAI,KAAK,cAAc,MAAM;AAC3B,qBAAa,KAAK,SAAS;AAC3B,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,yCAAgB,MAAM;AACpB,aAAO,KAAK;AAAA,IACd;AAAA;AAAA,EAvBA,OAAO,SAAS;AACd,WAAO,IAAI,SAAQ;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,IAAI;AACf,SAAK,MAAM;AACX,SAAK,YAAY,WAAW,MAAM;AAChC,WAAK,YAAY;AACjB,SAAG;AAAA,IACL,GAAG,KAAK;AAAA,EACV;AAUF;AACe,SAARC,cAA8B;AACnC,QAAM,UAAU,WAAW,QAAQ,MAAM,EAAE;AAC3C,aAAW,QAAQ,aAAa;AAChC,SAAO;AACT;;;ACjCA,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;AAMtB,IAAAC,uBAA2C;AAC3C,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,kBAAkB,eAAe,2BAA2B;AAAA,IACnE,aAAa,CAAC,2BAA2B;AAAA,IACzC,MAAM,CAAC,gBAAgB;AAAA,EACzB;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,iBAAiBC,gBAAO,OAAO;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,OAAO,KAAK,OAAO,WAAW;AAChC,CAAC;AACD,SAAS,mBAAmB,QAAQ;AAClC,QAAM,SAAS,kBAAkB;AACjC,QAAM,YAAYC,kBAAiB;AACnC,QAAM,YAAY,gBAAgB,QAAQ,qBAAqB;AAC/D,QAAM,gBAAgB,gBAAgB,QAAQ,yBAAyB;AACvE,QAAM;AAAA;AAAA,IAEN,OAAO,IAAI,gBAAgB,OAAO,QAAQ,SAAS,UAAU,OAAO,QAAQ,eAAe,OAAO,KAAK,OAAO;AAAA;AAC9G,QAAM,UAAgB,eAAO,IAAI;AACjC,QAAM,kBAAwB,eAAO,IAAI;AAGzC,QAAM,cAAoB,gBAAQ,MAAM,CAAC,CAAC,UAAU,iBAAiB,CAAC,UAAU,UAAU,CAAC,UAAU,YAAY,OAAO,KAAK,aAAa,EAAE,WAAW,GAAG,CAAC,UAAU,eAAe,WAAW,UAAU,UAAU,aAAa,CAAC;AACjO,QAAM,aAAa;AAAA,IACjB;AAAA,IACA,SAAS,UAAU;AAAA,EACrB;AACA,QAAM,UAAUF,mBAAkB,UAAU;AAC5C,QAAM,UAAgB,oBAAY,CAAC,WAAW,gBAAgB,WAAS;AAErE,QAAI,sBAAsB,KAAK,GAAG;AAChC;AAAA,IACF;AAGA,QAAI,CAAC,OAAO,QAAQ,OAAO,OAAO,EAAE,GAAG;AACrC;AAAA,IACF;AACA,WAAO,QAAQ,aAAa,WAAW,OAAO,QAAQ,aAAa,OAAO,EAAE,GAAG,KAAK;AACpF,QAAI,aAAa;AACf,kBAAY,KAAK;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,EAAE,CAAC;AACtB,QAAM,kBAAwB,oBAAY,MAAM;AA5DlD;AA8DI,uBAAO,QAAQ,mBAAf,mBAA+B,YAA/B,mBAAwC,UAAU,IAAI,YAAY,4BAA4B;AAAA,EAChG,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,gBAAsB,oBAAY,MAAM;AAhEhD;AAiEI,uBAAO,QAAQ,mBAAf,mBAA+B,YAA/B,mBAAwC,UAAU,OAAO,YAAY,4BAA4B;AAAA,EACnG,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,gBAAsB,oBAAY,WAAS;AAC/C,kBAAc;AACd,QAAI,OAAO,QAAQ,OAAO,OAAO,EAAE,GAAG;AACpC,aAAO,QAAQ,aAAa,cAAc,OAAO,QAAQ,aAAa,OAAO,EAAE,GAAG,KAAK;AAAA,IACzF;AACA,oBAAgB,QAAQ,oBAAoB,WAAW,aAAa;AACpE,oBAAgB,UAAU;AAAA,EAC5B,GAAG,CAAC,QAAQ,OAAO,IAAI,aAAa,CAAC;AACrC,QAAM,kBAAwB,oBAAY,WAAS;AACjD,QAAI,CAAC,QAAQ,SAAS;AACpB;AAAA,IACF;AACA,YAAQ,cAAc,EAAE,KAAK;AAC7B,YAAQ,QAAQ,iBAAiB,WAAW,aAAa;AAEzD,oBAAgB,UAAU,QAAQ;AAAA,EACpC,GAAG,CAAC,SAAS,aAAa,CAAC;AAC3B,QAAM,yBAAyB,cAAc;AAAA,IAC3C,aAAa;AAAA,IACb,YAAY,QAAQ,aAAa;AAAA,IACjC,aAAa;AAAA,IACb,WAAW;AAAA,EACb,IAAI;AACJ,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,WAAO;AAAA,EACT;AACA,aAAoB,qBAAAG,MAAM,OAAO,SAAS;AAAA,IACxC,KAAK;AAAA,IACL,WAAW,QAAQ;AAAA,IACnB,WAAW;AAAA,EACb,GAAG,wBAAwB;AAAA,IACzB,UAAU,KAAc,qBAAAC,KAAK,gBAAgB;AAAA,MAC3C,IAAI,UAAU,MAAM;AAAA,MACpB;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,UAAU;AAAA,IACZ,CAAC,OAAgB,qBAAAA,KAAK,OAAO;AAAA,MAC3B,WAAW,QAAQ;AAAA,MACnB,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ;AACA,OAAwC,mBAAmB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrE,KAAK,mBAAAC,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAItB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI5C,QAAQ,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpE,SAAS,mBAAAA,QAAU,MAAM;AAAA,MACvB,OAAO,mBAAAA,QAAU,KAAK;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,EAIH,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA,EAIzB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI9D,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,KAAK,mBAAAA,QAAU,IAAI;AAAA;AAAA;AAAA;AAAA,EAInB,SAAS,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,OAAO,mBAAAA,QAAU;AACnB,IAAI;AAEG,IAAM,uBAAuB,YAAU;AAC5C,MAAI,OAAO,QAAQ,SAAS,YAAY,OAAO,QAAQ,SAAS,aAAa;AAC3E,WAAO;AAAA,EACT;AACA,aAAoB,qBAAAC,KAAK,oBAAoB,SAAS,CAAC,GAAG,MAAM,CAAC;AACnE;AACA,IAAI,KAAuC,sBAAqB,cAAc;;;AClLvE,IAAM,uBAAuB,SAAS,CAAC,GAAG,qBAAqB;AAAA,EACpE,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA,EACZ,cAAc,MAAM;AAAA,EACpB,YAAY;AAAA,EACZ,oBAAoB,CAAC,GAAG,KAAK,IAAI,WAAW,kBAAkB,QAAQ,GAAG;AAC3E,CAAC;;;ALXD,IAAM,sBAAsB;AAAA,EAC1B,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,sBAAsB;AACxB;AACA,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,aAAa,CAAC,eAAe;AAAA,IAC7B,cAAc,CAAC,gBAAgB;AAAA,IAC/B,cAAc,CAAC,gBAAgB;AAAA,IAC/B,iBAAiB,CAAC,mBAAmB;AAAA,EACvC;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACO,IAAM,6BAA6B,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,EACrE,YAAY;AAAA,IACV,UAAU;AAAA,EACZ;AACF,CAAC;AAMM,IAAM,oBAAoB,CAAC,QAAQ,UAAU;AAClD,QAAM,SAAS,cAAc,QAAQ,mBAAmB;AACxD,QAAM,YAAY,gBAAgB,QAAQ,qBAAqB;AAC/D,QAAM,cAAoB,eAAO,IAAI;AACrC,QAAM,iBAAuB,eAAO,IAAI;AACxC,QAAM,yBAA+B,eAAO,MAAS;AACrD,QAAM,2BAAiC,eAAO,IAAI;AAClD,QAAM,aAAa;AAAA,IACjB,SAAS,MAAM;AAAA,EACjB;AACA,QAAM,UAAUA,mBAAkB,UAAU;AAC5C,QAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,EAAE;AACnD,QAAM,uBAAuB,gBAAgB,QAAQ,wCAAwC;AAC7F,QAAM,eAAqB,eAAO,EAAE;AACpC,QAAM,UAAUC,YAAW;AAC3B,QAAM,uBAA6B,eAAO,mBAAmB;AAC7D,QAAM,aAAmB,eAAO;AAAA,IAC9B,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,cAAc;AAAA,EAChB,CAAC;AACD,EAAM,kBAAU,MAAM;AACpB,WAAO,MAAM;AACX,mBAAa,uBAAuB,OAAO;AAAA,IAC7C;AAAA,EACF,GAAG,CAAC,CAAC;AAIL,QAAM,uBAA6B,gBAAQ,MAAM;AAC/C,WAAO,CAAC,MAAM,iBAAiB,CAAC,CAAC,UAAU,UAAU,MAAM;AAAA,EAC7D,GAAG,CAAC,MAAM,eAAe,WAAW,MAAM,QAAQ,CAAC;AACnD,QAAM,qBAA2B,oBAAY,CAAC,aAAa,aAAa;AApE1E;AAsEI,QAAI,yBAAyB,SAAS;AACpC,+BAAyB,QAAQ,UAAU,OAAO,QAAQ,cAAc,QAAQ,YAAY;AAC5F,+BAAyB,UAAU;AAAA,IACrC;AAGA,QAAI,gBAAgB,UAAa,aAAa,MAAM;AAClD,YAAM,aAAY,kBAAO,QAAQ,mBAAf,mBAA+B,YAA/B,mBAAwC,cAAc,aAAa,WAAW;AAChG,UAAI,WAAW;AACb,kBAAU,UAAU,IAAI,aAAa,UAAU,QAAQ,eAAe,QAAQ,YAAY;AAC1F,iCAAyB,UAAU;AAAA,MACrC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,CAAC;AACpB,QAAM,oBAA0B,oBAAY,CAAC,OAAO,cAAc;AApFpE;AAqFI,QAAI,OAAO;AACT,YAAM,cAAa,kBAAO,QAAQ,mBAAf,mBAA+B,YAA/B,mBAAwC,cAAc,aAAa,KAAK;AAC3F,UAAI,YAAY;AACd,YAAI,WAAW;AACb,qBAAW,UAAU,IAAI,QAAQ,eAAe;AAAA,QAClD,OAAO;AACL,qBAAW,UAAU,OAAO,QAAQ,eAAe;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,QAAQ,eAAe,CAAC;AACpC,QAAM,oBAA0B,oBAAY,cAAY;AAhG1D;AAiGI,UAAM,eAAc,YAAO,QAAQ,mBAAf,mBAA+B;AACnD,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,UAAM,cAAc,YAAY,iBAAiB,WAAW;AAC5D,QAAI,CAAC,YAAY,QAAQ;AACvB;AAAA,IACF;AACA,UAAM,YAAY,MAAM,KAAK,WAAW;AACxC,UAAM,mBAAmB,oBAAI,IAAI;AACjC,cAAU,QAAQ,SAAO;AACvB,YAAM,QAAQ,IAAI,aAAa,SAAS;AACxC,UAAI,OAAO;AACT,yBAAiB,IAAI,OAAO,IAAI,sBAAsB,CAAC;AAAA,MACzD;AAAA,IACF,CAAC;AACD,aAAS;AAGT,0BAAsB,MAAM;AAC1B,YAAM,UAAU,YAAY,iBAAiB,WAAW;AACxD,YAAM,aAAa,CAAC;AACpB,cAAQ,QAAQ,SAAO;AACrB,cAAM,QAAQ,IAAI,aAAa,SAAS;AACxC,YAAI,CAAC,OAAO;AACV;AAAA,QACF;AACA,cAAM,WAAW,iBAAiB,IAAI,KAAK;AAC3C,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AACA,cAAM,cAAc,IAAI,sBAAsB;AAC9C,cAAM,SAAS,SAAS,MAAM,YAAY;AAC1C,YAAI,KAAK,IAAI,MAAM,IAAI,GAAG;AACxB,gBAAM,YAAY,IAAI,QAAQ,CAAC;AAAA,YAC7B,WAAW,cAAc,MAAM;AAAA,UACjC,GAAG;AAAA,YACD,WAAW;AAAA,UACb,CAAC,GAAG;AAAA,YACF,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,UACR,CAAC;AACD,qBAAW,KAAK,SAAS;AAAA,QAC3B;AAAA,MACF,CAAC;AACD,UAAI,WAAW,SAAS,GAAG;AACzB,gBAAQ,WAAW,WAAW,IAAI,OAAK,EAAE,QAAQ,CAAC,EAAE,KAAK,MAAM;AAAA,QAAC,CAAC;AAAA,MACnE;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,kBAAwB,oBAAY,CAAC,QAAQ,UAAU;AAE3D,UAAM,gBAAgB,0BAA0B,MAAM;AACtD,UAAM,aAAa,gBAAgB;AACnC,QAAI,wBAAwB,OAAO,KAAK,aAAa,EAAE,WAAW,GAAG;AACnE;AAAA,IACF;AACA,QAAI,aAAa,SAAS;AACxB,cAAQ,MAAM;AACd,mBAAa,UAAU;AAAA,IACzB;AACA,WAAO,MAAM,sBAAsB,OAAO,EAAE,EAAE;AAG9C,UAAM,gBAAgB;AACtB,WAAO,QAAQ,iBAAiB,IAAI;AACpC,gBAAY,UAAU,MAAM;AAE5B,gBAAY,QAAQ,UAAU,IAAI,QAAQ,WAAW;AACrD,iBAAa,OAAO,EAAE;AAGtB,sBAAkB,OAAO,IAAI,IAAI;AACjC,2BAAuB,UAAU,WAAW,MAAM;AAChD,kBAAY,QAAQ,UAAU,OAAO,QAAQ,WAAW;AAAA,IAC1D,CAAC;AACD,mBAAe,UAAU,qBAAqB,OAAO,EAAE;AACvD,WAAO,QAAQ,aAAa,OAAO,IAAI,qBAAqB,KAAK;AAAA,EACnE,GAAG,CAAC,QAAQ,sBAAsB,QAAQ,QAAQ,aAAa,mBAAmB,sBAAsB,OAAO,CAAC;AAChH,QAAM,iBAAuB,oBAAY,CAAC,QAAQ,UAAU;AAC1D,QAAI,cAAc,IAAI;AACpB;AAAA,IACF;AACA,UAAM,aAAa,oBAAoB,QAAQ,OAAO,EAAE;AACxD,UAAM,aAAa,oBAAoB,QAAQ,SAAS;AACxD,QAAI,CAAC,cAAc,CAAC,cAAc,WAAW,SAAS,YAAY,WAAW,SAAS,eAAe,CAAC,MAAM,QAAQ;AAClH;AAAA,IACF;AAGA,UAAM,aAAa,MAAM,OAAO,sBAAsB;AACtD,UAAM,YAAY,KAAK,MAAM,MAAM,UAAU,WAAW,GAAG;AAC3D,UAAM,WAAW,KAAK,MAAM,WAAW,SAAS,CAAC;AACjD,WAAO,MAAM,qBAAqB,OAAO,EAAE,EAAE;AAC7C,UAAM,eAAe;AAGrB,UAAM,gBAAgB;AACtB,QAAI,aAAa,WAAW,aAAa,YAAY,OAAO,IAAI;AAC9D,cAAQ,MAAM;AACd,mBAAa,UAAU;AAAA,IACzB;AACA,QAAI,WAAW,SAAS,WAAW,WAAW,QAAQ,WAAW,SAAS,CAAC,WAAW,oBAAoB,CAAC,aAAa,SAAS;AAC/H,cAAQ,MAAM,KAAK,MAAM;AACvB,cAAM,UAAU,oBAAoB,QAAQ,OAAO,EAAE;AAErD,eAAO,QAAQ,wBAAwB,OAAO,IAAI,CAAC,QAAQ,gBAAgB;AAAA,MAC7E,CAAC;AACD,mBAAa,UAAU,OAAO;AAC9B;AAAA,IACF;AACA,UAAM,iBAAiB,qBAAqB,OAAO,EAAE;AACrD,UAAM,iBAAiB,qBAAqB,SAAS;AAGrD,UAAM,eAAe,YAAY,WAAW,UAAU;AACtD,UAAM,sBAAsB;AAAA,MAC1B,eAAe,iBAAiB,iBAAiB,OAAO;AAAA,MACxD,kBAAkB,OAAO;AAAA,MACzB,sBAAsB;AAAA,IACxB;AAGA,QAAI,qBAAqB,QAAQ,qBAAqB,OAAO,MAAM,qBAAqB,QAAQ,yBAAyB,cAAc;AACrI,YAAM,aAAa,mBAAmB;AAGtC,YAAM,qBAAqB,iBAAiB,WAAW,mBAAmB,iBAAiB,KAAK,iBAAiB,WAAW,mBAAmB,iBAAiB;AAChK,YAAM,iBAAiB,OAAO,QAAQ,6BAA6B,4BAA4B,IAAI;AAAA,QACjG,aAAa;AAAA,QACb,aAAa,OAAO;AAAA,QACpB;AAAA,QACA,eAAe,oBAAoB;AAAA,MACrC,CAAC;AAGD,UAAI,mBAAmB,MAAM,sBAAsB,YAAY;AAC7D,mBAAW,UAAU;AAAA,UACnB,aAAa,OAAO;AAAA,UACpB;AAAA,UACA;AAAA,QACF;AACA,2BAAmB,OAAO,IAAI,YAAY;AAAA,MAC5C,OAAO;AAEL,mBAAW,UAAU;AAAA,UACnB,aAAa;AAAA,UACb,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB;AACA,2BAAmB,MAAM,IAAI;AAAA,MAC/B;AACA,2BAAqB,UAAU;AAAA,IACjC;AAGA,QAAI,WAAW,QAAQ,gBAAgB,MAAM;AAC3C,YAAM,aAAa,aAAa;AAAA,IAClC,OAAO;AACL,YAAM,aAAa,aAAa;AAAA,IAClC;AAAA,EACF,GAAG,CAAC,WAAW,QAAQ,QAAQ,SAAS,sBAAsB,kBAAkB,CAAC;AACjF,QAAM,gBAAsB,oBAAY,CAAC,GAAG,UAAU;AAEpD,UAAM,gBAAgB,0BAA0B,MAAM;AACtD,QAAI,cAAc,MAAM,wBAAwB,OAAO,KAAK,aAAa,EAAE,WAAW,GAAG;AACvF;AAAA,IACF;AACA,QAAI,aAAa,SAAS;AACxB,cAAQ,MAAM;AACd,mBAAa,UAAU;AAAA,IACzB;AACA,WAAO,MAAM,kBAAkB;AAC/B,UAAM,eAAe;AAGrB,UAAM,gBAAgB;AACtB,iBAAa,uBAAuB,OAAO;AAC3C,gBAAY,UAAU;AACtB,UAAM,gBAAgB,qBAAqB,QAAQ;AACnD,yBAAqB,UAAU;AAG/B,uBAAmB,MAAM,IAAI;AAC7B,sBAAkB,WAAW,KAAK;AAClC,WAAO,QAAQ,iBAAiB,KAAK;AAGrC,QAAI,CAAC,MAAM,gBAAgB,MAAM,aAAa,eAAe,QAAQ;AAEnE,iBAAW,UAAU;AAAA,QACnB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AACA,qBAAe,UAAU;AACzB,mBAAa,EAAE;AACf;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,mBAAmB,QAAQ,WAAW,QAAQ,gBAAgB,MAAM;AACzF,YAAM,iBAAiB,eAAe;AACtC,YAAM,iBAAiB,WAAW,QAAQ;AAC1C,YAAM,iBAAiB,OAAO,QAAQ,6BAA6B,4BAA4B,gBAAgB;AAAA,QAC7G,aAAa;AAAA,QACb,aAAa,WAAW,QAAQ;AAAA,QAChC,cAAc,WAAW,QAAQ;AAAA,QACjC;AAAA,MACF,CAAC;AACD,UAAI,mBAAmB,IAAI;AACzB,0BAAkB,MAAM;AACtB,iBAAO,QAAQ,YAAY,WAAW,cAAc;AAGpD,gBAAM,uBAAuB;AAAA,YAC3B,KAAK,OAAO,QAAQ,OAAO,SAAS;AAAA,YACpC,aAAa;AAAA,YACb,UAAU;AAAA,UACZ;AACA,iBAAO,QAAQ,aAAa,kBAAkB,oBAAoB;AAAA,QACpE,CAAC;AAAA,MACH;AAAA,IACF;AAGA,eAAW,UAAU;AAAA,MACnB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB;AACA,iBAAa,EAAE;AAAA,EACjB,GAAG,CAAC,QAAQ,WAAW,sBAAsB,QAAQ,oBAAoB,mBAAmB,SAAS,iBAAiB,CAAC;AACvH,QAAM,2BAAiC,oBAAY,CAAC,cAAc;AAAA,IAChE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,gCAAgC,MAAM,IAAI,GAAG;AAC/C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,qBAAqB,WAAW;AACvD,UAAM,iBAAiB,qBAAqB,WAAW;AAGvD,UAAM,iBAAiB,iBAAiB,WAAW,mBAAmB,iBAAiB;AAAA,IAEvF,iBAAiB,WAAW,mBAAmB,iBAAiB;AAEhE,QAAI,kBAAkB,mBAAmB,gBAAgB;AAEvD,aAAO;AAAA,IACT;AACA,QAAI;AACJ,QAAI,kBAAkB,MAAM;AAC1B,yBAAmB,iBAAiB,UAAU,iBAAiB,iBAAiB;AAAA,IAClF,OAAO;AACL,yBAAmB,iBAAiB,UAAU,iBAAiB,IAAI;AAAA,IACrE;AACA,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,oBAAoB,CAAC;AACjC,+BAA6B,QAAQ,4BAA4B,wBAAwB;AACzF,eAAa,QAAQ,gBAAgB,eAAe;AACpD,eAAa,QAAQ,eAAe,cAAc;AAClD,eAAa,QAAQ,cAAc,aAAa;AAChD,eAAa,QAAQ,gBAAgB,cAAc;AACnD,uBAAqB,QAAQ,kBAAkB,MAAM,gBAAgB;AACrE,QAAM,mBAAyB,oBAAY,cAAY;AACrD,WAAO,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,MACnD,YAAY,SAAS,CAAC,GAAG,MAAM,YAAY;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,MAAM,CAAC;AACX,mBAAiB,QAAQ;AAAA,IACvB;AAAA,EACF,GAAG,SAAS;AACd;;;AMrXA,IAAAC,UAAuB;AAKvB,IAAMC,sBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAa,gBAAQ,MAAM;AACzB,UAAM,QAAQ;AAAA,MACZ,yBAAyB,CAAC,yBAAyB;AAAA,MACnD,qBAAqB,CAAC,qBAAqB;AAAA,IAC7C;AACA,WAAO,eAAe,OAAO,yBAAyB,OAAO;AAAA,EAC/D,GAAG,CAAC,OAAO,CAAC;AACd;AACO,IAAM,iCAAiC,CAAC,eAAe,UAAU;AACtE,QAAM,aAAa;AAAA,IACjB,SAAS,MAAM;AAAA,EACjB;AACA,QAAM,UAAUA,oBAAkB,UAAU;AAC5C,QAAM,sBAA4B,oBAAY,kBAAgB;AAC5D,UAAM,gBAAgB,SAAS,CAAC,GAAG,sBAAsB;AAAA,MACvD,eAAe,QAAQ;AAAA,MACvB,iBAAiB,QAAQ;AAAA,MACzB,YAAY,cAAc,QAAQ,cAAc,yBAAyB;AAAA,IAC3E,CAAC;AACD,UAAM,0BAA0B,MAAM;AACtC,UAAM,mBAAmB,aAAa,OAAO,cAAc,KAAK,KAAK;AACrE,QAAI,2BAA2B,CAAC,kBAAkB;AAChD,mBAAa,OAAO,cAAc,KAAK,IAAI;AAC3C,mBAAa,gBAAgB,CAAC,cAAc,OAAO,GAAG,aAAa,aAAa;AAAA,IAClF,WAAW,CAAC,2BAA2B,kBAAkB;AACvD,aAAO,aAAa,OAAO,cAAc,KAAK;AAC9C,mBAAa,gBAAgB,aAAa,cAAc,OAAO,WAAS,UAAU,cAAc,KAAK;AAAA,IACvG,WAAW,2BAA2B,kBAAkB;AACtD,mBAAa,OAAO,cAAc,KAAK,IAAI,SAAS,CAAC,GAAG,eAAe,aAAa,OAAO,cAAc,KAAK,CAAC;AAE/G,UAAI,CAAC,MAAM,QAAQ,KAAK,SAAO,IAAI,UAAU,qBAAqB,KAAK,GAAG;AACxE,qBAAa,gBAAgB,CAAC,cAAc,OAAO,GAAG,aAAa,cAAc,OAAO,WAAS,UAAU,cAAc,KAAK,CAAC;AAAA,MACjI;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,eAAe,SAAS,MAAM,SAAS,MAAM,aAAa,CAAC;AAC/D,+BAA6B,eAAe,kBAAkB,mBAAmB;AACnF;;;AC/CA,IAAAC,UAAuB;AAGhB,IAAM,kBAAkB,CAAC,QAAQ,UAAU;AAIhD,QAAM,oBAA0B,oBAAY,CAAC,QAAQ,UAAU;AAC7D,UAAM,aAAa,OAAO,QAAQ,cAAc,OAAO,IAAI,OAAO,KAAK;AACvE,QAAI,WAAW,OAAO,UAAU,kCAAkC,MAAM,QAAQ,OAAO,MAAM,QAAQ,YAAY,CAAC,MAAM,UAAU;AAChI,UAAI,OAAO,QAAQ,SAAS,SAAS;AACnC;AAAA,MACF;AACA,UAAI,MAAM,cAAc,CAAC,OAAO,QAAQ,kBAAkB;AACxD,eAAO,QAAQ,WAAW,UAAU,OAAO,EAAE;AAC7C;AAAA,MACF;AACA,aAAO,QAAQ,wBAAwB,OAAO,IAAI,CAAC,OAAO,QAAQ,gBAAgB;AAAA,IACpF;AAAA,EACF,GAAG,CAAC,QAAQ,MAAM,UAAU,CAAC;AAC7B,eAAa,QAAQ,eAAe,iBAAiB;AACvD;;;AChBA,IAAAC,UAAuB;;;ACJvB,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;AAMtB,IAAAC,uBAA2C;AAC3C,IAAMC,sBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,sBAAsB;AAAA,IAC7B,QAAQ,CAAC,4BAA4B;AAAA,EACvC;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,SAAS,yBAAyB,OAAO;AAnBzC;AAoBE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,YAAYC,kBAAiB;AACnC,QAAM,SAASC,mBAAkB;AACjC,QAAM,UAAUF,oBAAkB,SAAS;AAC3C,QAAM,gCAAgC,gBAAgB,QAAQ,yCAAyC;AACvG,QAAM,0BAA0B,8BAA8B,QAAQ,EAAE,KAAK;AAC7E,QAAM,OAAO,QAAQ,mBAAmB,UAAU,MAAM,uBAAuB,UAAU,MAAM;AAC/F,QAAM,cAAc,WAAS;AAC3B,WAAO,QAAQ,wBAAwB,IAAI,CAAC,QAAQ,gBAAgB;AACpE,WAAO,QAAQ,aAAa,IAAI,KAAK;AACrC,UAAM,gBAAgB;AAAA,EACxB;AACA,aAAoB,qBAAAG,MAAM,OAAO;AAAA,IAC/B,WAAW,QAAQ;AAAA,IACnB,OAAO;AAAA,MACL,YAAY,KAAK,QAAQ,QAAQ,QAAQ,gBAAgB;AAAA,IAC3D;AAAA,IACA,UAAU,KAAc,qBAAAC,KAAK,OAAO;AAAA,MAClC,WAAW,QAAQ;AAAA,MACnB,UAAU,0BAA0B,SAAkB,qBAAAA,KAAK,UAAU,MAAM,gBAAgB,SAAS;AAAA,QAClG,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,QACV,cAAc,QAAQ,mBAAmB,OAAO,QAAQ,cAAc,kBAAkB,IAAI,OAAO,QAAQ,cAAc,gBAAgB;AAAA,MAC3I,IAAG,4CAAW,cAAX,mBAAsB,gBAAgB;AAAA,QACvC,cAAuB,qBAAAA,KAAK,MAAM;AAAA,UAChC,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ,CAAC,OAAgB,qBAAAD,MAAM,QAAQ;AAAA,MAC7B,UAAU,CAAC,mBAAmB,SAAY,QAAQ,cAAc,gBAAgB,CAAC,uBAAuB,0BAA0B,IAAI,KAAK,uBAAuB,MAAM,EAAE;AAAA,IAC5K,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,yBAAyB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3E,KAAK,mBAAAE,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAItB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI5C,QAAQ,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,iBAAiB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM;AAAA,IACpE,SAAS,mBAAAA,QAAU,MAAM;AAAA,MACvB,OAAO,mBAAAA,QAAU,KAAK;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,EAIH,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,KAAK;AAAA,EACzB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI/B,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI9D,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,kBAAkB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI5B,KAAK,mBAAAA,QAAU,IAAI;AAAA;AAAA;AAAA;AAAA,EAInB,SAAS,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ACjIJ,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,MAAM,MAAM;AACtB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM;AAC7B;AAAA,IACF;AACA,UAAM,OAAO,KAAK;AAClB,SAAK,OAAO,KAAK;AACjB,SAAK,KAAK,OAAO;AAAA,EACnB;AACF;AAGA,IAAM,OAAN,MAAM,MAAK;AAAA,EACT,YAAY,OAAO,MAAM;AACvB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,UAAM,QAAQ,CAAC;AACf,SAAK,QAAQ,UAAQ;AACnB,YAAM,KAAK,KAAK,IAAI;AAAA,IACtB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,UAAU,KAAK;AACnB,WAAO,YAAY,MAAM;AACvB,SAAG,OAAO;AACV,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,KAAK,OAAO;AACjB,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO,IAAI,MAAK,MAAM,IAAI;AAAA,IAC5B;AACA,QAAI,QAAQ;AACZ,UAAM,QAAQ,IAAI,KAAK,MAAM,KAAK,GAAG,IAAI;AACzC,QAAI,UAAU;AACd,WAAO,QAAQ,IAAI,MAAM,QAAQ;AAC/B,eAAS;AACT,YAAM,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG,IAAI;AACxC,cAAQ,OAAO;AACf,gBAAU;AAAA,IACZ;AACA,WAAO,IAAI,MAAK,OAAO,OAAO;AAAA,EAChC;AACF;AACO,IAAM,cAAc,YAAU;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,4BAA4B,oBAAI,IAAI;AAC1C,QAAM,YAAY,UAAQ;AACxB,UAAM,kBAAkB,CAAC,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,UAAU;AACpF,QAAI;AACJ,QAAI,iBAAiB;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAChD,cAAM,YAAY,QAAQ,KAAK,SAAS,CAAC,CAAC;AAC1C,YAAI,UAAU,SAAS,SAAS;AAC9B,oBAAU,SAAS;AAAA,QACrB;AAAA,MACF;AACA,qBAAe,YAAY,KAAK,SAAS,IAAI,aAAW,QAAQ,OAAO,CAAC,CAAC;AAAA,IAC3E,WAAW,8BAA8B;AACvC,YAAM,iBAAiB,CAAC;AACxB,YAAM,iBAAiB,CAAC;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAChD,cAAM,UAAU,KAAK,SAAS,CAAC;AAC/B,cAAM,YAAY,QAAQ,OAAO;AACjC,YAAI,UAAU,SAAS,SAAS;AAC9B,oBAAU,SAAS;AACnB,yBAAe,KAAK,OAAO;AAAA,QAC7B,WAAW,UAAU,SAAS,QAAQ;AACpC,yBAAe,KAAK,OAAO;AAAA,QAC7B;AAAA,MACF;AACA,qBAAe,CAAC,GAAG,gBAAgB,GAAG,cAAc;AAAA,IACtD,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAChD,cAAM,YAAY,QAAQ,KAAK,SAAS,CAAC,CAAC;AAC1C,YAAI,UAAU,SAAS,SAAS;AAC9B,oBAAU,SAAS;AAAA,QACrB;AAAA,MACF;AACA,qBAAe,CAAC,GAAG,KAAK,QAAQ;AAAA,IAClC;AACA,QAAI,KAAK,YAAY,MAAM;AACzB,mBAAa,KAAK,KAAK,QAAQ;AAAA,IACjC;AACA,8BAA0B,IAAI,KAAK,IAAI,YAAY;AAAA,EACrD;AACA,YAAU,QAAQ,kBAAkB,CAAC;AACrC,QAAM,WAAW,KAAK,KAAK,0BAA0B,IAAI,kBAAkB,CAAC;AAC5E,WAAS,QAAQ,UAAQ;AACvB,UAAM,WAAW,0BAA0B,IAAI,KAAK,IAAI;AACxD,QAAI,qCAAU,QAAQ;AACpB,WAAK,YAAY,KAAK,KAAK,QAAQ,CAAC;AAAA,IACtC;AAAA,EACF,CAAC;AACD,SAAO,SAAS,KAAK;AACvB;;;AF9FA,IAAAC,uBAA4B;AAX5B,IAAMC,aAAY,CAAC,qBAAqB;AAYjC,IAAM,+BAA+B,CAAC,eAAe,UAAU;AACpE,QAAM,0BAAgC,oBAAY,MAAM;AACtD,kBAAc,QAAQ,wBAAwB,kBAAkB,SAAS,iBAAiB,SAAS,MAAM,YAAY,CAAC,MAAM,aAAa,MAAM,OAAO,MAAM,KAAK;AAAA,EACnK,GAAG,CAAC,eAAe,MAAM,UAAU,MAAM,UAAU,CAAC;AACpD,QAAM,oBAA0B,oBAAY,MAAM;AAChD,UAAM,qBAAqB,MAAM;AACjC,QAAI;AACJ,QAAI,OAAO,uBAAuB,YAAY;AAC5C,YAAM,SAAS;AAAA,QACb,cAAc,iBAAiB;AAAA,QAC/B,QAAQ,CAAC;AAAA,MACX;AACA,uBAAiB,mBAAmB,MAAM;AAAA,IAC5C,OAAO;AACL,uBAAiB;AAAA,IACnB;AACA,UAAM,OAAO,kBAAkB,CAAC,GAC9B;AAAA,MACE;AAAA,IACF,IAAI,MACJ,2BAA2B,8BAA8B,MAAMA,UAAS;AAC1E,UAAM,mBAAmB,SAAS,CAAC,GAAG,iCAAiC;AAAA,MACrE,YAAY,gBAAuB,qBAAAC,KAAK,0BAA0B,SAAS,CAAC,GAAG,QAAQ;AAAA,QACrF;AAAA,MACF,CAAC,CAAC;AAAA,MACF,YAAY,cAAc,QAAQ,cAAc,4BAA4B;AAAA,IAC9E,CAAC;AACD,WAAO,SAAS,CAAC,GAAG,kBAAkB,0BAA0B,iDAAiD;AAAA,EACnH,GAAG,CAAC,eAAe,MAAM,cAAc,CAAC;AACxC,QAAM,uBAA6B,oBAAY,kBAAgB;AAC7D,QAAI,MAAM,YAAY;AACpB,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,kDAAkD;AAC9E,UAAM,2BAA2B,MAAM;AACvC,UAAM,qBAAqB,aAAa,OAAO,mBAAmB;AAClE,QAAI,0BAA0B;AAC5B,YAAM,oBAAoB,kBAAkB;AAC5C,UAAI,oBAAoB;AACtB,0BAAkB,QAAQ,mBAAmB;AAC7C,0BAAkB,OAAO,mBAAmB;AAAA,MAC9C;AACA,mBAAa,OAAO,mBAAmB,IAAI;AAC3C,UAAI,sBAAsB,MAAM;AAC9B,qBAAa,gBAAgB,CAAC,qBAAqB,GAAG,aAAa,aAAa;AAAA,MAClF;AAAA,IACF,WAAW,CAAC,4BAA4B,oBAAoB;AAC1D,aAAO,aAAa,OAAO,mBAAmB;AAC9C,mBAAa,gBAAgB,aAAa,cAAc,OAAO,WAAS,UAAU,mBAAmB;AAAA,IACvG;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,UAAU,MAAM,YAAY,iBAAiB,CAAC;AACxD,QAAM,2BAAiC,oBAAY,YAAU;AAC3D,QAAI,CAAC,MAAM,iBAAiB;AAC1B,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,UAAM,wBAAwB,YAAU;AAAA,MACtC,IAAI;AAAA,MACJ,MAAM,MAAM,gBAAgB,OAAO,uBAAuB,KAAK,CAAC,EAAE,IAAI,UAAQ;AAAA,QAC5E;AAAA,QACA,OAAO;AAAA,MACT,EAAE;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,SAAS,UAAU,SAAS;AACnD,YAAM,IAAI,MAAM,CAAC,mEAAmE,qBAAqB,OAAO,SAAS,QAAQ,mBAAmB,SAAS,KAAK,UAAU,KAAK,IAAI,UAAQ,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,IACxN;AACA,QAAI,OAAO,QAAQ,SAAS,QAAQ;AAClC,aAAO,cAAc;AAAA,QACnB,cAAc,OAAO;AAAA,QACrB,OAAO,OAAO,QAAQ,KAAK,IAAI,qBAAqB;AAAA,QACpD,+BAA+B,MAAM;AAAA,QACrC,0BAA0B,MAAM;AAAA,QAChC,cAAc,iBAAiB;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,cAAc;AAAA,MACnB,OAAO;AAAA,QACL,UAAU,OAAO,QAAQ,QAAQ,OAAO,IAAI,qBAAqB;AAAA,QACjE,UAAU,OAAO,QAAQ,QAAQ,OAAO,IAAI,qBAAqB;AAAA,QACjE,SAAS,OAAO,QAAQ,QAAQ;AAAA,MAClC;AAAA,MACA,cAAc,OAAO;AAAA,MACrB,mBAAmB,OAAO;AAAA,MAC1B,+BAA+B,MAAM;AAAA,MACrC,0BAA0B,MAAM;AAAA,MAChC,cAAc,iBAAiB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,iBAAiB,MAAM,+BAA+B,MAAM,wBAAwB,CAAC;AAC/F,QAAM,aAAmB,oBAAY,YAAU;AAC7C,UAAM,UAAU,oBAAoB,aAAa;AACjD,WAAO,0BAA0B;AAAA,MAC/B;AAAA,MACA,sBAAsB,OAAO;AAAA,MAC7B,0BAA0B,MAAM;AAAA,MAChC,aAAa,OAAO;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,MAAM,wBAAwB,CAAC;AAClD,QAAM,WAAiB,oBAAY,YAAU;AAC3C,UAAM,UAAU,oBAAoB,aAAa;AACjD,WAAO,YAAY;AAAA,MACjB;AAAA,MACA,aAAa,OAAO;AAAA,MACpB,wBAAwB,MAAM;AAAA,MAC9B,8BAA8B;AAAA,IAChC,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,MAAM,sBAAsB,CAAC;AAChD,+BAA6B,eAAe,kBAAkB,oBAAoB;AAClF,mCAAiC,eAAe,iBAAiB,SAAS,mBAAmB,wBAAwB;AACrH,mCAAiC,eAAe,iBAAiB,SAAS,aAAa,UAAU;AACjG,mCAAiC,eAAe,iBAAiB,SAAS,WAAW,QAAQ;AAC7F,mCAAiC,eAAe,iBAAiB,SAAS,6BAA6B,oBAAoB;AAK3H,iBAAe,MAAM;AACnB,4BAAwB;AAAA,EAC1B,CAAC;AAKD,QAAM,gBAAsB,eAAO,IAAI;AACvC,EAAM,kBAAU,MAAM;AACpB,QAAI,CAAC,cAAc,SAAS;AAC1B,8BAAwB;AAAA,IAC1B,OAAO;AACL,oBAAc,UAAU;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,uBAAuB,CAAC;AAC9B;;;AGjJA,IAAAC,UAAuB;AAGvB,SAAS,8BAA8B,YAAY,UAAU;AAN7D;AAOE,QAAM,QAAQ;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,WAAW,CAAC;AAAA,IACZ,UAAU,CAAC;AAAA,EACb;AACA,iDAAY,QAAZ,mBAAiB,QAAQ,cAAY;AACnC,UAAM,KAAK,qBAAqB,UAAU,QAAQ;AAClD,UAAM,OAAO,KAAK,EAAE;AACpB,UAAM,SAAS,EAAE,IAAI;AAAA,EACvB;AACA,iDAAY,WAAZ,mBAAoB,QAAQ,cAAY;AACtC,UAAM,KAAK,qBAAqB,UAAU,QAAQ;AAClD,UAAM,UAAU,KAAK,EAAE;AACvB,UAAM,SAAS,EAAE,IAAI;AAAA,EACvB;AACA,SAAO;AACT;AACO,IAAM,6BAA6B,CAAC,OAAO,OAAO,WAAW;AAxBpE;AAyBE,SAAO,QAAQ,OAAO,aAAa,8BAA8B,MAAM,YAAY,MAAM,QAAQ;AACjG,SAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACzB,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM;AAAA,MAC7B,qBAAqB,SAAS,CAAC,IAAG,WAAM,SAAN,mBAAY,qBAAqB;AAAA,QACjE,YAAY;AAAA,UACV,KAAK,CAAC;AAAA,UACN,QAAQ,CAAC;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AACO,IAAM,oBAAoB,CAAC,QAAQ,UAAU;AAClD,QAAM,gBAAsB,oBAAY,mBAAiB;AACvD,WAAO,QAAQ,OAAO,aAAa,8BAA8B,eAAe,MAAM,QAAQ;AAC9F,WAAO,QAAQ,iCAAiC,aAAa;AAAA,EAC/D,GAAG,CAAC,QAAQ,MAAM,QAAQ,CAAC;AAC3B,mBAAiB,QAAQ;AAAA,IACvB,wBAAwB;AAAA,EAC1B,GAAG,QAAQ;AACX,QAAM,gBAAsB,eAAO,IAAI;AACvC,EAAM,kBAAU,MAAM;AACpB,QAAI,cAAc,SAAS;AACzB,oBAAc,UAAU;AACxB;AAAA,IACF;AACA,WAAO,QAAQ,uBAAuB,MAAM,UAAU;AAAA,EACxD,GAAG,CAAC,QAAQ,MAAM,UAAU,CAAC;AAC/B;;;ACpDA,IAAAC,UAAuB;AAIhB,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AAZH;AAaE,QAAM,yBAAyB,SAAS,CAAC,GAAG,eAAe,sBAAsB;AACjF,QAAM,OAAO,SAAS,CAAC,GAAG,eAAe,IAAI;AAC7C,QAAM,aAAa,SAAS,CAAC,GAAG,eAAe,UAAU;AAIzD,QAAM,OAAO;AAAA,IACX,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR;AAAA,EACF;AACA,mBAAiB,MAAM,MAAM,YAAY,IAAI;AAC7C,MAAI,CAAC,iBAAiB;AACpB,2BAAuB,KAAK,IAAI;AAAA,EAClC;AAGA,SAAO,QAAQ,OAAO,KAAK,uBAAuB,KAAK,IAAI,SAAS,CAAC,GAAG,QAAQ;AAChF,QAAM,uBAAqB,oBAAe,wBAAf,mBAAoC,eAAc,CAAC;AAC9E,QAAM,eAAe;AAAA,IACnB,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AACA,OAAI,gCAAe,wBAAf,mBAAoC,eAApC,mBAAiD,cAAjD,mBAA4D,SAAS,eAAe;AACtF,WAAO,SAAS,CAAC,GAAG,gBAAgB;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,SAAS,CAAC,GAAG,gBAAgB;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA,qBAAqB,SAAS,CAAC,GAAG,eAAe,qBAAqB;AAAA,MACpE,YAAY,SAAS,CAAC,GAAG,oBAAoB;AAAA,QAC3C,CAAC,QAAQ,GAAG,CAAC,GAAI,mBAAmB,QAAQ,KAAK,CAAC,GAAI,YAAY;AAAA,MACpE,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AACO,IAAM,iCAAiC,YAAU;AACtD,QAAM,yBAA+B,eAAO,IAAI;AAChD,QAAM,gBAAsB,oBAAY,oBAAkB;AA1D5D;AA2DI,UAAM,kBAAkB,OAAO,QAAQ,OAAO,cAAc,CAAC;AAC7D,UAAM,sBAAsB,uBAAuB;AACnD,2BAAuB,UAAU;AACjC,QAAI,oBAAoB,SAAS,CAAC,GAAG,gBAAgB;AAAA,MACnD,qBAAqB,SAAS,CAAC,GAAG,eAAe,qBAAqB;AAAA;AAAA,QAEpE,YAAY,CAAC;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AACD,QAAI,qBAAqB;AACvB,YAAM,mBAAmB,WAAS;AAChC,cAAM,OAAO,kBAAkB,KAAK,KAAK;AACzC,aAAI,6BAAM,UAAS,aAAa;AAC9B,iBAAO,kBAAkB,KAAK,KAAK;AACnC,iBAAO,kBAAkB,uBAAuB,KAAK;AACrD,iBAAO,OAAO,QAAQ,OAAO,KAAK,uBAAuB,KAAK;AAAA,QAChE;AAAA,MACF;AACA,gCAAoB,WAApB,mBAA4B,QAAQ;AACpC,gCAAoB,cAApB,mBAA+B,QAAQ;AAAA,IACzC;AACA,0BAAgB,WAAhB,mBAAwB,QAAQ,WAAS;AACvC,0BAAoB,aAAa;AAAA,QAC/B,gBAAgB;AAAA,QAChB,UAAU,gBAAgB,SAAS,KAAK;AAAA,QACxC;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AACA,0BAAgB,cAAhB,mBAA2B,QAAQ,WAAS;AAC1C,0BAAoB,aAAa;AAAA,QAC/B,gBAAgB;AAAA,QAChB,UAAU,gBAAgB,SAAS,KAAK;AAAA,QACxC;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AAGA,UAAI,qBAAgB,cAAhB,mBAA2B,aAAU,qBAAgB,WAAhB,mBAAwB,SAAQ;AACvE,YAAM,gBAAgB,WAAS;AAC7B,YAAI,kBAAkB,KAAK,KAAK,KAAK,kBAAkB,KAAK,KAAK,EAAE,SAAS,aAAa;AACvF,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,kBAAkB,KAAK,kBAAkB;AAC/D,wBAAkB,KAAK,kBAAkB,IAAI,SAAS,CAAC,GAAG,eAAe;AAAA,QACvE,UAAU,cAAc,SAAS,OAAO,aAAa;AAAA,MACvD,CAAC;AACD,wBAAkB,aAAa,kBAAkB,WAAW,OAAO,aAAa;AAAA,IAClF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,CAAC;AACX,+BAA6B,QAAQ,eAAe,aAAa;AACnE;;;ACtHA,IAAAC,UAAuB;;;ACChB,IAAM,0BAA0B,CAAC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AALN;AAME,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,qBAAqB,YAAY,MAAM,MAAM,eAAe,MAAM,YAAY;AACpF,MAAI,aAAa;AACjB,MAAI,WAAW,mBAAmB,SAAS;AAC3C,MAAI,yBAAyB;AAC7B,SAAO,CAAC,0BAA0B,gBAAgB,cAAc;AAC9D,UAAM,+BAA6B,yBAAoB,QAAQ,mBAAmB,UAAU,EAAE,EAAE,MAA7D,mBAAgE,UAAS;AAC5G,UAAM,6BAA2B,yBAAoB,QAAQ,mBAAmB,QAAQ,EAAE,EAAE,MAA3D,mBAA8D,UAAS;AACxG,QAAI,8BAA8B,0BAA0B;AAC1D,+BAAyB;AAAA,IAC3B;AACA,QAAI,CAAC,4BAA4B;AAC/B,oBAAc;AACd,uBAAiB;AAAA,IACnB;AACA,QAAI,CAAC,0BAA0B;AAC7B,kBAAY;AACZ,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,SAAO,yBAAyB;AAAA,IAC9B;AAAA,IACA;AAAA,EACF,IAAI;AACN;;;ADtBO,IAAM,oBAAoB,CAAC,eAAe,UAAU;AACzD,QAAM,YAAY,gBAAgB,eAAe,qBAAqB;AACtE,QAAM,cAAc,gBAAgB,eAAe,uBAAuB;AAC1E,QAAM,4BAAkC,eAAO;AAAA,IAC7C,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,aAAa,MAAM,oBAAoB;AAC7C,QAAM,mCAAyC,oBAAY,YAAU;AACnE,QAAI,YAAY;AACd;AAAA,IACF;AACA,UAAM,kBAAkB;AAAA,MACtB,kBAAkB,OAAO;AAAA,MACzB,iBAAiB,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,IACF;AACA,QAAI,0BAA0B,QAAQ,qBAAqB,OAAO,iBAAiB,0BAA0B,QAAQ,oBAAoB,OAAO,cAAc;AAC5J;AAAA,IACF;AACA,8BAA0B,UAAU;AAAA,MAClC,kBAAkB,OAAO;AAAA,MACzB,iBAAiB,OAAO;AAAA,IAC1B;AACA,QAAI,UAAU,WAAW,KAAK,YAAY,MAAM,WAAW,GAAG;AAC5D,YAAM,qBAAqB,eAAe,eAAe;AAAA,QACvD,YAAY,MAAM;AAAA,QAClB,gBAAgB,MAAM;AAAA,MACxB,CAAC;AACD,YAAM,sBAAsB,wBAAwB;AAAA,QAClD,QAAQ;AAAA,QACR,aAAa,mBAAmB;AAAA,QAChC,OAAO;AAAA,UACL,eAAe,OAAO;AAAA,UACtB,cAAc,OAAO;AAAA,QACvB;AAAA,MACF,CAAC;AACD,UAAI,CAAC,qBAAqB;AACxB;AAAA,MACF;AACA,sBAAgB,mBAAmB,oBAAoB;AACvD,sBAAgB,kBAAkB,oBAAoB;AAAA,IACxD;AACA,kBAAc,QAAQ,aAAa,aAAa,eAAe;AAAA,EACjE,GAAG,CAAC,eAAe,YAAY,MAAM,YAAY,MAAM,gBAAgB,WAAW,WAAW,CAAC;AAC9F,QAAM,4BAAkC,oBAAY,kBAAgB;AAClE,QAAI,YAAY;AACd;AAAA,IACF;AACA,kBAAc,QAAQ,iCAAiC,aAAa;AACpE,UAAM,gBAAgB,0BAA0B,aAAa;AAC7D,UAAM,kBAAkB;AAAA,MACtB,kBAAkB,cAAc;AAAA,MAChC,iBAAiB,cAAc;AAAA,MAC/B,WAAW;AAAA,MACX;AAAA,IACF;AACA,kBAAc,QAAQ,aAAa,aAAa,eAAe;AAAA,EACjE,GAAG,CAAC,eAAe,YAAY,WAAW,CAAC;AAC3C,QAAM,8BAAoC,oBAAY,oBAAkB;AACtE,QAAI,YAAY;AACd;AAAA,IACF;AACA,kBAAc,QAAQ,iCAAiC,aAAa;AACpE,UAAM,gBAAgB,0BAA0B,aAAa;AAC7D,UAAM,kBAAkB;AAAA,MACtB,kBAAkB,cAAc;AAAA,MAChC,iBAAiB,cAAc;AAAA,MAC/B;AAAA,MACA,aAAa;AAAA,IACf;AACA,kBAAc,QAAQ,aAAa,aAAa,eAAe;AAAA,EACjE,GAAG,CAAC,eAAe,YAAY,SAAS,CAAC;AACzC,eAAa,eAAe,8BAA8B,gCAAgC;AAC1F,eAAa,eAAe,mBAAmB,yBAAyB;AACxE,eAAa,eAAe,qBAAqB,2BAA2B;AAC5E,uBAAqB,eAAe,aAAa,MAAM,WAAW;AACpE;;;AExFA,IAAAC,UAAuB;AAGhB,IAAM,4BAA4B;AACzC,IAAM,mBAAmB,WAAS,GAAG,yBAAyB,IAAI,KAAK;AAChE,IAAM,iCAAiC,CAAC,eAAe,UAAU;AACtE,QAAM,kBAAwB,oBAAY,oBAAkB;AAC1D,UAAM,YAAY,eAAe,KAAK,kBAAkB;AACxD,QAAI,MAAM,oBAAoB,YAAY,CAAC,MAAM,YAAY,UAAU,SAAS,UAAU,MAAM,UAAU;AACxG,aAAO;AAAA,IACT;AACA,UAAM,OAAO,SAAS,CAAC,GAAG,eAAe,IAAI;AAC7C,UAAM,oBAAoB,CAAC,GAAG,UAAU,QAAQ;AAChD,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,UAAU,SAAS,QAAQ,KAAK,GAAG;AACtE,YAAM,aAAa,iBAAiB,CAAC;AACrC,wBAAkB,KAAK,UAAU;AACjC,YAAM,kBAAkB;AAAA,QACtB,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AACA,WAAK,UAAU,IAAI;AAAA,IACrB;AACA,SAAK,kBAAkB,IAAI,SAAS,CAAC,GAAG,WAAW;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,SAAS,CAAC,GAAG,gBAAgB;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,UAAU,MAAM,eAAe,CAAC;AAC1C,+BAA6B,eAAe,eAAe,eAAe;AAC5E;;;AC9BA,IAAAC,UAAuB;;;ACDR,SAAR,SAA0B,MAAM,OAAO,KAAK;AACjD,MAAI;AACJ,WAAS,aAAa,MAAM;AAC1B,UAAM,QAAQ,MAAM;AAElB,WAAK,MAAM,MAAM,IAAI;AAAA,IACvB;AACA,iBAAa,OAAO;AACpB,cAAU,WAAW,OAAO,IAAI;AAAA,EAClC;AACA,YAAU,QAAQ,MAAM;AACtB,iBAAa,OAAO;AAAA,EACtB;AACA,SAAO;AACT;;;ADLA,IAAI,kBAA8B,SAAUC,iBAAgB;AAC1D,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,CAAC,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,CAAC,IAAI;AACnD,SAAOA;AACT,GAAE,kBAAkB,CAAC,CAAC;AACtB,IAAM,+BAA+B;AAAA,EACnC,kBAAkB;AAAA,EAClB,iBAAiB;AACnB;AACA,IAAMC,oBAAmB,WAAS,GAAG,yBAAyB,IAAI,KAAK;AAOhE,IAAM,8BAA8B,CAAC,eAAe,UAAU;AACnE,QAAM,0BAAgC,oBAAY,MAAM;AACtD,kBAAc,QAAQ,wBAAwB,kBAAkB,YAAY,6BAA6B,aAAa,MAAM,cAAc,MAAM,cAAc,MAAM,OAAO,MAAM,KAAK;AAAA,EACxL,GAAG,CAAC,eAAe,MAAM,aAAa,MAAM,UAAU,CAAC;AACvD,QAAM,CAAC,qCAAqC,sCAAsC,IAAU,iBAAS,KAAK;AAC1G,QAAM,4BAAkC,eAAO,4BAA4B;AAC3E,QAAM,uBAA6B,eAAO,CAAC;AAC3C,QAAM,iBAAuB,eAAO,IAAI;AACxC,QAAM,YAAkB,eAAO,KAAK;AACpC,QAAM,eAAqB,eAAO,IAAI;AACtC,QAAM,YAAkB,oBAAY,YAAU;AAC5C,kBAAc,QAAQ,WAAW,UAAU,oBAAoB,MAAM;AAAA,EACvE,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,qBAA2B,gBAAQ,MAAM,SAAS,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;AAIlF,QAAM,kBAAwB,oBAAY,YAAU;AAClD,QAAI,OAAO,OAAO,UAAU,UAAU;AACpC,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,4BAA4B,aAAa;AACjE,WAAO,SAAS,CAAC,GAAG,QAAQ;AAAA,MAC1B,OAAO,OAAO,QAAQ,OAAO,QAAQ,gBAAgB;AAAA,MACrD,KAAK,OAAO,MAAM,gBAAgB,WAAW,OAAO,MAAM,gBAAgB,WAAW;AAAA,IACvF,CAAC;AAAA,EACH,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,YAAkB,oBAAY,MAAM;AACxC,kBAAc,QAAQ,WAAW,IAAI;AACrC,kBAAc,QAAQ,WAAW,MAAM,MAAM;AAC7C,cAAU,UAAU;AACpB,yBAAqB,UAAU;AAC/B,UAAM,kBAAkB,4BAA4B,aAAa;AACjE,UAAM,YAAY,sBAAsB,aAAa;AACrD,UAAM,cAAc,wBAAwB,aAAa;AACzD,UAAM,gBAAgB;AAAA,MACpB,OAAO;AAAA,MACP,KAAK,gBAAgB,WAAW;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AACA,cAAU,aAAa;AAAA,EACzB,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,QAAM,sBAA4B,oBAAY,CAAC,wBAAwB,sBAAsB;AAI3F,QAAI,2BAA2B,eAAe,YAAY,sBAAsB,eAAe,YAAY;AACzG,gBAAU;AACV;AAAA,IACF;AAIA,UAAM,OAAO,cAAc,QAAQ,MAAM,KAAK;AAC9C,UAAM,YAAY,KAAK,kBAAkB;AACzC,UAAM,oBAAoB,CAAC,GAAG,UAAU,QAAQ;AAChD,UAAM,eAAe,cAAc,QAAQ,MAAM,WAAW;AAC5D,UAAM,oBAAoB,kBAAkB;AAC5C,QAAI,oBAAoB,cAAc;AACpC,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,QAAM,kBAAwB,oBAAY,MAAM;AA1FlD;AA2FI,UAAM,OAAO,cAAc,QAAQ,MAAM,KAAK;AAC9C,UAAM,YAAY,KAAK,kBAAkB;AACzC,UAAM,oBAAoB,CAAC,GAAG,UAAU,QAAQ;AAChD,UAAM,eAAe,cAAc,QAAQ,MAAM,WAAW;AAC5D,UAAM,oBAAoB,kBAAkB;AAM5C,QAAI,sBAAsB,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB;AAAA,MACjC,OAAO,0BAA0B,QAAQ;AAAA,MACzC,KAAK,0BAA0B,QAAQ;AAAA,IACzC,CAAC;AACD,QAAI,aAAa;AACjB,UAAM,gBAAgB,0BAA0B,QAAQ,qBAAqB,KAAK,0BAA0B,QAAQ,oBAAoB;AACxI,aAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK,GAAG;AAC7C,UAAI,eAAe;AACjB;AAAA,MACF;AAEA,UAAI,WAAW,SAAS,KAAK,KAAK,WAAW,SAAO,UAAK,kBAAkB,CAAC,CAAC,MAAzB,mBAA4B,UAAS;AAAA,QAEzF,UAAK,kBAAkB,CAAC,CAAC,MAAzB,mBAA4B,QAAO,aAAa,SAC9C;AACA;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,kBAAkB,CAAC,CAAC,EAAE;AACzC,YAAM,kBAAkB;AAAA,QACtB,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AACA,WAAK,KAAK,IAAI;AACd,mBAAa;AAAA,IACf;AAGA,QAAI,eAAe,YAAY,eAAe,UAAU;AAEtD,eAAS,IAAI,GAAG,IAAI,eAAe,mBAAmB,KAAK,GAAG;AAC5D,cAAM,aAAaA,kBAAiB,IAAI,iBAAiB;AACzD,0BAAkB,KAAK,UAAU;AACjC,cAAM,kBAAkB;AAAA,UACtB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AACA,aAAK,UAAU,IAAI;AACnB,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,SAAK,kBAAkB,IAAI,SAAS,CAAC,GAAG,WAAW;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,kBAAc,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,MAC1D,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH,CAAC,GAAG,iBAAiB;AAAA,EACvB,GAAG,CAAC,eAAe,eAAe,CAAC;AACnC,QAAM,uBAA6B,oBAAY,cAAY;AACzD,UAAM,oBAAoB,aAAa,KAAK,eAAe,aAAa,eAAe;AACvF,QAAI,eAAe,YAAY,MAAM;AACnC,0BAAoB,eAAe,SAAS,iBAAiB;AAAA,IAC/D;AACA,QAAI,eAAe,YAAY,mBAAmB;AAChD,qBAAe,UAAU;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,mBAAmB,CAAC;AACxB,QAAM,mBAAyB,oBAAY,YAAU;AACnD,QAAI,WAAW,QAAQ;AACrB;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,cAAc,QAAQ,MAAM,WAAW;AAC5D,UAAM,OAAO,cAAc,QAAQ,MAAM,KAAK;AAC9C,UAAM,yBAAyB,cAAc,QAAQ,MAAM,KAAK;AAChE,QAAI,SAAS,aAAa,UAAa,iBAAiB,QAAW;AACjE,oBAAc,QAAQ,YAAY,SAAS,aAAa,SAAY,KAAK,SAAS,QAAQ;AAAA,IAC5F;AAGA,QAAI,UAAU,WAAW,OAAO,YAAY,UAAU,GAAG;AACvD,oBAAc,QAAQ,OAAO;AAAA,QAC3B,KAAK;AAAA,MACP,CAAC;AAED,oBAAc,QAAQ,QAAQ,SAAS,IAAI;AAAA,IAC7C,OAAO;AACL,YAAM,YAAY,KAAK,kBAAkB;AACzC,YAAM,oBAAoB,CAAC,GAAG,UAAU,QAAQ;AAChD,YAAM,uBAAuB,iCAAiC,aAAa;AAC3E,YAAM,gBAAgB,OAAO,YAAY,UAAU,WAAW,KAAK,IAAI,qBAAqB,QAAQ,YAAY,KAAK,GAAG,CAAC,IAAI,YAAY;AAGzI,UAAI,oBAAoB;AACxB,eAAS,KAAK,QAAQ,SAAO;AAC3B,cAAM,QAAQ,kBAAkB,eAAe,GAAG;AAClD,YAAI,KAAK,KAAK,KAAK,uBAAuB,KAAK,GAAG;AAChD,gBAAM,QAAQ,kBAAkB,QAAQ,KAAK;AAC7C,cAAI,UAAU,IAAI;AAChB,kBAAM,aAAaA,kBAAiB,KAAK;AACzC,8BAAkB,KAAK,IAAI;AAC3B,iBAAK,UAAU,IAAI;AAAA,cACjB,MAAM;AAAA,cACN,IAAI;AAAA,cACJ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,KAAK,KAAK;AACjB,iBAAO,uBAAuB,KAAK;AACnC,+BAAqB;AAAA,QACvB;AAAA,MACF,CAAC;AACD,UAAI,oBAAoB,GAAG;AACzB,aAAK,kBAAkB,IAAI,SAAS,CAAC,GAAG,WAAW;AAAA,UACjD,UAAU;AAAA,QACZ,CAAC;AACD,sBAAc,QAAQ,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,UAC1D,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM;AAAA,YAC7B;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC,CAAC;AAAA,MACJ;AACA,oBAAc,QAAQ,qBAAqB,eAAe,SAAS,IAAI;AAAA,IACzE;AACA,cAAU,UAAU;AACpB,QAAI,eAAe,YAAY,MAAM;AACnC,2BAAqB,cAAc,QAAQ,MAAM,WAAW,QAAQ;AAAA,IACtE;AACA,oBAAgB;AAChB,kBAAc,QAAQ,WAAW,KAAK;AACtC,kBAAc,QAAQ,6BAA6B,yBAAyB;AAAA,MAC1E,QAAQ,OAAO;AAAA,MACf;AAAA,IACF,GAAG,KAAK;AACR,kBAAc,QAAQ,iCAAiC,aAAa;AAAA,EACtE,GAAG,CAAC,eAAe,sBAAsB,eAAe,CAAC;AACzD,QAAM,uBAA6B,oBAAY,MAAM;AACnD,QAAI,UAAU,WAAW,eAAe,YAAY,MAAM;AACxD;AAAA,IACF;AACA,yBAAqB,cAAc,QAAQ,MAAM,WAAW,QAAQ;AACpE,oBAAgB;AAChB,kBAAc,QAAQ,iCAAiC,aAAa;AAAA,EACtE,GAAG,CAAC,eAAe,sBAAsB,eAAe,CAAC;AACzD,QAAM,qBAAqB,yBAAiB,MAAM;AAChD,QAAI,UAAU,WAAW,eAAe,YAAY,eAAe,YAAY;AAC7E;AAAA,IACF;AACA,UAAM,gBAAgB,0BAA0B,aAAa;AAC7D,QAAI,qBAAqB,WAAW,cAAc,cAAc;AAC9D;AAAA,IACF;AACA,yBAAqB,UAAU,cAAc;AAC7C,UAAM,kBAAkB,4BAA4B,aAAa;AACjE,UAAM,YAAY,sBAAsB,aAAa;AACrD,UAAM,cAAc,wBAAwB,aAAa;AACzD,UAAM,gBAAgB;AAAA,MACpB,OAAO,cAAc;AAAA,MACrB,KAAK,cAAc,eAAe,gBAAgB,WAAW;AAAA,MAC7D;AAAA,MACA;AAAA,IACF;AACA,kBAAc,QAAQ,WAAW,IAAI;AACrC,cAAU,gBAAgB,aAAa,CAAC;AAAA,EAC1C,CAAC;AACD,QAAM,mCAAyC,oBAAY,YAAU;AACnE,QAAI,UAAU,SAAS;AACrB;AAAA,IACF;AACA,UAAM,YAAY,sBAAsB,aAAa;AACrD,UAAM,cAAc,wBAAwB,aAAa;AACzD,UAAM,gBAAgB;AAAA,MACpB,OAAO,OAAO;AAAA,MACd,KAAK,OAAO,eAAe;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,QAAI,0BAA0B,QAAQ,qBAAqB,OAAO,iBAAiB,0BAA0B,QAAQ,oBAAoB,OAAO,cAAc;AAC5J;AAAA,IACF;AACA,8BAA0B,UAAU;AAAA,MAClC,kBAAkB,OAAO;AAAA,MACzB,iBAAiB,OAAO;AAAA,IAC1B;AACA,UAAM,qBAAqB,eAAe,aAAa;AACvD,UAAM,sBAAsB,wBAAwB;AAAA,MAClD,QAAQ;AAAA,MACR,aAAa,mBAAmB;AAAA,MAChC,OAAO;AAAA,QACL,eAAe,OAAO;AAAA,QACtB,cAAc,OAAO,eAAe;AAAA,MACtC;AAAA,IACF,CAAC;AACD,QAAI,CAAC,qBAAqB;AACxB;AAAA,IACF;AACA,kBAAc,QAAQ,oBAAoB;AAC1C,kBAAc,MAAM,oBAAoB;AACxC,cAAU,gBAAgB,aAAa,CAAC;AAAA,EAC1C,GAAG,CAAC,eAAe,iBAAiB,SAAS,CAAC;AAC9C,QAAM,4CAAkD,gBAAQ,MAAM,SAAS,kCAAkC,MAAM,4BAA4B,GAAG,CAAC,MAAM,8BAA8B,gCAAgC,CAAC;AAC5N,EAAM,kBAAU,MAAM;AACpB,WAAO,MAAM;AACX,gDAA0C,MAAM;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,yCAAyC,CAAC;AAC9C,QAAM,4BAAkC,oBAAY,kBAAgB;AAClE,cAAU,UAAU;AACpB,8CAA0C,MAAM;AAChD,yBAAqB,UAAU;AAC/B,UAAM,kBAAkB,4BAA4B,aAAa;AACjE,UAAM,cAAc,wBAAwB,aAAa;AACzD,UAAM,gBAAgB;AAAA,MACpB,OAAO;AAAA,MACP,KAAK,gBAAgB,WAAW;AAAA,MAChC,WAAW;AAAA,MACX;AAAA,IACF;AACA,kBAAc,QAAQ,WAAW,IAAI;AACrC,uBAAmB,aAAa;AAAA,EAClC,GAAG,CAAC,eAAe,oBAAoB,yCAAyC,CAAC;AACjF,QAAM,8BAAoC,oBAAY,oBAAkB;AACtE,cAAU,UAAU;AACpB,8CAA0C,MAAM;AAChD,yBAAqB,UAAU;AAC/B,UAAM,kBAAkB,4BAA4B,aAAa;AACjE,UAAM,YAAY,sBAAsB,aAAa;AACrD,UAAM,gBAAgB;AAAA,MACpB,OAAO;AAAA,MACP,KAAK,gBAAgB,WAAW;AAAA,MAChC;AAAA,MACA,aAAa;AAAA,IACf;AACA,kBAAc,QAAQ,WAAW,IAAI;AACrC,uBAAmB,aAAa;AAAA,EAClC,GAAG,CAAC,eAAe,oBAAoB,yCAAyC,CAAC;AACjF,QAAM,kBAAwB,oBAAY,SAAO;AAC/C,iBAAa,UAAU,IAAI;AAAA,EAC7B,GAAG,CAAC,CAAC;AACL,QAAM,gBAAsB,oBAAY,MAAM;AAC5C,iBAAa,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,QAAM,+BAAqC,oBAAY,MAAM;AAC3D,2CAAuC,cAAc,QAAQ,kBAAkB,kBAAkB,UAAU,MAAM,6BAA6B,WAAW;AAAA,EAC3J,GAAG,CAAC,aAAa,CAAC;AAClB,mCAAiC,eAAe,6BAA6B,aAAa,wBAAwB,gBAAgB;AAClI,eAAa,eAAe,8BAA8B,4BAA4B;AACtF,eAAa,eAAe,kBAAkB,MAAM,qCAAqC,oBAAoB,CAAC;AAC9G,eAAa,eAAe,6BAA6B,MAAM,qCAAqC,kBAAkB,CAAC;AACvH,eAAa,eAAe,8BAA8B,MAAM,qCAAqC,yCAAyC,CAAC;AAC/I,eAAa,eAAe,mBAAmB,MAAM,qCAAqC,yBAAyB,CAAC;AACpH,eAAa,eAAe,qBAAqB,MAAM,qCAAqC,2BAA2B,CAAC;AACxH,eAAa,eAAe,gBAAgB,MAAM,qCAAqC,eAAe,CAAC;AACvG,eAAa,eAAe,cAAc,MAAM,qCAAqC,aAAa,CAAC;AACnG,EAAM,kBAAU,MAAM;AACpB,4BAAwB;AAAA,EAC1B,GAAG,CAAC,uBAAuB,CAAC;AAC9B;;;AE1WA,IAAAC,UAAuB;AAKvB,IAAAC,uBAA4B;AAC5B,IAAM,gCAAgCC,gBAAO,KAAK,EAAE;AAAA,EAClD,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AAKM,IAAM,qCAAqC,CAAC,QAAQ,UAAU;AACnE,QAAM,UAAU,gBAAgB,QAAQ,sBAAsB,EAAE;AAChE,QAAM,WAAiB,eAAO,IAAI;AAClC,QAAM,sBAAsB,WAAW;AACvC,QAAM,iBAAuB,eAAO,IAAI;AACxC,QAAM,sBAAsB,MAAM,oBAAoB,YAAY,CAAC,CAAC,MAAM;AAC1E,QAAM,sBAAsB,MAAM,cAAc,MAAM;AACtD,QAAM,YAAY,uBAAuB;AACzC,QAAM,2BAA2B,yBAAiB,CAAC,CAAC,KAAK,MAAM;AA1BjE;AA2BI,UAAM,eAAe,MAAM;AAC3B,UAAM,iBAAiB,MAAM;AAC7B,QAAI,kBAAkB,iBAAiB,GAAG;AACxC,qBAAS,YAAT,mBAAkB;AAElB,qBAAe,UAAU;AACzB,aAAO,QAAQ,aAAa,2BAA2B;AAAA,IACzD;AAAA,EACF,CAAC;AACD,EAAM,kBAAU,MAAM;AApCxB;AAqCI,UAAM,kBAAkB,OAAO,QAAQ,mBAAmB;AAC1D,QAAI,CAAC,aAAa,CAAC,WAAW,CAAC,iBAAiB;AAC9C;AAAA,IACF;AACA,mBAAS,YAAT,mBAAkB;AAClB,UAAM,4BAA4B,sCAAsC,MAAM;AAC9E,UAAM,eAAe,MAAM,qBAAqB;AAChD,aAAS,UAAU,IAAI,qBAAqB,0BAA0B;AAAA,MACpE,WAAW;AAAA,MACX,MAAM;AAAA,MACN,YAAY,WAAW,YAAY;AAAA,IACrC,CAAC;AACD,QAAI,eAAe,SAAS;AAC1B,eAAS,QAAQ,QAAQ,eAAe,OAAO;AAAA,IACjD;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,0BAA0B,WAAW,MAAM,kBAAkB,CAAC;AACnF,QAAM,eAAe,UAAQ;AArD/B;AAsDI,QAAI,eAAe,YAAY,MAAM;AACnC,qBAAS,YAAT,mBAAkB;AAClB,qBAAe,UAAU;AACzB,UAAI,eAAe,SAAS;AAC1B,uBAAS,YAAT,mBAAkB,QAAQ,eAAe;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAmB,oBAAY,UAAQ;AAE3C,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAUA,wBAAoB,MAAM,GAAG,MAAM,aAAa,IAAI,CAAC;AAAA,EACvD,GAAG,CAAC,WAAW,mBAAmB,CAAC;AACnC,QAAM,mCAAyC,oBAAY,CAAC;AAAA,IAC1D;AAAA,EACF,MAAM;AACJ,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,eAAoB,qBAAAC,KAAK,+BAA+B;AAAA,MACtD,KAAK;AAAA,MAIL,MAAM;AAAA,IACR,GAAG,WAAW,SAAS,EAAE;AAAA,EAC3B,GAAG,CAAC,WAAW,UAAU,CAAC;AAC1B,QAAM,2BAA2B;AAAA,IAC/B;AAAA,EACF;AACA,mBAAiB,QAAQ,0BAA0B,SAAS;AAC9D;;;AC7FA,IAAAC,UAAuB;;;ACAvB,IAAAC,UAAuB;;;ACDvB,IAAM,0BAA0B;AACzB,IAAI,iBAA6B,SAAUC,gBAAe;AAC/D,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,SAAOA;AACT,GAAE,CAAC,CAAC;AAOG,IAAM,oBAAN,MAAwB;AAAA,EAI7B,YAAY,eAAe,wBAAwB,yBAAyB;AAH5E,2CAAmB,uBAAM,oBAAI,IAAI,GAAG;AACpC,0CAAkB,uBAAM,oBAAI,IAAI,GAAG;AACnC,2CAAmB,uBAAM,oBAAI,IAAI,GAAG;AAKpC,wCAAe,YAAY;AACzB,UAAI,KAAK,eAAe,SAAS,KAAK,KAAK,gBAAgB,QAAQ,KAAK,uBAAuB;AAC7F;AAAA,MACF;AACA,YAAM,aAAa,KAAK,IAAI,KAAK,wBAAwB,KAAK,gBAAgB,MAAM,KAAK,eAAe,IAAI;AAC5G,UAAI,eAAe,GAAG;AACpB;AAAA,MACF;AACA,YAAM,aAAa,MAAM,KAAK,KAAK,cAAc;AACjD,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK,GAAG;AACtC,cAAM,KAAK,WAAW,CAAC;AACvB,aAAK,eAAe,OAAO,EAAE;AAC7B,aAAK,gBAAgB,IAAI,EAAE;AAC3B,aAAK,IAAI,iBAAiB,EAAE;AAAA,MAC9B;AAAA,IACF;AACA,iCAAQ,OAAM,QAAO;AACnB,YAAM,aAAa,CAAC;AACpB,UAAI,QAAQ,QAAM;AAChB,aAAK,eAAe,IAAI,EAAE;AAC1B,mBAAW,EAAE,IAAI;AAAA,MACnB,CAAC;AACD,WAAK,IAAI,SAAS,WAAS,SAAS,CAAC,GAAG,OAAO;AAAA,QAC7C,YAAY,SAAS,CAAC,GAAG,MAAM,YAAY;AAAA,UACzC,SAAS,SAAS,CAAC,GAAG,MAAM,WAAW,SAAS,UAAU;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC,CAAC;AACF,WAAK,aAAa;AAAA,IACpB;AACA,6CAAoB,QAAM;AACxB,WAAK,gBAAgB,OAAO,EAAE;AAC9B,WAAK,gBAAgB,IAAI,EAAE;AAC3B,WAAK,aAAa;AAAA,IACpB;AACA,iCAAQ,MAAM;AACZ,WAAK,eAAe,MAAM;AAC1B,YAAM,KAAK,KAAK,eAAe,EAAE,QAAQ,QAAM,KAAK,oBAAoB,EAAE,CAAC;AAAA,IAC7E;AACA,+CAAsB,QAAM;AAC1B,WAAK,IAAI,WAAW,mBAAmB,IAAI,KAAK;AAChD,WAAK,gBAAgB,OAAO,EAAE;AAC9B,WAAK,aAAa;AAAA,IACpB;AACA,4CAAmB,QAAM;AACvB,UAAI,KAAK,gBAAgB,IAAI,EAAE,GAAG;AAChC,eAAO,cAAc;AAAA,MACvB;AACA,UAAI,KAAK,eAAe,IAAI,EAAE,GAAG;AAC/B,eAAO,cAAc;AAAA,MACvB;AACA,UAAI,KAAK,gBAAgB,IAAI,EAAE,GAAG;AAChC,eAAO,cAAc;AAAA,MACvB;AACA,aAAO,cAAc;AAAA,IACvB;AACA,kDAAyB,MAAM,KAAK,gBAAgB,OAAO,KAAK,eAAe;AA1D7E,SAAK,MAAM,cAAc;AACzB,SAAK,wBAAwB;AAAA,EAC/B;AAyDF;AACO,IAAM,eAAe,CAAC,MAAM,UAAU;AAC3C,QAAM,UAAU,KAAK,KAAK;AAC1B,MAAI,gBAAgB,QAAQ;AAC5B,QAAM,YAAY,CAAC;AACnB,SAAO,iBAAiB,kBAAkB,oBAAoB;AAC5D,UAAM,cAAc,KAAK,aAAa;AACtC,cAAU,KAAK,YAAY,eAAe,EAAE;AAC5C,oBAAgB,YAAY;AAAA,EAC9B;AACA,SAAO,UAAU,QAAQ;AAC3B;;;ADhFO,IAAM,gBAAgB;AAAA,EAC3B,SAAS,CAAC;AAAA,EACV,QAAQ,CAAC;AACX;AACO,IAAM,2BAA2B,CAAC,QAAQ,OAAOC,WAAU,CAAC,MAAM;AAfzE;AAgBE,QAAM,oBAAoB,gBAAgB,QAAQ,4BAA4B;AAC9E,QAAM,oBAAoB,WAAW,MAAM,IAAI,kBAAkB,MAAM,CAAC,EAAE;AAC1E,QAAM,kBAAwB,eAAO,CAAC;AACtC,QAAM,uBAA6B,oBAAY,MAAM;AACnD,sBAAkB,MAAM;AACxB,oBAAgB,UAAU;AAC1B,UAAM,kBAAkB,OAAO,QAAQ,MAAM;AAC7C,QAAI,oBAAoB,eAAe;AACrC,aAAO,QAAQ,qBAAqB;AAAA,IACtC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,iBAAiB,CAAC;AAC9B,QAAM,gBAAsB,oBAAY,CAAC,QAAQ,eAAe;AAC9D,UAAM,YAAY,aAAa,oBAAoB,MAAM,GAAG,OAAO,KAAK;AACxE,WAAO,QAAQ,iBAAiB,CAAC,UAAU,GAAG,SAAS;AACvD,QAAI,cAAc,CAAC,YAAY,YAAY,OAAO,WAAW,GAAG;AAE9D,aAAO,QAAQ,WAAW,MAAM,MAAM;AAAA,IACxC;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AACX,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,QAAQ,OAAO,SAAS;AAAA,IAChD,kBAAkB,kBAAkB;AAAA,IACpC;AAAA,IACA;AAAA,EACF,GAAGA,QAAO,CAAC;AACX,QAAM,0BAAgC,oBAAY,MAAM;AACtD,WAAO,QAAQ,wBAAwB,kBAAkB,YAAY,6BAA6B,SAAS,MAAM,cAAc,CAAC,MAAM,cAAc,MAAM,OAAO,MAAM,KAAK;AAAA,EAC9K,GAAG,CAAC,QAAQ,MAAM,YAAY,MAAM,WAAW,CAAC;AAChD,QAAM,wBAAwB,MAAM;AACpC,QAAM,mBAAyB,oBAAY,OAAM,OAAM;AApDzD,QAAAC,KAAA;AAqDI,UAAM,cAAc,OAAO,QAAQ,6BAA6B,iBAAiB,CAAC,CAAC;AACnF,QAAI,CAAC,MAAM,eAAaA,MAAA,YAAY,gBAAZ,gBAAAA,IAAyB,WAAU,OAAO,GAAG;AACnE,wBAAkB,oBAAoB,EAAE;AACxC;AAAA,IACF;AACA,UAAM,WAAU,WAAM,eAAN,mBAAkB;AAClC,QAAI,CAAC,SAAS;AACZ,wBAAkB,oBAAoB,EAAE;AACxC;AAAA,IACF;AACA,UAAM,UAAU,OAAO,QAAQ,WAAW,EAAE;AAC5C,QAAI,CAAC,SAAS;AACZ,wBAAkB,oBAAoB,EAAE;AACxC;AAAA,IACF;AACA,UAAM,cAAc,SAAS,CAAC,GAAG,0BAA0B,MAAM,GAAG,aAAa;AAAA,MAC/E,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,UAAM,YAAY,kBAAkB,aAAa,WAAW;AAC5D,UAAM,YAAY,UAAU,IAAI,cAAY,MAAM,IAAI,QAAQ,CAAC;AAC/D,UAAM,aAAa,UAAU,KAAK,cAAY,aAAa,MAAS,IAAI,SAAY,kBAAkB,eAAe,SAAS;AAC9H,QAAI,eAAe,QAAW;AAC5B,YAAM,OAAO,WAAW;AACxB,wBAAkB,kBAAkB,EAAE;AACtC,aAAO,QAAQ,iBAAiB,MAAM,QAAQ,IAAI;AAClD,UAAI,WAAW,aAAa,QAAW;AACrC,eAAO,QAAQ,YAAY,WAAW,QAAQ;AAAA,MAChD;AACA,aAAO,QAAQ,wBAAwB,IAAI,IAAI;AAC/C,aAAO,QAAQ,WAAW,mBAAmB,IAAI,KAAK;AACtD;AAAA,IACF;AACA,UAAM,gBAAgB,6BAA6B,MAAM,EAAE,EAAE,KAAK;AAClE,QAAI,eAAe;AACjB,aAAO,QAAQ,WAAW,sBAAsB,IAAI,IAAI;AAAA,IAC1D;AACA,QAAI;AACF,YAAM,kBAAkB,MAAM,QAAQ,WAAW;AACjD,UAAI,CAAC,OAAO,QAAQ,WAAW,EAAE,GAAG;AAElC,0BAAkB,oBAAoB,EAAE;AACxC;AAAA,MACF;AACA,UAAI,kBAAkB,iBAAiB,EAAE,MAAM,cAAc,SAAS;AACpE,eAAO,QAAQ,WAAW,mBAAmB,IAAI,KAAK;AACtD;AAAA,MACF;AACA,wBAAkB,kBAAkB,EAAE;AACtC,YAAM,iBAAiB,kBAAkB,cAAc,aAAa,eAAe;AACnF,qBAAe,QAAQ,CAAC,UAAU,QAAQ;AACxC,cAAM,IAAI,KAAK,QAAQ;AAAA,MACzB,CAAC;AACD,UAAI,gBAAgB,aAAa,QAAW;AAC1C,eAAO,QAAQ,YAAY,gBAAgB,QAAQ;AAAA,MACrD;AAEA,YAAM,eAAe,CAAC;AACtB,sBAAgB,KAAK,QAAQ,SAAO;AAClC,cAAM,QAAQ,kBAAkB,QAAQ,GAAG;AAC3C,cAAM,WAAW,oBAAoB,QAAQ,KAAK;AAClD,YAAI,UAAU;AACZ,uBAAa,KAAK;AAAA,YAChB,IAAI;AAAA,YACJ,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,UAAI,aAAa,SAAS,GAAG;AAE3B,eAAO,QAAQ,iBAAiB,cAAc,QAAQ,IAAI;AAAA,MAC5D;AACA,aAAO,QAAQ,iBAAiB,gBAAgB,MAAM,QAAQ,IAAI;AAClE,aAAO,QAAQ,wBAAwB,IAAI,IAAI;AAAA,IACjD,SAAS,OAAO;AACd,YAAM,qBAAqB;AAC3B,aAAO,QAAQ,WAAW,sBAAsB,IAAI,kBAAkB;AACtE,UAAI,OAAO,0BAA0B,YAAY;AAC/C,8BAAsB,IAAI,iBAAiB;AAAA,UACzC,SAAS,mBAAmB;AAAA,UAC5B,QAAQ;AAAA,UACR,OAAO;AAAA,QACT,CAAC,CAAC;AAAA,MACJ,WAAW,MAAuC;AAChD,iBAAS,CAAC,0HAA0H,uIAAuI,0FAA0F,GAAG,OAAO;AAAA,MACjX;AAAA,IACF,UAAE;AACA,aAAO,QAAQ,WAAW,mBAAmB,IAAI,KAAK;AACtD,wBAAkB,kBAAkB,EAAE;AAAA,IACxC;AAAA,EACF,GAAG,CAAC,mBAAmB,mBAAmB,OAAO,uBAAuB,QAAQ,MAAM,WAAU,WAAM,eAAN,mBAAkB,OAAO,CAAC;AAC1H,QAAM,qBAA2B,oBAAY,CAAC,UAAU,cAAc;AACpE,WAAO,QAAQ,SAAS,WAAS;AAC/B,UAAI,CAAC,MAAM,WAAW,QAAQ,QAAQ,KAAK,cAAc,OAAO;AAC9D,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,SAAS,CAAC,GAAG,MAAM,WAAW,OAAO;AAC7D,UAAI,cAAc,OAAO;AACvB,eAAO,gBAAgB,QAAQ;AAAA,MACjC,OAAO;AACL,wBAAgB,QAAQ,IAAI;AAAA,MAC9B;AACA,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,YAAY,SAAS,CAAC,GAAG,MAAM,YAAY;AAAA,UACzC,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,wBAA8B,oBAAY,CAAC,UAAU,UAAU;AACnE,WAAO,QAAQ,SAAS,WAAS;AAC/B,YAAM,iBAAiB,SAAS,CAAC,GAAG,MAAM,WAAW,MAAM;AAC3D,UAAI,UAAU,QAAQ,eAAe,QAAQ,MAAM,QAAW;AAC5D,eAAO,eAAe,QAAQ;AAAA,MAChC,OAAO;AACL,uBAAe,QAAQ,IAAI;AAAA,MAC7B;AACA,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,YAAY,SAAS,CAAC,GAAG,MAAM,YAAY;AAAA,UACzC,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,uBAA6B,oBAAY,MAAM;AACnD,WAAO,QAAQ,SAAS,WAAS;AAC/B,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,YAAY;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,qBAA2B,oBAAY,cAAY;AACvD,UAAM,UAAU,oBAAoB,QAAQ,QAAQ;AACpD,QAAI,CAAC,WAAW,QAAQ,SAAS,WAAW,QAAQ,SAAS,WAAW,GAAG;AACzE;AAAA,IACF;AACA,UAAM,cAAc,CAAC;AACrB,UAAM,WAAW,YAAU;AACzB,YAAM,OAAO,oBAAoB,QAAQ,MAAM;AAC/C,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,UAAI,KAAK,SAAS,WAAW,KAAK,SAAS,SAAS,GAAG;AACrD,aAAK,SAAS,QAAQ,QAAQ;AAAA,MAChC;AACA,kBAAY,KAAK;AAAA,QACf,IAAI;AAAA,QACJ,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,YAAQ,SAAS,QAAQ,QAAQ;AACjC,QAAI,YAAY,SAAS,GAAG;AAC1B,aAAO,QAAQ,iBAAiB,aAAa,QAAQ,IAAI;AAAA,IAC3D;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,gBAAgB;AAAA,IACpB,YAAY,SAAS,CAAC,GAAG,IAAI,OAAO,YAAY;AAAA,MAC9C;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,EAAM,kBAAU,MAAM;AACpB,QAAI,qBAAqB,kBAAkB,UAAU,gBAAgB,UAAU,kBAAkB,QAAQ;AACvG,YAAM,mBAAmB,kBAAkB,MAAM,gBAAgB,OAAO;AACxE,wBAAkB,MAAM,gBAAgB;AACxC,sBAAgB,UAAU,kBAAkB;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,QAAQ,mBAAmB,iBAAiB,CAAC;AACjD,SAAO;AAAA,IACL,KAAK;AAAA,MACH,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ADtOA,SAAS,UAAU,QAAQ;AACzB,SAAO,KAAK,UAAU,CAAC,OAAO,aAAa,OAAO,WAAW,OAAO,WAAW,OAAO,OAAO,OAAO,GAAG,CAAC;AAC1G;AACO,IAAM,6BAA6B,WAAS;AACjD,SAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AACA,IAAM,UAAU;AAAA,EACd,cAAc;AAAA,IACZ,QAAQ;AAAA,EACV;AACF;AACO,IAAM,uBAAuB,CAAC,QAAQ,UAAU;AACrD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,yBAAyB,QAAQ,OAAO,OAAO;AACnD,mBAAiB,QAAQ,IAAI,QAAQ,QAAQ;AAC7C,mBAAiB,QAAQ,IAAI,SAAS,SAAS;AAC/C,mCAAiC,QAAQ,kBAAkB,cAAc,kBAAkB,OAAO,kBAAkB,SAAS;AAC7H,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,OAAO,OAAO,MAAM;AACnD,iBAAgB,QAAQ,OAAO,OAAO;AAAA,EACxC,CAAC;AACD,EAAM,kBAAU,MAAM;AACpB,4BAAwB;AAAA,EAC1B,GAAG,CAAC,uBAAuB,CAAC;AAC9B;;;AGnCO,IAAM,4BAA4B,CAAC,GAAG,wBAAwB,WAAS,MAAM,cAAc,MAAM,sBAAsB,wFAAwF,QAAW,WAAS,MAAM,YAAY,MAAM,eAAe,YAAY,CAAC,MAAM,cAAc,4FAA4F,QAAW,WAAS,CAAC,MAAM,cAAc,MAAM,gCAAgC,sGAAsG,QAAW,WAAS,MAAM,cAAc,cAAc,YAAY,MAAM,mBAAmB,YAAY,MAAM,oBAAoB,YAAY,SAAS,MAAM,QAAQ,KAAK,oLAAoL,QAAW,WAAS,MAAM,cAAc,cAAc,aAAa,MAAM,oBAAoB,YAAY,MAAM,oBAAoB,MAAM,eAAe,oLAAoL,MAAS;;;ACmC9uC,IAAI,uBAAmC,SAAUC,sBAAqB;AAC3E,EAAAA,qBAAoB,SAAS,IAAI;AACjC,EAAAA,qBAAoB,YAAY,IAAI;AACpC,SAAOA;AACT,GAAE,CAAC,CAAC;", "names": ["React", "_a", "_jsx", "React", "React", "useGridRootProps", "import_jsx_runtime", "useGridRootProps", "_jsx", "_jsxs", "import_jsx_runtime", "_excluded", "GridColumnHeaders", "_jsxs", "_jsx", "PropTypes", "React", "React", "import_prop_types", "useGridApiContext", "import_jsx_runtime", "useGridApiContext", "useGridRootProps", "side", "_jsx", "_jsxs", "PropTypes", "import_jsx_runtime", "GridProColumnMenu", "_jsx", "React", "import_prop_types", "import_jsx_runtime", "_jsx", "_jsxs", "PropTypes", "React", "import_prop_types", "React", "React", "import_prop_types", "import_prop_types", "PropTypes", "React", "import_jsx_runtime", "_excluded", "useGridRootProps", "_jsxs", "_jsx", "PropTypes", "React", "import_jsx_runtime", "useGridRootProps", "_jsx", "import_jsx_runtime", "_excluded", "useUtilityClasses", "useGridRootProps", "_jsx", "_jsxs", "PropTypes", "React", "useGridPrivateApiContext", "React", "React", "import_prop_types", "import_jsx_runtime", "useUtilityClasses", "useGridRootProps", "useGridApiContext", "_jsx", "PropTypes", "import_jsx_runtime", "_jsx", "React", "React", "import_jsx_runtime", "useGridPrivateApiContext", "useGridRootProps", "_jsx", "import_jsx_runtime", "useUtilityClasses", "useGridRootProps", "useGridPrivateApiContext", "_jsx", "React", "import_jsx_runtime", "useUtilityClasses", "_jsx", "React", "import_jsx_runtime", "_jsx", "useGridRootProps", "React", "useGridPrivateApiContext", "useGridRootProps", "React", "_a", "React", "React", "useUtilityClasses", "React", "React", "import_jsx_runtime", "useUtilityClasses", "useGridPrivateApiContext", "useGridRootProps", "_jsx", "_jsxs", "TreeDataStrategy", "import_jsx_runtime", "_excluded", "_jsx", "React", "React", "React", "React", "useEnhancedEffect_default", "useEnhancedEffect_default", "React", "React", "React", "useTimeout", "React", "import_prop_types", "import_jsx_runtime", "useUtilityClasses", "styled_default", "useGridRootProps", "_jsxs", "_jsx", "PropTypes", "_jsx", "useUtilityClasses", "useTimeout", "React", "useUtilityClasses", "React", "React", "React", "import_prop_types", "import_jsx_runtime", "useUtilityClasses", "useGridRootProps", "useGridApiContext", "_jsxs", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "_jsx", "React", "React", "React", "React", "React", "LoadingTrigger", "getSkeletonRowId", "React", "import_jsx_runtime", "styled_default", "_jsx", "React", "React", "RequestStatus", "options", "_a", "RowGroupingStrategy"]}