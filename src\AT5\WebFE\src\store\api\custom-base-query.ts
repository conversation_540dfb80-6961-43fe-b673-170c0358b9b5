import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';

/**
 * Standard base query without 401 handling - that's handled by middleware
 */
export const createCustomBaseQuery = (baseUrl: string): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> => {
  return fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers) => {
      // Add the same headers that the existing API functions use
      const tenantId = 20;
      const userId = 27219;

      if (tenantId) {
        //headers.set('tenant-id', tenantId.toString());
      }
      if (userId) {
        //headers.set('user-id', userId.toString());
      }
      return headers;
    },
  });
};
