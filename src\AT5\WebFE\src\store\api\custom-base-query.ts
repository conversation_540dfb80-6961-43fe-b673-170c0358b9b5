import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { appConfig } from '@/config/app';

/**
 * Custom base query that handles 401 responses by redirecting to a configured login page
 * with a return URL parameter. Based on RTK Query official documentation pattern.
 */
export const createCustomBaseQuery = (baseUrl: string): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> => {
  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers) => {
      // Add the same headers that the existing API functions use
      const tenantId = 20;
      const userId = 27219;

      if (tenantId) {
        //headers.set('tenant-id', tenantId.toString());
      }
      if (userId) {
        //headers.set('user-id', userId.toString());
      }
      return headers;
    },
  });

  return async (args, api, extraOptions) => {
    const result = await baseQuery(args, api, extraOptions);

    // Check if we got a 401 response and have a redirect URI configured
    if (result.error && result.error.status === 401 && appConfig.notAuthorizedRedirectUri) {
      try {
        // Get the current URL to use as return URL
        const currentUrl = window.location.href;

        // Create the redirect URL with return URL parameter
        const redirectUrl = new URL(appConfig.notAuthorizedRedirectUri);

        // Only add returnUrl if it's not already the login page to avoid infinite redirects
        if (!currentUrl.includes(appConfig.notAuthorizedRedirectUri)) {
          redirectUrl.searchParams.set('returnUrl', encodeURIComponent(currentUrl));
        }

        // Redirect to the login page
        window.location.href = redirectUrl.toString();
      } catch (error) {
        // If URL construction fails, just redirect without return URL
        console.error('Failed to construct redirect URL:', error);
        window.location.href = appConfig.notAuthorizedRedirectUri;
      }
    }

    return result;
  };
};
