import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { appConfig } from '@/config/app';

/**
 * Custom fetch function that intercepts 401 responses before RTK Query processes them
 */
const originalFetch = window.fetch;
const customFetch: typeof fetch = async (input, init) => {
  const response = await originalFetch(input, init);

  // Check for 401 response and redirect if configured
  if (response.status === 401 && appConfig.notAuthorizedRedirectUri) {
    try {
      // Get the current URL to use as return URL
      const currentUrl = window.location.href;

      // Create the redirect URL with return URL parameter
      const redirectUrl = new URL(appConfig.notAuthorizedRedirectUri);

      // Only add returnUrl if it's not already the login page to avoid infinite redirects
      if (!currentUrl.includes(appConfig.notAuthorizedRedirectUri)) {
        redirectUrl.searchParams.set('returnUrl', encodeURIComponent(currentUrl));
      }

      // Redirect to the login page
      window.location.href = redirectUrl.toString();
    } catch (error) {
      // If URL construction fails, just redirect without return URL
      console.error('Failed to construct redirect URL:', error);
      window.location.href = appConfig.notAuthorizedRedirectUri;
    }
  }

  return response;
};

/**
 * Custom base query that handles 401 responses by redirecting to a configured login page
 * with a return URL parameter.
 */
export const createCustomBaseQuery = (baseUrl: string): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> => {
  return fetchBaseQuery({
    baseUrl,
    fetchFn: customFetch,
    prepareHeaders: (headers) => {
      // Add the same headers that the existing API functions use
      const tenantId = 20;
      const userId = 27219;

      if (tenantId) {
        //headers.set('tenant-id', tenantId.toString());
      }
      if (userId) {
        //headers.set('user-id', userId.toString());
      }
      return headers;
    },
  });
};
