import type { BaseQueryFn, <PERSON>tch<PERSON><PERSON><PERSON>, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { appConfig } from '@/config/app';

// Global flag to prevent multiple redirects
let isRedirecting = false;

/**
 * Handle 401 redirect logic
 */
const handle401Redirect = () => {
  if (isRedirecting || !appConfig.notAuthorizedRedirectUri) {
    return;
  }

  isRedirecting = true;

  try {
    const currentUrl = window.location.href;
    const redirectUrl = new URL(appConfig.notAuthorizedRedirectUri);

    // Only add returnUrl if it's not already the login page
    if (!currentUrl.includes(appConfig.notAuthorizedRedirectUri)) {
      redirectUrl.searchParams.set('returnUrl', encodeURIComponent(currentUrl));
    }

    console.log('401 detected - redirecting to:', redirectUrl.toString());
    window.location.href = redirectUrl.toString();
  } catch (error) {
    console.error('Failed to construct redirect URL:', error);
    window.location.href = appConfig.notAuthorizedRedirectUri;
  }
};

/**
 * Completely custom base query that bypasses fetchBaseQuery for better 401 handling
 */
export const createCustomBaseQuery = (baseUrl: string): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> => {
  return async (args, _api, _extraOptions) => {
    // Prepare the request
    let url: string;
    let config: RequestInit = {};

    if (typeof args === 'string') {
      url = `${baseUrl}${args}`;
    } else {
      url = `${baseUrl}${args.url}`;
      config = {
        method: args.method || 'GET',
        body: args.body,
      };

      // Handle headers properly
      if (args.headers) {
        config.headers = args.headers as HeadersInit;
      }
    }

    // Add default headers
    const headers = new Headers(config.headers);
    const tenantId = 20;
    const userId = 27219;

    if (tenantId) {
      //headers.set('tenant-id', tenantId.toString());
    }
    if (userId) {
      //headers.set('user-id', userId.toString());
    }

    config.headers = headers;

    try {
      console.log('Making request to:', url);
      const response = await fetch(url, config);

      console.log('Response status:', response.status);

      // Handle 401 immediately - this is the key part
      if (response.status === 401) {
        console.log('401 detected - handling redirect');
        handle401Redirect();
        // Return a fake successful response to prevent RTK Query from showing errors
        return {
          data: null,
        };
      }

      // Handle other error statuses
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        return {
          error: {
            status: response.status,
            statusText: response.statusText,
            data: errorText,
          } as FetchBaseQueryError,
        };
      }

      // Handle successful response
      const contentType = response.headers.get('content-type');
      let data: unknown;

      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      return { data };

    } catch (error) {
      console.error('Network error:', error);

      // Check if this might be a CORS-blocked 401
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.log('Potential CORS-blocked 401 - attempting redirect');
        handle401Redirect();
        return {
          data: null,
        };
      }

      return {
        error: {
          status: 'FETCH_ERROR',
          error: error instanceof Error ? error.message : 'Unknown error',
        } as FetchBaseQueryError,
      };
    }
  };
};
