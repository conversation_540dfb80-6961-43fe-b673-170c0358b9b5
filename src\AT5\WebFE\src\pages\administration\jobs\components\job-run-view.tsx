'use client';

import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

import { RouterLink } from '@/components/core/link';
import { toast } from '@/components/core/toaster';
import { logger } from '@/lib/default-logger';
import { paths } from '@/paths';

import { useGetJobRunQuery } from '@/store/api/administration';
import { TextField } from '@mui/material';

const JobResultTypeOptions = [
	{ label: 'JobResultType.None', value: 0 },
	{ label: 'JobResultType.Success', value: 1 },
	{ label: 'JobResultType.SuccessWithWarning', value: 2 },
	{ label: 'JobResultType.PartialSuccess', value: 3 },
	{ label: 'JobResultType.Failure', value: 4 },
] as const;

const JobRunStateTypeOptions = [
	{ label: 'JobRunState.Starting', value: 0 },
	{ label: 'JobRunState.Running', value: 1 },
	{ label: 'JobRunState.Finished', value: 2 },
] as const;


interface JobRunViewProps {
	readonly jobRunId?: number;
}

export function JobRunView({ jobRunId }: JobRunViewProps): React.JSX.Element {
	//	RTK Query hooks
	const { data: jobRunData, error: jobRunError, isLoading } = useGetJobRunQuery(
		{
			rootEntityId: `${jobRunId!}`
		}, {
			skip: jobRunId === undefined
	});

	// Handle job loading error
	React.useEffect(() => {
		if (jobRunError) {
			logger.error(jobRunError);
			toast.error('Failed to load job run data');
		}
	}, [jobRunError]);

	if (isLoading) {
		return <>Loading...</>
	}

	return (
		<Card>
			<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
				<Typography variant="h4">{`Job run ${jobRunId} details`}</Typography>
				<Box>
					<Button color="secondary" component={RouterLink} href={paths.administration.jobs.index(true)}>
						Go back
					</Button>
				</Box>
			</CardActions>
			<CardContent>
				<Stack divider={<Divider />} spacing={4}>
					<Stack spacing={3}>
						<Typography variant="h5">Detail</Typography>
						<Grid container spacing={3} 					
							direction="row"
							sx={{
								justifyContent: 'left',
								alignItems: 'center',
							}}
						>
							<Grid
								size={{
									md: 6,
									xs: 3,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Job Name'}
									value={jobRunData?.entity?.jobName ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 3,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Author Id'}
									value={jobRunData?.entity?.authorId ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 12,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'State'}
									value={JobRunStateTypeOptions.find(
										(option) => option.value === jobRunData?.entity?.state
									)?.label ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 12,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Result'}
									value={JobResultTypeOptions.find(
										(option) => option.value === jobRunData?.entity?.state
									)?.label ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 12,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Triggered'}
									value={jobRunData?.entity?.triggered ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 12,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Started'}
									value={jobRunData?.entity?.started ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
							<Grid
								size={{
									md: 6,
									xs: 12,
								}}
							>
								<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
									<TextField fullWidth label={'Finished'}
									value={jobRunData?.entity?.finished ?? ''}
									disabled
									/>
								</Tooltip>
							</Grid>
						</Grid>
					</Stack>
				</Stack>
			</CardContent>
			<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
				<Box>
					<Button color="secondary" component={RouterLink} href={paths.administration.jobs.index(true)}>
						Return
					</Button>
				</Box>
			</CardActions>
		</Card>
	);
}
