import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint: build.mutation<
      AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.entityId}`,
        method: "DELETE",
      }),
    }),
    atApiServiceEndpointsAdministrationJobsPutJobEndpoint: build.mutation<
      AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.entityId}`,
        method: "PUT",
        body: queryArg.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId,
      }),
    }),
    atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,
          method: "DELETE",
        }),
      }),
    atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,
          method: "PUT",
          body: queryArg.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId,
        }),
      }),
    atApiServiceEndpointsAdministrationJobsGetJobEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiArg
    >({
      query: (queryArg) => ({ url: `/api/v2/jobs/${queryArg.rootEntityId}` }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobRunEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns/${queryArg.rootEntityId}`,
      }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns/${queryArg.jobRunId}/logs`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobsEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsAdministrationJobsPostJobEndpoint: build.mutation<
      AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs`,
        method: "POST",
        body: queryArg.atApiServiceEndpointsAdministrationModelsJobDto,
      }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.rootEntityId}/triggers/${queryArg.subEntityId1}`,
      }),
    }),
    atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint: build.query<
      AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiResponse,
      AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.jobId}/triggers`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.jobId}/triggers`,
          method: "POST",
          body: queryArg.atApiServiceEndpointsAdministrationModelsJobTriggerDto,
        }),
      }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as administrationGeneratedApi };
export type AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiArg = {
  entityId: string;
};
export type AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiArg = {
  entityId: string;
  atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId: AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId;
};
export type AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiArg =
  {
    entityId1: string;
    entityId2: string;
  };
export type AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiArg =
  {
    entityId1: string;
    entityId2: string;
    atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId: AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId;
  };
export type AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto;
export type AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiArg = {
  rootEntityId: string;
};
export type AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto;
export type AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiArg = {
  rootEntityId: string;
};
export type AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiArg =
  {
    jobRunId: string;
    $filter?: string | null;
    $orderby?: string | null;
    $select?: string | null;
    $top?: number | null;
    $skip?: number | null;
    $count?: boolean | null;
  };
export type AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiArg = {
  $filter?: string | null;
  $orderby?: string | null;
  $select?: string | null;
  $top?: number | null;
  $skip?: number | null;
  $count?: boolean | null;
};
export type AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiArg = {
  $filter?: string | null;
  $orderby?: string | null;
  $select?: string | null;
  $top?: number | null;
  $skip?: number | null;
  $count?: boolean | null;
};
export type AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiArg = {
  atApiServiceEndpointsAdministrationModelsJobDto: AtApiServiceEndpointsAdministrationModelsJobDto;
};
export type AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto;
export type AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiArg =
  {
    rootEntityId: string;
    subEntityId1: string;
  };
export type AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiArg =
  {
    jobId: string;
    $filter?: string | null;
    $orderby?: string | null;
    $select?: string | null;
    $top?: number | null;
    $skip?: number | null;
    $count?: boolean | null;
  };
export type AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiArg =
  {
    jobId: string;
    atApiServiceEndpointsAdministrationModelsJobTriggerDto: AtApiServiceEndpointsAdministrationModelsJobTriggerDto;
  };
export type AtApiServiceEndpointsAdministrationModelsJobDto = {
  id?: number;
  name?: string;
  description?: string | null;
  parameters?: string;
  type?: number;
  disabled?: boolean;
  successEmails?: string | null;
  errorEmails?: string | null;
};
export type AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId =
  {
    model?: AtApiServiceEndpointsAdministrationModelsJobDto | null;
  };
export type AtApiServiceEndpointsAdministrationModelsJobTriggerDto = {
  id?: number;
  name?: string;
  description?: string | null;
  parameters?: string;
  type?: number;
  disabled?: boolean;
  jobId?: number;
  runParameters?: string | null;
};
export type AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId =
  {
    model?: AtApiServiceEndpointsAdministrationModelsJobTriggerDto | null;
  };
export type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto =
  {
    entity?: AtApiServiceEndpointsAdministrationModelsJobDto | null;
  };
export type AtPrimitivesEnumsJobRunState = 0 | 1 | 2;
export type AtPrimitivesEnumsJobResultType = 0 | 1 | 2 | 3 | 4;
export type AtApiServiceEndpointsAdministrationModelsJobRunDto = {
  id?: number;
  jobId?: number;
  jobName?: string;
  state?: AtPrimitivesEnumsJobRunState;
  result?: AtPrimitivesEnumsJobResultType;
  triggered?: string;
  started?: string | null;
  finished?: string | null;
  authorId?: number | null;
};
export type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto =
  {
    entity?: AtApiServiceEndpointsAdministrationModelsJobRunDto | null;
  };
export type AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse = {
  value?: {
    [key: string]: any;
  }[];
  count?: number | null;
};
export type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto =
  {
    entity?: AtApiServiceEndpointsAdministrationModelsJobTriggerDto | null;
  };
export const {
  useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation,
} = injectedRtkApi;
