import { isRejectedWithValue } from '@reduxjs/toolkit';
import type { Middleware } from '@reduxjs/toolkit';
import { appConfig } from '@/config/app';

/**
 * Redux middleware that intercepts RTK Query rejected actions and handles 401 responses
 * by redirecting to a configured login page with return URL.
 */
export const authRedirectMiddleware: Middleware = (api) => (next) => (action) => {
  // Check if this is a rejected RTK Query action with a 401 status
  if (isRejectedWithValue(action)) {
    const error = action.payload;
    
    // Check for 401 status in the error payload
    if ((error as any)?.status === 401 && appConfig.notAuthorizedRedirectUri) {
      try {
        // Get the current URL to use as return URL
        const currentUrl = window.location.href;

        // Create the redirect URL with return URL parameter
        const redirectUrl = new URL(appConfig.notAuthorizedRedirectUri);

        // Only add returnUrl if it's not already the login page to avoid infinite redirects
        if (!currentUrl.includes(appConfig.notAuthorizedRedirectUri)) {
          redirectUrl.searchParams.set('returnUrl', encodeURIComponent(currentUrl));
        }

        // Redirect to the login page
        window.location.href = redirectUrl.toString();

        // Don't continue processing this action since we're redirecting
        return;
      } catch (error) {
        // If URL construction fails, just redirect without return URL
        console.error('Failed to construct redirect URL:', error);
        window.location.href = appConfig.notAuthorizedRedirectUri;
        return;
      }
    }
  }

  // Continue with normal action processing
  return next(action);
};
