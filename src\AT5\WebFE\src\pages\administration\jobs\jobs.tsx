import * as React from "react";
import Box from "@mui/material/Box";
import { Helmet } from "react-helmet-async";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import FilterListIcon from '@mui/icons-material/FilterList';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import PlayArrowOutlinedIcon from '@mui/icons-material/PlayArrowOutlined';
import { DataGridPremium, GridColDef, GridRowId, useGridSelector, useGridApiRef, gridRowSelectionIdsSelector } from '@mui/x-data-grid-premium';
import { GridPreferencePanelsValue, GridRenderCellParams, gridPreferencePanelStateSelector } from '@mui/x-data-grid-pro';
import { useGridPanelContext } from '@mui/x-data-grid-pro/internals';
import { GRID_CHECKBOX_SELECTION_FIELD } from '@mui/x-data-grid';
import useId from '@mui/utils/useId';
import type { Metadata } from "@/types/metadata";
import { createRTKODataGridDataSource } from "@/store/api/odata-grid-data-source";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
	setActiveTab,
	setPageSize,
	setPage,
	setSortModel,
	setFilterModel,
	selectActiveTab,
	selectPageSize,
	selectPage,
	selectSortModel,
	selectFilterModel
} from "@/store/slices/jobs-slice";
import IconButton from "@mui/material/IconButton";
import { PencilSimpleIcon } from "@phosphor-icons/react";
import { RouterLink } from "@/components/core/link";
import { paths } from "@/paths";
import { JobRunList } from "./components/job-run-list";
import { administrationApi } from "@/store/api/administration";
import { useLoaderData } from "react-router-dom";

const metadata = { title: `Jobs` } satisfies Metadata;

function JobsToolbar({ apiRef, createNewPath, onDelete, onRun }: { apiRef: any, createNewPath: string, onDelete: (ids: GridRowId[]) => void, onRun: (ids: GridRowId[]) => void }) {
	const selectedIds = useGridSelector(apiRef, gridRowSelectionIdsSelector);
	const selectedCount = selectedIds.size;
	const hasSelection = selectedCount > 0;
	const buttonId = useId();
	const panelId = useId();
	const columnsButtonId = useId();
	const columnsPanelId = useId();
	const panelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);
	const isFiltersOpen = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.filters;
	const isColumnsOpen = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.columns;
	const { filterPanelTriggerRef, columnsPanelTriggerRef } = useGridPanelContext();
	return (
		<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 1 }}>
			<Stack direction="row" spacing={1} alignItems="center">
				<Button
					ref={columnsPanelTriggerRef}
					id={columnsButtonId}
					variant="outlined"
					sx={{ minWidth: 100 }}
					startIcon={<ViewColumnIcon />}
					aria-haspopup="true"
					aria-expanded={isColumnsOpen ? 'true' : undefined}
					aria-controls={isColumnsOpen ? columnsPanelId : undefined}
					onClick={() => {
						const current = apiRef?.current;
						if (!current) return;
						if (isColumnsOpen) {
							current.hidePreferences();
						} else {
							current.showPreferences(GridPreferencePanelsValue.columns, columnsPanelId, columnsButtonId);
						}
					}}
				>
					Columns
				</Button>
				<Button
					ref={filterPanelTriggerRef}
					id={buttonId}
					variant="outlined"
					sx={{ minWidth: 100 }}
					startIcon={<FilterListIcon />}
					aria-haspopup="true"
					aria-expanded={isFiltersOpen ? 'true' : undefined}
					aria-controls={isFiltersOpen ? panelId : undefined}
					onClick={() => {
						const current = apiRef?.current;
						if (!current) return;
						if (isFiltersOpen) {
							current.hidePreferences();
						} else {
							current.showPreferences(GridPreferencePanelsValue.filters, panelId, buttonId);
						}
					}}
				>
					Filter
				</Button>
			</Stack>
			<Stack direction="row" spacing={1} alignItems="center">
				{!hasSelection ? (
					<Button startIcon={<AddIcon />} variant="contained" component={RouterLink} href={`${createNewPath}`}>
						Create New
					</Button>
				) : (
					<>
						<Box sx={{ display: 'flex', alignItems: 'center', fontSize: 14, color: 'text.secondary', pr: 0.5 }}>
							{selectedCount} selected&nbsp;&nbsp;&nbsp;|
						</Box>
						<Button startIcon={<DeleteOutlinedIcon />} color="error" variant="text" onClick={() => onDelete(Array.from(selectedIds.keys()))} sx={{ border: 'none', minWidth: 90 }}>
							Delete
						</Button>
						<Button startIcon={<PlayArrowOutlinedIcon />} variant="contained" color="primary" onClick={() => onRun(Array.from(selectedIds.keys()))}>
							Run
						</Button>
					</>
				)}
			</Stack>
		</Box>
	);
}

type LoaderData = {
    showLogs: boolean;
};

export function Page(): React.JSX.Element {
	const dispatch = useAppDispatch();
	const tab = useAppSelector(selectActiveTab);
	const pageSize = useAppSelector(selectPageSize);
	const page = useAppSelector(selectPage);
	const sortModel = useAppSelector(selectSortModel);
	const filterModel = useAppSelector(selectFilterModel);
	const apiRef = useGridApiRef();
	const { showLogs } = useLoaderData() as LoaderData;

	const [dataSource, setDataSource] = React.useState(() => createRTKODataGridDataSource(
		administrationApi.endpoints.atApiServiceEndpointsAdministrationJobsGetJobsEndpoint.initiate,
		() => {
			const current = apiRef.current;
			if (!current) return undefined;
			// Collect visible, non-action column fields
			const visible = current.getVisibleColumns?.() ?? [];
			return visible
				.map((c: any) => c.field)
				// Exclude action and internal utility columns like checkbox selection
				.filter((f: string) => f && f !== 'actions' && !f.startsWith('__'));
		}
	));

	React.useEffect(
		() => {
			if (showLogs){
				dispatch(setActiveTab(1));
			}
		},
		[]
	)

	const columns : GridColDef[] = [
		{
			field: 'id',
			headerName: 'Id',
			width: 90,
			type: 'number',
		},
		{
			field: 'name',
			headerName: 'Name',
			width: 400,
			type: 'string',
			// editable: true,
		},
		{
			field: 'description',
			headerName: 'Description',
			width: 400,
			type: 'string',
			// editable: true,
		},
		{
			field: 'parameters',
			headerName: 'Parameters',
			width: 500,
			type: 'string',
			// editable: true,
		},
		{
			field: 'disabled',
			headerName: 'Disabled',
			width: 100,
			type: 'boolean',
			// editable: true,
		},
		{
			field: 'successEmails',
			headerName: 'Success Emails',
			width: 200,
			type: 'string',
			// editable: true,
		},
		{
			field: 'errorEmails',
			headerName: 'Error Emails',
			width: 200,
			type: 'string',
			// editable: true,
		},
		{
			field: 'type',
			headerName: 'Type',
			width: 200,
			// type: 'singleSelect',
			// valueOptions: ['ReportJob', 'MyJobType', 'CustomJobType'],
			// editable: true,
		},
		{
			field: 'actions',
			renderCell: (params: GridRenderCellParams) => {
				return (
					<IconButton component={RouterLink} href={paths.administration.jobs.edit(params.row.id)}>
						<PencilSimpleIcon />
					</IconButton>
				)
			},
			headerName: "Actions",
			width: 100,
			align: "right",
			type: 'actions',
		},
	];

	const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
		dispatch(setActiveTab(newValue));
	};

	const handleDelete = (ids: GridRowId[]) => {
		alert('Delete selected jobs: ' + ids.join(', '));
	};
	const handleRun = (ids: GridRowId[]) => {
		alert('Run selected jobs: ' + ids.join(', '));
	};

	const slots = React.useMemo(() => ({
		toolbar: () => (
			<JobsToolbar
				apiRef={apiRef}
				createNewPath={paths.administration.jobs.create}
				onDelete={handleDelete}
				onRun={handleRun}
			/>
		)
	}), [apiRef]);

	return (
		<React.Fragment>
			<Helmet>
				<title>{metadata.title}</title>
			</Helmet>
			<Box
				sx={{
					maxWidth: "var(--Content-maxWidth)",
					m: "var(--Content-margin)",
					p: "var(--Content-padding)",
					width: "var(--Content-width)",
				}}
			>
				<Tabs value={tab} onChange={handleTabChange} sx={{ mb: 2 }}>
					<Tab label="Jobs" />
					<Tab label="Logs" />
				</Tabs>
				<Box sx={{ display: tab === 0 ? 'block' : 'none' }}>
					<DataGridPremium
						apiRef={apiRef}
						columns={columns}
						dataSource={dataSource}
						filterMode="server"
						showToolbar={true}
						disablePivoting
						filterDebounceMs={500}
						ignoreDiacritics
						pagination
						pageSizeOptions={[10, 20, 50, 100]}
						paginationModel={{ pageSize, page }}
						onPaginationModelChange={(model) => {
							if (model.pageSize !== pageSize) {
								dispatch(setPageSize(model.pageSize));
							}
							if (model.page !== page) {
								dispatch(setPage(model.page));
							}
						}}
						sortModel={sortModel}
						onSortModelChange={(model) => {
							dispatch(setSortModel(model.map(item => ({
								field: item.field,
								sort: item.sort || null
							}))));
						}}
						filterModel={filterModel}
						onFilterModelChange={(model) => {
							dispatch(setFilterModel(model));
						}}
						checkboxSelection
						slots={slots}
						slotProps={{
							columnsManagement: {
								// Do not display the checkbox column in the columns panel.
								getTogglableColumns: (cols: GridColDef[]): string[] =>
									cols
										.map((col: GridColDef) => col.field)
										.filter((field: string) => field !== GRID_CHECKBOX_SELECTION_FIELD),
							},
						}}
						hideFooterSelectedRowCount
					/>
				</Box>
				<Box sx={{ display: tab === 1 ? 'block' : 'none' }}>
					<JobRunList/>
				</Box>
			</Box>
		</React.Fragment>
	);
}
