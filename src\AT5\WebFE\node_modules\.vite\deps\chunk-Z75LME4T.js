import {
  DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS,
  columnPinningStateInitializer,
  columnReorderStateInitializer,
  dataSourceStateInitializer,
  detailPanelStateInitializer,
  headerFilteringStateInitializer,
  propValidatorsDataGridPro,
  rowPinningStateInitializer,
  rowReorderStateInitializer,
  useGridAriaAttributesPro,
  useGridColumnPinning,
  useGridColumnPinningPreProcessors,
  useGridColumnReorder,
  useGridDataSourceLazyLoader,
  useGridDataSourcePro,
  useGridDataSourceTreeDataPreProcessors,
  useGridDetailPanel,
  useGridDetailPanelPreProcessors,
  useGridHeaderFiltering,
  useGridInfiniteLoader,
  useGridInfiniteLoadingIntersection,
  useGridLazyLoader,
  useGridLazyLoaderPreProcessors,
  useGridRowAriaAttributesPro,
  useGridRowPinning,
  useGridRowPinningPreProcessors,
  useGridRowReorder,
  useGridRowReorderPreProcessors,
  useGridTreeData,
  useGridTreeDataPreProcessors
} from "./chunk-F22U6ZUN.js";
import {
  DATA_GRID_PROPS_DEFAULT_VALUES,
  GRID_DEFAULT_LOCALE_TEXT,
  GridContextProvider,
  MemoizedGridRoot,
  ROW_SELECTION_PROPAGATION_DEFAULT,
  columnGroupsStateInitializer,
  columnMenuStateInitializer,
  columnResizeStateInitializer,
  columnsStateInitializer,
  computeSlots,
  densityStateInitializer,
  dimensionsStateInitializer,
  editingStateInitializer,
  fastMemo,
  filterStateInitializer,
  focusStateInitializer,
  forwardRef,
  listViewStateInitializer,
  paginationStateInitializer,
  preferencePanelStateInitializer,
  propsStateInitializer,
  rowSelectionStateInitializer,
  rowSpanningStateInitializer,
  rowsMetaStateInitializer,
  rowsStateInitializer,
  sortingStateInitializer,
  useGridApiInitialization,
  useGridApiRef,
  useGridClipboard,
  useGridColumnGrouping,
  useGridColumnMenu,
  useGridColumnResize,
  useGridColumnSpanning,
  useGridColumns,
  useGridCsvExport,
  useGridDensity,
  useGridDimensions,
  useGridEditing,
  useGridEvents,
  useGridFilter,
  useGridFocus,
  useGridInitialization,
  useGridInitializeState,
  useGridKeyboardNavigation,
  useGridListView,
  useGridPagination,
  useGridParamsApi,
  useGridPreferencesPanel,
  useGridPrintExport,
  useGridRowSelection,
  useGridRowSelectionPreProcessors,
  useGridRowSpanning,
  useGridRows,
  useGridRowsMeta,
  useGridRowsOverridableMethods,
  useGridRowsPreProcessors,
  useGridScroll,
  useGridSorting,
  useGridStatePersistence,
  useGridVirtualization,
  useGridVirtualizer,
  useMaterialCSSVariables,
  validateProps,
  virtualizationStateInitializer
} from "./chunk-MC3F52YZ.js";
import {
  getThemeProps
} from "./chunk-K3WPA3ZI.js";
import {
  useTheme
} from "./chunk-Y5VMRVGE.js";
import {
  require_prop_types
} from "./chunk-6ZYRDDF6.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-telemetry/esm/runtime/config.import-meta.js
var config_import_meta_exports = {};
__export(config_import_meta_exports, {
  importMetaEnv: () => importMetaEnv
});
var importMetaEnv;
var init_config_import_meta = __esm({
  "node_modules/@mui/x-telemetry/esm/runtime/config.import-meta.js"() {
    importMetaEnv = import.meta.env;
  }
});

// node_modules/@mui/x-data-grid-pro/esm/DataGridPro/DataGrid.js
function DataGrid() {
  if (false) {
    return null;
  }
  throw new Error(["You try to import Data Grid from @mui/x-data-grid-pro but this module isn't exported from this npm package.", "", "Instead, you can do `import { DataGridPro } from '@mui/x-data-grid-pro'`.", ""].join("\n"));
}
function DataGridPremium() {
  if (false) {
    return null;
  }
  throw new Error(["You try to import Data Grid Premium from @mui/x-data-grid-pro but this module isn't exported from this npm package.", "", "Instead, if you have a Premium plan license or want to try Premium, you can do this:", `import { DataGridPremium } from '@mui/x-data-grid-premium'`, "", "Otherwise, you can stay on the Pro plan: `import { DataGridPro } from '@mui/x-data-grid-pro'`.", ""].join("\n"));
}

// node_modules/@mui/x-data-grid-pro/esm/DataGridPro/DataGridPro.js
var React7 = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-license/esm/encoding/md5.js
var k = [];
var i = 0;
for (; i < 64; ) {
  k[i] = 0 | Math.sin(++i % Math.PI) * **********;
}
function md5(s) {
  const words = [];
  let b, c, d, j = unescape(encodeURI(s)) + "", a = j.length;
  const h = [b = **********, c = **********, ~b, ~c];
  s = --a / 4 + 2 | 15;
  words[--s] = a * 8;
  for (; ~a; ) {
    words[a >> 2] |= j.charCodeAt(a) << 8 * a--;
  }
  for (i = j = 0; i < s; i += 16) {
    a = h;
    for (; j < 64; a = [d = a[3], b + ((d = a[0] + [b & c | ~b & d, d & b | ~d & c, b ^ c ^ d, c ^ (b | ~d)][a = j >> 4] + k[j] + ~~words[i | [j, 5 * j + 1, 3 * j + 5, 7 * j][a] & 15]) << (a = [7, 12, 17, 22, 5, 9, 14, 20, 4, 11, 16, 23, 6, 10, 15, 21][4 * a + j++ % 4]) | d >>> -a), b, c]) {
      b = a[1] | 0;
      c = a[2];
    }
    for (j = 4; j; ) h[--j] += a[j];
  }
  for (s = ""; j < 32; ) {
    s += (h[j >> 3] >> (1 ^ j++) * 4 & 15).toString(16);
  }
  return s;
}

// node_modules/@mui/x-license/esm/encoding/base64.js
var _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
var base64Decode = (input) => {
  let output = "";
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i2 = 0;
  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
  while (i2 < input.length) {
    enc1 = _keyStr.indexOf(input.charAt(i2++));
    enc2 = _keyStr.indexOf(input.charAt(i2++));
    enc3 = _keyStr.indexOf(input.charAt(i2++));
    enc4 = _keyStr.indexOf(input.charAt(i2++));
    chr1 = enc1 << 2 | enc2 >> 4;
    chr2 = (enc2 & 15) << 4 | enc3 >> 2;
    chr3 = (enc3 & 3) << 6 | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  return output;
};

// node_modules/@mui/x-license/esm/utils/plan.js
var PLAN_SCOPES = ["pro", "premium"];

// node_modules/@mui/x-license/esm/utils/licenseModel.js
var LICENSE_MODELS = [
  /**
   * A license is outdated if the current version of the software was released after the expiry date of the license.
   * But the license can be used indefinitely with an older version of the software.
   */
  "perpetual",
  /**
   * On development, a license is outdated if the expiry date has been reached
   * On production, a license is outdated if the current version of the software was released after the expiry date of the license (see "perpetual")
   */
  "annual",
  /**
   * Legacy. The previous name for 'annual'.
   * Can be removed once old license keys generated with 'subscription' are no longer supported.
   * To support for a while. We need more years of backward support and we sell multi year licenses.
   */
  "subscription"
];

// node_modules/@mui/x-license/esm/utils/licenseErrorMessageUtils.js
var isCodeSandbox = typeof window !== "undefined" && window.location.hostname.endsWith(".csb.app");
function showError(message) {
  const logger = isCodeSandbox ? console.log : console.error;
  logger(["*************************************************************", "", ...message, "", "*************************************************************"].join("\n"));
}
function showInvalidLicenseKeyError() {
  showError(["MUI X: Invalid license key.", "", "Your MUI X license key format isn't valid. It could be because the license key is missing a character or has a typo.", "", "To solve the issue, you need to double check that `setLicenseKey()` is called with the right argument", "Please check the license key installation https://mui.com/r/x-license-key-installation."]);
}
function showLicenseKeyPlanMismatchError() {
  showError(["MUI X: License key plan mismatch.", "", "Your use of MUI X is not compatible with the plan of your license key. The feature you are trying to use is not included in the plan of your license key. This happens if you try to use Data Grid Premium with a license key for the Pro plan.", "", "To solve the issue, you can upgrade your plan from Pro to Premium at https://mui.com/r/x-get-license?scope=premium.", "Of if you didn't intend to use Premium features, you can replace the import of `@mui/x-data-grid-premium` with `@mui/x-data-grid-pro`."]);
}
function showNotAvailableInInitialProPlanError() {
  showError(["MUI X: Component not included in your license.", "", "The component you are trying to use is not included in the Pro Plan you purchased.", "", "Your license is from an old version of the Pro Plan that is only compatible with the `@mui/x-data-grid-pro` and `@mui/x-date-pickers-pro` commercial packages.", "", "To start using another Pro package, please consider reaching to our sales team to upgrade your license or visit https://mui.com/r/x-get-license to get a new license key."]);
}
function showMissingLicenseKeyError({
  plan,
  packageName
}) {
  showError(["MUI X: Missing license key.", "", `The license key is missing. You might not be allowed to use \`${packageName}\` which is part of MUI X ${plan}.`, "", "To solve the issue, you can check the free trial conditions: https://mui.com/r/x-license-trial.", "If you are eligible no actions are required. If you are not eligible to the free trial, you need to purchase a license https://mui.com/r/x-get-license or stop using the software immediately."]);
}
function showExpiredPackageVersionError({
  packageName
}) {
  showError(["MUI X: Expired package version.", "", `You have installed a version of \`${packageName}\` that is outside of the maintenance plan of your license key. By default, commercial licenses provide access to new versions released during the first year after the purchase.`, "", "To solve the issue, you can renew your license https://mui.com/r/x-get-license or install an older version of the npm package that is compatible with your license key."]);
}
function showExpiredAnnualGraceLicenseKeyError({
  plan,
  licenseKey,
  expiryTimestamp
}) {
  showError(["MUI X: Expired license key.", "", `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, "", "To solve the problem you can either:", "", "- Renew your license https://mui.com/r/x-get-license and use the new key", `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, "", "Note that your license is perpetual in production environments with any version released before your license term ends.", "", `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, ""]);
}
function showExpiredAnnualLicenseKeyError({
  plan,
  licenseKey,
  expiryTimestamp
}) {
  throw new Error(["MUI X: Expired license key.", "", `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, "", "To solve the problem you can either:", "", "- Renew your license https://mui.com/r/x-get-license and use the new key", `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, "", "Note that your license is perpetual in production environments with any version released before your license term ends.", "", `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, ""].join("\n"));
}

// node_modules/@mui/x-license/esm/utils/licenseInfo.js
globalThis.__MUI_LICENSE_INFO__ = globalThis.__MUI_LICENSE_INFO__ || {
  key: void 0
};
var LicenseInfo = class _LicenseInfo {
  static getLicenseInfo() {
    return globalThis.__MUI_LICENSE_INFO__;
  }
  static getLicenseKey() {
    return _LicenseInfo.getLicenseInfo().key;
  }
  static setLicenseKey(key) {
    const licenseInfo = _LicenseInfo.getLicenseInfo();
    licenseInfo.key = key;
  }
};

// node_modules/@mui/x-license/esm/utils/licenseStatus.js
var LICENSE_STATUS = (function(LICENSE_STATUS2) {
  LICENSE_STATUS2["NotFound"] = "NotFound";
  LICENSE_STATUS2["Invalid"] = "Invalid";
  LICENSE_STATUS2["ExpiredAnnual"] = "ExpiredAnnual";
  LICENSE_STATUS2["ExpiredAnnualGrace"] = "ExpiredAnnualGrace";
  LICENSE_STATUS2["ExpiredVersion"] = "ExpiredVersion";
  LICENSE_STATUS2["Valid"] = "Valid";
  LICENSE_STATUS2["OutOfScope"] = "OutOfScope";
  LICENSE_STATUS2["NotAvailableInInitialProPlan"] = "NotAvailableInInitialProPlan";
  return LICENSE_STATUS2;
})({});

// node_modules/@mui/x-license/esm/verifyLicense/verifyLicense.js
function isPlanScopeSufficient(packageName, planScope) {
  let acceptedScopes;
  if (packageName.includes("-pro")) {
    acceptedScopes = ["pro", "premium"];
  } else if (packageName.includes("-premium")) {
    acceptedScopes = ["premium"];
  } else {
    acceptedScopes = [];
  }
  return acceptedScopes.includes(planScope);
}
var expiryReg = /^.*EXPIRY=([0-9]+),.*$/;
var orderReg = /^.*ORDER:([0-9]+),.*$/;
var PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN = ["x-data-grid-pro", "x-date-pickers-pro"];
function decodeLicenseVersion1(license) {
  let expiryTimestamp;
  let orderId;
  try {
    expiryTimestamp = parseInt(license.match(expiryReg)[1], 10);
    if (!expiryTimestamp || Number.isNaN(expiryTimestamp)) {
      expiryTimestamp = null;
    }
    orderId = parseInt(license.match(orderReg)[1], 10);
    if (!orderId || Number.isNaN(orderId)) {
      orderId = null;
    }
  } catch (err) {
    expiryTimestamp = null;
    orderId = null;
  }
  return {
    version: 1,
    licenseModel: "perpetual",
    planScope: "pro",
    planVersion: "initial",
    expiryTimestamp,
    expiryDate: expiryTimestamp ? new Date(expiryTimestamp) : null,
    orderId
  };
}
function decodeLicenseVersion2(license) {
  const licenseInfo = {
    version: 2,
    licenseModel: null,
    planScope: null,
    planVersion: "initial",
    expiryTimestamp: null,
    expiryDate: null,
    orderId: null
  };
  license.split(",").map((token) => token.split("=")).filter((el) => el.length === 2).forEach(([key, value]) => {
    if (key === "S") {
      licenseInfo.planScope = value;
    }
    if (key === "LM") {
      licenseInfo.licenseModel = value;
    }
    if (key === "E") {
      const expiryTimestamp = parseInt(value, 10);
      if (expiryTimestamp && !Number.isNaN(expiryTimestamp)) {
        licenseInfo.expiryTimestamp = expiryTimestamp;
        licenseInfo.expiryDate = new Date(expiryTimestamp);
      }
    }
    if (key === "PV") {
      licenseInfo.planVersion = value;
    }
    if (key === "O") {
      const orderNum = parseInt(value, 10);
      if (orderNum && !Number.isNaN(orderNum)) {
        licenseInfo.orderId = orderNum;
      }
    }
  });
  return licenseInfo;
}
function decodeLicense(encodedLicense) {
  const license = base64Decode(encodedLicense);
  if (license.includes("KEYVERSION=1")) {
    return decodeLicenseVersion1(license);
  }
  if (license.includes("KV=2")) {
    return decodeLicenseVersion2(license);
  }
  return null;
}
function verifyLicense({
  releaseInfo: releaseInfo2,
  licenseKey,
  packageName
}) {
  if (false) {
    return {
      status: LICENSE_STATUS.Valid
    };
  }
  if (!releaseInfo2) {
    throw new Error("MUI X: The release information is missing. Not able to validate license.");
  }
  if (!licenseKey) {
    return {
      status: LICENSE_STATUS.NotFound
    };
  }
  const hash = licenseKey.substr(0, 32);
  const encoded = licenseKey.substr(32);
  if (hash !== md5(encoded)) {
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  const license = decodeLicense(encoded);
  if (license == null) {
    console.error("MUI X: Error checking license. Key version not found!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.licenseModel == null || !LICENSE_MODELS.includes(license.licenseModel)) {
    console.error("MUI X: Error checking license. License model not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.expiryTimestamp == null) {
    console.error("MUI X: Error checking license. Expiry timestamp not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.licenseModel === "perpetual" || false) {
    const pkgTimestamp = parseInt(base64Decode(releaseInfo2), 10);
    if (Number.isNaN(pkgTimestamp)) {
      throw new Error("MUI X: The release information is invalid. Not able to validate license.");
    }
    if (license.expiryTimestamp < pkgTimestamp) {
      return {
        status: LICENSE_STATUS.ExpiredVersion
      };
    }
  } else if (license.licenseModel === "subscription" || license.licenseModel === "annual") {
    if ((/* @__PURE__ */ new Date()).getTime() > license.expiryTimestamp) {
      if (
        // 30 days grace
        (/* @__PURE__ */ new Date()).getTime() < license.expiryTimestamp + 1e3 * 3600 * 24 * 30 || false
      ) {
        return {
          status: LICENSE_STATUS.ExpiredAnnualGrace,
          meta: {
            expiryTimestamp: license.expiryTimestamp,
            licenseKey
          }
        };
      }
      return {
        status: LICENSE_STATUS.ExpiredAnnual,
        meta: {
          expiryTimestamp: license.expiryTimestamp,
          licenseKey
        }
      };
    }
  }
  if (license.planScope == null || !PLAN_SCOPES.includes(license.planScope)) {
    console.error("MUI X: Error checking license. planScope not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (!isPlanScopeSufficient(packageName, license.planScope)) {
    return {
      status: LICENSE_STATUS.OutOfScope
    };
  }
  if (license.planVersion === "initial" && license.planScope === "pro" && !PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN.includes(packageName)) {
    return {
      status: LICENSE_STATUS.NotAvailableInInitialProPlan
    };
  }
  return {
    status: LICENSE_STATUS.Valid
  };
}

// node_modules/@mui/x-license/esm/useLicenseVerifier/useLicenseVerifier.js
var React2 = __toESM(require_react(), 1);

// node_modules/@mui/x-telemetry/esm/runtime/events.js
var muiXTelemetryEvents = {
  licenseVerification: false ? noop : (context, payload) => ({
    eventName: "licenseVerification",
    payload,
    context
  })
};
var events_default = muiXTelemetryEvents;

// node_modules/@mui/x-telemetry/esm/runtime/config.js
var envEnabledValues = ["1", "true", "yes", "y"];
var envDisabledValues = ["0", "false", "no", "n"];
function getBooleanEnv(value) {
  if (!value) {
    return void 0;
  }
  if (envEnabledValues.includes(value)) {
    return true;
  }
  if (envDisabledValues.includes(value)) {
    return false;
  }
  return void 0;
}
function getBooleanEnvFromEnvObject(envKey, envObj) {
  var _a;
  const keys = Object.keys(envObj);
  for (let i2 = 0; i2 < keys.length; i2 += 1) {
    const key = keys[i2];
    if (!key.endsWith(envKey)) {
      continue;
    }
    const value = getBooleanEnv((_a = envObj[key]) == null ? void 0 : _a.toLowerCase());
    if (typeof value === "boolean") {
      return value;
    }
  }
  return void 0;
}
function getIsTelemetryCollecting() {
  const globalValue = globalThis.__MUI_X_TELEMETRY_DISABLED__;
  if (typeof globalValue === "boolean") {
    return !globalValue;
  }
  try {
    if (typeof process !== "undefined" && process.env && typeof process.env === "object") {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DISABLED", process.env);
      if (typeof result === "boolean") {
        return !result;
      }
    }
  } catch (_) {
  }
  try {
    const {
      importMetaEnv: importMetaEnv2
    } = (init_config_import_meta(), __toCommonJS(config_import_meta_exports));
    if (importMetaEnv2) {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DISABLED", importMetaEnv2);
      if (typeof result === "boolean") {
        return !result;
      }
    }
  } catch (_) {
  }
  try {
    const envValue = process.env.MUI_X_TELEMETRY_DISABLED || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DISABLED || process.env.GATSBY_MUI_X_TELEMETRY_DISABLED || process.env.REACT_APP_MUI_X_TELEMETRY_DISABLED || process.env.PUBLIC_MUI_X_TELEMETRY_DISABLED;
    const result = getBooleanEnv(envValue);
    if (typeof result === "boolean") {
      return !result;
    }
  } catch (_) {
  }
  return void 0;
}
function getIsDebugModeEnabled() {
  try {
    const globalValue = globalThis.__MUI_X_TELEMETRY_DEBUG__;
    if (typeof globalValue === "boolean") {
      return globalValue;
    }
    if (typeof process !== "undefined" && process.env && typeof process.env === "object") {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DEBUG", process.env);
      if (typeof result === "boolean") {
        return result;
      }
    }
    if (process.env.MUI_X_TELEMETRY_DEBUG) {
      const result = getBooleanEnv(process.env.MUI_X_TELEMETRY_DEBUG);
      if (typeof result === "boolean") {
        return result;
      }
    }
  } catch (_) {
  }
  try {
    const {
      importMetaEnv: importMetaEnv2
    } = (init_config_import_meta(), __toCommonJS(config_import_meta_exports));
    if (importMetaEnv2) {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DEBUG", importMetaEnv2);
      if (typeof result === "boolean") {
        return result;
      }
    }
  } catch (_) {
  }
  try {
    const envValue = process.env.MUI_X_TELEMETRY_DEBUG || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DEBUG || process.env.GATSBY_MUI_X_TELEMETRY_DEBUG || process.env.REACT_APP_MUI_X_TELEMETRY_DEBUG || process.env.PUBLIC_MUI_X_TELEMETRY_DEBUG;
    const result = getBooleanEnv(envValue);
    if (typeof result === "boolean") {
      return result;
    }
  } catch (_) {
  }
  return false;
}
function getNodeEnv() {
  try {
    return "development";
  } catch (_) {
    return "<unknown>";
  }
}
var cachedEnv = null;
function getTelemetryEnvConfig(skipCache = false) {
  if (skipCache || !cachedEnv) {
    cachedEnv = {
      NODE_ENV: getNodeEnv(),
      IS_COLLECTING: getIsTelemetryCollecting(),
      DEBUG: getIsDebugModeEnabled()
    };
  }
  return cachedEnv;
}
function getTelemetryEnvConfigValue(key) {
  return getTelemetryEnvConfig()[key];
}

// node_modules/@mui/x-telemetry/esm/runtime/fetcher.js
async function fetchWithRetry(url, options, retries = 3) {
  try {
    const response = await fetch(url, options);
    if (response.ok) {
      return response;
    }
    throw new Error(`Request failed with status ${response.status}`);
  } catch (error) {
    if (retries === 0) {
      throw error;
    }
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fetchWithRetry(url, options, retries - 1));
      }, Math.random() * 3e3);
    });
  }
}

// node_modules/@mui/x-telemetry/esm/runtime/sender.js
var sendMuiXTelemetryRetries = 3;
function shouldSendTelemetry(telemetryContext) {
  if (typeof window === "undefined") {
    return false;
  }
  const envIsCollecting = getTelemetryEnvConfigValue("IS_COLLECTING");
  if (typeof envIsCollecting === "boolean") {
    return envIsCollecting;
  }
  if (telemetryContext.traits.isCI) {
    return false;
  }
  return false;
}
async function sendMuiXTelemetryEvent(event) {
  try {
    if (false) {
      return;
    }
    const {
      default: getTelemetryContext
    } = await import("./get-context-JTCQ7LEZ.js");
    const telemetryContext = await getTelemetryContext();
    if (!event || !shouldSendTelemetry(telemetryContext)) {
      return;
    }
    const eventPayload = _extends({}, event, {
      context: _extends({}, telemetryContext.traits, event.context)
    });
    if (getTelemetryEnvConfigValue("DEBUG")) {
      console.log("[mui-x-telemetry] event", JSON.stringify(eventPayload, null, 2));
      return;
    }
    await fetchWithRetry("https://x-telemetry.mui.com/v2/telemetry/record", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Telemetry-Client-Version": "8.11.0",
        "X-Telemetry-Node-Env": "development"
      },
      body: JSON.stringify([eventPayload])
    }, sendMuiXTelemetryRetries);
  } catch (_) {
    console.log("[mui-x-telemetry] error", _);
  }
}
var sender_default = sendMuiXTelemetryEvent;

// node_modules/@mui/x-telemetry/esm/index.js
var sendMuiXTelemetryEvent2 = false ? noop : sender_default;

// node_modules/@mui/x-license/esm/Unstable_LicenseInfoProvider/MuiLicenseInfoContext.js
var React = __toESM(require_react(), 1);
var MuiLicenseInfoContext = React.createContext({
  key: void 0
});
if (true) MuiLicenseInfoContext.displayName = "MuiLicenseInfoContext";
var MuiLicenseInfoContext_default = MuiLicenseInfoContext;

// node_modules/@mui/x-license/esm/useLicenseVerifier/useLicenseVerifier.js
var sharedLicenseStatuses = {};
function useLicenseVerifier(packageName, releaseInfo2) {
  const {
    key: contextKey
  } = React2.useContext(MuiLicenseInfoContext_default);
  return React2.useMemo(() => {
    const licenseKey = contextKey ?? LicenseInfo.getLicenseKey();
    if (sharedLicenseStatuses[packageName] && sharedLicenseStatuses[packageName].key === licenseKey) {
      return sharedLicenseStatuses[packageName].licenseVerifier;
    }
    const plan = packageName.includes("premium") ? "Premium" : "Pro";
    const licenseStatus = verifyLicense({
      releaseInfo: releaseInfo2,
      licenseKey,
      packageName
    });
    const fullPackageName = `@mui/${packageName}`;
    sendMuiXTelemetryEvent2(events_default.licenseVerification({
      licenseKey
    }, {
      packageName,
      packageReleaseInfo: releaseInfo2,
      licenseStatus: licenseStatus == null ? void 0 : licenseStatus.status
    }));
    if (licenseStatus.status === LICENSE_STATUS.Valid) {
    } else if (licenseStatus.status === LICENSE_STATUS.Invalid) {
      showInvalidLicenseKeyError();
    } else if (licenseStatus.status === LICENSE_STATUS.NotAvailableInInitialProPlan) {
      showNotAvailableInInitialProPlanError();
    } else if (licenseStatus.status === LICENSE_STATUS.OutOfScope) {
      showLicenseKeyPlanMismatchError();
    } else if (licenseStatus.status === LICENSE_STATUS.NotFound) {
      showMissingLicenseKeyError({
        plan,
        packageName: fullPackageName
      });
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnualGrace) {
      showExpiredAnnualGraceLicenseKeyError(_extends({
        plan
      }, licenseStatus.meta));
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnual) {
      showExpiredAnnualLicenseKeyError(_extends({
        plan
      }, licenseStatus.meta));
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredVersion) {
      showExpiredPackageVersionError({
        packageName: fullPackageName
      });
    } else if (true) {
      throw new Error("missing status handler");
    }
    sharedLicenseStatuses[packageName] = {
      key: licenseKey,
      licenseVerifier: licenseStatus
    };
    return licenseStatus;
  }, [packageName, releaseInfo2, contextKey]);
}

// node_modules/@mui/x-license/esm/Watermark/Watermark.js
var React3 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function getLicenseErrorMessage(licenseStatus) {
  switch (licenseStatus) {
    case LICENSE_STATUS.ExpiredAnnualGrace:
    case LICENSE_STATUS.ExpiredAnnual:
      return "MUI X Expired license key";
    case LICENSE_STATUS.ExpiredVersion:
      return "MUI X Expired package version";
    case LICENSE_STATUS.Invalid:
      return "MUI X Invalid license key";
    case LICENSE_STATUS.OutOfScope:
      return "MUI X License key plan mismatch";
    case LICENSE_STATUS.NotAvailableInInitialProPlan:
      return "MUI X Product not covered by plan";
    case LICENSE_STATUS.NotFound:
      return "MUI X Missing license key";
    default:
      throw new Error("Unhandled MUI X license status.");
  }
}
function Watermark(props) {
  const {
    packageName,
    releaseInfo: releaseInfo2
  } = props;
  const licenseStatus = useLicenseVerifier(packageName, releaseInfo2);
  if (licenseStatus.status === LICENSE_STATUS.Valid) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("div", {
    style: {
      position: "absolute",
      pointerEvents: "none",
      color: "#8282829e",
      zIndex: 1e5,
      width: "100%",
      textAlign: "center",
      bottom: "50%",
      right: 0,
      letterSpacing: 5,
      fontSize: 24
    },
    children: getLicenseErrorMessage(licenseStatus.status)
  });
}
var MemoizedWatermark = fastMemo(Watermark);

// node_modules/@mui/x-license/esm/Unstable_LicenseInfoProvider/LicenseInfoProvider.js
var React4 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mui/x-data-grid-pro/esm/DataGridPro/useDataGridProComponent.js
var React5 = __toESM(require_react(), 1);
var useDataGridProComponent = (apiRef, props, configuration2) => {
  useGridInitialization(apiRef, props);
  useGridDetailPanelPreProcessors(apiRef, props);
  useGridTreeDataPreProcessors(apiRef, props);
  useGridDataSourceTreeDataPreProcessors(apiRef, props);
  useGridRowSelectionPreProcessors(apiRef, props);
  useGridLazyLoaderPreProcessors(apiRef, props);
  useGridRowPinningPreProcessors(apiRef);
  useGridRowReorderPreProcessors(apiRef, props);
  useGridColumnPinningPreProcessors(apiRef, props);
  useGridRowsPreProcessors(apiRef);
  useGridInitializeState(propsStateInitializer, apiRef, props);
  useGridInitializeState(headerFilteringStateInitializer, apiRef, props);
  useGridInitializeState(rowSelectionStateInitializer, apiRef, props);
  useGridInitializeState(rowReorderStateInitializer, apiRef, props);
  useGridInitializeState(detailPanelStateInitializer, apiRef, props);
  useGridInitializeState(columnPinningStateInitializer, apiRef, props);
  useGridInitializeState(columnsStateInitializer, apiRef, props);
  useGridInitializeState(rowPinningStateInitializer, apiRef, props);
  useGridInitializeState(rowsStateInitializer, apiRef, props);
  useGridInitializeState(paginationStateInitializer, apiRef, props);
  useGridInitializeState(editingStateInitializer, apiRef, props);
  useGridInitializeState(focusStateInitializer, apiRef, props);
  useGridInitializeState(sortingStateInitializer, apiRef, props);
  useGridInitializeState(preferencePanelStateInitializer, apiRef, props);
  useGridInitializeState(filterStateInitializer, apiRef, props);
  useGridInitializeState(rowSpanningStateInitializer, apiRef, props);
  useGridInitializeState(densityStateInitializer, apiRef, props);
  useGridInitializeState(columnReorderStateInitializer, apiRef, props);
  useGridInitializeState(columnResizeStateInitializer, apiRef, props);
  useGridInitializeState(columnMenuStateInitializer, apiRef, props);
  useGridInitializeState(columnGroupsStateInitializer, apiRef, props);
  useGridInitializeState(virtualizationStateInitializer, apiRef, props);
  useGridInitializeState(dataSourceStateInitializer, apiRef, props);
  useGridInitializeState(dimensionsStateInitializer, apiRef, props);
  useGridInitializeState(rowsMetaStateInitializer, apiRef, props);
  useGridInitializeState(listViewStateInitializer, apiRef, props);
  useGridVirtualizer(apiRef, props);
  useGridHeaderFiltering(apiRef, props);
  useGridTreeData(apiRef, props);
  useGridKeyboardNavigation(apiRef, props);
  useGridRowSelection(apiRef, props);
  useGridColumnPinning(apiRef, props);
  useGridRowPinning(apiRef, props);
  useGridColumns(apiRef, props);
  useGridRows(apiRef, props, configuration2);
  useGridRowSpanning(apiRef, props);
  useGridParamsApi(apiRef, props);
  useGridDetailPanel(apiRef, props);
  useGridColumnSpanning(apiRef);
  useGridColumnGrouping(apiRef, props);
  useGridEditing(apiRef, props);
  useGridFocus(apiRef, props);
  useGridPreferencesPanel(apiRef, props);
  useGridFilter(apiRef, props);
  useGridSorting(apiRef, props);
  useGridDensity(apiRef, props);
  useGridColumnReorder(apiRef, props);
  useGridColumnResize(apiRef, props);
  useGridPagination(apiRef, props);
  useGridRowsMeta(apiRef, props);
  useGridRowReorder(apiRef, props);
  useGridScroll(apiRef, props);
  useGridInfiniteLoader(apiRef, props);
  useGridLazyLoader(apiRef, props);
  useGridDataSourceLazyLoader(apiRef, props);
  useGridInfiniteLoadingIntersection(apiRef, props);
  useGridColumnMenu(apiRef);
  useGridCsvExport(apiRef, props);
  useGridPrintExport(apiRef, props);
  useGridClipboard(apiRef, props);
  useGridDimensions(apiRef, props);
  useGridEvents(apiRef, props);
  useGridStatePersistence(apiRef);
  useGridVirtualization(apiRef, props);
  useGridDataSourcePro(apiRef, props);
  useGridListView(apiRef, props);
  React5.useEffect(() => {
    apiRef.current.runAppliersForPendingProcessors();
  });
};

// node_modules/@mui/x-data-grid-pro/esm/DataGridPro/useDataGridProProps.js
var React6 = __toESM(require_react(), 1);
var getDataGridProForcedProps = (themedProps) => _extends({
  signature: "DataGridPro"
}, themedProps.dataSource ? {
  filterMode: "server",
  sortingMode: "server",
  paginationMode: "server"
} : {});
var DATA_GRID_PRO_PROPS_DEFAULT_VALUES = _extends({}, DATA_GRID_PROPS_DEFAULT_VALUES, {
  autosizeOnMount: false,
  defaultGroupingExpansionDepth: 0,
  disableAutosize: false,
  disableChildrenFiltering: false,
  disableChildrenSorting: false,
  disableColumnPinning: false,
  getDetailPanelHeight: () => 500,
  headerFilters: false,
  keepColumnPositionIfDraggedOutside: false,
  rowSelectionPropagation: ROW_SELECTION_PROPAGATION_DEFAULT,
  rowReordering: false,
  rowsLoadingMode: "client",
  scrollEndThreshold: 80,
  treeData: false,
  lazyLoading: false,
  lazyLoadingRequestThrottleMs: 500,
  listView: false,
  multipleColumnsSortingMode: "withModifierKey"
});
var defaultSlots = DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS;
var useDataGridProProps = (inProps) => {
  const theme = useTheme();
  const themedProps = React6.useMemo(() => getThemeProps({
    props: inProps,
    theme,
    name: "MuiDataGrid"
  }), [theme, inProps]);
  const localeText = React6.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);
  const slots = React6.useMemo(() => computeSlots({
    defaultSlots,
    slots: themedProps.slots
  }), [themedProps.slots]);
  return React6.useMemo(() => _extends({}, DATA_GRID_PRO_PROPS_DEFAULT_VALUES, themedProps, {
    localeText,
    slots
  }, getDataGridProForcedProps(themedProps)), [themedProps, localeText, slots]);
};

// node_modules/@mui/x-data-grid-pro/esm/DataGridPro/DataGridPro.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var configuration = {
  hooks: {
    useCSSVariables: useMaterialCSSVariables,
    useGridAriaAttributes: useGridAriaAttributesPro,
    useGridRowAriaAttributes: useGridRowAriaAttributesPro,
    useGridRowsOverridableMethods,
    useCellAggregationResult: () => null
  }
};
var releaseInfo = "MTc1Njk0NDAwMDAwMA==";
var watermark = (0, import_jsx_runtime3.jsx)(MemoizedWatermark, {
  packageName: "x-data-grid-pro",
  releaseInfo
});
var DataGridProRaw = forwardRef(function DataGridPro(inProps, ref) {
  var _a;
  const props = useDataGridProProps(inProps);
  const privateApiRef = useGridApiInitialization(props.apiRef, props);
  useDataGridProComponent(privateApiRef, props, configuration);
  useLicenseVerifier("x-data-grid-pro", releaseInfo);
  if (true) {
    validateProps(props, propValidatorsDataGridPro);
  }
  return (0, import_jsx_runtime3.jsx)(GridContextProvider, {
    privateApiRef,
    configuration,
    props,
    children: (0, import_jsx_runtime3.jsx)(MemoizedGridRoot, _extends({
      className: props.className,
      style: props.style,
      sx: props.sx
    }, (_a = props.slotProps) == null ? void 0 : _a.root, {
      ref,
      children: watermark
    }))
  });
});
if (true) DataGridProRaw.displayName = "DataGridProRaw";
var DataGridPro2 = React7.memo(DataGridProRaw);
if (true) DataGridPro2.displayName = "DataGridPro";
DataGridProRaw.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The ref object that allows grid manipulation. Can be instantiated with `useGridApiRef()`.
   */
  apiRef: import_prop_types.default.shape({
    current: import_prop_types.default.object
  }),
  /**
   * The `aria-label` of the Data Grid.
   */
  "aria-label": import_prop_types.default.string,
  /**
   * The `id` of the element containing a label for the Data Grid.
   */
  "aria-labelledby": import_prop_types.default.string,
  /**
   * If `true`, the Data Grid height is dynamic and follows the number of rows in the Data Grid.
   * @default false
   * @deprecated Use flex parent container instead: https://mui.com/x/react-data-grid/layout/#flex-parent-container
   * @example
   * <div style={{ display: 'flex', flexDirection: 'column' }}>
   *   <DataGrid />
   * </div>
   */
  autoHeight: import_prop_types.default.bool,
  /**
   * If `true`, the pageSize is calculated according to the container size and the max number of rows to avoid rendering a vertical scroll bar.
   * @default false
   */
  autoPageSize: import_prop_types.default.bool,
  /**
   * If `true`, columns are autosized after the datagrid is mounted.
   * @default false
   */
  autosizeOnMount: import_prop_types.default.bool,
  /**
   * The options for autosize when user-initiated.
   */
  autosizeOptions: import_prop_types.default.shape({
    columns: import_prop_types.default.arrayOf(import_prop_types.default.string),
    disableColumnVirtualization: import_prop_types.default.bool,
    expand: import_prop_types.default.bool,
    includeHeaders: import_prop_types.default.bool,
    includeOutliers: import_prop_types.default.bool,
    outliersFactor: import_prop_types.default.number
  }),
  /**
   * Controls the modes of the cells.
   */
  cellModesModel: import_prop_types.default.object,
  /**
   * If `true`, the Data Grid will display an extra column with checkboxes for selecting rows.
   * @default false
   */
  checkboxSelection: import_prop_types.default.bool,
  /**
   * If `true`, the "Select All" header checkbox selects only the rows on the current page. To be used in combination with `checkboxSelection`.
   * It only works if the pagination is enabled.
   * @default false
   */
  checkboxSelectionVisibleOnly: import_prop_types.default.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  className: import_prop_types.default.string,
  /**
   * The character used to separate cell values when copying to the clipboard.
   * @default '\t'
   */
  clipboardCopyCellDelimiter: import_prop_types.default.string,
  /**
   * Column region in pixels to render before/after the viewport
   * @default 150
   */
  columnBufferPx: import_prop_types.default.number,
  /**
   * The milliseconds delay to wait after a keystroke before triggering filtering in the columns menu.
   * @default 150
   */
  columnFilterDebounceMs: import_prop_types.default.number,
  /**
   * Sets the height in pixels of the column group headers in the Data Grid.
   * Inherits the `columnHeaderHeight` value if not set.
   */
  columnGroupHeaderHeight: import_prop_types.default.number,
  columnGroupingModel: import_prop_types.default.arrayOf(import_prop_types.default.object),
  /**
   * Sets the height in pixel of the column headers in the Data Grid.
   * @default 56
   */
  columnHeaderHeight: import_prop_types.default.number,
  /**
   * Set of columns of type [[GridColDef]][].
   */
  columns: import_prop_types.default.arrayOf(import_prop_types.default.object).isRequired,
  /**
   * Set the column visibility model of the Data Grid.
   * If defined, the Data Grid will ignore the `hide` property in [[GridColDef]].
   */
  columnVisibilityModel: import_prop_types.default.object,
  /**
   * The data source of the Data Grid Pro.
   */
  dataSource: import_prop_types.default.shape({
    getChildrenCount: import_prop_types.default.func,
    getGroupKey: import_prop_types.default.func,
    getRows: import_prop_types.default.func.isRequired,
    updateRow: import_prop_types.default.func
  }),
  /**
   * Data source cache object.
   */
  dataSourceCache: import_prop_types.default.shape({
    clear: import_prop_types.default.func.isRequired,
    get: import_prop_types.default.func.isRequired,
    set: import_prop_types.default.func.isRequired
  }),
  /**
   * If above 0, the row children will be expanded up to this depth.
   * If equal to -1, all the row children will be expanded.
   * @default 0
   */
  defaultGroupingExpansionDepth: import_prop_types.default.number,
  /**
   * Set the density of the Data Grid.
   * @default "standard"
   */
  density: import_prop_types.default.oneOf(["comfortable", "compact", "standard"]),
  /**
   * The row ids to show the detail panel.
   */
  detailPanelExpandedRowIds: import_prop_types.default.instanceOf(Set),
  /**
   * If `true`, column autosizing on header separator double-click is disabled.
   * @default false
   */
  disableAutosize: import_prop_types.default.bool,
  /**
   * If `true`, the filtering will only be applied to the top level rows when grouping rows with the `treeData` prop.
   * @default false
   */
  disableChildrenFiltering: import_prop_types.default.bool,
  /**
   * If `true`, the sorting will only be applied to the top level rows when grouping rows with the `treeData` prop.
   * @default false
   */
  disableChildrenSorting: import_prop_types.default.bool,
  /**
   * If `true`, column filters are disabled.
   * @default false
   */
  disableColumnFilter: import_prop_types.default.bool,
  /**
   * If `true`, the column menu is disabled.
   * @default false
   */
  disableColumnMenu: import_prop_types.default.bool,
  /**
   * If `true`, the column pinning is disabled.
   * @default false
   */
  disableColumnPinning: import_prop_types.default.bool,
  /**
   * If `true`, reordering columns is disabled.
   * @default false
   */
  disableColumnReorder: import_prop_types.default.bool,
  /**
   * If `true`, resizing columns is disabled.
   * @default false
   */
  disableColumnResize: import_prop_types.default.bool,
  /**
   * If `true`, hiding/showing columns is disabled.
   * @default false
   */
  disableColumnSelector: import_prop_types.default.bool,
  /**
   * If `true`, the column sorting feature will be disabled.
   * @default false
   */
  disableColumnSorting: import_prop_types.default.bool,
  /**
   * If `true`, the density selector is disabled.
   * @default false
   */
  disableDensitySelector: import_prop_types.default.bool,
  /**
   * If `true`, `eval()` is not used for performance optimization.
   * @default false
   */
  disableEval: import_prop_types.default.bool,
  /**
   * If `true`, filtering with multiple columns is disabled.
   * @default false
   */
  disableMultipleColumnsFiltering: import_prop_types.default.bool,
  /**
   * If `true`, the sorting with multiple columns is disabled.
   * @default false
   */
  disableMultipleColumnsSorting: import_prop_types.default.bool,
  /**
   * If `true`, multiple selection using the Ctrl/CMD or Shift key is disabled.
   * The MIT DataGrid will ignore this prop, unless `checkboxSelection` is enabled.
   * @default false (`!props.checkboxSelection` for MIT Data Grid)
   */
  disableMultipleRowSelection: import_prop_types.default.bool,
  /**
   * If `true`, the selection on click on a row or cell is disabled.
   * @default false
   */
  disableRowSelectionOnClick: import_prop_types.default.bool,
  /**
   * If `true`, the virtualization is disabled.
   * @default false
   */
  disableVirtualization: import_prop_types.default.bool,
  /**
   * Controls whether to use the cell or row editing.
   * @default "cell"
   */
  editMode: import_prop_types.default.oneOf(["cell", "row"]),
  /**
   * Use if the actual rowCount is not known upfront, but an estimation is available.
   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.
   * Applicable only with `paginationMode="server"` and when `rowCount="-1"`
   */
  estimatedRowCount: import_prop_types.default.number,
  /**
   * Unstable features, breaking changes might be introduced.
   * For each feature, if the flag is not explicitly set to `true`, the feature will be fully disabled and any property / method call will not have any effect.
   */
  experimentalFeatures: import_prop_types.default.shape({
    warnIfFocusStateIsNotSynced: import_prop_types.default.bool
  }),
  /**
   * The milliseconds delay to wait after a keystroke before triggering filtering.
   * @default 150
   */
  filterDebounceMs: import_prop_types.default.number,
  /**
   * Filtering can be processed on the server or client-side.
   * Set it to 'server' if you would like to handle filtering on the server-side.
   * @default "client"
   */
  filterMode: import_prop_types.default.oneOf(["client", "server"]),
  /**
   * Set the filter model of the Data Grid.
   */
  filterModel: import_prop_types.default.shape({
    items: import_prop_types.default.arrayOf(import_prop_types.default.shape({
      field: import_prop_types.default.string.isRequired,
      id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]),
      operator: import_prop_types.default.string.isRequired,
      value: import_prop_types.default.any
    })).isRequired,
    logicOperator: import_prop_types.default.oneOf(["and", "or"]),
    quickFilterExcludeHiddenColumns: import_prop_types.default.bool,
    quickFilterLogicOperator: import_prop_types.default.oneOf(["and", "or"]),
    quickFilterValues: import_prop_types.default.array
  }),
  /**
   * Function that applies CSS classes dynamically on cells.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @returns {string} The CSS class to apply to the cell.
   */
  getCellClassName: import_prop_types.default.func,
  /**
   * Function that returns the element to render in row detail.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {React.JSX.Element} The row detail element.
   */
  getDetailPanelContent: import_prop_types.default.func,
  /**
   * Function that returns the height of the row detail panel.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {number | string} The height in pixels or "auto" to use the content height.
   * @default "() => 500"
   */
  getDetailPanelHeight: import_prop_types.default.func,
  /**
   * Function that returns the estimated height for a row.
   * Only works if dynamic row height is used.
   * Once the row height is measured this value is discarded.
   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].
   * @returns {number | null} The estimated row height value. If `null` or `undefined` then the default row height, based on the density, is applied.
   */
  getEstimatedRowHeight: import_prop_types.default.func,
  /**
   * Function that applies CSS classes dynamically on rows.
   * @param {GridRowClassNameParams} params With all properties from [[GridRowClassNameParams]].
   * @returns {string} The CSS class to apply to the row.
   */
  getRowClassName: import_prop_types.default.func,
  /**
   * Function that sets the row height per row.
   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].
   * @returns {GridRowHeightReturnValue} The row height value. If `null` or `undefined` then the default row height is applied. If "auto" then the row height is calculated based on the content.
   */
  getRowHeight: import_prop_types.default.func,
  /**
   * Return the id of a given [[GridRowModel]].
   * Ensure the reference of this prop is stable to avoid performance implications.
   * It could be done by either defining the prop outside of the component or by memoizing it.
   */
  getRowId: import_prop_types.default.func,
  /**
   * Function that allows to specify the spacing between rows.
   * @param {GridRowSpacingParams} params With all properties from [[GridRowSpacingParams]].
   * @returns {GridRowSpacing} The row spacing values.
   */
  getRowSpacing: import_prop_types.default.func,
  /**
   * Determines the path of a row in the tree data.
   * For instance, a row with the path ["A", "B"] is the child of the row with the path ["A"].
   * Note that all paths must contain at least one element.
   * @template R
   * @param {R} row The row from which we want the path.
   * @returns {string[]} The path to the row.
   */
  getTreeDataPath: import_prop_types.default.func,
  /**
   * The grouping column used by the tree data.
   */
  groupingColDef: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * Override the height of the header filters.
   */
  headerFilterHeight: import_prop_types.default.number,
  /**
   * If `true`, the header filters feature is enabled.
   * @default false
   */
  headerFilters: import_prop_types.default.bool,
  /**
   * If `true`, the footer component is hidden.
   * @default false
   */
  hideFooter: import_prop_types.default.bool,
  /**
   * If `true`, the pagination component in the footer is hidden.
   * @default false
   */
  hideFooterPagination: import_prop_types.default.bool,
  /**
   * If `true`, the row count in the footer is hidden.
   * It has no effect if the pagination is enabled.
   * @default false
   */
  hideFooterRowCount: import_prop_types.default.bool,
  /**
   * If `true`, the selected row count in the footer is hidden.
   * @default false
   */
  hideFooterSelectedRowCount: import_prop_types.default.bool,
  /**
   * If `true`, the diacritics (accents) are ignored when filtering or quick filtering.
   * E.g. when filter value is `cafe`, the rows with `café` will be visible.
   * @default false
   */
  ignoreDiacritics: import_prop_types.default.bool,
  /**
   * If `true`, the Data Grid will not use `valueFormatter` when exporting to CSV or copying to clipboard.
   * If an object is provided, you can choose to ignore the `valueFormatter` for CSV export or clipboard export.
   * @default false
   */
  ignoreValueFormatterDuringExport: import_prop_types.default.oneOfType([import_prop_types.default.shape({
    clipboardExport: import_prop_types.default.bool,
    csvExport: import_prop_types.default.bool
  }), import_prop_types.default.bool]),
  /**
   * The initial state of the DataGridPro.
   * The data in it will be set in the state on initialization but will not be controlled.
   * If one of the data in `initialState` is also being controlled, then the control state wins.
   */
  initialState: import_prop_types.default.object,
  /**
   * Callback fired when a cell is rendered, returns true if the cell is editable.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @returns {boolean} A boolean indicating if the cell is editable.
   */
  isCellEditable: import_prop_types.default.func,
  /**
   * Determines if a group should be expanded after its creation.
   * This prop takes priority over the `defaultGroupingExpansionDepth` prop.
   * @param {GridGroupNode} node The node of the group to test.
   * @returns {boolean} A boolean indicating if the group is expanded.
   */
  isGroupExpandedByDefault: import_prop_types.default.func,
  /**
   * Determines if a row can be selected.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {boolean} A boolean indicating if the row is selectable.
   */
  isRowSelectable: import_prop_types.default.func,
  /**
   * If `true`, moving the mouse pointer outside the grid before releasing the mouse button
   * in a column re-order action will not cause the column to jump back to its original position.
   * @default false
   */
  keepColumnPositionIfDraggedOutside: import_prop_types.default.bool,
  /**
   * If `true`, the selection model will retain selected rows that do not exist.
   * Useful when using server side pagination and row selections need to be retained
   * when changing pages.
   * @default false
   */
  keepNonExistentRowsSelected: import_prop_types.default.bool,
  /**
   * The label of the Data Grid.
   * If the `showToolbar` prop is `true`, the label will be displayed in the toolbar and applied to the `aria-label` attribute of the grid.
   * If the `showToolbar` prop is `false`, the label will not be visible but will be applied to the `aria-label` attribute of the grid.
   */
  label: import_prop_types.default.string,
  /**
   * Used together with `dataSource` to enable lazy loading.
   * If enabled, the grid stops adding `paginationModel` to the data requests (`getRows`)
   * and starts sending `start` and `end` values depending on the loading mode and the scroll position.
   * @default false
   */
  lazyLoading: import_prop_types.default.bool,
  /**
   * If positive, the Data Grid will throttle data source requests on rendered rows interval change.
   * @default 500
   */
  lazyLoadingRequestThrottleMs: import_prop_types.default.number,
  /**
   * If `true`, displays the data in a list view.
   * Use in combination with `listViewColumn`.
   */
  listView: import_prop_types.default.bool,
  /**
   * Definition of the column rendered when the `listView` prop is enabled.
   */
  listViewColumn: import_prop_types.default.shape({
    align: import_prop_types.default.oneOf(["center", "left", "right"]),
    cellClassName: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.string]),
    display: import_prop_types.default.oneOf(["flex", "text"]),
    field: import_prop_types.default.string.isRequired,
    renderCell: import_prop_types.default.func
  }),
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types.default.bool,
  /**
   * Set the locale text of the Data Grid.
   * You can find all the translation keys supported in [the source](https://github.com/mui/mui-x/blob/HEAD/packages/x-data-grid/src/constants/localeTextConstants.ts) in the GitHub repository.
   */
  localeText: import_prop_types.default.object,
  /**
   * Pass a custom logger in the components that implements the [[Logger]] interface.
   * @default console
   */
  logger: import_prop_types.default.shape({
    debug: import_prop_types.default.func.isRequired,
    error: import_prop_types.default.func.isRequired,
    info: import_prop_types.default.func.isRequired,
    warn: import_prop_types.default.func.isRequired
  }),
  /**
   * Allows to pass the logging level or false to turn off logging.
   * @default "error" ("warn" in dev mode)
   */
  logLevel: import_prop_types.default.oneOf(["debug", "error", "info", "warn", false]),
  /**
   * If set to "always", the multi-sorting is applied without modifier key.
   * Otherwise, the modifier key is required for multi-sorting to be applied.
   * @see See https://mui.com/x/react-data-grid/sorting/#multi-sorting
   * @default "withModifierKey"
   */
  multipleColumnsSortingMode: import_prop_types.default.oneOf(["always", "withModifierKey"]),
  /**
   * Nonce of the inline styles for [Content Security Policy](https://www.w3.org/TR/2016/REC-CSP2-20161215/#script-src-the-nonce-attribute).
   */
  nonce: import_prop_types.default.string,
  /**
   * Callback fired when any cell is clicked.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellClick: import_prop_types.default.func,
  /**
   * Callback fired when a double click event comes from a cell element.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellDoubleClick: import_prop_types.default.func,
  /**
   * Callback fired when the cell turns to edit mode.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.
   */
  onCellEditStart: import_prop_types.default.func,
  /**
   * Callback fired when the cell turns to view mode.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.
   */
  onCellEditStop: import_prop_types.default.func,
  /**
   * Callback fired when a keydown event comes from a cell element.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.KeyboardEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellKeyDown: import_prop_types.default.func,
  /**
   * Callback fired when the `cellModesModel` prop changes.
   * @param {GridCellModesModel} cellModesModel Object containing which cells are in "edit" mode.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellModesModelChange: import_prop_types.default.func,
  /**
   * Callback called when the data is copied to the clipboard.
   * @param {string} data The data copied to the clipboard.
   */
  onClipboardCopy: import_prop_types.default.func,
  /**
   * Callback fired when a click event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderClick: import_prop_types.default.func,
  /**
   * Callback fired when a contextmenu event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   */
  onColumnHeaderContextMenu: import_prop_types.default.func,
  /**
   * Callback fired when a double click event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderDoubleClick: import_prop_types.default.func,
  /**
   * Callback fired when a mouse enter event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderEnter: import_prop_types.default.func,
  /**
   * Callback fired when a mouse leave event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderLeave: import_prop_types.default.func,
  /**
   * Callback fired when a mouseout event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderOut: import_prop_types.default.func,
  /**
   * Callback fired when a mouseover event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderOver: import_prop_types.default.func,
  /**
   * Callback fired when a column is reordered.
   * @param {GridColumnOrderChangeParams} params With all properties from [[GridColumnOrderChangeParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnOrderChange: import_prop_types.default.func,
  /**
   * Callback fired while a column is being resized.
   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnResize: import_prop_types.default.func,
  /**
   * Callback fired when the column visibility model changes.
   * @param {GridColumnVisibilityModel} model The new model.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnVisibilityModelChange: import_prop_types.default.func,
  /**
   * Callback fired when the width of a column is changed.
   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnWidthChange: import_prop_types.default.func,
  /**
   * Callback fired when a data source request fails.
   * @param {GridGetRowsError | GridUpdateRowError} error The data source error object.
   */
  onDataSourceError: import_prop_types.default.func,
  /**
   * Callback fired when the density changes.
   * @param {GridDensity} density New density value.
   */
  onDensityChange: import_prop_types.default.func,
  /**
   * Callback fired when the detail panel of a row is opened or closed.
   * @param {GridRowId[]} ids The ids of the rows which have the detail panel open.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onDetailPanelExpandedRowIdsChange: import_prop_types.default.func,
  /**
   * Callback fired when rowCount is set and the next batch of virtualized rows is rendered.
   * @param {GridFetchRowsParams} params With all properties from [[GridFetchRowsParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#viewport-loading Server-side data-Viewport loading} instead.
   */
  onFetchRows: import_prop_types.default.func,
  /**
   * Callback fired when the Filter model changes before the filters are applied.
   * @param {GridFilterModel} model With all properties from [[GridFilterModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onFilterModelChange: import_prop_types.default.func,
  /**
   * Callback fired when the menu is closed.
   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onMenuClose: import_prop_types.default.func,
  /**
   * Callback fired when the menu is opened.
   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onMenuOpen: import_prop_types.default.func,
  /**
   * Callback fired when the pagination meta has changed.
   * @param {GridPaginationMeta} paginationMeta Updated pagination meta.
   */
  onPaginationMetaChange: import_prop_types.default.func,
  /**
   * Callback fired when the pagination model has changed.
   * @param {GridPaginationModel} model Updated pagination model.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPaginationModelChange: import_prop_types.default.func,
  /**
   * Callback fired when the pinned columns have changed.
   * @param {GridPinnedColumnFields} pinnedColumns The changed pinned columns.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPinnedColumnsChange: import_prop_types.default.func,
  /**
   * Callback fired when the preferences panel is closed.
   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPreferencePanelClose: import_prop_types.default.func,
  /**
   * Callback fired when the preferences panel is opened.
   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPreferencePanelOpen: import_prop_types.default.func,
  /**
   * Callback called when `processRowUpdate()` throws an error or rejects.
   * @param {any} error The error thrown.
   */
  onProcessRowUpdateError: import_prop_types.default.func,
  /**
   * Callback fired when the Data Grid is resized.
   * @param {ElementSize} containerSize With all properties from [[ElementSize]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onResize: import_prop_types.default.func,
  /**
   * Callback fired when a row is clicked.
   * Not called if the target clicked is an interactive element added by the built-in columns.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowClick: import_prop_types.default.func,
  /**
   * Callback fired when the row count has changed.
   * @param {number} count Updated row count.
   */
  onRowCountChange: import_prop_types.default.func,
  /**
   * Callback fired when a double click event comes from a row container element.
   * @param {GridRowParams} params With all properties from [[RowParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowDoubleClick: import_prop_types.default.func,
  /**
   * Callback fired when the row turns to edit mode.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.
   */
  onRowEditStart: import_prop_types.default.func,
  /**
   * Callback fired when the row turns to view mode.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.
   */
  onRowEditStop: import_prop_types.default.func,
  /**
   * Callback fired when the `rowModesModel` prop changes.
   * @param {GridRowModesModel} rowModesModel Object containing which rows are in "edit" mode.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowModesModelChange: import_prop_types.default.func,
  /**
   * Callback fired when a row is being reordered.
   * @param {GridRowOrderChangeParams} params With all properties from [[GridRowOrderChangeParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowOrderChange: import_prop_types.default.func,
  /**
   * Callback fired when the selection state of one or multiple rows changes.
   * @param {GridRowSelectionModel} rowSelectionModel With all the row ids [[GridSelectionModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowSelectionModelChange: import_prop_types.default.func,
  /**
   * Callback fired when scrolling to the bottom of the grid viewport.
   * @param {GridRowScrollEndParams} params With all properties from [[GridRowScrollEndParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#infinite-loading Server-side data-Infinite loading} instead.
   */
  onRowsScrollEnd: import_prop_types.default.func,
  /**
   * Callback fired when the sort model changes before a column is sorted.
   * @param {GridSortModel} model With all properties from [[GridSortModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onSortModelChange: import_prop_types.default.func,
  /**
   * Callback fired when the state of the Data Grid is updated.
   * @param {GridState} state The new state.
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @ignore - do not document.
   */
  onStateChange: import_prop_types.default.func,
  /**
   * Select the pageSize dynamically using the component UI.
   * @default [25, 50, 100]
   */
  pageSizeOptions: import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.shape({
    label: import_prop_types.default.string.isRequired,
    value: import_prop_types.default.number.isRequired
  })]).isRequired),
  /**
   * If `true`, pagination is enabled.
   * @default false
   */
  pagination: import_prop_types.default.bool,
  /**
   * The extra information about the pagination state of the Data Grid.
   * Only applicable with `paginationMode="server"`.
   */
  paginationMeta: import_prop_types.default.shape({
    hasNextPage: import_prop_types.default.bool
  }),
  /**
   * Pagination can be processed on the server or client-side.
   * Set it to 'client' if you would like to handle the pagination on the client-side.
   * Set it to 'server' if you would like to handle the pagination on the server-side.
   * @default "client"
   */
  paginationMode: import_prop_types.default.oneOf(["client", "server"]),
  /**
   * The pagination model of type [[GridPaginationModel]] which refers to current `page` and `pageSize`.
   */
  paginationModel: import_prop_types.default.shape({
    page: import_prop_types.default.number.isRequired,
    pageSize: import_prop_types.default.number.isRequired
  }),
  /**
   * The column fields to display pinned to left or right.
   */
  pinnedColumns: import_prop_types.default.object,
  /**
   * Rows data to pin on top or bottom.
   */
  pinnedRows: import_prop_types.default.shape({
    bottom: import_prop_types.default.arrayOf(import_prop_types.default.object),
    top: import_prop_types.default.arrayOf(import_prop_types.default.object)
  }),
  /**
   * Callback called before updating a row with new values in the row and cell editing.
   * @template R
   * @param {R} newRow Row object with the new values.
   * @param {R} oldRow Row object with the old values.
   * @param {{ rowId: GridRowId }} params Additional parameters.
   * @returns {Promise<R> | R} The final values to update the row.
   */
  processRowUpdate: import_prop_types.default.func,
  /**
   * The milliseconds throttle delay for resizing the grid.
   * @default 60
   */
  resizeThrottleMs: import_prop_types.default.number,
  /**
   * Row region in pixels to render before/after the viewport
   * @default 150
   */
  rowBufferPx: import_prop_types.default.number,
  /**
   * Set the total number of rows, if it is different from the length of the value `rows` prop.
   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.
   * Only works with `paginationMode="server"`, ignored when `paginationMode="client"`.
   */
  rowCount: import_prop_types.default.number,
  /**
   * Sets the height in pixel of a row in the Data Grid.
   * @default 52
   */
  rowHeight: import_prop_types.default.number,
  /**
   * Controls the modes of the rows.
   */
  rowModesModel: import_prop_types.default.object,
  /**
   * If `true`, the reordering of rows is enabled.
   * @default false
   */
  rowReordering: import_prop_types.default.bool,
  /**
   * Set of rows of type [[GridRowsProp]].
   * @default []
   */
  rows: import_prop_types.default.arrayOf(import_prop_types.default.object),
  /**
   * If `false`, the row selection mode is disabled.
   * @default true
   */
  rowSelection: import_prop_types.default.bool,
  /**
   * Sets the row selection model of the Data Grid.
   */
  rowSelectionModel: import_prop_types.default.shape({
    ids: import_prop_types.default.instanceOf(Set).isRequired,
    type: import_prop_types.default.oneOf(["exclude", "include"]).isRequired
  }),
  /**
   * When `rowSelectionPropagation.descendants` is set to `true`.
   * - Selecting a parent selects all its filtered descendants automatically.
   * - Deselecting a parent row deselects all its filtered descendants automatically.
   *
   * When `rowSelectionPropagation.parents` is set to `true`
   * - Selecting all the filtered descendants of a parent selects the parent automatically.
   * - Deselecting a descendant of a selected parent deselects the parent automatically.
   *
   * Works with tree data and row grouping on the client-side only.
   * @default { parents: true, descendants: true }
   */
  rowSelectionPropagation: import_prop_types.default.shape({
    descendants: import_prop_types.default.bool,
    parents: import_prop_types.default.bool
  }),
  /**
   * Loading rows can be processed on the server or client-side.
   * Set it to 'client' if you would like enable infnite loading.
   * Set it to 'server' if you would like to enable lazy loading.
   * @default "client"
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#viewport-loading Server-side data-Viewport loading} instead.
   */
  rowsLoadingMode: import_prop_types.default.oneOf(["client", "server"]),
  /**
   * Sets the type of space between rows added by `getRowSpacing`.
   * @default "margin"
   */
  rowSpacingType: import_prop_types.default.oneOf(["border", "margin"]),
  /**
   * If `true`, the Data Grid will auto span the cells over the rows having the same value.
   * @default false
   */
  rowSpanning: import_prop_types.default.bool,
  /**
   * Override the height/width of the Data Grid inner scrollbar.
   */
  scrollbarSize: import_prop_types.default.number,
  /**
   * Set the area in `px` at the bottom of the grid viewport where onRowsScrollEnd is called.
   * If combined with `lazyLoading`, it defines the area where the next data request is triggered.
   * @default 80
   */
  scrollEndThreshold: import_prop_types.default.number,
  /**
   * If `true`, vertical borders will be displayed between cells.
   * @default false
   */
  showCellVerticalBorder: import_prop_types.default.bool,
  /**
   * If `true`, vertical borders will be displayed between column header items.
   * @default false
   */
  showColumnVerticalBorder: import_prop_types.default.bool,
  /**
   * If `true`, the toolbar is displayed.
   * @default false
   */
  showToolbar: import_prop_types.default.bool,
  /**
   * Overridable components props dynamically passed to the component at rendering.
   */
  slotProps: import_prop_types.default.object,
  /**
   * Overridable components.
   */
  slots: import_prop_types.default.object,
  /**
   * Sorting can be processed on the server or client-side.
   * Set it to 'client' if you would like to handle sorting on the client-side.
   * Set it to 'server' if you would like to handle sorting on the server-side.
   * @default "client"
   */
  sortingMode: import_prop_types.default.oneOf(["client", "server"]),
  /**
   * The order of the sorting sequence.
   * @default ['asc', 'desc', null]
   */
  sortingOrder: import_prop_types.default.arrayOf(import_prop_types.default.oneOf(["asc", "desc"])),
  /**
   * Set the sort model of the Data Grid.
   */
  sortModel: import_prop_types.default.arrayOf(import_prop_types.default.shape({
    field: import_prop_types.default.string.isRequired,
    sort: import_prop_types.default.oneOf(["asc", "desc"])
  })),
  style: import_prop_types.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * If positive, the Data Grid will throttle updates coming from `apiRef.current.updateRows` and `apiRef.current.setRows`.
   * It can be useful if you have a high update rate but do not want to do heavy work like filtering / sorting or rendering on each  individual update.
   * @default 0
   */
  throttleRowsMs: import_prop_types.default.number,
  /**
   * If `true`, the rows will be gathered in a tree structure according to the `getTreeDataPath` prop.
   * @default false
   */
  treeData: import_prop_types.default.bool,
  /**
   * If `true`, the Data Grid enables column virtualization when `getRowHeight` is set to `() => 'auto'`.
   * By default, column virtualization is disabled when dynamic row height is enabled to measure the row height correctly.
   * For datasets with a large number of columns, this can cause performance issues.
   * The downside of enabling this prop is that the row height will be estimated based the cells that are currently rendered, which can cause row height change when scrolling horizontally.
   * @default false
   */
  virtualizeColumnsWithAutoRowHeight: import_prop_types.default.bool
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/utils/useGridApiRef.js
var useGridApiRef2 = useGridApiRef;

export {
  DataGrid,
  DataGridPremium,
  useLicenseVerifier,
  MemoizedWatermark,
  DATA_GRID_PRO_PROPS_DEFAULT_VALUES,
  DataGridPro2 as DataGridPro,
  useGridApiRef2 as useGridApiRef
};
/*! Bundled license information:

@mui/x-telemetry/esm/index.js:
  (**
   * @mui/x-telemetry v8.11.0
   *
   * @license SEE LICENSE IN LICENSE
   * This source code is licensed under the SEE LICENSE IN LICENSE license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@mui/x-license/esm/index.js:
  (**
   * @mui/x-license v8.11.1
   *
   * @license SEE LICENSE IN LICENSE
   * This source code is licensed under the SEE LICENSE IN LICENSE license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@mui/x-data-grid-pro/esm/index.js:
  (**
   * @mui/x-data-grid-pro v8.11.1
   *
   * @license SEE LICENSE IN LICENSE
   * This source code is licensed under the SEE LICENSE IN LICENSE license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Z75LME4T.js.map
