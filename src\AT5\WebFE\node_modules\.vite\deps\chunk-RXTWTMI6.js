import {
  inputBaseClasses_default
} from "./chunk-5IEXKNXB.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-QMOJV6NA.js";

// node_modules/@mui/material/esm/OutlinedInput/outlinedInputClasses.js
function getOutlinedInputUtilityClass(slot) {
  return generateUtilityClass("MuiOutlinedInput", slot);
}
var outlinedInputClasses = {
  ...inputBaseClasses_default,
  ...generateUtilityClasses("MuiOutlinedInput", ["root", "notchedOutline", "input"])
};
var outlinedInputClasses_default = outlinedInputClasses;

export {
  getOutlinedInputUtilityClass,
  outlinedInputClasses_default
};
//# sourceMappingURL=chunk-RXTWTMI6.js.map
