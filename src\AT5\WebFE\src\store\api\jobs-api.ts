import { baseApi } from './base-api';

export interface Job {
  id?: number;
  name: string;
  type: number;
  description?: string;
  parameters: string;
  successEmails?: string;
  errorEmails?: string;
  disabled: boolean;
}

export interface JobTrigger {
  id?: number;
  name: string;
  description?: string;
  parameters: string;
  type: number;
  disabled: boolean;
  jobId: number;
  runParameters?: string;
}

export interface JobCreateRequest {
  name: string;
  type: number;
  description?: string;
  parameters: string;
  emailSuccess?: string;
  emailFail?: string;
  disabled: boolean;
}

export interface JobUpdateRequest {
  name: string;
  type: number;
  description?: string;
  parameters: string;
  successEmails?: string;
  errorEmails?: string;
  disabled: boolean;
}

export interface JobsListResponse {
  value: Job[];
  count: number;
}

export interface JobsTriggersListResponse {
  value: JobTrigger[];
  count: number;
}

export interface JobDetailResponse {
  entity: Job;
}

// OData query parameters interface
export interface ODataParams {
  $top?: number;
  $skip?: number;
  $count?: boolean;
  $filter?: string;
  $orderby?: string;
  $select?: string;
}

export const jobsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get jobs list with OData support
    getJobs: builder.query<JobsListResponse, ODataParams>({
      query: (params) => {
        const searchParams = new URLSearchParams();
        
        // Add OData parameters
        if (params.$top !== undefined) {
          searchParams.append('$top', params.$top.toString());
        }
        if (params.$skip !== undefined) {
          searchParams.append('$skip', params.$skip.toString());
        }
        if (params.$count !== undefined) {
          searchParams.append('$count', params.$count.toString());
        }
        if (params.$filter) {
          searchParams.append('$filter', params.$filter);
        }
        if (params.$orderby) {
          searchParams.append('$orderby', params.$orderby);
        }
        if (params.$select) {
          searchParams.append('$select', params.$select);
        }

        return {
          url: `/jobs?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Job'],
    }),

    // Get single job by ID
    getJob: builder.query<JobDetailResponse, number>({
      query: (id) => ({
        url: `/jobs/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Job', id }],
    }),

    // Create new job
    createJob: builder.mutation<any, JobCreateRequest>({
      query: (job) => ({
        url: '/jobs',
        method: 'POST',
        body: job,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ['Job'],
    }),

    // Update existing job
    updateJob: builder.mutation<any, { id: number; job: JobUpdateRequest }>({
      query: ({ id, job }) => ({
        url: `/jobs/${id}`,
        method: 'PUT',
        body: { model: job }, // Matching the existing putToApi format
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Job', id }, 'Job'],
    }),

    deleteJob: builder.mutation<any, number>({
      query: (id) => ({
        url: `/jobs/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Job'],
    }),

    getJobTriggers: builder.query<JobsTriggersListResponse, { jobId: number, params: ODataParams }>({
      query: ({jobId, params}) => {
        const searchParams = new URLSearchParams();
        
        // Add OData parameters
        if (params.$top !== undefined) {
          searchParams.append('$top', params.$top.toString());
        }
        if (params.$skip !== undefined) {
          searchParams.append('$skip', params.$skip.toString());
        }
        if (params.$count !== undefined) {
          searchParams.append('$count', params.$count.toString());
        }
        if (params.$filter) {
          searchParams.append('$filter', params.$filter);
        }
        if (params.$orderby) {
          searchParams.append('$orderby', params.$orderby);
        }
        if (params.$select) {
          searchParams.append('$select', params.$select);
        }

        return {
          url: `/jobs/${jobId}/triggers?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      // providesTags: ['Job'],
    }),

    getJobTrigger: builder.query<JobTrigger, {jobId: number, triggerId: number}>({
      query: ({jobId, triggerId}) => ({
        url: `/jobs/${jobId}/triggers/${triggerId}`,
        method: 'GET',
      }),
      // providesTags: ['Job'],
    }),

    createJobTrigger: builder.mutation<any, {jobId: number, trigger: JobTrigger}>({
      query: ({jobId, trigger}) => ({
        url: `/jobs/${jobId}/triggers`,
        method: 'POST',
        body: trigger,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ['Job'],
    }),

    updateJobTrigger: builder.mutation<any, {jobId: number, triggerId: number, trigger: JobTrigger}>({
      query: ({jobId, triggerId, trigger}) => ({
        url: `/jobs/${jobId}/triggers/${triggerId}`,
        method: 'PUT',
        body: { model: trigger },
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ['Job'],
    }),

    deleteJobTrigger: builder.mutation<any, {jobId: number, triggerId: number}>({
      query: ({jobId, triggerId}) => ({
        url: `/jobs/${jobId}/triggers/${triggerId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Job'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetJobsQuery,
  useGetJobQuery,
  useCreateJobMutation,
  useUpdateJobMutation,
  useDeleteJobMutation,
  useGetJobTriggersQuery,
  useGetJobTriggerQuery,
  useCreateJobTriggerMutation,
  useUpdateJobTriggerMutation,
  useDeleteJobTriggerMutation,
} = jobsApi;
