import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/demo/currentuserpermission` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as demoGeneratedApi };
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg =
  void;
export type AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse = {
  hasPermissionEnum?: boolean;
  hasPermissionSmartEnum?: boolean;
};
export const {
  useAtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointQuery,
} = injectedRtkApi;
