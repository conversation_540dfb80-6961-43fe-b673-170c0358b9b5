{"version": 3, "sources": ["../../@mui/material/esm/Switch/Switch.js", "../../@mui/material/esm/internal/SwitchBase.js", "../../@mui/material/esm/internal/switchBaseClasses.js", "../../@mui/material/esm/Switch/switchClasses.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n})), memoTheme(({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.alpha((theme.vars || theme).palette.action.active, (theme.vars || theme).palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.alpha((theme.vars || theme).palette[color].main, (theme.vars || theme).palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? theme.lighten(theme.palette[color].main, 0.62) : theme.darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n})));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track'\n})(memoTheme(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n})));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb'\n})(memoTheme(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n})));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        input: {\n          role: 'switch'\n        },\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from \"../utils/capitalize.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport useFormControl from \"../FormControl/useFormControl.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { getSwitchBaseUtilityClass } from \"./switchBaseClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    checked,\n    disabled,\n    edge\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', edge && `edge${capitalize(edge)}`],\n    input: ['input']\n  };\n  return composeClasses(slots, getSwitchBaseUtilityClass, classes);\n};\nconst SwitchBaseRoot = styled(ButtonBase, {\n  name: 'MuiSwitchBase'\n})({\n  padding: 9,\n  borderRadius: '50%',\n  variants: [{\n    props: {\n      edge: 'start',\n      size: 'small'\n    },\n    style: {\n      marginLeft: -3\n    }\n  }, {\n    props: ({\n      edge,\n      ownerState\n    }) => edge === 'start' && ownerState.size !== 'small',\n    style: {\n      marginLeft: -12\n    }\n  }, {\n    props: {\n      edge: 'end',\n      size: 'small'\n    },\n    style: {\n      marginRight: -3\n    }\n  }, {\n    props: ({\n      edge,\n      ownerState\n    }) => edge === 'end' && ownerState.size !== 'small',\n    style: {\n      marginRight: -12\n    }\n  }]\n});\nconst SwitchBaseInput = styled('input', {\n  name: 'MuiSwitchBase',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  cursor: 'inherit',\n  position: 'absolute',\n  opacity: 0,\n  width: '100%',\n  height: '100%',\n  top: 0,\n  left: 0,\n  margin: 0,\n  padding: 0,\n  zIndex: 1\n});\n\n/**\n * @ignore - internal component.\n */\nconst SwitchBase = /*#__PURE__*/React.forwardRef(function SwitchBase(props, ref) {\n  const {\n    autoFocus,\n    checked: checkedProp,\n    checkedIcon,\n    defaultChecked,\n    disabled: disabledProp,\n    disableFocusRipple = false,\n    edge = false,\n    icon,\n    id,\n    inputProps,\n    inputRef,\n    name,\n    onBlur,\n    onChange,\n    onFocus,\n    readOnly,\n    required = false,\n    tabIndex,\n    type,\n    value,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [checked, setCheckedState] = useControlled({\n    controlled: checkedProp,\n    default: Boolean(defaultChecked),\n    name: 'SwitchBase',\n    state: 'checked'\n  });\n  const muiFormControl = useFormControl();\n  const handleFocus = event => {\n    if (onFocus) {\n      onFocus(event);\n    }\n    if (muiFormControl && muiFormControl.onFocus) {\n      muiFormControl.onFocus(event);\n    }\n  };\n  const handleBlur = event => {\n    if (onBlur) {\n      onBlur(event);\n    }\n    if (muiFormControl && muiFormControl.onBlur) {\n      muiFormControl.onBlur(event);\n    }\n  };\n  const handleInputChange = event => {\n    // Workaround for https://github.com/facebook/react/issues/9023\n    if (event.nativeEvent.defaultPrevented) {\n      return;\n    }\n    const newChecked = event.target.checked;\n    setCheckedState(newChecked);\n    if (onChange) {\n      // TODO v6: remove the second argument.\n      onChange(event, newChecked);\n    }\n  };\n  let disabled = disabledProp;\n  if (muiFormControl) {\n    if (typeof disabled === 'undefined') {\n      disabled = muiFormControl.disabled;\n    }\n  }\n  const hasLabelFor = type === 'checkbox' || type === 'radio';\n  const ownerState = {\n    ...props,\n    checked,\n    disabled,\n    disableFocusRipple,\n    edge\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      input: inputProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    elementType: SwitchBaseRoot,\n    className: classes.root,\n    shouldForwardComponentProp: true,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      component: 'span',\n      ...other\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onFocus: event => {\n        handlers.onFocus?.(event);\n        handleFocus(event);\n      },\n      onBlur: event => {\n        handlers.onBlur?.(event);\n        handleBlur(event);\n      }\n    }),\n    ownerState,\n    additionalProps: {\n      centerRipple: true,\n      focusRipple: !disableFocusRipple,\n      disabled,\n      role: undefined,\n      tabIndex: null\n    }\n  });\n  const [InputSlot, inputSlotProps] = useSlot('input', {\n    ref: inputRef,\n    elementType: SwitchBaseInput,\n    className: classes.input,\n    externalForwardedProps,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onChange: event => {\n        handlers.onChange?.(event);\n        handleInputChange(event);\n      }\n    }),\n    ownerState,\n    additionalProps: {\n      autoFocus,\n      checked: checkedProp,\n      defaultChecked,\n      disabled,\n      id: hasLabelFor ? id : undefined,\n      name,\n      readOnly,\n      required,\n      tabIndex,\n      type,\n      ...(type === 'checkbox' && value === undefined ? {} : {\n        value\n      })\n    }\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(InputSlot, {\n      ...inputSlotProps\n    }), checked ? checkedIcon : icon]\n  });\n});\n\n// NB: If changed, please update Checkbox, Switch and Radio\n// so that the API documentation is updated.\nprocess.env.NODE_ENV !== \"production\" ? SwitchBase.propTypes = {\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node.isRequired,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /*\n   * @ignore\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {object} event The event source of the callback.\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * It prevents the user from changing the value of the field\n   * (not from interacting with the field).\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.object,\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The input component prop `type`.\n   */\n  type: PropTypes.string.isRequired,\n  /**\n   * The value of the component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default SwitchBase;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchBaseUtilityClass(slot) {\n  return generateUtilityClass('PrivateSwitchBase', slot);\n}\nconst switchBaseClasses = generateUtilityClasses('PrivateSwitchBase', ['root', 'checked', 'disabled', 'input', 'edgeStart', 'edgeEnd']);\nexport default switchBaseClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchClasses = generateUtilityClasses('MuiSwitch', ['root', 'edgeStart', 'edgeEnd', 'switchBase', 'colorPrimary', 'colorSecondary', 'sizeSmall', 'sizeMedium', 'checked', 'disabled', 'input', 'thumb', 'track']);\nexport default switchClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,SAAuB;AACvB,IAAAC,qBAAsB;;;ACFtB,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,qBAAqB,IAAI;AACvD;AACA,IAAM,oBAAoB,uBAAuB,qBAAqB,CAAC,QAAQ,WAAW,YAAY,SAAS,aAAa,SAAS,CAAC;;;ADStI,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,WAAW,WAAW,YAAY,YAAY,QAAQ,OAAO,mBAAW,IAAI,CAAC,EAAE;AAAA,IAC9F,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACA,IAAM,iBAAiB,eAAO,oBAAY;AAAA,EACxC,MAAM;AACR,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,cAAc;AAAA,EACd,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,SAAS,WAAW,WAAW,SAAS;AAAA,IAC9C,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,SAAS,SAAS,WAAW,SAAS;AAAA,IAC5C,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,kBAAkB,eAAO,SAAS;AAAA,EACtC,MAAM;AAAA,EACN,mBAAmB;AACrB,CAAC,EAAE;AAAA,EACD,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AACV,CAAC;AAKD,IAAM,aAAgC,iBAAW,SAASC,YAAW,OAAO,KAAK;AAC/E,QAAM;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,SAAS,eAAe,IAAI,sBAAc;AAAA,IAC/C,YAAY;AAAA,IACZ,SAAS,QAAQ,cAAc;AAAA,IAC/B,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM,iBAAiB,eAAe;AACtC,QAAM,cAAc,WAAS;AAC3B,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AACA,QAAI,kBAAkB,eAAe,SAAS;AAC5C,qBAAe,QAAQ,KAAK;AAAA,IAC9B;AAAA,EACF;AACA,QAAM,aAAa,WAAS;AAC1B,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AACA,QAAI,kBAAkB,eAAe,QAAQ;AAC3C,qBAAe,OAAO,KAAK;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,oBAAoB,WAAS;AAEjC,QAAI,MAAM,YAAY,kBAAkB;AACtC;AAAA,IACF;AACA,UAAM,aAAa,MAAM,OAAO;AAChC,oBAAgB,UAAU;AAC1B,QAAI,UAAU;AAEZ,eAAS,OAAO,UAAU;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,WAAW;AACf,MAAI,gBAAgB;AAClB,QAAI,OAAO,aAAa,aAAa;AACnC,iBAAW,eAAe;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,cAAc,SAAS,cAAc,SAAS;AACpD,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,MACP,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD;AAAA,IACA,aAAa;AAAA,IACb,WAAW,QAAQ;AAAA,IACnB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,MACtB,GAAG;AAAA,MACH,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,IACA,cAAc,eAAa;AAAA,MACzB,GAAG;AAAA,MACH,SAAS,WAAS;AArLxB;AAsLQ,uBAAS,YAAT,kCAAmB;AACnB,oBAAY,KAAK;AAAA,MACnB;AAAA,MACA,QAAQ,WAAS;AAzLvB;AA0LQ,uBAAS,WAAT,kCAAkB;AAClB,mBAAW,KAAK;AAAA,MAClB;AAAA,IACF;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc;AAAA,MACd,aAAa,CAAC;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACD,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW,QAAQ;AAAA,IACnB;AAAA,IACA,cAAc,eAAa;AAAA,MACzB,GAAG;AAAA,MACH,UAAU,WAAS;AA9MzB;AA+MQ,uBAAS,aAAT,kCAAoB;AACpB,0BAAkB,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,IAAI,cAAc,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAI,SAAS,cAAc,UAAU,SAAY,CAAC,IAAI;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,KAAc,mBAAAC,KAAK,WAAW;AAAA,MACtC,GAAG;AAAA,IACL,CAAC,GAAG,UAAU,cAAc,IAAI;AAAA,EAClC,CAAC;AACH,CAAC;AAID,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA,EAI7D,WAAW,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,aAAa,kBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA,EAI5B,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,oBAAoB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9B,MAAM,kBAAAA,QAAU,MAAM,CAAC,OAAO,SAAS,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAI7C,MAAM,kBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIlE,MAAM,kBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvB,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,qBAAQ;;;AEnWR,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,aAAa,WAAW,cAAc,gBAAgB,kBAAkB,aAAa,cAAc,WAAW,YAAY,SAAS,SAAS,OAAO,CAAC;AACvN,IAAO,wBAAQ;;;AHUf,IAAAC,sBAA2C;AAC3C,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,QAAQ,OAAO,mBAAW,IAAI,CAAC,IAAI,OAAO,mBAAW,IAAI,CAAC,EAAE;AAAA,IAC3E,YAAY,CAAC,cAAc,QAAQ,mBAAW,KAAK,CAAC,IAAI,WAAW,WAAW,YAAY,UAAU;AAAA,IACpG,OAAO,CAAC,OAAO;AAAA,IACf,OAAO,CAAC,OAAO;AAAA,IACf,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,QAAM,kBAAkB,eAAe,OAAO,uBAAuB,OAAO;AAC5E,SAAO;AAAA,IACL,GAAG;AAAA;AAAA,IAEH,GAAG;AAAA,EACL;AACF;AACA,IAAM,aAAa,eAAO,QAAQ;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,QAAQ,OAAO,OAAO,mBAAW,WAAW,IAAI,CAAC,EAAE,GAAG,OAAO,OAAO,mBAAW,WAAW,IAAI,CAAC,EAAE,CAAC;AAAA,EACpI;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,OAAO,KAAK,KAAK;AAAA,EACjB,QAAQ,KAAK,KAAK;AAAA,EAClB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA,EAER,eAAe;AAAA;AAAA,EAEf,gBAAgB;AAAA,IACd,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG;AAAA,QAC7B,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,CAAC,MAAM,sBAAc,UAAU,EAAE,GAAG;AAAA,QAClC,SAAS;AAAA,QACT,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,UAC9B,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,mBAAmB,eAAO,oBAAY;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,YAAY;AAAA,MACzB,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG,OAAO;AAAA,IACxC,GAAG,WAAW,UAAU,aAAa,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,CAAC;AAAA,EACrF;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,eAAe,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,EACrJ,YAAY,MAAM,YAAY,OAAO,CAAC,QAAQ,WAAW,GAAG;AAAA,IAC1D,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,IAC9B,WAAW;AAAA,EACb;AAAA,EACA,CAAC,KAAK,sBAAc,QAAQ,EAAE,GAAG;AAAA,IAC/B,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,uBAAuB,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC5J;AAAA,EACA,CAAC,KAAK,sBAAc,OAAO,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,IACxD,SAAS;AAAA,EACX;AAAA,EACA,CAAC,KAAK,sBAAc,QAAQ,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,IACzD,SAAS,MAAM,OAAO,MAAM,KAAK,QAAQ,sBAAsB,GAAG,MAAM,QAAQ,SAAS,UAAU,OAAO,GAAG;AAAA,EAC/G;AAAA,EACA,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG;AAAA,IAC7B,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AACF,EAAE,GAAG,kBAAU,CAAC;AAAA,EACd;AACF,OAAO;AAAA,EACL,WAAW;AAAA,IACT,iBAAiB,MAAM,OAAO,MAAM,QAAQ,OAAO,QAAQ,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,OAAO,YAAY;AAAA;AAAA,IAE3H,wBAAwB;AAAA,MACtB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,UAAU,CAAC,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,OAAO,CAAC,CAAC,EAC3F,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,IACjB,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,QAC9B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,QAC5C,WAAW;AAAA,UACT,iBAAiB,MAAM,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE,OAAO,MAAM,QAAQ,OAAO,QAAQ,OAAO,YAAY;AAAA,UACzH,wBAAwB;AAAA,YACtB,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,QACA,CAAC,KAAK,sBAAc,QAAQ,EAAE,GAAG;AAAA,UAC/B,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,GAAG,KAAK,eAAe,IAAI,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,MAAM,QAAQ,KAAK,EAAE,MAAM,IAAI,IAAI,MAAM,OAAO,MAAM,QAAQ,KAAK,EAAE,MAAM,IAAI,CAAC;AAAA,QAC7M;AAAA,MACF;AAAA,MACA,CAAC,KAAK,sBAAc,OAAO,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,QACxD,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL,EAAE,CAAC;AACH,IAAM,cAAc,eAAO,QAAQ;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,cAAc,KAAK;AAAA,EACnB,QAAQ;AAAA,EACR,YAAY,MAAM,YAAY,OAAO,CAAC,WAAW,kBAAkB,GAAG;AAAA,IACpE,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,iBAAiB,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,eAAe,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK;AAAA,EAClK,SAAS,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,GAAG,MAAM,QAAQ,SAAS,UAAU,OAAO,GAAG;AACvG,EAAE,CAAC;AACH,IAAM,cAAc,eAAO,QAAQ;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,EAC1C,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,cAAc;AAChB,EAAE,CAAC;AACH,IAAM,SAA4B,kBAAW,SAASC,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAUD,mBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,WAAoB,oBAAAE,KAAK,WAAW;AAAA,IACxC,GAAG;AAAA,EACL,CAAC;AACD,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,KAAc,oBAAAD,KAAK,kBAAkB;AAAA,MAC7C,MAAM;AAAA,MACN;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,SAAS;AAAA,QACP,GAAG;AAAA,QACH,MAAM,QAAQ;AAAA,MAChB;AAAA,MACA,OAAO;AAAA,QACL,GAAI,MAAM,cAAc;AAAA,UACtB,MAAM,MAAM;AAAA,QACd;AAAA,QACA,GAAI,MAAM,SAAS;AAAA,UACjB,OAAO,MAAM;AAAA,QACf;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,GAAI,UAAU,cAAc;AAAA,UAC1B,MAAM,OAAO,UAAU,eAAe,aAAa,UAAU,WAAW,UAAU,IAAI,UAAU;AAAA,QAClG;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,QACA,GAAI,UAAU,SAAS;AAAA,UACrB,OAAO,OAAO,UAAU,UAAU,aAAa,UAAU,MAAM,UAAU,IAAI,UAAU;AAAA,QACzF;AAAA,MACF;AAAA,IACF,CAAC,OAAgB,oBAAAA,KAAK,WAAW;AAAA,MAC/B,GAAG;AAAA,IACL,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhF,SAAS,mBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,mBAAAA,QAAgD,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,aAAa,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhL,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzB,MAAM,mBAAAA,QAAU,MAAM,CAAC,OAAO,SAAS,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAI7C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,IAAI,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQV,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,MAAM,mBAAAA,QAAgD,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxH,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,MAAM,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,YAAY,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAClE,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,MAAM,mBAAAA,QAAU;AAAA,IAChB,YAAY,mBAAAA,QAAU;AAAA,IACtB,OAAO,mBAAAA,QAAU;AAAA,IACjB,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,OAAO,mBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,iBAAQ;", "names": ["React", "import_prop_types", "SwitchBase", "_jsxs", "_jsx", "PropTypes", "import_jsx_runtime", "useUtilityClasses", "Switch", "_jsx", "_jsxs", "PropTypes"]}