'use client';

import * as React from 'react';
import { Stack } from '@mui/material';
import { DataGridPremium, GridColDef, useGridApiRef } from '@mui/x-data-grid-premium';
import './jobs-emails-list.scss';
import { administrationApi } from '@/store/api/administration';
import { createRTKODataGridDataSource } from '@/store/api/odata-grid-data-source';

interface JobRunLogsListProps {
    readonly jobRunId: number;
};

export function JobRunLogsList({jobRunId}: JobRunLogsListProps): React.JSX.Element {
    const apiRef = useGridApiRef();

    const [dataSource, setDataSource] = React.useState(() => createRTKODataGridDataSource(
        administrationApi.endpoints.atApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint.initiate,
        () => {
            const current = apiRef.current;
            if (!current) return undefined;
            // Collect visible, non-action column fields
            const visible = current.getVisibleColumns?.() ?? [];
            return visible
                .map((c: any) => c.field)
                // Exclude action and internal utility columns like checkbox selection
                .filter((f: string) => f && f !== 'actions' && !f.startsWith('__'));
        },
        { jobRunId },
    ));

    const columns: GridColDef[] = [
        {
            field: 'id',
            headerName: 'Id',
            flex: 1,
            minWidth: 90,
            type: 'number',
        },
        {
            field: 'timestamp',
            headerName: 'Time Stamp',
            minWidth: 180,
            align: 'center',
            headerAlign: 'center',
            type: 'dateTime',
            valueGetter: (value) => value ? new Date(value) : null,
        },
        {
            field: 'messageId',
            headerName: 'Message Id',
            minWidth: 240,
            align: 'center',
            headerAlign: 'center',
            type:'string',
        },
        {
            field: 'messageTranslated',
            headerName: 'Message Translated',
            minWidth: 240,
            align: 'center',
            headerAlign: 'center',
            type:'string',
        },
        {
            field: 'messageParameters',
            headerName: 'Message Parameters',
            minWidth: 240,
            align: 'center',
            headerAlign: 'center',
            type:'string',
        },
    ];

	return (
        <Stack>
            <DataGridPremium
                apiRef={apiRef}
                dataSource={dataSource}
                columns={columns}
                pagination
                showToolbar={true}
                pageSizeOptions={[5, 10, 25]}
                initialState={{
                    pagination: { paginationModel: { pageSize: 10, page: 0 } },
                }}
                disableRowSelectionOnClick
                rowHeight={52}
                className="jobs-emails-list__data-grid"
            />
        </Stack>
	);
}