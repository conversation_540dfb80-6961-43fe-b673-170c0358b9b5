import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsCurrentUserGetBasicInfoEndpoint: build.query<
      AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiResponse,
      AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiArg
    >({
      query: () => ({ url: `/currentuser/basicinfo` }),
    }),
    atApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpoint:
      build.query<
        AtApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpointApiResponse,
        AtApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpointApiArg
      >({
        query: () => ({ url: `/demo/currentuserpermission` }),
      }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as currentUserGeneratedApi };
export type AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiResponse =
  /** status 200 Success */ CurrentUserBasicInfoResponse;
export type AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiArg = void;
export type AtApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpointApiResponse =
  /** status 200 Success */ CurrentUserPermissionDemoResponse;
export type AtApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpointApiArg =
  void;
export type CurrentUserBasicInfoResponse = {
  username?: string;
  firstName?: string;
  lastName?: string;
};
export type CurrentUserPermissionDemoResponse = {
  hasPermissionEnum?: boolean;
  hasPermissionSmartEnum?: boolean;
};
export const {
  useAtApiServiceEndpointsCurrentUserGetBasicInfoEndpointQuery,
  useAtApiServiceEndpointsCurrentUserPermissionDemoGetCurrentUserPermissionDemoEndpointQuery,
} = injectedRtkApi;
