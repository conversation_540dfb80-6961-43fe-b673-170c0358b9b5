import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsCurrentUserGetBasicInfoEndpoint: build.query<
      AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiResponse,
      AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/currentuser/basicinfo` }),
    }),
    atApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/demo/currentuserpermission` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as currentUserGeneratedApi };
export type AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsCurrentUserModelsCurrentUserBasicInfoResponse;
export type AtApiServiceEndpointsCurrentUserGetBasicInfoEndpointApiArg = void;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg =
  void;
export type AtApiServiceEndpointsCurrentUserModelsCurrentUserBasicInfoResponse =
  {
    username?: string;
    firstName?: string;
    lastName?: string;
  };
export type AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse = {
  hasPermissionEnum?: boolean;
  hasPermissionSmartEnum?: boolean;
};
export const {
  useAtApiServiceEndpointsCurrentUserGetBasicInfoEndpointQuery,
  useAtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointQuery,
} = injectedRtkApi;
