{"version": 3, "sources": ["../../@mui/material/esm/Link/Link.js", "../../@mui/material/esm/Link/linkClasses.js", "../../@mui/material/esm/Link/getTextDecoration.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport capitalize from \"../utils/capitalize.js\";\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Typography from \"../Typography/index.js\";\nimport linkClasses, { getLinkUtilityClass } from \"./linkClasses.js\";\nimport getTextDecoration from \"./getTextDecoration.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst v6Colors = {\n  primary: true,\n  secondary: true,\n  error: true,\n  info: true,\n  success: true,\n  warning: true,\n  textPrimary: true,\n  textSecondary: true,\n  textDisabled: true\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  return {\n    variants: [{\n      props: {\n        underline: 'none'\n      },\n      style: {\n        textDecoration: 'none'\n      }\n    }, {\n      props: {\n        underline: 'hover'\n      },\n      style: {\n        textDecoration: 'none',\n        '&:hover': {\n          textDecoration: 'underline'\n        }\n      }\n    }, {\n      props: {\n        underline: 'always'\n      },\n      style: {\n        textDecoration: 'underline',\n        '&:hover': {\n          textDecorationColor: 'inherit'\n        }\n      }\n    }, {\n      props: ({\n        underline,\n        ownerState\n      }) => underline === 'always' && ownerState.color !== 'inherit',\n      style: {\n        textDecorationColor: 'var(--Link-underlineColor)'\n      }\n    }, {\n      props: ({\n        underline,\n        ownerState\n      }) => underline === 'always' && ownerState.color === 'inherit',\n      style: theme.colorSpace ? {\n        textDecorationColor: theme.alpha('currentColor', 0.4)\n      } : null\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n      props: {\n        underline: 'always',\n        color\n      },\n      style: {\n        '--Link-underlineColor': theme.alpha((theme.vars || theme).palette[color].main, 0.4)\n      }\n    })), {\n      props: {\n        underline: 'always',\n        color: 'textPrimary'\n      },\n      style: {\n        '--Link-underlineColor': theme.alpha((theme.vars || theme).palette.text.primary, 0.4)\n      }\n    }, {\n      props: {\n        underline: 'always',\n        color: 'textSecondary'\n      },\n      style: {\n        '--Link-underlineColor': theme.alpha((theme.vars || theme).palette.text.secondary, 0.4)\n      }\n    }, {\n      props: {\n        underline: 'always',\n        color: 'textDisabled'\n      },\n      style: {\n        '--Link-underlineColor': (theme.vars || theme).palette.text.disabled\n      }\n    }, {\n      props: {\n        component: 'button'\n      },\n      style: {\n        position: 'relative',\n        WebkitTapHighlightColor: 'transparent',\n        backgroundColor: 'transparent',\n        // Reset default value\n        // We disable the focus ring for mouse, touch and keyboard users.\n        outline: 0,\n        border: 0,\n        margin: 0,\n        // Remove the margin in Safari\n        borderRadius: 0,\n        padding: 0,\n        // Remove the padding in Firefox\n        cursor: 'pointer',\n        userSelect: 'none',\n        verticalAlign: 'middle',\n        MozAppearance: 'none',\n        // Reset\n        WebkitAppearance: 'none',\n        // Reset\n        '&::-moz-focus-inner': {\n          borderStyle: 'none' // Remove Firefox dotted outline.\n        },\n        [`&.${linkClasses.focusVisible}`]: {\n          outline: 'auto'\n        }\n      }\n    }]\n  };\n}));\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const theme = useTheme();\n  const {\n    className,\n    color = 'primary',\n    component = 'a',\n    onBlur,\n    onFocus,\n    TypographyClasses,\n    underline = 'always',\n    variant = 'inherit',\n    sx,\n    ...other\n  } = props;\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    if (isFocusVisible(event.target)) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = {\n    ...props,\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, {\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: ref,\n    ownerState: ownerState,\n    variant: variant,\n    ...other,\n    sx: [...(v6Colors[color] === undefined ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])],\n    style: {\n      ...other.style,\n      ...(underline === 'always' && color !== 'inherit' && !v6Colors[color] && {\n        '--Link-underlineColor': getTextDecoration({\n          theme,\n          ownerState\n        })\n      })\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'success', 'error', 'info', 'warning', 'textPrimary', 'textSecondary', 'textDisabled']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](https://mui.com/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system/style';\nimport { alpha } from '@mui/system/colorManipulator';\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = ownerState.color;\n  if ('colorSpace' in theme && theme.colorSpace) {\n    const color = getPath(theme, `palette.${transformedColor}.main`) || getPath(theme, `palette.${transformedColor}`) || ownerState.color;\n    return theme.alpha(color, 0.4);\n  }\n\n  // check the `main` color first for a custom palette, then fallback to the color itself\n  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,oBAAoB,MAAM;AACxC,SAAO,qBAAqB,WAAW,IAAI;AAC7C;AACA,IAAM,cAAc,uBAAuB,WAAW,CAAC,QAAQ,iBAAiB,kBAAkB,mBAAmB,UAAU,cAAc,CAAC;AAC9I,IAAO,sBAAQ;;;ACJf,IAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,mBAAmB,WAAW;AACpC,MAAI,gBAAgB,SAAS,MAAM,YAAY;AAC7C,UAAMA,SAAQ,QAAQ,OAAO,WAAW,gBAAgB,OAAO,KAAK,QAAQ,OAAO,WAAW,gBAAgB,EAAE,KAAK,WAAW;AAChI,WAAO,MAAM,MAAMA,QAAO,GAAG;AAAA,EAC/B;AAGA,QAAM,QAAQ,QAAQ,OAAO,WAAW,gBAAgB,SAAS,KAAK,KAAK,QAAQ,OAAO,WAAW,gBAAgB,IAAI,KAAK,KAAK,WAAW;AAC9I,QAAM,eAAe,QAAQ,OAAO,WAAW,gBAAgB,cAAc,KAAK,QAAQ,OAAO,WAAW,gBAAgB,SAAS;AACrI,MAAI,UAAU,SAAS,cAAc;AACnC,WAAO,QAAQ,YAAY;AAAA,EAC7B;AACA,SAAO,MAAM,OAAO,GAAG;AACzB;AACA,IAAO,4BAAQ;;;AFJf,yBAA4B;AAC5B,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAChB;AACA,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,mBAAW,SAAS,CAAC,IAAI,cAAc,YAAY,UAAU,gBAAgB,cAAc;AAAA,EACxH;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AACA,IAAM,WAAW,eAAO,oBAAY;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,YAAY,mBAAW,WAAW,SAAS,CAAC,EAAE,GAAG,WAAW,cAAc,YAAY,OAAO,MAAM;AAAA,EACjI;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,MAAM;AACJ,SAAO;AAAA,IACL,UAAU,CAAC;AAAA,MACT,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,gBAAgB;AAAA,MAClB;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,WAAW;AAAA,UACT,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,WAAW;AAAA,UACT,qBAAqB;AAAA,QACvB;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,QACA;AAAA,MACF,MAAM,cAAc,YAAY,WAAW,UAAU;AAAA,MACrD,OAAO;AAAA,QACL,qBAAqB;AAAA,MACvB;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,QACA;AAAA,MACF,MAAM,cAAc,YAAY,WAAW,UAAU;AAAA,MACrD,OAAO,MAAM,aAAa;AAAA,QACxB,qBAAqB,MAAM,MAAM,gBAAgB,GAAG;AAAA,MACtD,IAAI;AAAA,IACN,GAAG,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,MAC7F,OAAO;AAAA,QACL,WAAW;AAAA,QACX;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,yBAAyB,MAAM,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE,MAAM,GAAG;AAAA,MACrF;AAAA,IACF,EAAE,GAAG;AAAA,MACH,OAAO;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,yBAAyB,MAAM,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,SAAS,GAAG;AAAA,MACtF;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,yBAAyB,MAAM,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,WAAW,GAAG;AAAA,MACxF;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,MAC9D;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,QACV,yBAAyB;AAAA,QACzB,iBAAiB;AAAA;AAAA;AAAA,QAGjB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,QAER,cAAc;AAAA,QACd,SAAS;AAAA;AAAA,QAET,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,eAAe;AAAA;AAAA,QAEf,kBAAkB;AAAA;AAAA,QAElB,uBAAuB;AAAA,UACrB,aAAa;AAAA;AAAA,QACf;AAAA,QACA,CAAC,KAAK,oBAAY,YAAY,EAAE,GAAG;AAAA,UACjC,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC,CAAC;AACF,IAAM,OAA0B,iBAAW,SAASC,MAAK,SAAS,KAAK;AACrE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,QAAQ,SAAS;AACvB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,KAAK;AAC5D,QAAM,aAAa,WAAS;AAC1B,QAAI,CAAC,eAAe,MAAM,MAAM,GAAG;AACjC,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,cAAc,WAAS;AAC3B,QAAI,eAAe,MAAM,MAAM,GAAG;AAChC,sBAAgB,IAAI;AAAA,IACtB;AACA,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,UAAU;AAAA,IACjC;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,SAAS;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,IAAI,CAAC,GAAI,SAAS,KAAK,MAAM,SAAY,CAAC;AAAA,MACxC;AAAA,IACF,CAAC,IAAI,CAAC,GAAI,GAAI,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,EAAE,CAAE;AAAA,IAC5C,OAAO;AAAA,MACL,GAAG,MAAM;AAAA,MACT,GAAI,cAAc,YAAY,UAAU,aAAa,CAAC,SAAS,KAAK,KAAK;AAAA,QACvE,yBAAyB,0BAAkB;AAAA,UACzC;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,aAAa,WAAW,SAAS,QAAQ,WAAW,eAAe,iBAAiB,cAAc,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrN,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW,kBAAAA,QAAU,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,SAAS,UAAU,WAAW,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,WAAW,YAAY,aAAa,WAAW,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AACtO,IAAI;AACJ,IAAO,eAAQ;", "names": ["color", "Link", "_jsx", "PropTypes"]}