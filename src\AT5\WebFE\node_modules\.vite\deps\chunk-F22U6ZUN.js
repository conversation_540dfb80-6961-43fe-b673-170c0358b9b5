import {
  CacheChunkManager,
  DATA_GRID_DEFAULT_SLOTS_COMPONENTS,
  DataSourceRowsUpdateStrategy,
  GRID_COLUMN_MENU_SLOTS,
  GRID_COLUMN_MENU_SLOT_PROPS,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GRID_ROOT_GROUP_ID,
  GRID_STRING_COL_DEF,
  GRID_TREE_DATA_GROUPING_FIELD,
  GridBaseColumnHeaders,
  GridColumnHeaderRow,
  GridFilterInputBoolean,
  GridFilterInputDate,
  GridFilterInputSingleSelect,
  GridFilterInputValue,
  GridGenericColumnMenu,
  GridGetRowsError,
  GridMenu,
  GridPinnedColumnPosition,
  GridSignature,
  GridStrategyGroup,
  PinnedColumnPosition,
  attachPinnedStyle,
  buildRootGroup,
  createRootSelector,
  createSelector,
  createSelectorMemoized,
  createSvgIcon,
  defaultGridFilterLookup,
  fastMemo,
  forwardRef,
  getDataGridUtilityClass,
  getGridFilter,
  getRowIdFromRowModel,
  getTreeNodeDescendants,
  getVisibleRows,
  gridClasses,
  gridColumnFieldsSelector,
  gridColumnLookupSelector,
  gridColumnPositionsSelector,
  gridColumnVisibilityModelSelector,
  gridColumnsTotalWidthSelector,
  gridDataRowIdsSelector,
  gridDimensionsSelector,
  gridEditRowsStateSelector,
  gridExistingPinnedColumnSelector,
  gridExpandedSortedRowIndexLookupSelector,
  gridExpandedSortedRowTreeLevelPositionLookupSelector,
  gridFilterModelSelector,
  gridFilterableColumnLookupSelector,
  gridFilteredChildrenCountLookupSelector,
  gridFilteredDescendantCountLookupSelector,
  gridFilteredSortedRowIdsSelector,
  gridFilteredTopLevelRowCountSelector,
  gridFocusColumnHeaderFilterSelector,
  gridGetRowsParamsSelector,
  gridHasFillerSelector,
  gridHeaderFilterHeightSelector,
  gridHeaderFilteringEditFieldSelector,
  gridHeaderFilteringMenuSelector,
  gridHorizontalScrollbarHeightSelector,
  gridPaginationModelSelector,
  gridPinnedColumnsSelector,
  gridPinnedRowsSelector,
  gridRenderContextSelector,
  gridRowGroupsToFetchSelector,
  gridRowIdSelector,
  gridRowMaximumTreeDepthSelector,
  gridRowNodeSelector,
  gridRowSelector,
  gridRowTreeSelector,
  gridSortModelSelector,
  gridTabIndexColumnHeaderFilterSelector,
  gridVerticalScrollbarWidthSelector,
  gridVisibleColumnDefinitionsSelector,
  gridVisibleColumnFieldsSelector,
  gridVisiblePinnedColumnDefinitionsSelector,
  isDeepEqual,
  isEventTargetInPortal,
  isNavigationKey,
  isNumber,
  passFilterLogic,
  propValidatorsDataGrid,
  runIf,
  shouldCellShowLeftBorder,
  shouldCellShowRightBorder,
  throttle,
  useEnhancedEffect_default,
  useFirstRender,
  useGridApiContext,
  useGridApiMethod,
  useGridAriaAttributes,
  useGridColumnHeaders,
  useGridDataSourceBase,
  useGridEvent,
  useGridEventPriority,
  useGridLogger,
  useGridPrivateApiContext,
  useGridRegisterPipeProcessor,
  useGridRegisterStrategyProcessor,
  useGridRootProps,
  useGridRowAriaAttributes,
  useGridSelector,
  useGridVisibleRows,
  useTimeout,
  vars,
  warnOnce
} from "./chunk-MC3F52YZ.js";
import {
  styled_default as styled_default2
} from "./chunk-K3WPA3ZI.js";
import {
  useRtl
} from "./chunk-TWYH2CSE.js";
import {
  inputBaseClasses_default
} from "./chunk-5IEXKNXB.js";
import {
  Box_default
} from "./chunk-2GLHLTFV.js";
import {
  CircularProgress_default
} from "./chunk-OKOP3THR.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-BFL632LT.js";
import {
  styled_default
} from "./chunk-3OAPAV2J.js";
import {
  clsx_default,
  require_prop_types
} from "./chunk-6ZYRDDF6.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __publicField,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-data-grid/esm/hooks/features/headerFiltering/useGridHeaderFiltering.js
var React = __toESM(require_react(), 1);
var headerFilteringStateInitializer = (state, props) => _extends({}, state, {
  headerFiltering: {
    enabled: props.headerFilters ?? false,
    editing: null,
    menuOpen: null
  }
});
var useGridHeaderFiltering = (apiRef, props) => {
  const logger = useGridLogger(apiRef, "useGridHeaderFiltering");
  const setHeaderFilterState = React.useCallback((headerFilterState) => {
    apiRef.current.setState((state) => {
      if (props.signature === "DataGrid") {
        return state;
      }
      return _extends({}, state, {
        headerFiltering: {
          enabled: props.headerFilters ?? false,
          editing: headerFilterState.editing ?? null,
          menuOpen: headerFilterState.menuOpen ?? null
        }
      });
    });
  }, [apiRef, props.signature, props.headerFilters]);
  const startHeaderFilterEditMode = React.useCallback((field) => {
    logger.debug(`Starting edit mode on header filter for field: ${field}`);
    apiRef.current.setHeaderFilterState({
      editing: field
    });
  }, [apiRef, logger]);
  const stopHeaderFilterEditMode = React.useCallback(() => {
    logger.debug(`Stopping edit mode on header filter`);
    apiRef.current.setHeaderFilterState({
      editing: null
    });
  }, [apiRef, logger]);
  const showHeaderFilterMenu = React.useCallback((field) => {
    logger.debug(`Opening header filter menu for field: ${field}`);
    apiRef.current.setHeaderFilterState({
      menuOpen: field
    });
  }, [apiRef, logger]);
  const hideHeaderFilterMenu = React.useCallback(() => {
    logger.debug(`Hiding header filter menu for active field`);
    let fieldToFocus = apiRef.current.state.headerFiltering.menuOpen;
    if (fieldToFocus) {
      const columnLookup = gridColumnLookupSelector(apiRef);
      const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);
      const orderedFields = gridColumnFieldsSelector(apiRef);
      if (!columnLookup[fieldToFocus]) {
        fieldToFocus = orderedFields[0];
      }
      if (columnVisibilityModel[fieldToFocus] === false) {
        const visibleOrderedFields = orderedFields.filter((field) => {
          if (field === fieldToFocus) {
            return true;
          }
          return columnVisibilityModel[field] !== false;
        });
        const fieldIndex = visibleOrderedFields.indexOf(fieldToFocus);
        fieldToFocus = visibleOrderedFields[fieldIndex + 1] || visibleOrderedFields[fieldIndex - 1];
      }
      apiRef.current.setHeaderFilterState({
        menuOpen: null
      });
      apiRef.current.setColumnHeaderFilterFocus(fieldToFocus);
    }
  }, [apiRef, logger]);
  const headerFilterPrivateApi = {
    setHeaderFilterState
  };
  const headerFilterApi = {
    startHeaderFilterEditMode,
    stopHeaderFilterEditMode,
    showHeaderFilterMenu,
    hideHeaderFilterMenu
  };
  useGridApiMethod(apiRef, headerFilterApi, "public");
  useGridApiMethod(apiRef, headerFilterPrivateApi, "private");
  const isFirstRender = React.useRef(true);
  React.useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
    } else {
      apiRef.current.setHeaderFilterState({
        enabled: props.headerFilters ?? false
      });
    }
  }, [apiRef, props.headerFilters]);
};

// node_modules/@mui/x-data-grid/esm/internals/demo/TailwindDemoContainer.js
var React2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function TailwindDemoContainer(props) {
  const {
    children,
    documentBody
  } = props;
  const [isLoaded, setIsLoaded] = React2.useState(false);
  React2.useEffect(() => {
    const body = documentBody ?? document.body;
    const script = document.createElement("script");
    script.src = "https://unpkg.com/@tailwindcss/browser@4";
    let mounted = true;
    const cleanup = () => {
      var _a;
      mounted = false;
      script.remove();
      const head = (_a = body == null ? void 0 : body.ownerDocument) == null ? void 0 : _a.head;
      if (!head) {
        return;
      }
      const styles = head.querySelectorAll("style:not([data-emotion])");
      styles.forEach((style) => {
        var _a2;
        const styleText = (_a2 = style.textContent) == null ? void 0 : _a2.substring(0, 100);
        const isTailwindStylesheet = styleText == null ? void 0 : styleText.includes("tailwind");
        if (isTailwindStylesheet) {
          style.remove();
        }
      });
    };
    script.onload = () => {
      if (!mounted) {
        cleanup();
        return;
      }
      setIsLoaded(true);
    };
    body.appendChild(script);
    return cleanup;
  }, [documentBody]);
  return isLoaded ? children : (0, import_jsx_runtime.jsx)(Box_default, {
    sx: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%"
    },
    children: (0, import_jsx_runtime.jsx)(CircularProgress_default, {})
  });
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridColumnHeaders.js
var React4 = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnHeaders/useGridColumnHeaders.js
var React3 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/composeClasses/composeClasses.js
function composeClasses(slots, getUtilityClass, classes = void 0) {
  const output = {};
  for (const slotName in slots) {
    const slot = slots[slotName];
    let buffer = "";
    let start = true;
    for (let i = 0; i < slot.length; i += 1) {
      const value = slot[i];
      if (value) {
        buffer += (start === true ? "" : " ") + getUtilityClass(value);
        start = false;
        if (classes && classes[value]) {
          buffer += " " + classes[value];
        }
      }
    }
    output[slotName] = buffer;
  }
  return output;
}

// node_modules/@mui/x-data-grid-pro/esm/hooks/utils/useGridRootProps.js
var useGridRootProps2 = useGridRootProps;

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnHeaders/useGridColumnHeaders.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var _excluded = ["getColumnsToRender", "getPinnedCellOffset", "renderContext", "leftRenderContext", "rightRenderContext", "pinnedColumns", "visibleColumns", "columnPositions"];
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  return React3.useMemo(() => {
    const slots = {
      headerFilterRow: ["headerFilterRow"]
    };
    return composeClasses(slots, getDataGridUtilityClass, classes);
  }, [classes]);
};
var useGridColumnHeadersPro = (props) => {
  const apiRef = useGridPrivateApiContext();
  const {
    headerGroupingMaxDepth,
    hasOtherElementInTabSequence
  } = props;
  const columnHeaderFilterTabIndexState = useGridSelector(apiRef, gridTabIndexColumnHeaderFilterSelector);
  const _useGridColumnHeaders = useGridColumnHeaders(_extends({}, props, {
    hasOtherElementInTabSequence: hasOtherElementInTabSequence || columnHeaderFilterTabIndexState !== null
  })), {
    getColumnsToRender,
    getPinnedCellOffset,
    renderContext,
    leftRenderContext,
    rightRenderContext,
    pinnedColumns,
    visibleColumns,
    columnPositions
  } = _useGridColumnHeaders, otherProps = _objectWithoutPropertiesLoose(_useGridColumnHeaders, _excluded);
  const headerFiltersRef = React3.useRef(null);
  apiRef.current.register("private", {
    headerFiltersElementRef: headerFiltersRef
  });
  const headerFilterMenuRef = React3.useRef(null);
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses(rootProps);
  const disableHeaderFiltering = !rootProps.headerFilters;
  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);
  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);
  const gridHasFiller = useGridSelector(apiRef, gridHasFillerSelector);
  const headerFilterHeight = useGridSelector(apiRef, gridHeaderFilterHeightSelector);
  const scrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);
  const columnHeaderFilterFocus = useGridSelector(apiRef, gridFocusColumnHeaderFilterSelector);
  const filterItemsCache = React3.useRef(/* @__PURE__ */ Object.create(null)).current;
  const getFilterItem = React3.useCallback(
    (colDef) => {
      const filterModelItem = filterModel == null ? void 0 : filterModel.items.find((it) => it.field === colDef.field && it.operator !== "isAnyOf");
      if (filterModelItem != null) {
        return filterModelItem;
      }
      const defaultCachedItem = filterItemsCache[colDef.field];
      if (defaultCachedItem != null) {
        return defaultCachedItem;
      }
      const defaultItem = getGridFilter(colDef);
      filterItemsCache[colDef.field] = defaultItem;
      return defaultItem;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filterModel]
  );
  const getColumnFilters = (params) => {
    var _a;
    const {
      renderedColumns,
      firstColumnToRender
    } = getColumnsToRender(params);
    const filters = [];
    for (let i = 0; i < renderedColumns.length; i += 1) {
      const colDef = renderedColumns[i];
      const columnIndex = firstColumnToRender + i;
      const hasFocus = (columnHeaderFilterFocus == null ? void 0 : columnHeaderFilterFocus.field) === colDef.field;
      const isFirstColumn = columnIndex === 0;
      const tabIndexField = columnHeaderFilterTabIndexState == null ? void 0 : columnHeaderFilterTabIndexState.field;
      const tabIndex = tabIndexField === colDef.field || isFirstColumn && !props.hasOtherElementInTabSequence ? 0 : -1;
      const headerClassName = typeof colDef.headerClassName === "function" ? colDef.headerClassName({
        field: colDef.field,
        colDef
      }) : colDef.headerClassName;
      const item = getFilterItem(colDef);
      const pinnedPosition = params == null ? void 0 : params.position;
      const pinnedOffset = getPinnedCellOffset(pinnedPosition, colDef.computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth);
      const indexInSection = i;
      const sectionLength = renderedColumns.length;
      const showLeftBorder = shouldCellShowLeftBorder(pinnedPosition, indexInSection);
      const showRightBorder = shouldCellShowRightBorder(pinnedPosition, indexInSection, sectionLength, rootProps.showColumnVerticalBorder, gridHasFiller);
      filters.push((0, import_jsx_runtime2.jsx)(rootProps.slots.headerFilterCell, _extends({
        colIndex: columnIndex,
        height: headerFilterHeight,
        width: colDef.computedWidth,
        colDef,
        hasFocus,
        tabIndex,
        headerFilterMenuRef,
        headerClassName,
        "data-field": colDef.field,
        item,
        pinnedPosition,
        pinnedOffset,
        showLeftBorder,
        showRightBorder
      }, (_a = rootProps.slotProps) == null ? void 0 : _a.headerFilterCell), `${colDef.field}-filter`));
    }
    return otherProps.getFillers(params, filters, 0, true);
  };
  const getColumnFiltersRow = () => {
    if (disableHeaderFiltering) {
      return null;
    }
    return (0, import_jsx_runtime2.jsxs)(GridColumnHeaderRow, {
      ref: headerFiltersRef,
      className: classes.headerFilterRow,
      role: "row",
      "aria-rowindex": headerGroupingMaxDepth + 2,
      ownerState: rootProps,
      children: [leftRenderContext && getColumnFilters({
        position: PinnedColumnPosition.LEFT,
        renderContext: leftRenderContext,
        maxLastColumn: leftRenderContext.lastColumnIndex
      }), getColumnFilters({
        renderContext,
        maxLastColumn: visibleColumns.length - pinnedColumns.right.length
      }), rightRenderContext && getColumnFilters({
        position: PinnedColumnPosition.RIGHT,
        renderContext: rightRenderContext,
        maxLastColumn: rightRenderContext.lastColumnIndex
      })]
    });
  };
  if (true) getColumnFiltersRow.displayName = "getColumnFiltersRow";
  return _extends({}, otherProps, {
    getColumnFiltersRow
  });
};

// node_modules/@mui/x-data-grid-pro/esm/components/GridColumnHeaders.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var _excluded2 = ["style", "className", "visibleColumns", "sortColumnLookup", "filterColumnLookup", "columnHeaderTabIndexState", "columnGroupHeaderTabIndexState", "columnHeaderFocus", "columnGroupHeaderFocus", "headerGroupingMaxDepth", "columnMenuState", "columnVisibility", "columnGroupsHeaderStructure", "hasOtherElementInTabSequence"];
var Filler = styled_default("div")({
  flex: 1,
  backgroundColor: vars.header.background.base
});
var GridColumnHeaders = forwardRef(function GridColumnHeaders2(props, ref) {
  const {
    className,
    visibleColumns,
    sortColumnLookup,
    filterColumnLookup,
    columnHeaderTabIndexState,
    columnGroupHeaderTabIndexState,
    columnHeaderFocus,
    columnGroupHeaderFocus,
    headerGroupingMaxDepth,
    columnMenuState,
    columnVisibility,
    columnGroupsHeaderStructure,
    hasOtherElementInTabSequence
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const {
    getInnerProps,
    getColumnHeadersRow,
    getColumnFiltersRow,
    getColumnGroupHeadersRows
  } = useGridColumnHeadersPro({
    visibleColumns,
    sortColumnLookup,
    filterColumnLookup,
    columnHeaderTabIndexState,
    hasOtherElementInTabSequence,
    columnGroupHeaderTabIndexState,
    columnHeaderFocus,
    columnGroupHeaderFocus,
    headerGroupingMaxDepth,
    columnMenuState,
    columnVisibility,
    columnGroupsHeaderStructure
  });
  return (0, import_jsx_runtime3.jsxs)(GridBaseColumnHeaders, _extends({
    className
  }, other, getInnerProps(), {
    ref,
    children: [getColumnGroupHeadersRows(), getColumnHeadersRow(), getColumnFiltersRow(), (0, import_jsx_runtime3.jsx)(Filler, {})]
  }));
});
if (true) GridColumnHeaders.displayName = "GridColumnHeaders";
true ? GridColumnHeaders.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  columnGroupHeaderFocus: import_prop_types.default.shape({
    depth: import_prop_types.default.number.isRequired,
    field: import_prop_types.default.string.isRequired
  }),
  columnGroupHeaderTabIndexState: import_prop_types.default.shape({
    depth: import_prop_types.default.number.isRequired,
    field: import_prop_types.default.string.isRequired
  }),
  columnGroupsHeaderStructure: import_prop_types.default.arrayOf(import_prop_types.default.arrayOf(import_prop_types.default.shape({
    columnFields: import_prop_types.default.arrayOf(import_prop_types.default.string).isRequired,
    groupId: import_prop_types.default.string
  }))).isRequired,
  columnHeaderFocus: import_prop_types.default.shape({
    field: import_prop_types.default.string.isRequired
  }),
  columnHeaderTabIndexState: import_prop_types.default.shape({
    field: import_prop_types.default.string.isRequired
  }),
  columnMenuState: import_prop_types.default.shape({
    field: import_prop_types.default.string,
    open: import_prop_types.default.bool.isRequired
  }).isRequired,
  columnVisibility: import_prop_types.default.object.isRequired,
  filterColumnLookup: import_prop_types.default.object.isRequired,
  hasOtherElementInTabSequence: import_prop_types.default.bool.isRequired,
  headerGroupingMaxDepth: import_prop_types.default.number.isRequired,
  sortColumnLookup: import_prop_types.default.object.isRequired,
  visibleColumns: import_prop_types.default.arrayOf(import_prop_types.default.object).isRequired
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/components/GridProColumnMenu.js
var React6 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/components/GridColumnMenuPinningItem.js
var React5 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/utils/useGridApiContext.js
var useGridApiContext2 = useGridApiContext;

// node_modules/@mui/x-data-grid-pro/esm/components/GridColumnMenuPinningItem.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
function GridColumnMenuPinningItem(props) {
  const {
    colDef,
    onClick
  } = props;
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps2();
  const isRtl = useRtl();
  const pinColumn = React5.useCallback((side2) => (event) => {
    apiRef.current.pinColumn(colDef.field, side2);
    onClick(event);
  }, [apiRef, colDef.field, onClick]);
  const unpinColumn = (event) => {
    apiRef.current.unpinColumn(colDef.field);
    onClick(event);
  };
  const pinToLeftMenuItem = (0, import_jsx_runtime4.jsx)(rootProps.slots.baseMenuItem, {
    onClick: pinColumn(GridPinnedColumnPosition.LEFT),
    iconStart: (0, import_jsx_runtime4.jsx)(rootProps.slots.columnMenuPinLeftIcon, {
      fontSize: "small"
    }),
    children: apiRef.current.getLocaleText("pinToLeft")
  });
  const pinToRightMenuItem = (0, import_jsx_runtime4.jsx)(rootProps.slots.baseMenuItem, {
    onClick: pinColumn(GridPinnedColumnPosition.RIGHT),
    iconStart: (0, import_jsx_runtime4.jsx)(rootProps.slots.columnMenuPinRightIcon, {
      fontSize: "small"
    }),
    children: apiRef.current.getLocaleText("pinToRight")
  });
  if (!colDef) {
    return null;
  }
  const side = apiRef.current.isColumnPinned(colDef.field);
  if (side) {
    const otherSide = side === GridPinnedColumnPosition.RIGHT ? GridPinnedColumnPosition.LEFT : GridPinnedColumnPosition.RIGHT;
    const label = otherSide === GridPinnedColumnPosition.RIGHT ? "pinToRight" : "pinToLeft";
    const Icon = side === GridPinnedColumnPosition.RIGHT ? rootProps.slots.columnMenuPinLeftIcon : rootProps.slots.columnMenuPinRightIcon;
    return (0, import_jsx_runtime4.jsxs)(React5.Fragment, {
      children: [(0, import_jsx_runtime4.jsx)(rootProps.slots.baseMenuItem, {
        onClick: pinColumn(otherSide),
        iconStart: (0, import_jsx_runtime4.jsx)(Icon, {
          fontSize: "small"
        }),
        children: apiRef.current.getLocaleText(label)
      }), (0, import_jsx_runtime4.jsx)(rootProps.slots.baseMenuItem, {
        onClick: unpinColumn,
        iconStart: "",
        children: apiRef.current.getLocaleText("unpin")
      })]
    });
  }
  if (isRtl) {
    return (0, import_jsx_runtime4.jsxs)(React5.Fragment, {
      children: [pinToRightMenuItem, pinToLeftMenuItem]
    });
  }
  return (0, import_jsx_runtime4.jsxs)(React5.Fragment, {
    children: [pinToLeftMenuItem, pinToRightMenuItem]
  });
}
true ? GridColumnMenuPinningItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  colDef: import_prop_types2.default.object.isRequired,
  onClick: import_prop_types2.default.func.isRequired
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/components/GridProColumnMenu.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var GRID_COLUMN_MENU_SLOTS_PRO = _extends({}, GRID_COLUMN_MENU_SLOTS, {
  columnMenuPinningItem: GridColumnMenuPinningItem
});
var GRID_COLUMN_MENU_SLOT_PROPS_PRO = _extends({}, GRID_COLUMN_MENU_SLOT_PROPS, {
  columnMenuPinningItem: {
    displayOrder: 15
  }
});
var GridProColumnMenu = forwardRef(function GridProColumnMenu2(props, ref) {
  return (0, import_jsx_runtime5.jsx)(GridGenericColumnMenu, _extends({}, props, {
    defaultSlots: GRID_COLUMN_MENU_SLOTS_PRO,
    defaultSlotProps: GRID_COLUMN_MENU_SLOT_PROPS_PRO,
    ref
  }));
});
if (true) GridProColumnMenu.displayName = "GridProColumnMenu";

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenu.js
var React7 = __toESM(require_react(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/capitalize/capitalize.js
function capitalize(string) {
  if (typeof string !== "string") {
    throw new Error(true ? "MUI: `capitalize(string)` expects a string argument." : formatMuiErrorMessage(7));
  }
  return string.charAt(0).toUpperCase() + string.slice(1);
}

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/HTMLElementType/HTMLElementType.js
function HTMLElementType(props, propName, componentName, location, propFullName) {
  if (false) {
    return null;
  }
  const propValue = props[propName];
  const safePropName = propFullName || propName;
  if (propValue == null) {
    return null;
  }
  if (propValue && propValue.nodeType !== 1) {
    return new Error(`Invalid ${location} \`${safePropName}\` supplied to \`${componentName}\`. Expected an HTMLElement.`);
  }
  return null;
}

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenu.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
function GridHeaderFilterMenu({
  open,
  field,
  target,
  applyFilterChanges,
  operators,
  item,
  id,
  labelledBy,
  showClearItem,
  clearFilterItem
}) {
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps();
  const hideMenu = React7.useCallback(() => {
    apiRef.current.hideHeaderFilterMenu();
  }, [apiRef]);
  if (!target) {
    return null;
  }
  return (0, import_jsx_runtime6.jsx)(GridMenu, {
    position: "bottom-end",
    open,
    target,
    onClose: hideMenu,
    children: (0, import_jsx_runtime6.jsxs)(rootProps.slots.baseMenuList, {
      "aria-labelledby": labelledBy,
      id,
      children: [showClearItem && [(0, import_jsx_runtime6.jsx)(rootProps.slots.baseMenuItem, {
        iconStart: (0, import_jsx_runtime6.jsx)(rootProps.slots.columnMenuClearIcon, {
          fontSize: "small"
        }),
        onClick: () => {
          clearFilterItem();
          hideMenu();
        },
        children: apiRef.current.getLocaleText("headerFilterClear")
      }, "filter-menu-clear-filter"), (0, import_jsx_runtime6.jsx)(rootProps.slots.baseDivider, {}, "filter-menu-divider")], operators.map((op) => {
        const selected = op.value === item.operator;
        const label = (op == null ? void 0 : op.headerLabel) ?? apiRef.current.getLocaleText(`headerFilterOperator${capitalize(op.value)}`);
        return (0, import_jsx_runtime6.jsx)(rootProps.slots.baseMenuItem, {
          iconStart: selected ? (0, import_jsx_runtime6.jsx)(rootProps.slots.menuItemCheckIcon, {
            fontSize: "small"
          }) : (0, import_jsx_runtime6.jsx)("span", {}),
          onClick: () => {
            applyFilterChanges(_extends({}, item, {
              operator: op.value
            }));
            hideMenu();
          },
          autoFocus: selected ? open : false,
          children: label
        }, `${field}-${op.value}`);
      })]
    })
  });
}
true ? GridHeaderFilterMenu.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  applyFilterChanges: import_prop_types3.default.func.isRequired,
  clearFilterItem: import_prop_types3.default.func.isRequired,
  field: import_prop_types3.default.string.isRequired,
  id: import_prop_types3.default.string,
  item: import_prop_types3.default.shape({
    field: import_prop_types3.default.string.isRequired,
    id: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]),
    operator: import_prop_types3.default.string.isRequired,
    value: import_prop_types3.default.any
  }).isRequired,
  labelledBy: import_prop_types3.default.string,
  open: import_prop_types3.default.bool.isRequired,
  operators: import_prop_types3.default.arrayOf(import_prop_types3.default.shape({
    getApplyFilterFn: import_prop_types3.default.func.isRequired,
    getValueAsString: import_prop_types3.default.func,
    headerLabel: import_prop_types3.default.string,
    InputComponent: import_prop_types3.default.elementType,
    InputComponentProps: import_prop_types3.default.shape({
      apiRef: import_prop_types3.default.shape({
        current: import_prop_types3.default.object.isRequired
      }),
      applyValue: import_prop_types3.default.func,
      className: import_prop_types3.default.string,
      clearButton: import_prop_types3.default.node,
      disabled: import_prop_types3.default.bool,
      focusElementRef: import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.shape({
        current: import_prop_types3.default.any.isRequired
      })]),
      headerFilterMenu: import_prop_types3.default.node,
      inputRef: import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.shape({
        current: (props, propName) => {
          if (props[propName] == null) {
            return null;
          }
          if (typeof props[propName] !== "object" || props[propName].nodeType !== 1) {
            return new Error(`Expected prop '${propName}' to be of type Element`);
          }
          return null;
        }
      })]),
      isFilterActive: import_prop_types3.default.bool,
      item: import_prop_types3.default.shape({
        field: import_prop_types3.default.string.isRequired,
        id: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]),
        operator: import_prop_types3.default.string.isRequired,
        value: import_prop_types3.default.any
      }),
      onBlur: import_prop_types3.default.func,
      onFocus: import_prop_types3.default.func,
      slotProps: import_prop_types3.default.object,
      tabIndex: import_prop_types3.default.number
    }),
    label: import_prop_types3.default.string,
    requiresFilterValue: import_prop_types3.default.bool,
    value: import_prop_types3.default.string.isRequired
  })).isRequired,
  showClearItem: import_prop_types3.default.bool.isRequired,
  target: HTMLElementType
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterCell.js
var React12 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useForkRef/useForkRef.js
var React8 = __toESM(require_react(), 1);
function useForkRef(...refs) {
  const cleanupRef = React8.useRef(void 0);
  const refEffect = React8.useCallback((instance) => {
    const cleanups = refs.map((ref) => {
      if (ref == null) {
        return null;
      }
      if (typeof ref === "function") {
        const refCallback = ref;
        const refCleanup = refCallback(instance);
        return typeof refCleanup === "function" ? refCleanup : () => {
          refCallback(null);
        };
      }
      ref.current = instance;
      return () => {
        ref.current = null;
      };
    });
    return () => {
      cleanups.forEach((refCleanup) => refCleanup == null ? void 0 : refCleanup());
    };
  }, refs);
  return React8.useMemo(() => {
    if (refs.every((ref) => ref == null)) {
      return null;
    }
    return (value) => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = void 0;
      }
      if (value != null) {
        cleanupRef.current = refEffect(value);
      }
    };
  }, refs);
}

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenuContainer.js
var React10 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/refType/refType.js
var import_prop_types4 = __toESM(require_prop_types(), 1);
var refType = import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object]);
var refType_default = refType;

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useId/useId.js
var React9 = __toESM(require_react(), 1);
var globalId = 0;
function useGlobalId(idOverride) {
  const [defaultId, setDefaultId] = React9.useState(idOverride);
  const id = idOverride || defaultId;
  React9.useEffect(() => {
    if (defaultId == null) {
      globalId += 1;
      setDefaultId(`mui-${globalId}`);
    }
  }, [defaultId]);
  return id;
}
var safeReact = {
  ...React9
};
var maybeReactUseId = safeReact.useId;
function useId(idOverride) {
  if (maybeReactUseId !== void 0) {
    const reactId = maybeReactUseId();
    return idOverride ?? reactId;
  }
  return useGlobalId(idOverride);
}

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterMenuContainer.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var _excluded3 = ["operators", "item", "field", "buttonRef", "headerFilterMenuRef", "disabled", "showClearItem", "clearFilterItem"];
function GridHeaderFilterMenuContainer(props) {
  var _a;
  const {
    operators,
    item,
    field,
    buttonRef,
    headerFilterMenuRef,
    disabled = false,
    showClearItem,
    clearFilterItem
  } = props, others = _objectWithoutPropertiesLoose(props, _excluded3);
  const buttonId = useId();
  const menuId = useId();
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext();
  const menuOpenField = useGridSelector(apiRef, gridHeaderFilteringMenuSelector);
  const open = Boolean(menuOpenField === field && headerFilterMenuRef.current);
  const handleClick = (event) => {
    headerFilterMenuRef.current = event.currentTarget;
    apiRef.current.showHeaderFilterMenu(field);
  };
  if (!rootProps.slots.headerFilterMenu) {
    return null;
  }
  const label = apiRef.current.getLocaleText("filterPanelOperator");
  const labelString = label ? String(label) : void 0;
  return (0, import_jsx_runtime7.jsxs)(React10.Fragment, {
    children: [(0, import_jsx_runtime7.jsx)(rootProps.slots.baseIconButton, _extends({
      id: buttonId,
      ref: buttonRef,
      "aria-label": labelString,
      title: labelString,
      "aria-controls": menuId,
      "aria-expanded": open ? "true" : void 0,
      "aria-haspopup": "true",
      tabIndex: -1,
      size: "small",
      onClick: handleClick,
      disabled
    }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
      children: (0, import_jsx_runtime7.jsx)(rootProps.slots.baseBadge, {
        color: "primary",
        variant: "dot",
        badgeContent: showClearItem ? 1 : 0,
        children: (0, import_jsx_runtime7.jsx)(rootProps.slots.openFilterButtonIcon, {
          fontSize: "inherit"
        })
      })
    })), (0, import_jsx_runtime7.jsx)(rootProps.slots.headerFilterMenu, _extends({
      field,
      open,
      item,
      target: headerFilterMenuRef.current,
      operators,
      labelledBy: buttonId,
      id: menuId,
      clearFilterItem,
      showClearItem
    }, others))]
  });
}
true ? GridHeaderFilterMenuContainer.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  applyFilterChanges: import_prop_types5.default.func.isRequired,
  buttonRef: refType_default,
  clearFilterItem: import_prop_types5.default.func,
  disabled: import_prop_types5.default.bool,
  field: import_prop_types5.default.string.isRequired,
  headerFilterMenuRef: import_prop_types5.default.shape({
    current: import_prop_types5.default.object
  }).isRequired,
  item: import_prop_types5.default.shape({
    field: import_prop_types5.default.string.isRequired,
    id: import_prop_types5.default.oneOfType([import_prop_types5.default.number, import_prop_types5.default.string]),
    operator: import_prop_types5.default.string.isRequired,
    value: import_prop_types5.default.any
  }).isRequired,
  operators: import_prop_types5.default.arrayOf(import_prop_types5.default.shape({
    getApplyFilterFn: import_prop_types5.default.func.isRequired,
    getValueAsString: import_prop_types5.default.func,
    headerLabel: import_prop_types5.default.string,
    InputComponent: import_prop_types5.default.elementType,
    InputComponentProps: import_prop_types5.default.shape({
      apiRef: import_prop_types5.default.shape({
        current: import_prop_types5.default.object.isRequired
      }),
      applyValue: import_prop_types5.default.func,
      className: import_prop_types5.default.string,
      clearButton: import_prop_types5.default.node,
      disabled: import_prop_types5.default.bool,
      focusElementRef: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.shape({
        current: import_prop_types5.default.any.isRequired
      })]),
      headerFilterMenu: import_prop_types5.default.node,
      inputRef: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.shape({
        current: (props, propName) => {
          if (props[propName] == null) {
            return null;
          }
          if (typeof props[propName] !== "object" || props[propName].nodeType !== 1) {
            return new Error(`Expected prop '${propName}' to be of type Element`);
          }
          return null;
        }
      })]),
      isFilterActive: import_prop_types5.default.bool,
      item: import_prop_types5.default.shape({
        field: import_prop_types5.default.string.isRequired,
        id: import_prop_types5.default.oneOfType([import_prop_types5.default.number, import_prop_types5.default.string]),
        operator: import_prop_types5.default.string.isRequired,
        value: import_prop_types5.default.any
      }),
      onBlur: import_prop_types5.default.func,
      onFocus: import_prop_types5.default.func,
      slotProps: import_prop_types5.default.object,
      tabIndex: import_prop_types5.default.number
    }),
    label: import_prop_types5.default.string,
    requiresFilterValue: import_prop_types5.default.bool,
    value: import_prop_types5.default.string.isRequired
  })).isRequired,
  showClearItem: import_prop_types5.default.bool
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterClearButton.js
var React11 = __toESM(require_react(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
function GridHeaderFilterClearButton(props) {
  var _a;
  const rootProps = useGridRootProps2();
  return (0, import_jsx_runtime8.jsx)(rootProps.slots.baseIconButton, _extends({
    tabIndex: -1,
    "aria-label": "Clear filter",
    size: "small"
  }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, props, {
    children: (0, import_jsx_runtime8.jsx)(rootProps.slots.columnMenuClearIcon, {
      fontSize: "inherit"
    })
  }));
}

// node_modules/@mui/x-data-grid-pro/esm/components/headerFiltering/GridHeaderFilterCell.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var _excluded4 = ["colIndex", "height", "hasFocus", "width", "headerClassName", "colDef", "item", "headerFilterMenuRef", "InputComponentProps", "showClearIcon", "pinnedPosition", "pinnedOffset", "style", "showLeftBorder", "showRightBorder"];
var StyledInputComponent = styled_default(GridFilterInputValue, {
  name: "MuiDataGrid",
  slot: "ColumnHeaderFilterInput"
})({
  flex: 1,
  marginRight: vars.spacing(0.5),
  marginBottom: vars.spacing(-0.25),
  '& input[type="number"], & input[type="date"], & input[type="datetime-local"]': {
    '&[value=""]:not(:focus)': {
      color: "transparent"
    }
  },
  [`& .${inputBaseClasses_default.input}`]: {
    fontSize: "14px"
  },
  [`.${gridClasses["root--densityCompact"]} & .${inputBaseClasses_default.input}`]: {
    paddingTop: vars.spacing(0.5),
    paddingBottom: vars.spacing(0.5),
    height: 23
  }
});
var OperatorLabel = styled_default("span", {
  name: "MuiDataGrid",
  slot: "ColumnHeaderFilterOperatorLabel"
})({
  flex: 1,
  marginRight: vars.spacing(0.5),
  color: vars.colors.foreground.muted,
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden"
});
var useUtilityClasses2 = (ownerState) => {
  const {
    colDef,
    classes,
    showRightBorder,
    showLeftBorder,
    pinnedPosition
  } = ownerState;
  const slots = {
    root: ["columnHeader", "columnHeader--filter", colDef.headerAlign === "left" && "columnHeader--alignLeft", colDef.headerAlign === "center" && "columnHeader--alignCenter", colDef.headerAlign === "right" && "columnHeader--alignRight", "withBorderColor", showRightBorder && "columnHeader--withRightBorder", showLeftBorder && "columnHeader--withLeftBorder", pinnedPosition === PinnedColumnPosition.LEFT && "columnHeader--pinnedLeft", pinnedPosition === PinnedColumnPosition.RIGHT && "columnHeader--pinnedRight"],
    input: ["columnHeaderFilterInput"],
    operatorLabel: ["columnHeaderFilterOperatorLabel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var DEFAULT_INPUT_COMPONENTS = {
  string: GridFilterInputValue,
  number: GridFilterInputValue,
  date: GridFilterInputDate,
  dateTime: GridFilterInputDate,
  boolean: GridFilterInputBoolean,
  singleSelect: GridFilterInputSingleSelect,
  actions: null,
  custom: null
};
var GridHeaderFilterCell = forwardRef((props, ref) => {
  const {
    colIndex,
    height,
    hasFocus,
    width,
    headerClassName,
    colDef,
    item,
    headerFilterMenuRef,
    InputComponentProps,
    showClearIcon = false,
    pinnedPosition,
    pinnedOffset,
    style: styleProp,
    showLeftBorder,
    showRightBorder
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const apiRef = useGridPrivateApiContext();
  const isRtl = useRtl();
  const columnFields = useGridSelector(apiRef, gridVisibleColumnFieldsSelector);
  const rootProps = useGridRootProps2();
  const cellRef = React12.useRef(null);
  const handleRef = useForkRef(ref, cellRef);
  const inputRef = React12.useRef(null);
  const buttonRef = React12.useRef(null);
  const editingField = useGridSelector(apiRef, gridHeaderFilteringEditFieldSelector);
  const isEditing = editingField === colDef.field;
  const menuOpenField = useGridSelector(apiRef, gridHeaderFilteringMenuSelector);
  const isMenuOpen = menuOpenField === colDef.field;
  const filterOperators = React12.useMemo(() => {
    if (!colDef.filterOperators) {
      return [];
    }
    return colDef.filterOperators.filter((operator) => operator.value !== "isAnyOf");
  }, [colDef.filterOperators]);
  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);
  const filterableColumnsLookup = useGridSelector(apiRef, gridFilterableColumnLookupSelector);
  const isFilterReadOnly = React12.useMemo(() => {
    if (!(filterModel == null ? void 0 : filterModel.items.length)) {
      return false;
    }
    const filterModelItem = filterModel.items.find((it) => it.field === colDef.field);
    return filterModelItem ? !filterableColumnsLookup[filterModelItem.field] : false;
  }, [colDef.field, filterModel, filterableColumnsLookup]);
  const currentOperator = React12.useMemo(() => filterOperators.find((operator) => operator.value === item.operator) ?? filterOperators[0], [item.operator, filterOperators]);
  const InputComponent = colDef.filterable || isFilterReadOnly ? currentOperator.InputComponent ?? DEFAULT_INPUT_COMPONENTS[colDef.type] : null;
  const clearFilterItem = React12.useCallback(() => {
    apiRef.current.deleteFilterItem(item);
  }, [apiRef, item]);
  let headerFilterComponent;
  if (colDef.renderHeaderFilter) {
    headerFilterComponent = colDef.renderHeaderFilter(_extends({}, props, {
      inputRef
    }));
  }
  React12.useLayoutEffect(() => {
    if (hasFocus && !isMenuOpen) {
      let focusableElement = cellRef.current.querySelector('[tabindex="0"]');
      if (isEditing && InputComponent) {
        focusableElement = inputRef.current;
      }
      const elementToFocus = focusableElement || cellRef.current;
      elementToFocus == null ? void 0 : elementToFocus.focus();
      if (apiRef.current.columnHeadersContainerRef.current) {
        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;
      }
    }
  }, [InputComponent, apiRef, hasFocus, isEditing, isMenuOpen]);
  const onKeyDown = React12.useCallback((event) => {
    if (isMenuOpen || isNavigationKey(event.key) || isFilterReadOnly) {
      return;
    }
    switch (event.key) {
      case "Escape":
        if (isEditing) {
          apiRef.current.stopHeaderFilterEditMode();
        }
        break;
      case "Enter":
        if (isEditing) {
          if (!event.defaultPrevented) {
            apiRef.current.stopHeaderFilterEditMode();
            break;
          }
        }
        if (event.metaKey || event.ctrlKey) {
          headerFilterMenuRef.current = buttonRef.current;
          apiRef.current.showHeaderFilterMenu(colDef.field);
          break;
        }
        apiRef.current.startHeaderFilterEditMode(colDef.field);
        break;
      case "Tab": {
        if (isEditing) {
          const fieldToFocus = columnFields[colIndex + (event.shiftKey ? -1 : 1)] ?? null;
          if (fieldToFocus) {
            apiRef.current.startHeaderFilterEditMode(fieldToFocus);
            apiRef.current.setColumnHeaderFilterFocus(fieldToFocus, event);
          }
        }
        break;
      }
      default:
        if (isEditing || event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) {
          break;
        }
        apiRef.current.startHeaderFilterEditMode(colDef.field);
        break;
    }
  }, [apiRef, colDef.field, colIndex, columnFields, headerFilterMenuRef, isEditing, isFilterReadOnly, isMenuOpen]);
  const publish = React12.useCallback((eventName, propHandler) => (event) => {
    apiRef.current.publishEvent(eventName, apiRef.current.getColumnHeaderParams(colDef.field), event);
    if (propHandler) {
      propHandler(event);
    }
  }, [apiRef, colDef.field]);
  const onMouseDown = React12.useCallback((event) => {
    var _a, _b;
    if (!hasFocus) {
      if ((_b = (_a = inputRef.current) == null ? void 0 : _a.contains) == null ? void 0 : _b.call(_a, event.target)) {
        inputRef.current.focus();
      }
      apiRef.current.setColumnHeaderFilterFocus(colDef.field, event);
    }
  }, [apiRef, colDef.field, hasFocus]);
  const mouseEventsHandlers = React12.useMemo(() => ({
    onKeyDown: publish("headerFilterKeyDown", onKeyDown),
    onClick: publish("headerFilterClick"),
    onMouseDown: publish("headerFilterMouseDown", onMouseDown),
    onBlur: publish("headerFilterBlur")
  }), [onMouseDown, onKeyDown, publish]);
  const ownerState = _extends({}, rootProps, {
    pinnedPosition,
    colDef,
    showLeftBorder,
    showRightBorder
  });
  const classes = useUtilityClasses2(ownerState);
  const label = currentOperator.headerLabel ?? apiRef.current.getLocaleText(`headerFilterOperator${capitalize(item.operator)}`);
  const isNoInputOperator = currentOperator.requiresFilterValue === false;
  const isApplied = (item == null ? void 0 : item.value) !== void 0 || isNoInputOperator;
  const isFilterActive = isApplied || hasFocus;
  const headerFilterMenu = (0, import_jsx_runtime9.jsx)(GridHeaderFilterMenuContainer, {
    operators: filterOperators,
    item,
    field: colDef.field,
    disabled: isFilterReadOnly,
    applyFilterChanges: apiRef.current.upsertFilterItem,
    headerFilterMenuRef,
    buttonRef,
    showClearItem: !showClearIcon && isApplied,
    clearFilterItem
  });
  const clearButton = showClearIcon && isApplied ? (0, import_jsx_runtime9.jsx)(GridHeaderFilterClearButton, {
    onClick: clearFilterItem,
    disabled: isFilterReadOnly
  }) : null;
  return (0, import_jsx_runtime9.jsxs)("div", _extends({
    className: clsx_default(classes.root, headerClassName),
    style: attachPinnedStyle(_extends({
      height,
      width
    }, styleProp), isRtl, pinnedPosition, pinnedOffset),
    role: "columnheader",
    "aria-colindex": colIndex + 1,
    "aria-label": headerFilterComponent == null ? colDef.headerName ?? colDef.field : void 0
  }, other, mouseEventsHandlers, {
    ref: handleRef,
    children: [headerFilterComponent, headerFilterComponent === void 0 ? (0, import_jsx_runtime9.jsxs)(React12.Fragment, {
      children: [isNoInputOperator ? (0, import_jsx_runtime9.jsxs)(React12.Fragment, {
        children: [(0, import_jsx_runtime9.jsx)(OperatorLabel, {
          className: classes.operatorLabel,
          children: label
        }), clearButton, headerFilterMenu]
      }) : null, InputComponent && !isNoInputOperator ? (0, import_jsx_runtime9.jsx)(StyledInputComponent, _extends({
        as: InputComponent,
        className: classes.input,
        apiRef,
        item,
        inputRef,
        applyValue: apiRef.current.upsertFilterItem,
        onFocus: () => apiRef.current.startHeaderFilterEditMode(colDef.field),
        onBlur: (event) => {
          var _a;
          apiRef.current.stopHeaderFilterEditMode();
          if (!((_a = event.relatedTarget) == null ? void 0 : _a.className.includes("columnHeader"))) {
            apiRef.current.setState((state) => _extends({}, state, {
              focus: {
                cell: null,
                columnHeader: null,
                columnHeaderFilter: null,
                columnGroupHeader: null
              }
            }));
          }
        },
        isFilterActive,
        headerFilterMenu,
        clearButton,
        disabled: isFilterReadOnly || isNoInputOperator,
        tabIndex: -1,
        slotProps: {
          root: {
            size: "small",
            label: capitalize(label),
            placeholder: ""
          }
        }
      }, isNoInputOperator ? {
        value: ""
      } : {}, currentOperator == null ? void 0 : currentOperator.InputComponentProps, InputComponentProps)) : null]
    }) : null]
  }));
});
if (true) GridHeaderFilterCell.displayName = "GridHeaderFilterCell";
true ? GridHeaderFilterCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  colDef: import_prop_types6.default.object.isRequired,
  colIndex: import_prop_types6.default.number.isRequired,
  hasFocus: import_prop_types6.default.bool,
  /**
   * Class name added to the column header cell.
   */
  headerClassName: import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.string]),
  headerFilterMenuRef: import_prop_types6.default.shape({
    current: import_prop_types6.default.object
  }).isRequired,
  height: import_prop_types6.default.number.isRequired,
  InputComponentProps: import_prop_types6.default.shape({
    apiRef: import_prop_types6.default.shape({
      current: import_prop_types6.default.object.isRequired
    }),
    applyValue: import_prop_types6.default.func,
    className: import_prop_types6.default.string,
    clearButton: import_prop_types6.default.node,
    disabled: import_prop_types6.default.bool,
    focusElementRef: import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.shape({
      current: import_prop_types6.default.any.isRequired
    })]),
    headerFilterMenu: import_prop_types6.default.node,
    inputRef: import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.shape({
      current: (props, propName) => {
        if (props[propName] == null) {
          return null;
        }
        if (typeof props[propName] !== "object" || props[propName].nodeType !== 1) {
          return new Error(`Expected prop '${propName}' to be of type Element`);
        }
        return null;
      }
    })]),
    isFilterActive: import_prop_types6.default.bool,
    item: import_prop_types6.default.shape({
      field: import_prop_types6.default.string.isRequired,
      id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
      operator: import_prop_types6.default.string.isRequired,
      value: import_prop_types6.default.any
    }),
    onBlur: import_prop_types6.default.func,
    onFocus: import_prop_types6.default.func,
    slotProps: import_prop_types6.default.object,
    tabIndex: import_prop_types6.default.number
  }),
  item: import_prop_types6.default.shape({
    field: import_prop_types6.default.string.isRequired,
    id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    operator: import_prop_types6.default.string.isRequired,
    value: import_prop_types6.default.any
  }).isRequired,
  pinnedOffset: import_prop_types6.default.number,
  pinnedPosition: import_prop_types6.default.oneOf([0, 1, 2, 3]),
  showClearIcon: import_prop_types6.default.bool,
  showLeftBorder: import_prop_types6.default.bool.isRequired,
  showRightBorder: import_prop_types6.default.bool.isRequired,
  sortIndex: import_prop_types6.default.number,
  style: import_prop_types6.default.object,
  tabIndex: import_prop_types6.default.oneOf([-1, 0]).isRequired,
  width: import_prop_types6.default.number.isRequired
} : void 0;
var Memoized = fastMemo(GridHeaderFilterCell);

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanels.js
var React17 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/utils/useGridPrivateApiContext.js
var useGridPrivateApiContext2 = useGridPrivateApiContext;

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/detailPanel/gridDetailPanelToggleColDef.js
var React14 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanelToggleCell.js
var React13 = __toESM(require_react(), 1);
var import_prop_types7 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/detailPanel/gridDetailPanelSelector.js
var gridDetailPanelStateSelector = createRootSelector((state) => state.detailPanel);
var gridDetailPanelExpandedRowIdsSelector = createSelector(gridDetailPanelStateSelector, (detailPanelState) => detailPanelState.expandedRowIds);
var gridDetailPanelExpandedRowsContentCacheSelector = createSelector(gridDetailPanelStateSelector, (detailPanelState) => detailPanelState.contentCache);
var gridDetailPanelRawHeightCacheSelector = createSelectorMemoized(gridDetailPanelStateSelector, (detailPanelState) => detailPanelState.heightCache);

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanelToggleCell.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses3 = (ownerState) => {
  const {
    classes,
    isExpanded
  } = ownerState;
  const slots = {
    root: ["detailPanelToggleCell", isExpanded && "detailPanelToggleCell--expanded"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var isExpandedSelector = createSelector(gridDetailPanelExpandedRowIdsSelector, (expandedRowIds, rowId) => {
  return expandedRowIds.has(rowId);
});
function GridDetailPanelToggleCell(props) {
  var _a;
  const {
    id,
    row,
    api
  } = props;
  const rowId = api.getRowId(row);
  const isExpanded = useGridSelector({
    current: api
  }, isExpandedSelector, rowId);
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const ownerState = {
    classes: rootProps.classes,
    isExpanded
  };
  const classes = useUtilityClasses3(ownerState);
  const contentCache = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);
  const hasContent = React13.isValidElement(contentCache[id]);
  const Icon = isExpanded ? rootProps.slots.detailPanelCollapseIcon : rootProps.slots.detailPanelExpandIcon;
  return (0, import_jsx_runtime10.jsx)(rootProps.slots.baseIconButton, _extends({
    size: "small",
    tabIndex: -1,
    disabled: !hasContent,
    className: classes.root,
    "aria-expanded": isExpanded,
    "aria-label": isExpanded ? apiRef.current.getLocaleText("collapseDetailPanel") : apiRef.current.getLocaleText("expandDetailPanel")
  }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
    children: (0, import_jsx_runtime10.jsx)(Icon, {
      fontSize: "inherit"
    })
  }));
}
true ? GridDetailPanelToggleCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: import_prop_types7.default.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: import_prop_types7.default.oneOf(["edit", "view"]).isRequired,
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: import_prop_types7.default.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: import_prop_types7.default.string.isRequired,
  /**
   * A ref allowing to set imperative focus.
   * It can be passed to the element that should receive focus.
   * @ignore - do not document.
   */
  focusElementRef: import_prop_types7.default.oneOfType([import_prop_types7.default.func, import_prop_types7.default.shape({
    current: import_prop_types7.default.shape({
      focus: import_prop_types7.default.func.isRequired
    })
  })]),
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: import_prop_types7.default.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: import_prop_types7.default.bool.isRequired,
  /**
   * The grid row id.
   */
  id: import_prop_types7.default.oneOfType([import_prop_types7.default.number, import_prop_types7.default.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: import_prop_types7.default.bool,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: import_prop_types7.default.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: import_prop_types7.default.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: import_prop_types7.default.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: import_prop_types7.default.any
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/detailPanel/gridDetailPanelToggleColDef.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var GRID_DETAIL_PANEL_TOGGLE_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {
  type: "custom",
  field: GRID_DETAIL_PANEL_TOGGLE_FIELD,
  editable: false,
  sortable: false,
  filterable: false,
  resizable: false,
  // @ts-ignore
  aggregable: false,
  disableColumnMenu: true,
  disableReorder: true,
  disableExport: true,
  align: "left",
  width: 40,
  valueGetter: (value, row, column, apiRef) => {
    const rowId = gridRowIdSelector(apiRef, row);
    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(apiRef);
    return expandedRowIds.has(rowId);
  },
  rowSpanValueGetter: (_, row, __, apiRef) => gridRowIdSelector(apiRef, row),
  renderCell: (params) => (0, import_jsx_runtime11.jsx)(GridDetailPanelToggleCell, _extends({}, params)),
  renderHeader: ({
    colDef
  }) => (0, import_jsx_runtime11.jsx)("div", {
    "aria-label": colDef.headerName
  })
});

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanel.js
var React16 = __toESM(require_react(), 1);

// node_modules/@mui/x-internals/esm/useResizeObserver/useResizeObserver.js
var React15 = __toESM(require_react(), 1);
var isDevEnvironment = true;
var noop = () => {
};
function useResizeObserver(ref, fn, enabled) {
  const fnRef = React15.useRef(null);
  fnRef.current = fn;
  useEnhancedEffect_default(() => {
    if (enabled === false || typeof ResizeObserver === "undefined") {
      return noop;
    }
    let frameID = 0;
    const target = ref.current;
    const observer = new ResizeObserver((entries) => {
      if (isDevEnvironment) {
        frameID = requestAnimationFrame(() => {
          fnRef.current(entries);
        });
      } else {
        fnRef.current(entries);
      }
    });
    if (target) {
      observer.observe(target);
    }
    return () => {
      if (frameID) {
        cancelAnimationFrame(frameID);
      }
      observer.disconnect();
    };
  }, [ref, enabled]);
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanel.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var DetailPanel = styled_default("div", {
  name: "MuiDataGrid",
  slot: "DetailPanel"
})({
  width: "calc(var(--DataGrid-rowWidth) - var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))",
  backgroundColor: vars.colors.background.base,
  overflow: "auto"
});
function GridDetailPanel(props) {
  const {
    rowId,
    height,
    className,
    children
  } = props;
  const apiRef = useGridPrivateApiContext2();
  const ref = React16.useRef(null);
  const rootProps = useGridRootProps2();
  const ownerState = rootProps;
  const hasAutoHeight = height === "auto";
  const rowNode = gridRowNodeSelector(apiRef, rowId);
  React16.useLayoutEffect(() => {
    if (hasAutoHeight && typeof ResizeObserver === "undefined") {
      apiRef.current.storeDetailPanelHeight(rowId, ref.current.clientHeight);
    }
  }, [apiRef, hasAutoHeight, rowId]);
  useResizeObserver(ref, (entries) => {
    const [entry] = entries;
    const observedHeight = entry.borderBoxSize && entry.borderBoxSize.length > 0 ? entry.borderBoxSize[0].blockSize : entry.contentRect.height;
    apiRef.current.storeDetailPanelHeight(rowId, observedHeight);
  }, hasAutoHeight);
  if ((rowNode == null ? void 0 : rowNode.type) === "skeletonRow") {
    return null;
  }
  return (0, import_jsx_runtime12.jsx)(DetailPanel, {
    ref,
    ownerState,
    role: "presentation",
    style: {
      height
    },
    className,
    children
  });
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridDetailPanels.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses4 = () => {
  const slots = {
    detailPanel: ["detailPanel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, {});
};
function GridDetailPanels(props) {
  const rootProps = useGridRootProps2();
  if (!rootProps.getDetailPanelContent) {
    return null;
  }
  return React17.createElement(GridDetailPanelsImpl, props);
}
function GridDetailPanelsImpl({
  virtualScroller
}) {
  const apiRef = useGridPrivateApiContext2();
  const classes = useUtilityClasses4();
  const {
    setPanels
  } = virtualScroller;
  const expandedRowIds = useGridSelector(apiRef, gridDetailPanelExpandedRowIdsSelector);
  const detailPanelsContent = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);
  const detailPanelsHeights = useGridSelector(apiRef, gridDetailPanelRawHeightCacheSelector);
  const getDetailPanel = React17.useCallback((rowId) => {
    const content = detailPanelsContent[rowId];
    const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(rowId);
    const exists = rowIndex !== void 0;
    if (!React17.isValidElement(content) || !exists) {
      return null;
    }
    const heightCache = detailPanelsHeights[rowId];
    const height = heightCache.autoHeight ? "auto" : heightCache.height;
    return (0, import_jsx_runtime13.jsx)(GridDetailPanel, {
      rowId,
      height,
      className: classes.detailPanel,
      children: content
    }, `panel-${rowId}`);
  }, [apiRef, classes.detailPanel, detailPanelsHeights, detailPanelsContent]);
  React17.useEffect(() => {
    const map = /* @__PURE__ */ new Map();
    for (const rowId of expandedRowIds) {
      map.set(rowId, getDetailPanel(rowId));
    }
    setPanels(map);
  }, [expandedRowIds, setPanels, getDetailPanel]);
  return null;
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridPinnedRows.js
var React18 = __toESM(require_react(), 1);
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses5 = () => {
  const slots = {
    root: ["pinnedRows"]
  };
  return composeClasses(slots, getDataGridUtilityClass, {});
};
function GridPinnedRows({
  position,
  virtualScroller
}) {
  const classes = useUtilityClasses5();
  const apiRef = useGridPrivateApiContext();
  const pinnedRowsData = useGridSelector(apiRef, gridPinnedRowsSelector);
  const rows = pinnedRowsData[position];
  const {
    getRows
  } = virtualScroller;
  const pinnedRenderContext = React18.useMemo(() => ({
    firstRowIndex: 0,
    lastRowIndex: rows.length,
    firstColumnIndex: -1,
    lastColumnIndex: -1
  }), [rows]);
  if (rows.length === 0) {
    return null;
  }
  const pinnedRows = getRows({
    position,
    rows,
    renderContext: pinnedRenderContext
  }, gridRowTreeSelector(apiRef));
  return (0, import_jsx_runtime14.jsx)("div", {
    className: clsx_default(classes.root, gridClasses[`pinnedRows--${position}`]),
    role: "presentation",
    children: pinnedRows
  });
}

// node_modules/@mui/x-data-grid-pro/esm/material/icons.js
var React19 = __toESM(require_react(), 1);
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var GridPushPinRightIcon = createSvgIcon((0, import_jsx_runtime15.jsx)("g", {
  transform: "rotate(-30 15 10)",
  children: (0, import_jsx_runtime15.jsx)("path", {
    d: "M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z",
    fillRule: "evenodd"
  })
}), "PushPinRight");
var GridPushPinLeftIcon = createSvgIcon((0, import_jsx_runtime15.jsx)("g", {
  transform: "rotate(30 8 12)",
  children: (0, import_jsx_runtime15.jsx)("path", {
    d: "M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z",
    fillRule: "evenodd"
  })
}), "PushPinLeft");

// node_modules/@mui/x-data-grid-pro/esm/material/index.js
var iconSlots = {
  columnMenuPinRightIcon: GridPushPinRightIcon,
  columnMenuPinLeftIcon: GridPushPinLeftIcon
};
var materialSlots = _extends({}, iconSlots);
var material_default = materialSlots;

// node_modules/@mui/x-data-grid-pro/esm/constants/dataGridProDefaultSlotsComponents.js
var DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS = _extends({}, DATA_GRID_DEFAULT_SLOTS_COMPONENTS, material_default, {
  columnMenu: GridProColumnMenu,
  columnHeaders: GridColumnHeaders,
  detailPanels: GridDetailPanels,
  headerFilterCell: Memoized,
  headerFilterMenu: GridHeaderFilterMenu,
  pinnedRows: GridPinnedRows
});

// node_modules/@mui/x-data-grid-pro/esm/hooks/utils/useGridAriaAttributes.js
var useGridAriaAttributesPro = () => {
  const ariaAttributesCommunity = useGridAriaAttributes();
  const rootProps = useGridRootProps2();
  const ariaAttributesPro = rootProps.treeData ? {
    role: "treegrid"
  } : {};
  return _extends({}, ariaAttributesCommunity, ariaAttributesPro);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rows/useGridRowAriaAttributes.js
var React20 = __toESM(require_react(), 1);
var useGridRowAriaAttributesPro = (addTreeDataAttributes) => {
  const apiRef = useGridPrivateApiContext2();
  const props = useGridRootProps2();
  const getRowAriaAttributesCommunity = useGridRowAriaAttributes();
  const filteredTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);
  const filteredChildrenCountLookup = useGridSelector(apiRef, gridFilteredChildrenCountLookupSelector);
  const sortedVisibleRowPositionsLookup = useGridSelector(apiRef, gridExpandedSortedRowTreeLevelPositionLookupSelector);
  return React20.useCallback((rowNode, index) => {
    const ariaAttributes = getRowAriaAttributesCommunity(rowNode, index);
    if (!rowNode || !(props.treeData || addTreeDataAttributes)) {
      return ariaAttributes;
    }
    if (rowNode.type === "footer" || rowNode.type === "pinnedRow") {
      return ariaAttributes;
    }
    ariaAttributes["aria-level"] = rowNode.depth + 1;
    const filteredChildrenCount = filteredChildrenCountLookup[rowNode.id] ?? 0;
    if (rowNode.type === "group" && filteredChildrenCount > 0) {
      ariaAttributes["aria-expanded"] = Boolean(rowNode.childrenExpanded);
    }
    if (rowNode.parent !== null) {
      ariaAttributes["aria-setsize"] = rowNode.parent === GRID_ROOT_GROUP_ID ? filteredTopLevelRowCount : filteredChildrenCountLookup[rowNode.parent];
      ariaAttributes["aria-posinset"] = sortedVisibleRowPositionsLookup[rowNode.id];
    }
    return ariaAttributes;
  }, [props.treeData, addTreeDataAttributes, filteredTopLevelRowCount, filteredChildrenCountLookup, sortedVisibleRowPositionsLookup, getRowAriaAttributesCommunity]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnPinning/useGridColumnPinning.js
var React21 = __toESM(require_react(), 1);
var columnPinningStateInitializer = (state, props, apiRef) => {
  var _a;
  apiRef.current.caches.columnPinning = {
    orderedFieldsBeforePinningColumns: null
  };
  let model;
  if (props.pinnedColumns) {
    model = props.pinnedColumns;
  } else if ((_a = props.initialState) == null ? void 0 : _a.pinnedColumns) {
    model = props.initialState.pinnedColumns;
  } else {
    model = {};
  }
  return _extends({}, state, {
    pinnedColumns: model
  });
};
var useGridColumnPinning = (apiRef, props) => {
  var _a;
  const pinnedColumns = useGridSelector(apiRef, gridPinnedColumnsSelector);
  const calculateScrollLeft = React21.useCallback((initialValue, params) => {
    const visiblePinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);
    if (!params.colIndex || visiblePinnedColumns.left.length === 0 && visiblePinnedColumns.right.length === 0) {
      return initialValue;
    }
    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);
    const columnsTotalWidth = gridColumnsTotalWidthSelector(apiRef);
    const columnPositions = gridColumnPositionsSelector(apiRef);
    const clientWidth = apiRef.current.virtualScrollerRef.current.clientWidth;
    const scrollLeft = Math.abs(apiRef.current.virtualScrollerRef.current.scrollLeft);
    const offsetWidth = visibleColumns[params.colIndex].computedWidth;
    const offsetLeft = columnPositions[params.colIndex];
    const leftPinnedColumnsWidth = columnPositions[visiblePinnedColumns.left.length];
    const rightPinnedColumnsWidth = columnsTotalWidth - columnPositions[columnPositions.length - visiblePinnedColumns.right.length];
    const elementBottom = offsetLeft + offsetWidth;
    if (elementBottom - (clientWidth - rightPinnedColumnsWidth) > scrollLeft) {
      const left = elementBottom - (clientWidth - rightPinnedColumnsWidth);
      return _extends({}, initialValue, {
        left
      });
    }
    if (offsetLeft < scrollLeft + leftPinnedColumnsWidth) {
      const left = offsetLeft - leftPinnedColumnsWidth;
      return _extends({}, initialValue, {
        left
      });
    }
    return initialValue;
  }, [apiRef]);
  const addColumnMenuItems = React21.useCallback((columnMenuItems, colDef) => {
    if (props.disableColumnPinning) {
      return columnMenuItems;
    }
    if (colDef.pinnable === false) {
      return columnMenuItems;
    }
    return [...columnMenuItems, "columnMenuPinningItem"];
  }, [props.disableColumnPinning]);
  const checkIfCanBeReordered = React21.useCallback((initialValue, {
    targetIndex
  }) => {
    const visiblePinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);
    if (visiblePinnedColumns.left.length === 0 && visiblePinnedColumns.right.length === 0) {
      return initialValue;
    }
    if (visiblePinnedColumns.left.length > 0 && targetIndex < visiblePinnedColumns.left.length) {
      return false;
    }
    if (visiblePinnedColumns.right.length > 0) {
      const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);
      const firstRightPinnedColumnIndex = visibleColumns.length - visiblePinnedColumns.right.length;
      return targetIndex >= firstRightPinnedColumnIndex ? false : initialValue;
    }
    return initialValue;
  }, [apiRef]);
  const stateExportPreProcessing = React21.useCallback((prevState, context) => {
    var _a2;
    const pinnedColumnsToExport = gridPinnedColumnsSelector(apiRef);
    const shouldExportPinnedColumns = (
      // Always export if the `exportOnlyDirtyModels` property is not activated
      !context.exportOnlyDirtyModels || // Always export if the model is controlled
      props.pinnedColumns != null || // Always export if the model has been initialized
      ((_a2 = props.initialState) == null ? void 0 : _a2.pinnedColumns) != null || // Export if the model is not empty
      (pinnedColumnsToExport.left ?? []).length > 0 || (pinnedColumnsToExport.right ?? []).length > 0
    );
    if (!shouldExportPinnedColumns) {
      return prevState;
    }
    return _extends({}, prevState, {
      pinnedColumns: pinnedColumnsToExport
    });
  }, [apiRef, props.pinnedColumns, (_a = props.initialState) == null ? void 0 : _a.pinnedColumns]);
  const stateRestorePreProcessing = React21.useCallback((params, context) => {
    const newPinnedColumns = context.stateToRestore.pinnedColumns;
    if (newPinnedColumns != null) {
      setState(apiRef, newPinnedColumns);
    }
    return params;
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "scrollToIndexes", calculateScrollLeft);
  useGridRegisterPipeProcessor(apiRef, "columnMenu", addColumnMenuItems);
  useGridRegisterPipeProcessor(apiRef, "canBeReordered", checkIfCanBeReordered);
  useGridRegisterPipeProcessor(apiRef, "exportState", stateExportPreProcessing);
  useGridRegisterPipeProcessor(apiRef, "restoreState", stateRestorePreProcessing);
  apiRef.current.registerControlState({
    stateId: "pinnedColumns",
    propModel: props.pinnedColumns,
    propOnChange: props.onPinnedColumnsChange,
    stateSelector: gridPinnedColumnsSelector,
    changeEvent: "pinnedColumnsChange"
  });
  const pinColumn = React21.useCallback((field, side) => {
    if (apiRef.current.isColumnPinned(field) === side) {
      return;
    }
    const otherSide = side === GridPinnedColumnPosition.RIGHT ? GridPinnedColumnPosition.LEFT : GridPinnedColumnPosition.RIGHT;
    const newPinnedColumns = {
      [side]: [...pinnedColumns[side] || [], field],
      [otherSide]: (pinnedColumns[otherSide] || []).filter((column) => column !== field)
    };
    apiRef.current.setPinnedColumns(newPinnedColumns);
  }, [apiRef, pinnedColumns]);
  const unpinColumn = React21.useCallback((field) => {
    apiRef.current.setPinnedColumns({
      left: (pinnedColumns.left || []).filter((column) => column !== field),
      right: (pinnedColumns.right || []).filter((column) => column !== field)
    });
  }, [apiRef, pinnedColumns.left, pinnedColumns.right]);
  const getPinnedColumns = React21.useCallback(() => {
    return gridPinnedColumnsSelector(apiRef);
  }, [apiRef]);
  const setPinnedColumns = React21.useCallback((newPinnedColumns) => {
    setState(apiRef, newPinnedColumns);
    apiRef.current.requestPipeProcessorsApplication("hydrateColumns");
  }, [apiRef]);
  const isColumnPinned = React21.useCallback((field) => {
    const leftPinnedColumns = pinnedColumns.left || [];
    if (leftPinnedColumns.includes(field)) {
      return GridPinnedColumnPosition.LEFT;
    }
    const rightPinnedColumns = pinnedColumns.right || [];
    if (rightPinnedColumns.includes(field)) {
      return GridPinnedColumnPosition.RIGHT;
    }
    return false;
  }, [pinnedColumns.left, pinnedColumns.right]);
  const columnPinningApi = {
    pinColumn,
    unpinColumn,
    getPinnedColumns,
    setPinnedColumns,
    isColumnPinned
  };
  useGridApiMethod(apiRef, columnPinningApi, "public");
  const handleColumnOrderChange = (params) => {
    if (!apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns) {
      return;
    }
    const {
      column,
      targetIndex,
      oldIndex
    } = params;
    const delta = targetIndex > oldIndex ? 1 : -1;
    const latestColumnFields = gridColumnFieldsSelector(apiRef);
    const siblingField = latestColumnFields[targetIndex - delta];
    const newOrderedFieldsBeforePinningColumns = [...apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns];
    let i = newOrderedFieldsBeforePinningColumns.findIndex((currentColumn) => currentColumn === column.field);
    let j = i + delta;
    const stop = newOrderedFieldsBeforePinningColumns.findIndex((currentColumn) => currentColumn === siblingField);
    while (delta > 0 ? i < stop : i > stop) {
      while (apiRef.current.isColumnPinned(newOrderedFieldsBeforePinningColumns[j])) {
        j += delta;
      }
      const temp = newOrderedFieldsBeforePinningColumns[i];
      newOrderedFieldsBeforePinningColumns[i] = newOrderedFieldsBeforePinningColumns[j];
      newOrderedFieldsBeforePinningColumns[j] = temp;
      i = j;
      j = i + delta;
    }
    apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = newOrderedFieldsBeforePinningColumns;
  };
  useGridEvent(apiRef, "columnOrderChange", handleColumnOrderChange);
  React21.useEffect(() => {
    if (props.pinnedColumns) {
      apiRef.current.setPinnedColumns(props.pinnedColumns);
    }
  }, [apiRef, props.pinnedColumns]);
};
function setState(apiRef, model) {
  apiRef.current.setState((state) => _extends({}, state, {
    pinnedColumns: model
  }));
}

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnPinning/useGridColumnPinningPreProcessors.js
var React22 = __toESM(require_react(), 1);
var useGridColumnPinningPreProcessors = (apiRef, props) => {
  const {
    disableColumnPinning
  } = props;
  const prevAllPinnedColumns = React22.useRef([]);
  const reorderPinnedColumns = React22.useCallback((columnsState) => {
    if (columnsState.orderedFields.length === 0 || disableColumnPinning) {
      return columnsState;
    }
    const savedState = apiRef.current.state;
    apiRef.current.state = _extends({}, savedState, {
      columns: columnsState
    });
    const pinnedColumns = gridExistingPinnedColumnSelector(apiRef);
    apiRef.current.state = savedState;
    const leftPinnedColumns = pinnedColumns.left;
    const rightPinnedColumns = pinnedColumns.right;
    let newOrderedFields;
    const allPinnedColumns = [...leftPinnedColumns, ...rightPinnedColumns];
    const {
      orderedFieldsBeforePinningColumns
    } = apiRef.current.caches.columnPinning;
    if (orderedFieldsBeforePinningColumns) {
      newOrderedFields = new Array(columnsState.orderedFields.length).fill(null);
      const newOrderedFieldsBeforePinningColumns = [...newOrderedFields];
      const remainingFields = [...columnsState.orderedFields];
      prevAllPinnedColumns.current.forEach((field) => {
        if (!allPinnedColumns.includes(field) && columnsState.lookup[field]) {
          const index = orderedFieldsBeforePinningColumns.indexOf(field);
          newOrderedFields[index] = field;
          newOrderedFieldsBeforePinningColumns[index] = field;
          remainingFields.splice(remainingFields.indexOf(field), 1);
        }
      });
      allPinnedColumns.forEach((field) => {
        let index = orderedFieldsBeforePinningColumns.indexOf(field);
        if (index === -1 || index >= newOrderedFieldsBeforePinningColumns.length) {
          index = columnsState.orderedFields.indexOf(field);
        }
        if (newOrderedFieldsBeforePinningColumns[index] !== null) {
          index = 0;
          while (newOrderedFieldsBeforePinningColumns[index] !== null) {
            index += 1;
          }
        }
        newOrderedFields[index] = field;
        newOrderedFieldsBeforePinningColumns[index] = field;
        remainingFields.splice(remainingFields.indexOf(field), 1);
      });
      let i = 0;
      remainingFields.forEach((field) => {
        while (newOrderedFieldsBeforePinningColumns[i] !== null) {
          i += 1;
        }
        newOrderedFieldsBeforePinningColumns[i] = field;
        newOrderedFields[i] = field;
      });
      apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = newOrderedFieldsBeforePinningColumns;
    } else {
      newOrderedFields = [...columnsState.orderedFields];
      apiRef.current.caches.columnPinning.orderedFieldsBeforePinningColumns = [...columnsState.orderedFields];
    }
    prevAllPinnedColumns.current = allPinnedColumns;
    const centerColumns = newOrderedFields.filter((field) => {
      return !leftPinnedColumns.includes(field) && !rightPinnedColumns.includes(field);
    });
    return _extends({}, columnsState, {
      orderedFields: [...leftPinnedColumns, ...centerColumns, ...rightPinnedColumns]
    });
  }, [apiRef, disableColumnPinning]);
  useGridRegisterPipeProcessor(apiRef, "hydrateColumns", reorderPinnedColumns);
  const isColumnPinned = React22.useCallback((initialValue, field) => apiRef.current.isColumnPinned(field), [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "isColumnPinned", isColumnPinned);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnReorder/useGridColumnReorder.js
var React23 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/ownerDocument/ownerDocument.js
function ownerDocument(node) {
  return node && node.ownerDocument || document;
}

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnReorder/columnReorderSelector.js
var gridColumnReorderSelector = createRootSelector((state) => state.columnReorder);
var gridColumnReorderDragColSelector = createSelector(gridColumnReorderSelector, (columnReorder) => columnReorder.dragCol);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/columnReorder/useGridColumnReorder.js
var CURSOR_MOVE_DIRECTION_LEFT = "left";
var CURSOR_MOVE_DIRECTION_RIGHT = "right";
var getCursorMoveDirectionX = (currentCoordinates, nextCoordinates) => {
  return currentCoordinates.x <= nextCoordinates.x ? CURSOR_MOVE_DIRECTION_RIGHT : CURSOR_MOVE_DIRECTION_LEFT;
};
var hasCursorPositionChanged = (currentCoordinates, nextCoordinates) => currentCoordinates.x !== nextCoordinates.x || currentCoordinates.y !== nextCoordinates.y;
var useUtilityClasses6 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    columnHeaderDragging: ["columnHeader--dragging"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var columnReorderStateInitializer = (state) => _extends({}, state, {
  columnReorder: {
    dragCol: ""
  }
});
var useGridColumnReorder = (apiRef, props) => {
  const logger = useGridLogger(apiRef, "useGridColumnReorder");
  const dragColNode = React23.useRef(null);
  const cursorPosition = React23.useRef({
    x: 0,
    y: 0
  });
  const originColumnIndex = React23.useRef(null);
  const forbiddenIndexes = React23.useRef({});
  const removeDnDStylesTimeout = React23.useRef(void 0);
  const ownerState = {
    classes: props.classes
  };
  const classes = useUtilityClasses6(ownerState);
  const isRtl = useRtl();
  React23.useEffect(() => {
    return () => {
      clearTimeout(removeDnDStylesTimeout.current);
    };
  }, []);
  const handleDragEnd = React23.useCallback((params, event) => {
    const dragColField = gridColumnReorderDragColSelector(apiRef);
    if (props.disableColumnReorder || !dragColField) {
      return;
    }
    logger.debug("End dragging col");
    event.preventDefault();
    event.stopPropagation();
    clearTimeout(removeDnDStylesTimeout.current);
    if (dragColNode.current.classList.contains(classes.columnHeaderDragging)) {
      dragColNode.current.classList.remove(classes.columnHeaderDragging);
    }
    dragColNode.current = null;
    if (event.dataTransfer.dropEffect === "none" && !props.keepColumnPositionIfDraggedOutside) {
      apiRef.current.setColumnIndex(dragColField, originColumnIndex.current);
      originColumnIndex.current = null;
    } else {
      const columnOrderChangeParams = {
        column: apiRef.current.getColumn(dragColField),
        targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(dragColField),
        oldIndex: originColumnIndex.current
      };
      apiRef.current.publishEvent("columnOrderChange", columnOrderChangeParams);
    }
    apiRef.current.setState((state) => _extends({}, state, {
      columnReorder: _extends({}, state.columnReorder, {
        dragCol: ""
      })
    }));
  }, [apiRef, props.disableColumnReorder, props.keepColumnPositionIfDraggedOutside, logger, classes.columnHeaderDragging]);
  const handleDragStart = React23.useCallback((params, event) => {
    if (props.disableColumnReorder || params.colDef.disableReorder) {
      return;
    }
    logger.debug(`Start dragging col ${params.field}`);
    event.stopPropagation();
    dragColNode.current = event.currentTarget;
    dragColNode.current.classList.add(classes.columnHeaderDragging);
    const handleDragEndEvent = (dragEndEvent) => {
      dragColNode.current.removeEventListener("dragend", handleDragEndEvent);
      apiRef.current.publishEvent("columnHeaderDragEndNative", params, dragEndEvent);
    };
    dragColNode.current.addEventListener("dragend", handleDragEndEvent);
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = "move";
    }
    apiRef.current.setState((state) => _extends({}, state, {
      columnReorder: _extends({}, state.columnReorder, {
        dragCol: params.field
      })
    }));
    removeDnDStylesTimeout.current = setTimeout(() => {
      dragColNode.current.classList.remove(classes.columnHeaderDragging);
    });
    originColumnIndex.current = apiRef.current.getColumnIndex(params.field, false);
    const draggingColumnGroupPath = apiRef.current.getColumnGroupPath(params.field);
    const columnIndex = originColumnIndex.current;
    const allColumns = apiRef.current.getAllColumns();
    const groupsLookup = apiRef.current.getAllGroupDetails();
    const getGroupPathFromColumnIndex = (colIndex) => {
      const field = allColumns[colIndex].field;
      return apiRef.current.getColumnGroupPath(field);
    };
    let limitingGroupId = null;
    draggingColumnGroupPath.forEach((groupId) => {
      var _a;
      if (!((_a = groupsLookup[groupId]) == null ? void 0 : _a.freeReordering)) {
        if (columnIndex > 0 && getGroupPathFromColumnIndex(columnIndex - 1).includes(groupId)) {
          limitingGroupId = groupId;
        } else if (columnIndex + 1 < allColumns.length && getGroupPathFromColumnIndex(columnIndex + 1).includes(groupId)) {
          limitingGroupId = groupId;
        }
      }
    });
    forbiddenIndexes.current = {};
    for (let indexToForbid = 0; indexToForbid < allColumns.length; indexToForbid += 1) {
      const leftIndex = indexToForbid <= columnIndex ? indexToForbid - 1 : indexToForbid;
      const rightIndex = indexToForbid < columnIndex ? indexToForbid : indexToForbid + 1;
      if (limitingGroupId !== null) {
        let allowIndex = false;
        if (leftIndex >= 0 && getGroupPathFromColumnIndex(leftIndex).includes(limitingGroupId)) {
          allowIndex = true;
        } else if (rightIndex < allColumns.length && getGroupPathFromColumnIndex(rightIndex).includes(limitingGroupId)) {
          allowIndex = true;
        }
        if (!allowIndex) {
          forbiddenIndexes.current[indexToForbid] = true;
        }
      }
      if (leftIndex >= 0 && rightIndex < allColumns.length) {
        getGroupPathFromColumnIndex(rightIndex).forEach((groupId) => {
          var _a;
          if (getGroupPathFromColumnIndex(leftIndex).includes(groupId)) {
            if (!draggingColumnGroupPath.includes(groupId)) {
              if (!((_a = groupsLookup[groupId]) == null ? void 0 : _a.freeReordering)) {
                forbiddenIndexes.current[indexToForbid] = true;
              }
            }
          }
        });
      }
    }
  }, [props.disableColumnReorder, classes.columnHeaderDragging, logger, apiRef]);
  const handleDragEnter = React23.useCallback((params, event) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);
  const handleDragOver = React23.useCallback((params, event) => {
    const dragColField = gridColumnReorderDragColSelector(apiRef);
    if (!dragColField) {
      return;
    }
    logger.debug(`Dragging over col ${params.field}`);
    event.preventDefault();
    event.stopPropagation();
    const coordinates = {
      x: event.clientX,
      y: event.clientY
    };
    if (params.field !== dragColField && hasCursorPositionChanged(cursorPosition.current, coordinates)) {
      const targetColIndex = apiRef.current.getColumnIndex(params.field, false);
      const targetColVisibleIndex = apiRef.current.getColumnIndex(params.field, true);
      const targetCol = apiRef.current.getColumn(params.field);
      const dragColIndex = apiRef.current.getColumnIndex(dragColField, false);
      const visibleColumns = apiRef.current.getVisibleColumns();
      const allColumns = apiRef.current.getAllColumns();
      const cursorMoveDirectionX = getCursorMoveDirectionX(cursorPosition.current, coordinates);
      const hasMovedLeft = cursorMoveDirectionX === CURSOR_MOVE_DIRECTION_LEFT && (isRtl ? dragColIndex < targetColIndex : targetColIndex < dragColIndex);
      const hasMovedRight = cursorMoveDirectionX === CURSOR_MOVE_DIRECTION_RIGHT && (isRtl ? targetColIndex < dragColIndex : dragColIndex < targetColIndex);
      if (hasMovedLeft || hasMovedRight) {
        let canBeReordered;
        let indexOffsetInHiddenColumns = 0;
        if (!targetCol.disableReorder) {
          canBeReordered = true;
        } else if (hasMovedLeft) {
          canBeReordered = targetColVisibleIndex > 0 && !visibleColumns[targetColVisibleIndex - 1].disableReorder;
        } else {
          canBeReordered = targetColVisibleIndex < visibleColumns.length - 1 && !visibleColumns[targetColVisibleIndex + 1].disableReorder;
        }
        if (forbiddenIndexes.current[targetColIndex]) {
          let nextVisibleColumnField;
          let indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;
          if (hasMovedLeft) {
            nextVisibleColumnField = targetColVisibleIndex > 0 ? visibleColumns[targetColVisibleIndex - 1].field : null;
            while (indexWithOffset > 0 && allColumns[indexWithOffset].field !== nextVisibleColumnField && forbiddenIndexes.current[indexWithOffset]) {
              indexOffsetInHiddenColumns -= 1;
              indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;
            }
          } else {
            nextVisibleColumnField = targetColVisibleIndex + 1 < visibleColumns.length ? visibleColumns[targetColVisibleIndex + 1].field : null;
            while (indexWithOffset < allColumns.length - 1 && allColumns[indexWithOffset].field !== nextVisibleColumnField && forbiddenIndexes.current[indexWithOffset]) {
              indexOffsetInHiddenColumns += 1;
              indexWithOffset = targetColIndex + indexOffsetInHiddenColumns;
            }
          }
          if (forbiddenIndexes.current[indexWithOffset] || allColumns[indexWithOffset].field === nextVisibleColumnField) {
            canBeReordered = false;
          }
        }
        const canBeReorderedProcessed = apiRef.current.unstable_applyPipeProcessors("canBeReordered", canBeReordered, {
          targetIndex: targetColVisibleIndex
        });
        if (canBeReorderedProcessed) {
          apiRef.current.setColumnIndex(dragColField, targetColIndex + indexOffsetInHiddenColumns);
        }
      }
      cursorPosition.current = coordinates;
    }
  }, [apiRef, logger, isRtl]);
  React23.useEffect(() => {
    if (!props.keepColumnPositionIfDraggedOutside) {
      return () => {
      };
    }
    const doc = ownerDocument(apiRef.current.rootElementRef.current);
    const listener = (event) => {
      if (event.dataTransfer) {
        event.preventDefault();
        event.dataTransfer.dropEffect = "move";
      }
    };
    doc.addEventListener("dragover", listener);
    return () => {
      doc.removeEventListener("dragover", listener);
    };
  }, [apiRef, props.keepColumnPositionIfDraggedOutside]);
  useGridEvent(apiRef, "columnHeaderDragStart", handleDragStart);
  useGridEvent(apiRef, "columnHeaderDragEnter", handleDragEnter);
  useGridEvent(apiRef, "columnHeaderDragOver", handleDragOver);
  useGridEvent(apiRef, "columnHeaderDragEndNative", handleDragEnd);
  useGridEvent(apiRef, "cellDragEnter", handleDragEnter);
  useGridEvent(apiRef, "cellDragOver", handleDragOver);
  useGridEventPriority(apiRef, "columnOrderChange", props.onColumnOrderChange);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideTreeData/useGridDataSourceTreeDataPreProcessors.js
var React25 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/treeData/gridTreeDataGroupColDef.js
var GRID_TREE_DATA_GROUPING_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {
  type: "custom",
  sortable: false,
  filterable: false,
  disableColumnMenu: true,
  disableReorder: true,
  align: "left",
  width: 200,
  valueGetter: (value, row, column, apiRef) => {
    const rowId = gridRowIdSelector(apiRef, row);
    const rowNode = gridRowNodeSelector(apiRef, rowId);
    return (rowNode == null ? void 0 : rowNode.type) === "group" || (rowNode == null ? void 0 : rowNode.type) === "leaf" ? rowNode.groupingKey : void 0;
  }
});
var GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES = {
  field: GRID_TREE_DATA_GROUPING_FIELD,
  editable: false,
  groupable: false
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideTreeData/utils.js
function skipFiltering(rowTree) {
  const filteredChildrenCountLookup = {};
  const nodes = Object.values(rowTree);
  for (let i = 0; i < nodes.length; i += 1) {
    const node = nodes[i];
    filteredChildrenCountLookup[node.id] = node.serverChildrenCount ?? 0;
  }
  return {
    filteredRowsLookup: defaultGridFilterLookup.filteredRowsLookup,
    filteredChildrenCountLookup,
    filteredDescendantCountLookup: defaultGridFilterLookup.filteredDescendantCountLookup
  };
}
function skipSorting(rowTree) {
  return getTreeNodeDescendants(rowTree, GRID_ROOT_GROUP_ID, false);
}
function getParentPath(rowId, treeCreationParams) {
  var _a;
  if (treeCreationParams.updates.type !== "full" || !((_a = treeCreationParams.previousTree) == null ? void 0 : _a[rowId]) || treeCreationParams.previousTree[rowId].depth < 1 || !("path" in treeCreationParams.previousTree[rowId])) {
    return [];
  }
  return treeCreationParams.previousTree[rowId].path || [];
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridDataSourceTreeDataGroupingCell.js
var React24 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/gridDataSourceSelector.js
var gridDataSourceStateSelector = createRootSelector((state) => state.dataSource);
var gridDataSourceLoadingSelector = createSelector(gridDataSourceStateSelector, (dataSource) => dataSource.loading);
var gridDataSourceLoadingIdSelector = createSelector(gridDataSourceStateSelector, (dataSource, id) => dataSource.loading[id] ?? false);
var gridDataSourceErrorsSelector = createSelector(gridDataSourceStateSelector, (dataSource) => dataSource.errors);
var gridDataSourceErrorSelector = createSelector(gridDataSourceStateSelector, (dataSource, id) => dataSource.errors[id]);

// node_modules/@mui/x-data-grid-pro/esm/components/GridDataSourceTreeDataGroupingCell.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses7 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["treeDataGroupingCell"],
    toggle: ["treeDataGroupingCellToggle"],
    loadingContainer: ["treeDataGroupingCellLoadingContainer"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridTreeDataGroupingCellIcon(props) {
  var _a;
  const apiRef = useGridPrivateApiContext2();
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses7(rootProps);
  const {
    rowNode,
    id,
    field,
    descendantCount
  } = props;
  const isDataLoading = useGridSelector(apiRef, gridDataSourceLoadingIdSelector, id);
  const error = useGridSelector(apiRef, gridDataSourceErrorSelector, id);
  const handleClick = (event) => {
    if (!rowNode.childrenExpanded) {
      apiRef.current.dataSource.fetchRows(id);
    } else {
      apiRef.current.setRowChildrenExpansion(id, false);
      apiRef.current.removeChildrenRows(id);
    }
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };
  const Icon = rowNode.childrenExpanded ? rootProps.slots.treeDataCollapseIcon : rootProps.slots.treeDataExpandIcon;
  if (isDataLoading) {
    return (0, import_jsx_runtime16.jsx)("div", {
      className: classes.loadingContainer,
      children: (0, import_jsx_runtime16.jsx)(rootProps.slots.baseCircularProgress, {
        size: "1rem",
        color: "inherit"
      })
    });
  }
  return descendantCount === -1 || descendantCount > 0 ? (0, import_jsx_runtime16.jsx)(rootProps.slots.baseIconButton, _extends({
    size: "small",
    onClick: handleClick,
    tabIndex: -1,
    "aria-label": rowNode.childrenExpanded ? apiRef.current.getLocaleText("treeDataCollapse") : apiRef.current.getLocaleText("treeDataExpand")
  }, (_a = rootProps == null ? void 0 : rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
    children: (0, import_jsx_runtime16.jsx)(rootProps.slots.baseTooltip, {
      title: (error == null ? void 0 : error.message) ?? null,
      children: (0, import_jsx_runtime16.jsx)(rootProps.slots.baseBadge, {
        variant: "dot",
        color: "error",
        invisible: !error,
        children: (0, import_jsx_runtime16.jsx)(Icon, {
          fontSize: "inherit"
        })
      })
    })
  })) : null;
}
function GridDataSourceTreeDataGroupingCell(props) {
  var _a, _b;
  const {
    id,
    field,
    formattedValue,
    rowNode,
    hideDescendantCount,
    offsetMultiplier = 2
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridPrivateApiContext2();
  const row = useGridSelector(apiRef, gridRowSelector, id);
  const classes = useUtilityClasses7(rootProps);
  let descendantCount = 0;
  if (row) {
    descendantCount = ((_b = (_a = rootProps.dataSource) == null ? void 0 : _a.getChildrenCount) == null ? void 0 : _b.call(_a, row)) ?? 0;
  }
  return (0, import_jsx_runtime16.jsxs)("div", {
    className: classes.root,
    style: {
      marginLeft: vars.spacing(rowNode.depth * offsetMultiplier)
    },
    children: [(0, import_jsx_runtime16.jsx)("div", {
      className: classes.toggle,
      children: (0, import_jsx_runtime16.jsx)(GridTreeDataGroupingCellIcon, {
        id,
        field,
        rowNode,
        row,
        descendantCount
      })
    }), (0, import_jsx_runtime16.jsxs)("span", {
      children: [formattedValue === void 0 ? rowNode.groupingKey : formattedValue, !hideDescendantCount && descendantCount > 0 ? ` (${descendantCount})` : ""]
    })]
  });
}

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/utils.js
var getGroupRowIdFromPath = (path) => {
  const pathStr = path.map((groupingCriteria) => `${groupingCriteria.field}/${groupingCriteria.key}`).join("-");
  return `auto-generated-row-${pathStr}`;
};
var getNodePathInTree = ({
  id,
  tree
}) => {
  const path = [];
  let node = tree[id];
  while (node.id !== GRID_ROOT_GROUP_ID) {
    path.push({
      field: node.type === "leaf" ? null : node.groupingField,
      key: node.groupingKey
    });
    node = tree[node.parent];
  }
  path.reverse();
  return path;
};
var checkGroupChildrenExpansion = (node, defaultGroupingExpansionDepth, isGroupExpandedByDefault) => {
  let childrenExpanded;
  if (node.id === GRID_ROOT_GROUP_ID) {
    childrenExpanded = true;
  } else if (isGroupExpandedByDefault) {
    childrenExpanded = isGroupExpandedByDefault(node);
  } else {
    childrenExpanded = defaultGroupingExpansionDepth === -1 || defaultGroupingExpansionDepth > node.depth;
  }
  return childrenExpanded;
};
var updateGroupDefaultExpansion = (node, defaultGroupingExpansionDepth, isGroupExpandedByDefault) => {
  const childrenExpanded = checkGroupChildrenExpansion(node, defaultGroupingExpansionDepth, isGroupExpandedByDefault);
  node.childrenExpanded = childrenExpanded;
  return node;
};
var insertNodeInTree = (node, tree, treeDepths, previousTree) => {
  var _a;
  tree[node.id] = node;
  treeDepths[node.depth] = (treeDepths[node.depth] ?? 0) + 1;
  const parentNode = tree[node.parent];
  if (node.type === "group" || node.type === "leaf") {
    const groupingFieldName = node.groupingField ?? "__no_field__";
    const groupingKeyName = node.groupingKey ?? "__no_key__";
    const groupingField = (_a = parentNode.childrenFromPath) == null ? void 0 : _a[groupingFieldName];
    if (previousTree !== null && previousTree[parentNode.id] === tree[parentNode.id]) {
      parentNode.children = [...parentNode.children, node.id];
    } else {
      parentNode.children.push(node.id);
    }
    if (groupingField == null) {
      parentNode.childrenFromPath[groupingFieldName] = {
        [groupingKeyName.toString()]: node.id
      };
    } else {
      groupingField[groupingKeyName.toString()] = node.id;
    }
  } else if (node.type === "footer") {
    parentNode.footerId = node.id;
  }
};
var removeNodeFromTree = ({
  node,
  tree,
  treeDepths
}) => {
  delete tree[node.id];
  const nodeDepth = node.depth;
  const currentNodeCount = treeDepths[nodeDepth];
  if (currentNodeCount === 1) {
    delete treeDepths[nodeDepth];
  } else {
    treeDepths[nodeDepth] = currentNodeCount - 1;
  }
  const parentNode = tree[node.parent];
  if (node.type === "footer") {
    tree[parentNode.id] = _extends({}, parentNode, {
      footerId: null
    });
  } else {
    const groupingField = node.groupingField ?? "__no_field__";
    const groupingKey = node.groupingKey ?? "__no_key__";
    const children = parentNode.children.filter((childId) => childId !== node.id);
    const childrenFromPath = parentNode.childrenFromPath;
    delete childrenFromPath[groupingField][groupingKey.toString()];
    tree[parentNode.id] = _extends({}, parentNode, {
      children,
      childrenFromPath
    });
  }
};
var updateGroupNodeIdAndAutoGenerated = ({
  node,
  updatedNode,
  previousTree,
  tree,
  treeDepths
}) => {
  node.children.forEach((childId) => {
    tree[childId] = _extends({}, tree[childId], {
      parent: updatedNode.id
    });
  });
  removeNodeFromTree({
    node,
    tree,
    treeDepths
  });
  const groupNode = _extends({}, node, updatedNode);
  insertNodeInTree(groupNode, tree, treeDepths, previousTree);
};
var createUpdatedGroupsManager = () => ({
  value: {},
  addAction(groupId, action) {
    if (!this.value[groupId]) {
      this.value[groupId] = {};
    }
    this.value[groupId][action] = true;
  }
});
var getVisibleRowsLookup = ({
  tree,
  filteredRowsLookup
}) => {
  if (!filteredRowsLookup) {
    return {};
  }
  const visibleRowsLookup = {};
  const handleTreeNode = (node, areAncestorsExpanded) => {
    const isPassingFiltering = filteredRowsLookup[node.id] !== false;
    if (node.type === "group") {
      node.children.forEach((childId) => {
        const childNode = tree[childId];
        handleTreeNode(childNode, areAncestorsExpanded && !!node.childrenExpanded);
      });
    }
    const isVisible = isPassingFiltering && areAncestorsExpanded;
    if (!isVisible) {
      visibleRowsLookup[node.id] = isVisible;
    }
    if (node.type === "group" && node.footerId != null) {
      const isFooterVisible = isPassingFiltering && areAncestorsExpanded && !!node.childrenExpanded;
      if (!isFooterVisible) {
        visibleRowsLookup[node.footerId] = isFooterVisible;
      }
    }
  };
  const nodes = Object.values(tree);
  for (let i = 0; i < nodes.length; i += 1) {
    const node = nodes[i];
    if (node.depth === 0) {
      handleTreeNode(node, true);
    }
  }
  return visibleRowsLookup;
};

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/insertDataRowInTree.js
var insertDataRowInTree = ({
  id,
  path,
  updatedGroupsManager,
  previousTree,
  tree,
  treeDepths,
  onDuplicatePath,
  isGroupExpandedByDefault,
  defaultGroupingExpansionDepth,
  serverChildrenCount,
  groupsToFetch
}) => {
  var _a, _b;
  let parentNodeId = GRID_ROOT_GROUP_ID;
  for (let depth = 0; depth < path.length; depth += 1) {
    const {
      key,
      field
    } = path[depth];
    const fieldWithDefaultValue = field ?? "__no_field__";
    const keyWithDefaultValue = key ?? "__no_key__";
    const existingNodeIdWithPartialPath = (_b = (_a = tree[parentNodeId].childrenFromPath) == null ? void 0 : _a[fieldWithDefaultValue]) == null ? void 0 : _b[keyWithDefaultValue.toString()];
    if (depth === path.length - 1) {
      if (existingNodeIdWithPartialPath == null) {
        let node;
        if (serverChildrenCount !== void 0 && serverChildrenCount !== 0) {
          node = {
            type: "group",
            id,
            parent: parentNodeId,
            path: path.map((step) => step.key),
            depth,
            isAutoGenerated: false,
            groupingKey: key,
            groupingField: field,
            children: [],
            childrenFromPath: {},
            childrenExpanded: false,
            serverChildrenCount
          };
          const shouldFetchChildren = checkGroupChildrenExpansion(node, defaultGroupingExpansionDepth, isGroupExpandedByDefault);
          if (shouldFetchChildren) {
            groupsToFetch == null ? void 0 : groupsToFetch.add(id);
          }
        } else {
          node = {
            type: "leaf",
            id,
            depth,
            parent: parentNodeId,
            groupingKey: key
          };
        }
        updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(parentNodeId, "insertChildren");
        insertNodeInTree(node, tree, treeDepths, previousTree);
      } else {
        const existingNodeWithPartialPath = tree[existingNodeIdWithPartialPath];
        if (existingNodeWithPartialPath.type === "group" && existingNodeWithPartialPath.isAutoGenerated) {
          updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(parentNodeId, "removeChildren");
          updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(parentNodeId, "insertChildren");
          updateGroupNodeIdAndAutoGenerated({
            tree,
            previousTree,
            treeDepths,
            node: existingNodeWithPartialPath,
            updatedNode: {
              id,
              isAutoGenerated: false
            }
          });
        } else {
          onDuplicatePath == null ? void 0 : onDuplicatePath(existingNodeIdWithPartialPath, id, path);
        }
      }
    } else if (existingNodeIdWithPartialPath == null) {
      const nodeId = getGroupRowIdFromPath(path.slice(0, depth + 1));
      const autoGeneratedGroupNode = {
        type: "group",
        id: nodeId,
        parent: parentNodeId,
        depth,
        isAutoGenerated: true,
        groupingKey: key,
        groupingField: field,
        children: [],
        childrenFromPath: {},
        childrenExpanded: false
      };
      updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(parentNodeId, "insertChildren");
      insertNodeInTree(updateGroupDefaultExpansion(autoGeneratedGroupNode, defaultGroupingExpansionDepth, isGroupExpandedByDefault), tree, treeDepths, previousTree);
      parentNodeId = nodeId;
    } else {
      const currentGroupNode = tree[existingNodeIdWithPartialPath];
      if (currentGroupNode.type !== "group") {
        const groupNode = {
          type: "group",
          id: currentGroupNode.id,
          parent: currentGroupNode.parent,
          depth: currentGroupNode.depth,
          isAutoGenerated: false,
          groupingKey: key,
          groupingField: field,
          children: [],
          childrenFromPath: {},
          childrenExpanded: false
        };
        tree[existingNodeIdWithPartialPath] = updateGroupDefaultExpansion(groupNode, defaultGroupingExpansionDepth, isGroupExpandedByDefault);
      }
      parentNodeId = currentGroupNode.id;
    }
  }
};

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/createRowTree.js
var createRowTree = (params) => {
  const dataRowIds = [];
  const tree = {
    [GRID_ROOT_GROUP_ID]: buildRootGroup()
  };
  const treeDepths = {};
  const groupsToFetch = /* @__PURE__ */ new Set();
  for (let i = 0; i < params.nodes.length; i += 1) {
    const node = params.nodes[i];
    dataRowIds.push(node.id);
    insertDataRowInTree({
      tree,
      previousTree: params.previousTree,
      id: node.id,
      path: node.path,
      serverChildrenCount: node.serverChildrenCount,
      onDuplicatePath: params.onDuplicatePath,
      treeDepths,
      isGroupExpandedByDefault: params.isGroupExpandedByDefault,
      defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,
      groupsToFetch
    });
  }
  return {
    tree,
    treeDepths,
    groupingName: params.groupingName,
    dataRowIds,
    groupsToFetch: Array.from(groupsToFetch)
  };
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/treeData/gridTreeDataUtils.js
var TreeDataStrategy = (function(TreeDataStrategy2) {
  TreeDataStrategy2["Default"] = "tree-data";
  TreeDataStrategy2["DataSource"] = "tree-data-source";
  return TreeDataStrategy2;
})({});
var filterRowTreeFromTreeData = (params) => {
  const {
    apiRef,
    rowTree,
    disableChildrenFiltering,
    isRowMatchingFilters
  } = params;
  const filteredRowsLookup = {};
  const filteredChildrenCountLookup = {};
  const filteredDescendantCountLookup = {};
  const filterCache = {};
  const filterResults = {
    passingFilterItems: null,
    passingQuickFilterValues: null
  };
  const filterTreeNode = (node, isParentMatchingFilters, areAncestorsExpanded) => {
    const shouldSkipFilters = disableChildrenFiltering && node.depth > 0;
    let isMatchingFilters;
    if (shouldSkipFilters) {
      isMatchingFilters = null;
    } else if (!isRowMatchingFilters || node.type === "footer") {
      isMatchingFilters = true;
    } else {
      const row = apiRef.current.getRow(node.id);
      isRowMatchingFilters(row, void 0, filterResults);
      isMatchingFilters = passFilterLogic([filterResults.passingFilterItems], [filterResults.passingQuickFilterValues], params.filterModel, params.apiRef, filterCache);
    }
    let filteredChildrenCount = 0;
    let filteredDescendantCount = 0;
    if (node.type === "group") {
      node.children.forEach((childId) => {
        const childNode = rowTree[childId];
        const childSubTreeSize = filterTreeNode(childNode, isMatchingFilters ?? isParentMatchingFilters, areAncestorsExpanded && !!node.childrenExpanded);
        filteredDescendantCount += childSubTreeSize;
        if (childSubTreeSize > 0) {
          filteredChildrenCount += 1;
        }
      });
    }
    let shouldPassFilters;
    switch (isMatchingFilters) {
      case true: {
        shouldPassFilters = true;
        break;
      }
      case false: {
        shouldPassFilters = filteredDescendantCount > 0;
        break;
      }
      default: {
        shouldPassFilters = isParentMatchingFilters;
        break;
      }
    }
    if (!shouldPassFilters) {
      filteredRowsLookup[node.id] = false;
    }
    if (!shouldPassFilters) {
      return 0;
    }
    filteredChildrenCountLookup[node.id] = filteredChildrenCount;
    filteredDescendantCountLookup[node.id] = filteredDescendantCount;
    if (node.type === "footer") {
      return filteredDescendantCount;
    }
    return filteredDescendantCount + 1;
  };
  const nodes = Object.values(rowTree);
  for (let i = 0; i < nodes.length; i += 1) {
    const node = nodes[i];
    if (node.depth === 0) {
      filterTreeNode(node, true, true);
    }
  }
  return {
    filteredRowsLookup,
    filteredChildrenCountLookup,
    filteredDescendantCountLookup
  };
};

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/removeDataRowFromTree.js
var removeNode = ({
  node,
  tree,
  treeDepths
}) => {
  removeNodeFromTree({
    node,
    tree,
    treeDepths
  });
  if (node.type === "group" && node.footerId != null) {
    removeNodeFromTree({
      node: tree[node.footerId],
      tree,
      treeDepths
    });
  }
};
var removeNodeAndCleanParent = ({
  node,
  tree,
  treeDepths,
  updatedGroupsManager
}) => {
  removeNode({
    node,
    tree,
    treeDepths
  });
  const parentNode = tree[node.parent];
  updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(parentNode.id, "removeChildren");
  const shouldDeleteGroup = parentNode.id !== GRID_ROOT_GROUP_ID && parentNode.children.length === 0;
  if (shouldDeleteGroup) {
    if (parentNode.isAutoGenerated) {
      removeNodeAndCleanParent({
        node: parentNode,
        tree,
        treeDepths
      });
    } else {
      tree[parentNode.id] = {
        type: "leaf",
        id: parentNode.id,
        depth: parentNode.depth,
        parent: parentNode.parent,
        groupingKey: parentNode.groupingKey
      };
    }
  }
};
var replaceDataGroupWithAutoGeneratedGroup = ({
  node,
  tree,
  treeDepths,
  updatedGroupsManager
}) => {
  updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(node.parent, "removeChildren");
  updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(node.parent, "insertChildren");
  updateGroupNodeIdAndAutoGenerated({
    previousTree: null,
    tree,
    treeDepths,
    node,
    updatedNode: {
      id: getGroupRowIdFromPath(getNodePathInTree({
        id: node.id,
        tree
      })),
      isAutoGenerated: true
    }
  });
};
var removeDataRowFromTree = ({
  id,
  tree,
  treeDepths,
  updatedGroupsManager,
  groupingName
}) => {
  const node = tree[id];
  if (node.type === "group" && node.children.length > 0) {
    replaceDataGroupWithAutoGeneratedGroup({
      node,
      tree,
      treeDepths,
      updatedGroupsManager
    });
  } else if (groupingName === TreeDataStrategy.Default || groupingName === RowGroupingStrategy.Default) {
    removeNodeAndCleanParent({
      node,
      tree,
      treeDepths,
      updatedGroupsManager
    });
  } else {
    removeNode({
      node,
      tree,
      treeDepths
    });
  }
};

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/updateRowTree.js
var updateRowTree = (params) => {
  const tree = _extends({}, params.previousTree);
  const treeDepths = _extends({}, params.previousTreeDepth);
  const updatedGroupsManager = createUpdatedGroupsManager();
  const groupsToFetch = params.previousGroupsToFetch ? /* @__PURE__ */ new Set([...params.previousGroupsToFetch]) : /* @__PURE__ */ new Set([]);
  for (let i = 0; i < params.nodes.inserted.length; i += 1) {
    const {
      id,
      path,
      serverChildrenCount
    } = params.nodes.inserted[i];
    insertDataRowInTree({
      previousTree: params.previousTree,
      tree,
      treeDepths,
      updatedGroupsManager,
      id,
      path,
      serverChildrenCount,
      onDuplicatePath: params.onDuplicatePath,
      isGroupExpandedByDefault: params.isGroupExpandedByDefault,
      defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,
      groupsToFetch
    });
  }
  for (let i = 0; i < params.nodes.removed.length; i += 1) {
    const nodeId = params.nodes.removed[i];
    removeDataRowFromTree({
      tree,
      treeDepths,
      updatedGroupsManager,
      id: nodeId,
      groupingName: params.groupingName
    });
  }
  for (let i = 0; i < params.nodes.modified.length; i += 1) {
    const {
      id,
      path,
      serverChildrenCount
    } = params.nodes.modified[i];
    const pathInPreviousTree = getNodePathInTree({
      tree,
      id
    });
    const isInSameGroup = isDeepEqual(pathInPreviousTree, path);
    if (!isInSameGroup) {
      removeDataRowFromTree({
        tree,
        treeDepths,
        updatedGroupsManager,
        id,
        groupingName: params.groupingName
      });
      insertDataRowInTree({
        previousTree: params.previousTree,
        tree,
        treeDepths,
        updatedGroupsManager,
        id,
        path,
        serverChildrenCount,
        onDuplicatePath: params.onDuplicatePath,
        isGroupExpandedByDefault: params.isGroupExpandedByDefault,
        defaultGroupingExpansionDepth: params.defaultGroupingExpansionDepth,
        groupsToFetch
      });
    } else {
      updatedGroupsManager == null ? void 0 : updatedGroupsManager.addAction(tree[id].parent, "modifyChildren");
    }
  }
  const dataRowIds = getTreeNodeDescendants(tree, GRID_ROOT_GROUP_ID, true);
  return {
    tree,
    treeDepths,
    groupingName: params.groupingName,
    dataRowIds,
    updatedGroupsManager,
    groupsToFetch: Array.from(groupsToFetch)
  };
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideTreeData/useGridDataSourceTreeDataPreProcessors.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var _excluded5 = ["hideDescendantCount"];
var useGridDataSourceTreeDataPreProcessors = (privateApiRef, props) => {
  const setStrategyAvailability = React25.useCallback(() => {
    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.RowTree, TreeDataStrategy.DataSource, props.treeData && props.dataSource ? () => true : () => false);
  }, [privateApiRef, props.treeData, props.dataSource]);
  const getGroupingColDef = React25.useCallback(() => {
    const groupingColDefProp = props.groupingColDef;
    let colDefOverride;
    if (typeof groupingColDefProp === "function") {
      const params = {
        groupingName: TreeDataStrategy.DataSource,
        fields: []
      };
      colDefOverride = groupingColDefProp(params);
    } else {
      colDefOverride = groupingColDefProp;
    }
    const _ref = colDefOverride ?? {}, {
      hideDescendantCount
    } = _ref, colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref, _excluded5);
    const commonProperties = _extends({}, GRID_TREE_DATA_GROUPING_COL_DEF, {
      renderCell: (params) => (0, import_jsx_runtime17.jsx)(GridDataSourceTreeDataGroupingCell, _extends({}, params, {
        hideDescendantCount
      })),
      headerName: privateApiRef.current.getLocaleText("treeDataGroupingHeaderName")
    });
    return _extends({}, commonProperties, colDefOverrideProperties, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES);
  }, [privateApiRef, props.groupingColDef]);
  const updateGroupingColumn = React25.useCallback((columnsState) => {
    if (!props.dataSource) {
      return columnsState;
    }
    const groupingColDefField = GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES.field;
    const shouldHaveGroupingColumn = props.treeData;
    const prevGroupingColumn = columnsState.lookup[groupingColDefField];
    if (shouldHaveGroupingColumn) {
      const newGroupingColumn = getGroupingColDef();
      if (prevGroupingColumn) {
        newGroupingColumn.width = prevGroupingColumn.width;
        newGroupingColumn.flex = prevGroupingColumn.flex;
      }
      columnsState.lookup[groupingColDefField] = newGroupingColumn;
      if (prevGroupingColumn == null) {
        columnsState.orderedFields = [groupingColDefField, ...columnsState.orderedFields];
      }
    } else if (!shouldHaveGroupingColumn && prevGroupingColumn) {
      delete columnsState.lookup[groupingColDefField];
      columnsState.orderedFields = columnsState.orderedFields.filter((field) => field !== groupingColDefField);
    }
    return columnsState;
  }, [props.treeData, props.dataSource, getGroupingColDef]);
  const createRowTreeForTreeData = React25.useCallback((params) => {
    var _a, _b;
    const getGroupKey = (_a = props.dataSource) == null ? void 0 : _a.getGroupKey;
    if (!getGroupKey) {
      throw new Error("MUI X: No `getGroupKey` method provided with the dataSource.");
    }
    const getChildrenCount = (_b = props.dataSource) == null ? void 0 : _b.getChildrenCount;
    if (!getChildrenCount) {
      throw new Error("MUI X: No `getChildrenCount` method provided with the dataSource.");
    }
    const getRowTreeBuilderNode = (rowId) => {
      const parentPath = params.updates.groupKeys ?? getParentPath(rowId, params);
      const count = getChildrenCount(params.dataRowIdToModelLookup[rowId]);
      return {
        id: rowId,
        path: [...parentPath, getGroupKey(params.dataRowIdToModelLookup[rowId])].map((key) => ({
          key,
          field: null
        })),
        serverChildrenCount: count
      };
    };
    const onDuplicatePath = (firstId, secondId, path) => {
      throw new Error(["MUI X: The values returned by `getGroupKey` for all the sibling rows should be unique.", `The rows with id #${firstId} and #${secondId} have the same.`, `Path: ${JSON.stringify(path.map((step) => step.key))}.`].join("\n"));
    };
    if (params.updates.type === "full") {
      return createRowTree({
        previousTree: params.previousTree,
        nodes: params.updates.rows.map(getRowTreeBuilderNode),
        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
        isGroupExpandedByDefault: props.isGroupExpandedByDefault,
        groupingName: TreeDataStrategy.DataSource,
        onDuplicatePath
      });
    }
    return updateRowTree({
      nodes: {
        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),
        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),
        removed: params.updates.actions.remove
      },
      previousTree: params.previousTree,
      previousGroupsToFetch: params.previousGroupsToFetch,
      previousTreeDepth: params.previousTreeDepths,
      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
      isGroupExpandedByDefault: props.isGroupExpandedByDefault,
      groupingName: TreeDataStrategy.DataSource
    });
  }, [props.dataSource, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);
  const filterRows = React25.useCallback(() => {
    const rowTree = gridRowTreeSelector(privateApiRef);
    return skipFiltering(rowTree);
  }, [privateApiRef]);
  const sortRows = React25.useCallback(() => {
    const rowTree = gridRowTreeSelector(privateApiRef);
    return skipSorting(rowTree);
  }, [privateApiRef]);
  useGridRegisterPipeProcessor(privateApiRef, "hydrateColumns", updateGroupingColumn);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, "rowTreeCreation", createRowTreeForTreeData);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, "filtering", filterRows);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, "sorting", sortRows);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.DataSource, "visibleRowsLookupCreation", getVisibleRowsLookup);
  useFirstRender(() => {
    setStrategyAvailability();
  });
  const isFirstRender = React25.useRef(true);
  React25.useEffect(() => {
    if (!isFirstRender.current) {
      setStrategyAvailability();
    } else {
      isFirstRender.current = false;
    }
  }, [setStrategyAvailability]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/detailPanel/useGridDetailPanel.js
var React26 = __toESM(require_react(), 1);
var emptySet = /* @__PURE__ */ new Set();
var detailPanelStateInitializer = (state, props) => {
  var _a, _b;
  return _extends({}, state, {
    detailPanel: {
      heightCache: {},
      expandedRowIds: props.detailPanelExpandedRowIds ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.detailPanel) == null ? void 0 : _b.expandedRowIds) ?? emptySet
    }
  });
};
function cacheContentAndHeight(apiRef, getDetailPanelContent, getDetailPanelHeight, previousHeightCache) {
  var _a;
  if (typeof getDetailPanelContent !== "function") {
    return {};
  }
  const rowIds = gridDataRowIdsSelector(apiRef);
  const contentCache = {};
  const heightCache = {};
  for (let i = 0; i < rowIds.length; i += 1) {
    const id = rowIds[i];
    const params = apiRef.current.getRowParams(id);
    const content = getDetailPanelContent(params);
    contentCache[id] = content;
    if (content == null) {
      continue;
    }
    const height = getDetailPanelHeight(params);
    const autoHeight = height === "auto";
    heightCache[id] = {
      autoHeight,
      height: autoHeight ? (_a = previousHeightCache[id]) == null ? void 0 : _a.height : height
    };
  }
  return {
    contentCache,
    heightCache
  };
}
var useGridDetailPanel = (apiRef, props) => {
  const contentCache = useGridSelector(apiRef, gridDetailPanelExpandedRowsContentCacheSelector);
  const handleCellClick = React26.useCallback((params, event) => {
    if (params.field !== GRID_DETAIL_PANEL_TOGGLE_FIELD || props.getDetailPanelContent == null) {
      return;
    }
    const content = contentCache[params.id];
    if (!React26.isValidElement(content)) {
      return;
    }
    if (event.target === event.currentTarget) {
      return;
    }
    apiRef.current.toggleDetailPanel(params.id);
  }, [apiRef, contentCache, props.getDetailPanelContent]);
  const handleCellKeyDown = React26.useCallback((params, event) => {
    if (props.getDetailPanelContent == null) {
      return;
    }
    if (params.field === GRID_DETAIL_PANEL_TOGGLE_FIELD && event.key === " ") {
      apiRef.current.toggleDetailPanel(params.id);
    }
  }, [apiRef, props.getDetailPanelContent]);
  useGridEvent(apiRef, "cellClick", handleCellClick);
  useGridEvent(apiRef, "cellKeyDown", handleCellKeyDown);
  apiRef.current.registerControlState({
    stateId: "detailPanels",
    propModel: props.detailPanelExpandedRowIds,
    propOnChange: props.onDetailPanelExpandedRowIdsChange,
    stateSelector: gridDetailPanelExpandedRowIdsSelector,
    changeEvent: "detailPanelsExpandedRowIdsChange"
  });
  const toggleDetailPanel = React26.useCallback((id) => {
    if (props.getDetailPanelContent == null) {
      return;
    }
    const content = contentCache[id];
    if (!React26.isValidElement(content)) {
      return;
    }
    const ids = apiRef.current.getExpandedDetailPanels();
    const newIds = new Set(ids);
    if (ids.has(id)) {
      newIds.delete(id);
    } else {
      newIds.add(id);
    }
    apiRef.current.setExpandedDetailPanels(newIds);
  }, [apiRef, contentCache, props.getDetailPanelContent]);
  const getExpandedDetailPanels = React26.useCallback(() => gridDetailPanelExpandedRowIdsSelector(apiRef), [apiRef]);
  const setExpandedDetailPanels = React26.useCallback((ids) => {
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        detailPanel: _extends({}, state.detailPanel, {
          expandedRowIds: ids
        })
      });
    });
    apiRef.current.requestPipeProcessorsApplication("rowHeight");
  }, [apiRef]);
  const storeDetailPanelHeight = React26.useCallback((id, height) => {
    const heightCache = gridDetailPanelRawHeightCacheSelector(apiRef);
    if (!heightCache[id] || heightCache[id].height === height) {
      return;
    }
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        detailPanel: _extends({}, state.detailPanel, {
          heightCache: _extends({}, heightCache, {
            [id]: _extends({}, heightCache[id], {
              height
            })
          })
        })
      });
    });
    apiRef.current.requestPipeProcessorsApplication("rowHeight");
  }, [apiRef]);
  const detailPanelPubicApi = {
    toggleDetailPanel,
    getExpandedDetailPanels,
    setExpandedDetailPanels
  };
  const detailPanelPrivateApi = {
    storeDetailPanelHeight
  };
  useGridApiMethod(apiRef, detailPanelPubicApi, "public");
  useGridApiMethod(apiRef, detailPanelPrivateApi, "private");
  React26.useEffect(() => {
    if (props.detailPanelExpandedRowIds) {
      const currentModel = gridDetailPanelExpandedRowIdsSelector(apiRef);
      if (currentModel !== props.detailPanelExpandedRowIds) {
        apiRef.current.setExpandedDetailPanels(props.detailPanelExpandedRowIds);
      }
    }
  }, [apiRef, props.detailPanelExpandedRowIds]);
  const updateCaches = React26.useCallback(() => {
    if (!props.getDetailPanelContent) {
      return;
    }
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        detailPanel: _extends({}, state.detailPanel, cacheContentAndHeight(apiRef, props.getDetailPanelContent, props.getDetailPanelHeight, state.detailPanel.heightCache))
      });
    });
  }, [apiRef, props.getDetailPanelContent, props.getDetailPanelHeight]);
  useGridEvent(apiRef, "sortedRowsSet", updateCaches);
  const previousGetDetailPanelContentProp = React26.useRef(void 0);
  const previousGetDetailPanelHeightProp = React26.useRef(void 0);
  const updateCachesIfNeeded = React26.useCallback(() => {
    if (props.getDetailPanelContent === previousGetDetailPanelContentProp.current && props.getDetailPanelHeight === previousGetDetailPanelHeightProp.current) {
      return;
    }
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        detailPanel: _extends({}, state.detailPanel, cacheContentAndHeight(apiRef, props.getDetailPanelContent, props.getDetailPanelHeight, state.detailPanel.heightCache))
      });
    });
    previousGetDetailPanelContentProp.current = props.getDetailPanelContent;
    previousGetDetailPanelHeightProp.current = props.getDetailPanelHeight;
  }, [apiRef, props.getDetailPanelContent, props.getDetailPanelHeight]);
  const addDetailHeight = React26.useCallback((initialValue, row) => {
    var _a;
    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(apiRef);
    if (!expandedRowIds || !expandedRowIds.has(row.id)) {
      initialValue.detail = 0;
      return initialValue;
    }
    updateCachesIfNeeded();
    const heightCache = gridDetailPanelRawHeightCacheSelector(apiRef);
    initialValue.detail = ((_a = heightCache[row.id]) == null ? void 0 : _a.height) ?? 0;
    return initialValue;
  }, [apiRef, updateCachesIfNeeded]);
  const enabled = props.getDetailPanelContent !== void 0;
  useGridRegisterPipeProcessor(apiRef, "rowHeight", addDetailHeight, enabled);
  const isFirstRender = React26.useRef(true);
  if (isFirstRender.current) {
    updateCachesIfNeeded();
  }
  React26.useEffect(() => {
    if (!isFirstRender.current) {
      updateCachesIfNeeded();
    }
    isFirstRender.current = false;
  }, [apiRef, updateCachesIfNeeded]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/detailPanel/useGridDetailPanelPreProcessors.js
var React27 = __toESM(require_react(), 1);
var useGridDetailPanelPreProcessors = (privateApiRef, props) => {
  const addToggleColumn = React27.useCallback((columnsState) => {
    const detailPanelToggleColumn = _extends({}, GRID_DETAIL_PANEL_TOGGLE_COL_DEF, {
      headerName: privateApiRef.current.getLocaleText("detailPanelToggle")
    });
    const shouldHaveToggleColumn = !!props.getDetailPanelContent;
    const hasToggleColumn = columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] != null;
    if (shouldHaveToggleColumn && !hasToggleColumn) {
      columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] = detailPanelToggleColumn;
      columnsState.orderedFields = [GRID_DETAIL_PANEL_TOGGLE_FIELD, ...columnsState.orderedFields];
    } else if (!shouldHaveToggleColumn && hasToggleColumn) {
      delete columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD];
      columnsState.orderedFields = columnsState.orderedFields.filter((field) => field !== GRID_DETAIL_PANEL_TOGGLE_FIELD);
    } else if (shouldHaveToggleColumn && hasToggleColumn) {
      columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD] = _extends({}, detailPanelToggleColumn, columnsState.lookup[GRID_DETAIL_PANEL_TOGGLE_FIELD]);
      if (!props.columns.some((col) => col.field === GRID_DETAIL_PANEL_TOGGLE_FIELD)) {
        columnsState.orderedFields = [GRID_DETAIL_PANEL_TOGGLE_FIELD, ...columnsState.orderedFields.filter((field) => field !== GRID_DETAIL_PANEL_TOGGLE_FIELD)];
      }
    }
    return columnsState;
  }, [privateApiRef, props.columns, props.getDetailPanelContent]);
  const addExpandedClassToRow = React27.useCallback((classes, id) => {
    if (props.getDetailPanelContent == null) {
      return classes;
    }
    const expandedRowIds = gridDetailPanelExpandedRowIdsSelector(privateApiRef);
    if (!expandedRowIds.has(id)) {
      return classes;
    }
    return [...classes, gridClasses["row--detailPanelExpanded"]];
  }, [privateApiRef, props.getDetailPanelContent]);
  useGridRegisterPipeProcessor(privateApiRef, "hydrateColumns", addToggleColumn);
  useGridRegisterPipeProcessor(privateApiRef, "rowClassName", addExpandedClassToRow);
};

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js
var React29 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js
var React28 = __toESM(require_react(), 1);
var useEnhancedEffect = typeof window !== "undefined" ? React28.useLayoutEffect : React28.useEffect;
var useEnhancedEffect_default2 = useEnhancedEffect;

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js
function useEventCallback(fn) {
  const ref = React29.useRef(fn);
  useEnhancedEffect_default2(() => {
    ref.current = fn;
  });
  return React29.useRef((...args) => (
    // @ts-expect-error hide `this`
    (0, ref.current)(...args)
  )).current;
}
var useEventCallback_default = useEventCallback;

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/infiniteLoader/useGridInfiniteLoader.js
var useGridInfiniteLoader = (apiRef, props) => {
  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);
  const currentPage = useGridVisibleRows(apiRef, props);
  const isEnabled = props.rowsLoadingMode === "client" && !!props.onRowsScrollEnd;
  const handleLoadMoreRows = useEventCallback_default(() => {
    const viewportPageSize = apiRef.current.getViewportPageSize();
    const rowScrollEndParams = {
      visibleColumns,
      viewportPageSize,
      visibleRowsCount: currentPage.rows.length
    };
    apiRef.current.publishEvent("rowsScrollEnd", rowScrollEndParams);
  });
  useGridEventPriority(apiRef, "rowsScrollEnd", props.onRowsScrollEnd);
  useGridEvent(apiRef, "rowsScrollEndIntersection", runIf(isEnabled, handleLoadMoreRows));
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowReorder/useGridRowReorder.js
var React33 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js
var React30 = __toESM(require_react(), 1);
var UNINITIALIZED = {};
function useLazyRef(init, initArg) {
  const ref = React30.useRef(UNINITIALIZED);
  if (ref.current === UNINITIALIZED) {
    ref.current = init(initArg);
  }
  return ref;
}

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useOnMount/useOnMount.js
var React31 = __toESM(require_react(), 1);
var EMPTY = [];
function useOnMount(fn) {
  React31.useEffect(fn, EMPTY);
}

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/useTimeout/useTimeout.js
var Timeout = class _Timeout {
  constructor() {
    __publicField(this, "currentId", null);
    __publicField(this, "clear", () => {
      if (this.currentId !== null) {
        clearTimeout(this.currentId);
        this.currentId = null;
      }
    });
    __publicField(this, "disposeEffect", () => {
      return this.clear;
    });
  }
  static create() {
    return new _Timeout();
  }
  /**
   * Executes `fn` after `delay`, clearing any previously scheduled call.
   */
  start(delay, fn) {
    this.clear();
    this.currentId = setTimeout(() => {
      this.currentId = null;
      fn();
    }, delay);
  }
};
function useTimeout2() {
  const timeout = useLazyRef(Timeout.create).current;
  useOnMount(timeout.disposeEffect);
  return timeout;
}

// node_modules/@mui/x-data-grid-pro/esm/components/GridRowReorderCell.js
var React32 = __toESM(require_react(), 1);
var import_prop_types8 = __toESM(require_prop_types(), 1);
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses8 = (ownerState) => {
  const {
    isDraggable,
    classes
  } = ownerState;
  const slots = {
    root: ["rowReorderCell", isDraggable && "rowReorderCell--draggable"],
    placeholder: ["rowReorderCellPlaceholder"],
    icon: ["rowReorderIcon"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var RowReorderIcon = styled_default2("svg", {
  name: "MuiDataGrid",
  slot: "RowReorderIcon"
})({
  color: vars.colors.foreground.muted
});
function GridRowReorderCell(params) {
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps2();
  const sortModel = useGridSelector(apiRef, gridSortModelSelector);
  const editRowsState = useGridSelector(apiRef, gridEditRowsStateSelector);
  const cellValue = (
    // eslint-disable-next-line no-underscore-dangle
    params.row.__reorder__ || (params.rowNode.type === "group" ? params.rowNode.groupingKey ?? params.id : params.id)
  );
  const cellRef = React32.useRef(null);
  const listenerNodeRef = React32.useRef(null);
  const isDraggable = React32.useMemo(() => !!rootProps.rowReordering && !sortModel.length && !rootProps.treeData && Object.keys(editRowsState).length === 0, [rootProps.rowReordering, sortModel, rootProps.treeData, editRowsState]);
  const ownerState = {
    isDraggable,
    classes: rootProps.classes
  };
  const classes = useUtilityClasses8(ownerState);
  const publish = React32.useCallback((eventName, propHandler) => (event) => {
    if (isEventTargetInPortal(event)) {
      return;
    }
    if (!apiRef.current.getRow(params.id)) {
      return;
    }
    apiRef.current.publishEvent(eventName, apiRef.current.getRowParams(params.id), event);
    if (propHandler) {
      propHandler(event);
    }
  }, [apiRef, params.id]);
  const handleMouseDown = React32.useCallback(() => {
    var _a, _b;
    (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.classList.add(gridClasses["root--disableUserSelection"]);
  }, [apiRef]);
  const handleMouseUp = React32.useCallback(() => {
    var _a, _b;
    (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.classList.remove(gridClasses["root--disableUserSelection"]);
  }, [apiRef]);
  const handleDragEnd = React32.useCallback((event) => {
    handleMouseUp();
    if (apiRef.current.getRow(params.id)) {
      apiRef.current.publishEvent("rowDragEnd", apiRef.current.getRowParams(params.id), event);
    }
    listenerNodeRef.current.removeEventListener("dragend", handleDragEnd);
    listenerNodeRef.current = null;
  }, [apiRef, params.id, handleMouseUp]);
  const handleDragStart = React32.useCallback((event) => {
    if (!cellRef.current) {
      return;
    }
    publish("rowDragStart")(event);
    cellRef.current.addEventListener("dragend", handleDragEnd);
    listenerNodeRef.current = cellRef.current;
  }, [publish, handleDragEnd]);
  const draggableEventHandlers = isDraggable ? {
    onDragStart: handleDragStart,
    onDragOver: publish("rowDragOver"),
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp
  } : null;
  if (params.rowNode.type === "footer") {
    return null;
  }
  return (0, import_jsx_runtime18.jsxs)("div", _extends({
    ref: cellRef,
    className: classes.root,
    draggable: isDraggable
  }, draggableEventHandlers, {
    children: [(0, import_jsx_runtime18.jsx)(RowReorderIcon, {
      as: rootProps.slots.rowReorderIcon,
      ownerState,
      className: classes.icon,
      fontSize: "small"
    }), (0, import_jsx_runtime18.jsx)("div", {
      className: classes.placeholder,
      children: cellValue
    })]
  }));
}
true ? GridRowReorderCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: import_prop_types8.default.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: import_prop_types8.default.oneOf(["edit", "view"]).isRequired,
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: import_prop_types8.default.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: import_prop_types8.default.string.isRequired,
  /**
   * A ref allowing to set imperative focus.
   * It can be passed to the element that should receive focus.
   * @ignore - do not document.
   */
  focusElementRef: import_prop_types8.default.oneOfType([import_prop_types8.default.func, import_prop_types8.default.shape({
    current: import_prop_types8.default.shape({
      focus: import_prop_types8.default.func.isRequired
    })
  })]),
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: import_prop_types8.default.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: import_prop_types8.default.bool.isRequired,
  /**
   * The grid row id.
   */
  id: import_prop_types8.default.oneOfType([import_prop_types8.default.number, import_prop_types8.default.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: import_prop_types8.default.bool,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: import_prop_types8.default.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: import_prop_types8.default.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: import_prop_types8.default.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: import_prop_types8.default.any
} : void 0;
var renderRowReorderCell = (params) => {
  if (params.rowNode.type === "footer" || params.rowNode.type === "pinnedRow") {
    return null;
  }
  return (0, import_jsx_runtime18.jsx)(GridRowReorderCell, _extends({}, params));
};
if (true) renderRowReorderCell.displayName = "renderRowReorderCell";

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowReorder/gridRowReorderColDef.js
var GRID_REORDER_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {
  type: "custom",
  field: "__reorder__",
  sortable: false,
  filterable: false,
  width: 50,
  align: "center",
  headerAlign: "center",
  disableColumnMenu: true,
  disableExport: true,
  disableReorder: true,
  resizable: false,
  // @ts-ignore
  aggregable: false,
  renderHeader: () => " ",
  renderCell: renderRowReorderCell,
  rowSpanValueGetter: (_, row, __, apiRef) => gridRowIdSelector(apiRef, row)
});

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowReorder/useGridRowReorder.js
var EMPTY_REORDER_STATE = {
  previousTargetId: null,
  dragDirection: null,
  previousDropPosition: null
};
var useUtilityClasses9 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    rowDragging: ["row--dragging"],
    rowDropAbove: ["row--dropAbove"],
    rowDropBelow: ["row--dropBelow"],
    rowBeingDragged: ["row--beingDragged"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var rowReorderStateInitializer = (state) => _extends({}, state, {
  rowReorder: {
    isActive: false
  }
});
var useGridRowReorder = (apiRef, props) => {
  const logger = useGridLogger(apiRef, "useGridRowReorder");
  const sortModel = useGridSelector(apiRef, gridSortModelSelector);
  const dragRowNode = React33.useRef(null);
  const originRowIndex = React33.useRef(null);
  const removeDnDStylesTimeout = React33.useRef(void 0);
  const previousDropIndicatorRef = React33.useRef(null);
  const ownerState = {
    classes: props.classes
  };
  const classes = useUtilityClasses9(ownerState);
  const [dragRowId, setDragRowId] = React33.useState("");
  const sortedRowIndexLookup = useGridSelector(apiRef, gridExpandedSortedRowIndexLookupSelector);
  const timeoutRowId = React33.useRef("");
  const timeout = useTimeout2();
  const previousReorderState = React33.useRef(EMPTY_REORDER_STATE);
  const dropTarget = React33.useRef({
    targetRowId: null,
    targetRowIndex: null,
    dropPosition: null
  });
  React33.useEffect(() => {
    return () => {
      clearTimeout(removeDnDStylesTimeout.current);
    };
  }, []);
  const isRowReorderDisabled = React33.useMemo(() => {
    return !props.rowReordering || !!sortModel.length || props.treeData;
  }, [props.rowReordering, sortModel, props.treeData]);
  const applyDropIndicator = React33.useCallback((targetRowId, position) => {
    var _a, _b;
    if (previousDropIndicatorRef.current) {
      previousDropIndicatorRef.current.classList.remove(classes.rowDropAbove, classes.rowDropBelow);
      previousDropIndicatorRef.current = null;
    }
    if (targetRowId !== void 0 && position !== null) {
      const targetRow = (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.querySelector(`[data-id="${targetRowId}"]`);
      if (targetRow) {
        targetRow.classList.add(position === "above" ? classes.rowDropAbove : classes.rowDropBelow);
        previousDropIndicatorRef.current = targetRow;
      }
    }
  }, [apiRef, classes]);
  const applyDraggedState = React33.useCallback((rowId, isDragged) => {
    var _a, _b;
    if (rowId) {
      const draggedRow = (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.querySelector(`[data-id="${rowId}"]`);
      if (draggedRow) {
        if (isDragged) {
          draggedRow.classList.add(classes.rowBeingDragged);
        } else {
          draggedRow.classList.remove(classes.rowBeingDragged);
        }
      }
    }
  }, [apiRef, classes.rowBeingDragged]);
  const applyRowAnimation = React33.useCallback((callback) => {
    var _a;
    const rootElement = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current;
    if (!rootElement) {
      return;
    }
    const visibleRows = rootElement.querySelectorAll("[data-id]");
    if (!visibleRows.length) {
      return;
    }
    const rowsArray = Array.from(visibleRows);
    const initialPositions = /* @__PURE__ */ new Map();
    rowsArray.forEach((row) => {
      const rowId = row.getAttribute("data-id");
      if (rowId) {
        initialPositions.set(rowId, row.getBoundingClientRect());
      }
    });
    callback();
    requestAnimationFrame(() => {
      const newRows = rootElement.querySelectorAll("[data-id]");
      const animations = [];
      newRows.forEach((row) => {
        const rowId = row.getAttribute("data-id");
        if (!rowId) {
          return;
        }
        const prevRect = initialPositions.get(rowId);
        if (!prevRect) {
          return;
        }
        const currentRect = row.getBoundingClientRect();
        const deltaY = prevRect.top - currentRect.top;
        if (Math.abs(deltaY) > 1) {
          const animation = row.animate([{
            transform: `translateY(${deltaY}px)`
          }, {
            transform: "translateY(0)"
          }], {
            duration: 200,
            easing: "ease-in-out",
            fill: "forwards"
          });
          animations.push(animation);
        }
      });
      if (animations.length > 0) {
        Promise.allSettled(animations.map((a) => a.finished)).then(() => {
        });
      }
    });
  }, [apiRef]);
  const handleDragStart = React33.useCallback((params, event) => {
    const editRowsState = gridEditRowsStateSelector(apiRef);
    event.dataTransfer.effectAllowed = "copy";
    if (isRowReorderDisabled || Object.keys(editRowsState).length !== 0) {
      return;
    }
    if (timeoutRowId.current) {
      timeout.clear();
      timeoutRowId.current = "";
    }
    logger.debug(`Start dragging row ${params.id}`);
    event.stopPropagation();
    apiRef.current.setRowDragActive(true);
    dragRowNode.current = event.currentTarget;
    dragRowNode.current.classList.add(classes.rowDragging);
    setDragRowId(params.id);
    applyDraggedState(params.id, true);
    removeDnDStylesTimeout.current = setTimeout(() => {
      dragRowNode.current.classList.remove(classes.rowDragging);
    });
    originRowIndex.current = sortedRowIndexLookup[params.id];
    apiRef.current.setCellFocus(params.id, GRID_REORDER_COL_DEF.field);
  }, [apiRef, isRowReorderDisabled, logger, classes.rowDragging, applyDraggedState, sortedRowIndexLookup, timeout]);
  const handleDragOver = React33.useCallback((params, event) => {
    if (dragRowId === "") {
      return;
    }
    const targetNode = gridRowNodeSelector(apiRef, params.id);
    const sourceNode = gridRowNodeSelector(apiRef, dragRowId);
    if (!sourceNode || !targetNode || targetNode.type === "footer" || targetNode.type === "pinnedRow" || !event.target) {
      return;
    }
    const targetRect = event.target.getBoundingClientRect();
    const relativeY = Math.floor(event.clientY - targetRect.top);
    const midPoint = Math.floor(targetRect.height / 2);
    logger.debug(`Dragging over row ${params.id}`);
    event.preventDefault();
    event.stopPropagation();
    if (timeoutRowId.current && timeoutRowId.current !== params.id) {
      timeout.clear();
      timeoutRowId.current = "";
    }
    if (targetNode.type === "group" && targetNode.depth < sourceNode.depth && !targetNode.childrenExpanded && !timeoutRowId.current) {
      timeout.start(500, () => {
        const rowNode = gridRowNodeSelector(apiRef, params.id);
        apiRef.current.setRowChildrenExpansion(params.id, !rowNode.childrenExpanded);
      });
      timeoutRowId.current = params.id;
      return;
    }
    const targetRowIndex = sortedRowIndexLookup[params.id];
    const sourceRowIndex = sortedRowIndexLookup[dragRowId];
    const dropPosition = relativeY < midPoint ? "above" : "below";
    const currentReorderState = {
      dragDirection: targetRowIndex < sourceRowIndex ? "up" : "down",
      previousTargetId: params.id,
      previousDropPosition: dropPosition
    };
    if (previousReorderState.current.previousTargetId !== params.id || previousReorderState.current.previousDropPosition !== dropPosition) {
      const isSameNode = targetRowIndex === sourceRowIndex;
      const isAdjacentPosition = dropPosition === "above" && targetRowIndex === sourceRowIndex + 1 || dropPosition === "below" && targetRowIndex === sourceRowIndex - 1;
      const validatedIndex = apiRef.current.unstable_applyPipeProcessors("getRowReorderTargetIndex", -1, {
        sourceRowId: dragRowId,
        targetRowId: params.id,
        dropPosition,
        dragDirection: currentReorderState.dragDirection
      });
      if (validatedIndex !== -1 || isAdjacentPosition || isSameNode) {
        dropTarget.current = {
          targetRowId: params.id,
          targetRowIndex,
          dropPosition
        };
        applyDropIndicator(params.id, dropPosition);
      } else {
        dropTarget.current = {
          targetRowId: null,
          targetRowIndex: null,
          dropPosition: null
        };
        applyDropIndicator(null, null);
      }
      previousReorderState.current = currentReorderState;
    }
    if (dropTarget.current.targetRowId === null) {
      event.dataTransfer.dropEffect = "none";
    } else {
      event.dataTransfer.dropEffect = "copy";
    }
  }, [dragRowId, apiRef, logger, timeout, sortedRowIndexLookup, applyDropIndicator]);
  const handleDragEnd = React33.useCallback((_, event) => {
    const editRowsState = gridEditRowsStateSelector(apiRef);
    if (dragRowId === "" || isRowReorderDisabled || Object.keys(editRowsState).length !== 0) {
      return;
    }
    if (timeoutRowId.current) {
      timeout.clear();
      timeoutRowId.current = "";
    }
    logger.debug("End dragging row");
    event.preventDefault();
    event.stopPropagation();
    clearTimeout(removeDnDStylesTimeout.current);
    dragRowNode.current = null;
    const dragDirection = previousReorderState.current.dragDirection;
    previousReorderState.current = EMPTY_REORDER_STATE;
    applyDropIndicator(null, null);
    applyDraggedState(dragRowId, false);
    apiRef.current.setRowDragActive(false);
    if (!event.dataTransfer || event.dataTransfer.dropEffect === "none") {
      dropTarget.current = {
        targetRowId: null,
        targetRowIndex: null,
        dropPosition: null
      };
      originRowIndex.current = null;
      setDragRowId("");
      return;
    }
    if (dropTarget.current.targetRowIndex !== null && dropTarget.current.targetRowId !== null) {
      const sourceRowIndex = originRowIndex.current;
      const targetRowIndex = dropTarget.current.targetRowIndex;
      const validatedIndex = apiRef.current.unstable_applyPipeProcessors("getRowReorderTargetIndex", targetRowIndex, {
        sourceRowId: dragRowId,
        targetRowId: dropTarget.current.targetRowId,
        dropPosition: dropTarget.current.dropPosition,
        dragDirection
      });
      if (validatedIndex !== -1) {
        applyRowAnimation(() => {
          apiRef.current.setRowIndex(dragRowId, validatedIndex);
          const rowOrderChangeParams = {
            row: apiRef.current.getRow(dragRowId),
            targetIndex: validatedIndex,
            oldIndex: sourceRowIndex
          };
          apiRef.current.publishEvent("rowOrderChange", rowOrderChangeParams);
        });
      }
    }
    dropTarget.current = {
      targetRowId: null,
      targetRowIndex: null,
      dropPosition: null
    };
    setDragRowId("");
  }, [apiRef, dragRowId, isRowReorderDisabled, logger, applyDropIndicator, applyDraggedState, timeout, applyRowAnimation]);
  const getRowReorderTargetIndex = React33.useCallback((initialValue, {
    sourceRowId,
    targetRowId,
    dropPosition,
    dragDirection
  }) => {
    if (gridRowMaximumTreeDepthSelector(apiRef) > 1) {
      return initialValue;
    }
    const targetRowIndex = sortedRowIndexLookup[targetRowId];
    const sourceRowIndex = sortedRowIndexLookup[sourceRowId];
    const isAdjacentNode = dropPosition === "above" && targetRowIndex === sourceRowIndex + 1 || // dragging to immediately below (above next row)
    dropPosition === "below" && targetRowIndex === sourceRowIndex - 1;
    if (isAdjacentNode || sourceRowIndex === targetRowIndex) {
      return -1;
    }
    let finalTargetIndex;
    if (dragDirection === "up") {
      finalTargetIndex = dropPosition === "above" ? targetRowIndex : targetRowIndex + 1;
    } else {
      finalTargetIndex = dropPosition === "above" ? targetRowIndex - 1 : targetRowIndex;
    }
    return finalTargetIndex;
  }, [apiRef, sortedRowIndexLookup]);
  useGridRegisterPipeProcessor(apiRef, "getRowReorderTargetIndex", getRowReorderTargetIndex);
  useGridEvent(apiRef, "rowDragStart", handleDragStart);
  useGridEvent(apiRef, "rowDragOver", handleDragOver);
  useGridEvent(apiRef, "rowDragEnd", handleDragEnd);
  useGridEvent(apiRef, "cellDragOver", handleDragOver);
  useGridEventPriority(apiRef, "rowOrderChange", props.onRowOrderChange);
  const setRowDragActive = React33.useCallback((isActive) => {
    apiRef.current.setState((state) => _extends({}, state, {
      rowReorder: _extends({}, state.rowReorder, {
        isActive
      })
    }));
  }, [apiRef]);
  useGridApiMethod(apiRef, {
    setRowDragActive
  }, "private");
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowReorder/useGridRowReorderPreProcessors.js
var React34 = __toESM(require_react(), 1);
var useUtilityClasses10 = (ownerState) => {
  const {
    classes
  } = ownerState;
  return React34.useMemo(() => {
    const slots = {
      rowReorderCellContainer: ["rowReorderCellContainer"],
      columnHeaderReorder: ["columnHeaderReorder"]
    };
    return composeClasses(slots, getDataGridUtilityClass, classes);
  }, [classes]);
};
var useGridRowReorderPreProcessors = (privateApiRef, props) => {
  const ownerState = {
    classes: props.classes
  };
  const classes = useUtilityClasses10(ownerState);
  const updateReorderColumn = React34.useCallback((columnsState) => {
    const reorderColumn = _extends({}, GRID_REORDER_COL_DEF, {
      cellClassName: classes.rowReorderCellContainer,
      headerClassName: classes.columnHeaderReorder,
      headerName: privateApiRef.current.getLocaleText("rowReorderingHeaderName")
    });
    const shouldHaveReorderColumn = props.rowReordering;
    const hasReorderColumn = columnsState.lookup[reorderColumn.field] != null;
    if (shouldHaveReorderColumn && !hasReorderColumn) {
      columnsState.lookup[reorderColumn.field] = reorderColumn;
      columnsState.orderedFields = [reorderColumn.field, ...columnsState.orderedFields];
    } else if (!shouldHaveReorderColumn && hasReorderColumn) {
      delete columnsState.lookup[reorderColumn.field];
      columnsState.orderedFields = columnsState.orderedFields.filter((field) => field !== reorderColumn.field);
    } else if (shouldHaveReorderColumn && hasReorderColumn) {
      columnsState.lookup[reorderColumn.field] = _extends({}, reorderColumn, columnsState.lookup[reorderColumn.field]);
      if (!props.columns.some((col) => col.field === GRID_REORDER_COL_DEF.field)) {
        columnsState.orderedFields = [reorderColumn.field, ...columnsState.orderedFields.filter((field) => field !== reorderColumn.field)];
      }
    }
    return columnsState;
  }, [privateApiRef, classes, props.columns, props.rowReordering]);
  useGridRegisterPipeProcessor(privateApiRef, "hydrateColumns", updateReorderColumn);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/treeData/useGridTreeData.js
var React35 = __toESM(require_react(), 1);
var useGridTreeData = (apiRef, props) => {
  const handleCellKeyDown = React35.useCallback((params, event) => {
    const cellParams = apiRef.current.getCellParams(params.id, params.field);
    if (cellParams.colDef.field === GRID_TREE_DATA_GROUPING_FIELD && (event.key === " " || event.key === "Enter") && !event.shiftKey) {
      if (params.rowNode.type !== "group") {
        return;
      }
      if (props.dataSource && !params.rowNode.childrenExpanded) {
        apiRef.current.dataSource.fetchRows(params.id);
        return;
      }
      apiRef.current.setRowChildrenExpansion(params.id, !params.rowNode.childrenExpanded);
    }
  }, [apiRef, props.dataSource]);
  useGridEvent(apiRef, "cellKeyDown", handleCellKeyDown);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/treeData/useGridTreeDataPreProcessors.js
var React37 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/components/GridTreeDataGroupingCell.js
var React36 = __toESM(require_react(), 1);
var import_prop_types9 = __toESM(require_prop_types(), 1);
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses11 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["treeDataGroupingCell"],
    toggle: ["treeDataGroupingCellToggle"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridTreeDataGroupingCell(props) {
  var _a;
  const {
    id,
    field,
    formattedValue,
    rowNode,
    hideDescendantCount,
    offsetMultiplier = 2
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const classes = useUtilityClasses11(rootProps);
  const filteredDescendantCountLookup = useGridSelector(apiRef, gridFilteredDescendantCountLookupSelector);
  const filteredDescendantCount = filteredDescendantCountLookup[rowNode.id] ?? 0;
  const Icon = rowNode.childrenExpanded ? rootProps.slots.treeDataCollapseIcon : rootProps.slots.treeDataExpandIcon;
  const handleClick = (event) => {
    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };
  return (0, import_jsx_runtime19.jsxs)("div", {
    className: classes.root,
    style: {
      marginLeft: vars.spacing(rowNode.depth * offsetMultiplier)
    },
    children: [(0, import_jsx_runtime19.jsx)("div", {
      className: classes.toggle,
      children: filteredDescendantCount > 0 && (0, import_jsx_runtime19.jsx)(rootProps.slots.baseIconButton, _extends({
        size: "small",
        onClick: handleClick,
        tabIndex: -1,
        "aria-label": rowNode.childrenExpanded ? apiRef.current.getLocaleText("treeDataCollapse") : apiRef.current.getLocaleText("treeDataExpand")
      }, (_a = rootProps == null ? void 0 : rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
        children: (0, import_jsx_runtime19.jsx)(Icon, {
          fontSize: "inherit"
        })
      }))
    }), (0, import_jsx_runtime19.jsxs)("span", {
      children: [formattedValue === void 0 ? rowNode.groupingKey : formattedValue, !hideDescendantCount && filteredDescendantCount > 0 ? ` (${filteredDescendantCount})` : ""]
    })]
  });
}
true ? GridTreeDataGroupingCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: import_prop_types9.default.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: import_prop_types9.default.oneOf(["edit", "view"]).isRequired,
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: import_prop_types9.default.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: import_prop_types9.default.string.isRequired,
  /**
   * A ref allowing to set imperative focus.
   * It can be passed to the element that should receive focus.
   * @ignore - do not document.
   */
  focusElementRef: import_prop_types9.default.oneOfType([import_prop_types9.default.func, import_prop_types9.default.shape({
    current: import_prop_types9.default.shape({
      focus: import_prop_types9.default.func.isRequired
    })
  })]),
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: import_prop_types9.default.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: import_prop_types9.default.bool.isRequired,
  hideDescendantCount: import_prop_types9.default.bool,
  /**
   * The grid row id.
   */
  id: import_prop_types9.default.oneOfType([import_prop_types9.default.number, import_prop_types9.default.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: import_prop_types9.default.bool,
  /**
   * The cell offset multiplier used for calculating cell offset (`rowNode.depth * offsetMultiplier` px).
   * @default 2
   */
  offsetMultiplier: import_prop_types9.default.number,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: import_prop_types9.default.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: import_prop_types9.default.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: import_prop_types9.default.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: import_prop_types9.default.any
} : void 0;

// node_modules/@mui/x-data-grid-pro/esm/utils/tree/sortRowTree.js
var Node = class {
  constructor(data, next) {
    this.next = next;
    this.data = data;
  }
  insertAfter(list) {
    if (!list.first || !list.last) {
      return;
    }
    const next = this.next;
    this.next = list.first;
    list.last.next = next;
  }
};
var List = class _List {
  constructor(first, last) {
    this.first = first;
    this.last = last;
  }
  data() {
    const array = [];
    this.forEach((node) => {
      array.push(node.data);
    });
    return array;
  }
  forEach(fn) {
    let current = this.first;
    while (current !== null) {
      fn(current);
      current = current.next;
    }
  }
  static from(array) {
    if (array.length === 0) {
      return new _List(null, null);
    }
    let index = 0;
    const first = new Node(array[index], null);
    let current = first;
    while (index + 1 < array.length) {
      index += 1;
      const node = new Node(array[index], null);
      current.next = node;
      current = node;
    }
    return new _List(first, current);
  }
};
var sortRowTree = (params) => {
  const {
    rowTree,
    disableChildrenSorting,
    sortRowList,
    shouldRenderGroupBelowLeaves
  } = params;
  const sortedGroupedByParentRows = /* @__PURE__ */ new Map();
  const sortGroup = (node) => {
    const shouldSortGroup = !!sortRowList && (!disableChildrenSorting || node.depth === -1);
    let sortedRowIds;
    if (shouldSortGroup) {
      for (let i = 0; i < node.children.length; i += 1) {
        const childNode = rowTree[node.children[i]];
        if (childNode.type === "group") {
          sortGroup(childNode);
        }
      }
      sortedRowIds = sortRowList(node.children.map((childId) => rowTree[childId]));
    } else if (shouldRenderGroupBelowLeaves) {
      const childrenLeaves = [];
      const childrenGroups = [];
      for (let i = 0; i < node.children.length; i += 1) {
        const childId = node.children[i];
        const childNode = rowTree[childId];
        if (childNode.type === "group") {
          sortGroup(childNode);
          childrenGroups.push(childId);
        } else if (childNode.type === "leaf") {
          childrenLeaves.push(childId);
        }
      }
      sortedRowIds = [...childrenLeaves, ...childrenGroups];
    } else {
      for (let i = 0; i < node.children.length; i += 1) {
        const childNode = rowTree[node.children[i]];
        if (childNode.type === "group") {
          sortGroup(childNode);
        }
      }
      sortedRowIds = [...node.children];
    }
    if (node.footerId != null) {
      sortedRowIds.push(node.footerId);
    }
    sortedGroupedByParentRows.set(node.id, sortedRowIds);
  };
  sortGroup(rowTree[GRID_ROOT_GROUP_ID]);
  const rootList = List.from(sortedGroupedByParentRows.get(GRID_ROOT_GROUP_ID));
  rootList.forEach((node) => {
    const children = sortedGroupedByParentRows.get(node.data);
    if (children == null ? void 0 : children.length) {
      node.insertAfter(List.from(children));
    }
  });
  return rootList.data();
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/treeData/useGridTreeDataPreProcessors.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
var _excluded6 = ["hideDescendantCount"];
var useGridTreeDataPreProcessors = (privateApiRef, props) => {
  const setStrategyAvailability = React37.useCallback(() => {
    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.RowTree, TreeDataStrategy.Default, props.treeData && !props.dataSource ? () => true : () => false);
  }, [privateApiRef, props.treeData, props.dataSource]);
  const getGroupingColDef = React37.useCallback(() => {
    const groupingColDefProp = props.groupingColDef;
    let colDefOverride;
    if (typeof groupingColDefProp === "function") {
      const params = {
        groupingName: TreeDataStrategy.Default,
        fields: []
      };
      colDefOverride = groupingColDefProp(params);
    } else {
      colDefOverride = groupingColDefProp;
    }
    const _ref = colDefOverride ?? {}, {
      hideDescendantCount
    } = _ref, colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref, _excluded6);
    const commonProperties = _extends({}, GRID_TREE_DATA_GROUPING_COL_DEF, {
      renderCell: (params) => (0, import_jsx_runtime20.jsx)(GridTreeDataGroupingCell, _extends({}, params, {
        hideDescendantCount
      })),
      headerName: privateApiRef.current.getLocaleText("treeDataGroupingHeaderName")
    });
    return _extends({}, commonProperties, colDefOverrideProperties, GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES);
  }, [privateApiRef, props.groupingColDef]);
  const updateGroupingColumn = React37.useCallback((columnsState) => {
    if (props.dataSource) {
      return columnsState;
    }
    const groupingColDefField = GRID_TREE_DATA_GROUPING_COL_DEF_FORCED_PROPERTIES.field;
    const shouldHaveGroupingColumn = props.treeData;
    const prevGroupingColumn = columnsState.lookup[groupingColDefField];
    if (shouldHaveGroupingColumn) {
      const newGroupingColumn = getGroupingColDef();
      if (prevGroupingColumn) {
        newGroupingColumn.width = prevGroupingColumn.width;
        newGroupingColumn.flex = prevGroupingColumn.flex;
      }
      columnsState.lookup[groupingColDefField] = newGroupingColumn;
      if (prevGroupingColumn == null) {
        columnsState.orderedFields = [groupingColDefField, ...columnsState.orderedFields];
      }
    } else if (!shouldHaveGroupingColumn && prevGroupingColumn) {
      delete columnsState.lookup[groupingColDefField];
      columnsState.orderedFields = columnsState.orderedFields.filter((field) => field !== groupingColDefField);
    }
    return columnsState;
  }, [props.treeData, props.dataSource, getGroupingColDef]);
  const createRowTreeForTreeData = React37.useCallback((params) => {
    if (!props.getTreeDataPath) {
      throw new Error("MUI X: No getTreeDataPath given.");
    }
    const getRowTreeBuilderNode = (rowId) => ({
      id: rowId,
      path: props.getTreeDataPath(params.dataRowIdToModelLookup[rowId]).map((key) => ({
        key,
        field: null
      }))
    });
    const onDuplicatePath = (firstId, secondId, path) => {
      throw new Error(["MUI X: The path returned by `getTreeDataPath` should be unique.", `The rows with id #${firstId} and #${secondId} have the same.`, `Path: ${JSON.stringify(path.map((step) => step.key))}.`].join("\n"));
    };
    if (params.updates.type === "full") {
      return createRowTree({
        previousTree: params.previousTree,
        nodes: params.updates.rows.map(getRowTreeBuilderNode),
        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
        isGroupExpandedByDefault: props.isGroupExpandedByDefault,
        groupingName: TreeDataStrategy.Default,
        onDuplicatePath
      });
    }
    return updateRowTree({
      nodes: {
        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),
        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),
        removed: params.updates.actions.remove
      },
      previousTree: params.previousTree,
      previousTreeDepth: params.previousTreeDepths,
      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
      isGroupExpandedByDefault: props.isGroupExpandedByDefault,
      groupingName: TreeDataStrategy.Default
    });
  }, [props.getTreeDataPath, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);
  const filterRows = React37.useCallback((params) => {
    const rowTree = gridRowTreeSelector(privateApiRef);
    return filterRowTreeFromTreeData({
      rowTree,
      isRowMatchingFilters: params.isRowMatchingFilters,
      disableChildrenFiltering: props.disableChildrenFiltering,
      filterModel: params.filterModel,
      apiRef: privateApiRef
    });
  }, [privateApiRef, props.disableChildrenFiltering]);
  const sortRows = React37.useCallback((params) => {
    const rowTree = gridRowTreeSelector(privateApiRef);
    return sortRowTree({
      rowTree,
      sortRowList: params.sortRowList,
      disableChildrenSorting: props.disableChildrenSorting,
      shouldRenderGroupBelowLeaves: false
    });
  }, [privateApiRef, props.disableChildrenSorting]);
  useGridRegisterPipeProcessor(privateApiRef, "hydrateColumns", updateGroupingColumn);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, "rowTreeCreation", createRowTreeForTreeData);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, "filtering", filterRows);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, "sorting", sortRows);
  useGridRegisterStrategyProcessor(privateApiRef, TreeDataStrategy.Default, "visibleRowsLookupCreation", getVisibleRowsLookup);
  useFirstRender(() => {
    setStrategyAvailability();
  });
  const isFirstRender = React37.useRef(true);
  React37.useEffect(() => {
    if (!isFirstRender.current) {
      setStrategyAvailability();
    } else {
      isFirstRender.current = false;
    }
  }, [setStrategyAvailability]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowPinning/useGridRowPinning.js
var React38 = __toESM(require_react(), 1);
function createPinnedRowsInternalCache(pinnedRows, getRowId) {
  var _a, _b;
  const cache = {
    topIds: [],
    bottomIds: [],
    idLookup: {}
  };
  (_a = pinnedRows == null ? void 0 : pinnedRows.top) == null ? void 0 : _a.forEach((rowModel) => {
    const id = getRowIdFromRowModel(rowModel, getRowId);
    cache.topIds.push(id);
    cache.idLookup[id] = rowModel;
  });
  (_b = pinnedRows == null ? void 0 : pinnedRows.bottom) == null ? void 0 : _b.forEach((rowModel) => {
    const id = getRowIdFromRowModel(rowModel, getRowId);
    cache.bottomIds.push(id);
    cache.idLookup[id] = rowModel;
  });
  return cache;
}
var rowPinningStateInitializer = (state, props, apiRef) => {
  var _a;
  apiRef.current.caches.pinnedRows = createPinnedRowsInternalCache(props.pinnedRows, props.getRowId);
  return _extends({}, state, {
    rows: _extends({}, state.rows, {
      additionalRowGroups: _extends({}, (_a = state.rows) == null ? void 0 : _a.additionalRowGroups, {
        pinnedRows: {
          top: [],
          bottom: []
        }
      })
    })
  });
};
var useGridRowPinning = (apiRef, props) => {
  const setPinnedRows = React38.useCallback((newPinnedRows) => {
    apiRef.current.caches.pinnedRows = createPinnedRowsInternalCache(newPinnedRows, props.getRowId);
    apiRef.current.requestPipeProcessorsApplication("hydrateRows");
  }, [apiRef, props.getRowId]);
  useGridApiMethod(apiRef, {
    unstable_setPinnedRows: setPinnedRows
  }, "public");
  const isFirstRender = React38.useRef(true);
  React38.useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    apiRef.current.unstable_setPinnedRows(props.pinnedRows);
  }, [apiRef, props.pinnedRows]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/rowPinning/useGridRowPinningPreProcessors.js
var React39 = __toESM(require_react(), 1);
function addPinnedRow({
  groupingParams,
  rowModel,
  rowId,
  position,
  apiRef,
  isAutoGenerated
}) {
  var _a, _b, _c, _d;
  const dataRowIdToModelLookup = _extends({}, groupingParams.dataRowIdToModelLookup);
  const tree = _extends({}, groupingParams.tree);
  const treeDepths = _extends({}, groupingParams.treeDepths);
  const node = {
    type: "pinnedRow",
    id: rowId,
    depth: 0,
    parent: GRID_ROOT_GROUP_ID,
    isAutoGenerated
  };
  insertNodeInTree(node, tree, treeDepths, null);
  if (!isAutoGenerated) {
    dataRowIdToModelLookup[rowId] = rowModel;
  }
  apiRef.current.caches.rows.dataRowIdToModelLookup[rowId] = _extends({}, rowModel);
  const previousPinnedRows = ((_a = groupingParams.additionalRowGroups) == null ? void 0 : _a.pinnedRows) || {};
  const newPinnedRow = {
    id: rowId,
    model: rowModel
  };
  if ((_d = (_c = (_b = groupingParams.additionalRowGroups) == null ? void 0 : _b.pinnedRows) == null ? void 0 : _c[position]) == null ? void 0 : _d.includes(newPinnedRow)) {
    return _extends({}, groupingParams, {
      dataRowIdToModelLookup,
      tree,
      treeDepths
    });
  }
  return _extends({}, groupingParams, {
    dataRowIdToModelLookup,
    tree,
    treeDepths,
    additionalRowGroups: _extends({}, groupingParams.additionalRowGroups, {
      pinnedRows: _extends({}, previousPinnedRows, {
        [position]: [...previousPinnedRows[position] || [], newPinnedRow]
      })
    })
  });
}
var useGridRowPinningPreProcessors = (apiRef) => {
  const prevPinnedRowsCacheRef = React39.useRef(null);
  const addPinnedRows = React39.useCallback((groupingParams) => {
    var _a, _b, _c, _d, _e, _f;
    const pinnedRowsCache = apiRef.current.caches.pinnedRows || {};
    const prevPinnedRowsCache = prevPinnedRowsCacheRef.current;
    prevPinnedRowsCacheRef.current = pinnedRowsCache;
    let newGroupingParams = _extends({}, groupingParams, {
      additionalRowGroups: _extends({}, groupingParams.additionalRowGroups, {
        // reset pinned rows state
        pinnedRows: {}
      })
    });
    if (prevPinnedRowsCache) {
      const pinnedRowCleanup = (rowId) => {
        const node = newGroupingParams.tree[rowId];
        if ((node == null ? void 0 : node.type) === "pinnedRow") {
          delete newGroupingParams.tree[rowId];
          delete newGroupingParams.dataRowIdToModelLookup[rowId];
          delete apiRef.current.caches.rows.dataRowIdToModelLookup[rowId];
        }
      };
      (_a = prevPinnedRowsCache.topIds) == null ? void 0 : _a.forEach(pinnedRowCleanup);
      (_b = prevPinnedRowsCache.bottomIds) == null ? void 0 : _b.forEach(pinnedRowCleanup);
    }
    (_c = pinnedRowsCache.topIds) == null ? void 0 : _c.forEach((rowId) => {
      newGroupingParams = addPinnedRow({
        groupingParams: newGroupingParams,
        rowModel: pinnedRowsCache.idLookup[rowId],
        rowId,
        position: "top",
        apiRef,
        isAutoGenerated: false
      });
    });
    (_d = pinnedRowsCache.bottomIds) == null ? void 0 : _d.forEach((rowId) => {
      newGroupingParams = addPinnedRow({
        groupingParams: newGroupingParams,
        rowModel: pinnedRowsCache.idLookup[rowId],
        rowId,
        position: "bottom",
        apiRef,
        isAutoGenerated: false
      });
    });
    if (((_e = pinnedRowsCache.bottomIds) == null ? void 0 : _e.length) || ((_f = pinnedRowsCache.topIds) == null ? void 0 : _f.length)) {
      const shouldKeepRow = (rowId) => {
        if (newGroupingParams.tree[rowId] && newGroupingParams.tree[rowId].type === "pinnedRow") {
          return false;
        }
        return true;
      };
      const rootGroupNode = newGroupingParams.tree[GRID_ROOT_GROUP_ID];
      newGroupingParams.tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroupNode, {
        children: rootGroupNode.children.filter(shouldKeepRow)
      });
      newGroupingParams.dataRowIds = newGroupingParams.dataRowIds.filter(shouldKeepRow);
    }
    return newGroupingParams;
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "hydrateRows", addPinnedRows);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/useGridLazyLoader.js
var React40 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/utils.js
var findSkeletonRowsSection = ({
  apiRef,
  visibleRows,
  range
}) => {
  var _a, _b;
  let {
    firstRowIndex,
    lastRowIndex
  } = range;
  const visibleRowsSection = visibleRows.slice(range.firstRowIndex, range.lastRowIndex);
  let startIndex = 0;
  let endIndex = visibleRowsSection.length - 1;
  let isSkeletonSectionFound = false;
  while (!isSkeletonSectionFound && firstRowIndex < lastRowIndex) {
    const isStartingWithASkeletonRow = ((_a = gridRowNodeSelector(apiRef, visibleRowsSection[startIndex].id)) == null ? void 0 : _a.type) === "skeletonRow";
    const isEndingWithASkeletonRow = ((_b = gridRowNodeSelector(apiRef, visibleRowsSection[endIndex].id)) == null ? void 0 : _b.type) === "skeletonRow";
    if (isStartingWithASkeletonRow && isEndingWithASkeletonRow) {
      isSkeletonSectionFound = true;
    }
    if (!isStartingWithASkeletonRow) {
      startIndex += 1;
      firstRowIndex += 1;
    }
    if (!isEndingWithASkeletonRow) {
      endIndex -= 1;
      lastRowIndex -= 1;
    }
  }
  return isSkeletonSectionFound ? {
    firstRowIndex,
    lastRowIndex
  } : void 0;
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/useGridLazyLoader.js
var useGridLazyLoader = (privateApiRef, props) => {
  const sortModel = useGridSelector(privateApiRef, gridSortModelSelector);
  const filterModel = useGridSelector(privateApiRef, gridFilterModelSelector);
  const renderedRowsIntervalCache = React40.useRef({
    firstRowToRender: 0,
    lastRowToRender: 0
  });
  const isDisabled = props.rowsLoadingMode !== "server";
  const handleRenderedRowsIntervalChange = React40.useCallback((params) => {
    if (isDisabled) {
      return;
    }
    const fetchRowsParams = {
      firstRowToRender: params.firstRowIndex,
      lastRowToRender: params.lastRowIndex,
      sortModel,
      filterModel
    };
    if (renderedRowsIntervalCache.current.firstRowToRender === params.firstRowIndex && renderedRowsIntervalCache.current.lastRowToRender === params.lastRowIndex) {
      return;
    }
    renderedRowsIntervalCache.current = {
      firstRowToRender: params.firstRowIndex,
      lastRowToRender: params.lastRowIndex
    };
    if (sortModel.length === 0 && filterModel.items.length === 0) {
      const currentVisibleRows = getVisibleRows(privateApiRef, {
        pagination: props.pagination,
        paginationMode: props.paginationMode
      });
      const skeletonRowsSection = findSkeletonRowsSection({
        apiRef: privateApiRef,
        visibleRows: currentVisibleRows.rows,
        range: {
          firstRowIndex: params.firstRowIndex,
          lastRowIndex: params.lastRowIndex
        }
      });
      if (!skeletonRowsSection) {
        return;
      }
      fetchRowsParams.firstRowToRender = skeletonRowsSection.firstRowIndex;
      fetchRowsParams.lastRowToRender = skeletonRowsSection.lastRowIndex;
    }
    privateApiRef.current.publishEvent("fetchRows", fetchRowsParams);
  }, [privateApiRef, isDisabled, props.pagination, props.paginationMode, sortModel, filterModel]);
  const handleGridSortModelChange = React40.useCallback((newSortModel) => {
    if (isDisabled) {
      return;
    }
    privateApiRef.current.requestPipeProcessorsApplication("hydrateRows");
    const renderContext = gridRenderContextSelector(privateApiRef);
    const fetchRowsParams = {
      firstRowToRender: renderContext.firstRowIndex,
      lastRowToRender: renderContext.lastRowIndex,
      sortModel: newSortModel,
      filterModel
    };
    privateApiRef.current.publishEvent("fetchRows", fetchRowsParams);
  }, [privateApiRef, isDisabled, filterModel]);
  const handleGridFilterModelChange = React40.useCallback((newFilterModel) => {
    if (isDisabled) {
      return;
    }
    privateApiRef.current.requestPipeProcessorsApplication("hydrateRows");
    const renderContext = gridRenderContextSelector(privateApiRef);
    const fetchRowsParams = {
      firstRowToRender: renderContext.firstRowIndex,
      lastRowToRender: renderContext.lastRowIndex,
      sortModel,
      filterModel: newFilterModel
    };
    privateApiRef.current.publishEvent("fetchRows", fetchRowsParams);
  }, [privateApiRef, isDisabled, sortModel]);
  useGridEvent(privateApiRef, "renderedRowsIntervalChange", handleRenderedRowsIntervalChange);
  useGridEvent(privateApiRef, "sortModelChange", handleGridSortModelChange);
  useGridEvent(privateApiRef, "filterModelChange", handleGridFilterModelChange);
  useGridEventPriority(privateApiRef, "fetchRows", props.onFetchRows);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/lazyLoader/useGridLazyLoaderPreProcessors.js
var React41 = __toESM(require_react(), 1);
var GRID_SKELETON_ROW_ROOT_ID = "auto-generated-skeleton-row-root";
var getSkeletonRowId = (index) => `${GRID_SKELETON_ROW_ROOT_ID}-${index}`;
var useGridLazyLoaderPreProcessors = (privateApiRef, props) => {
  const addSkeletonRows = React41.useCallback((groupingParams) => {
    const rootGroup = groupingParams.tree[GRID_ROOT_GROUP_ID];
    if (props.rowsLoadingMode !== "server" || !props.rowCount || rootGroup.children.length >= props.rowCount) {
      return groupingParams;
    }
    const tree = _extends({}, groupingParams.tree);
    const rootGroupChildren = [...rootGroup.children];
    for (let i = 0; i < props.rowCount - rootGroup.children.length; i += 1) {
      const skeletonId = getSkeletonRowId(i);
      rootGroupChildren.push(skeletonId);
      const skeletonRowNode = {
        type: "skeletonRow",
        id: skeletonId,
        parent: GRID_ROOT_GROUP_ID,
        depth: 0
      };
      tree[skeletonId] = skeletonRowNode;
    }
    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {
      children: rootGroupChildren
    });
    return _extends({}, groupingParams, {
      tree
    });
  }, [props.rowCount, props.rowsLoadingMode]);
  useGridRegisterPipeProcessor(privateApiRef, "hydrateRows", addSkeletonRows);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideLazyLoader/useGridDataSourceLazyLoader.js
var React42 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/node_modules/@mui/utils/esm/debounce/debounce.js
function debounce(func, wait = 166) {
  let timeout;
  function debounced(...args) {
    const later = () => {
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  }
  debounced.clear = () => {
    clearTimeout(timeout);
  };
  return debounced;
}

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideLazyLoader/useGridDataSourceLazyLoader.js
var LoadingTrigger = (function(LoadingTrigger2) {
  LoadingTrigger2[LoadingTrigger2["VIEWPORT"] = 0] = "VIEWPORT";
  LoadingTrigger2[LoadingTrigger2["SCROLL_END"] = 1] = "SCROLL_END";
  return LoadingTrigger2;
})(LoadingTrigger || {});
var INTERVAL_CACHE_INITIAL_STATE = {
  firstRowToRender: 0,
  lastRowToRender: 0
};
var getSkeletonRowId2 = (index) => `${GRID_SKELETON_ROW_ROOT_ID}-${index}`;
var useGridDataSourceLazyLoader = (privateApiRef, props) => {
  const setStrategyAvailability = React42.useCallback(() => {
    privateApiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.LazyLoading, props.dataSource && props.lazyLoading ? () => true : () => false);
  }, [privateApiRef, props.lazyLoading, props.dataSource]);
  const [lazyLoadingRowsUpdateStrategyActive, setLazyLoadingRowsUpdateStrategyActive] = React42.useState(false);
  const renderedRowsIntervalCache = React42.useRef(INTERVAL_CACHE_INITIAL_STATE);
  const previousLastRowIndex = React42.useRef(0);
  const loadingTrigger = React42.useRef(null);
  const rowsStale = React42.useRef(false);
  const draggedRowId = React42.useRef(null);
  const fetchRows = React42.useCallback((params) => {
    privateApiRef.current.dataSource.fetchRows(GRID_ROOT_GROUP_ID, params);
  }, [privateApiRef]);
  const debouncedFetchRows = React42.useMemo(() => debounce(fetchRows, 0), [fetchRows]);
  const adjustRowParams = React42.useCallback((params) => {
    if (typeof params.start !== "number") {
      return params;
    }
    const paginationModel = gridPaginationModelSelector(privateApiRef);
    return _extends({}, params, {
      start: params.start - params.start % paginationModel.pageSize,
      end: params.end + paginationModel.pageSize - params.end % paginationModel.pageSize - 1
    });
  }, [privateApiRef]);
  const resetGrid = React42.useCallback(() => {
    privateApiRef.current.setLoading(true);
    privateApiRef.current.dataSource.cache.clear();
    rowsStale.current = true;
    previousLastRowIndex.current = 0;
    const paginationModel = gridPaginationModelSelector(privateApiRef);
    const sortModel = gridSortModelSelector(privateApiRef);
    const filterModel = gridFilterModelSelector(privateApiRef);
    const getRowsParams = {
      start: 0,
      end: paginationModel.pageSize - 1,
      sortModel,
      filterModel
    };
    fetchRows(getRowsParams);
  }, [privateApiRef, fetchRows]);
  const ensureValidRowCount = React42.useCallback((previousLoadingTrigger, newLoadingTrigger) => {
    if (previousLoadingTrigger === LoadingTrigger.VIEWPORT && newLoadingTrigger === LoadingTrigger.SCROLL_END) {
      resetGrid();
      return;
    }
    const tree = privateApiRef.current.state.rows.tree;
    const rootGroup = tree[GRID_ROOT_GROUP_ID];
    const rootGroupChildren = [...rootGroup.children];
    const pageRowCount = privateApiRef.current.state.pagination.rowCount;
    const rootChildrenCount = rootGroupChildren.length;
    if (rootChildrenCount > pageRowCount) {
      resetGrid();
    }
  }, [privateApiRef, resetGrid]);
  const addSkeletonRows = React42.useCallback(() => {
    var _a, _b;
    const tree = privateApiRef.current.state.rows.tree;
    const rootGroup = tree[GRID_ROOT_GROUP_ID];
    const rootGroupChildren = [...rootGroup.children];
    const pageRowCount = privateApiRef.current.state.pagination.rowCount;
    const rootChildrenCount = rootGroupChildren.length;
    if (rootChildrenCount === 0) {
      return;
    }
    const pageToSkip = adjustRowParams({
      start: renderedRowsIntervalCache.current.firstRowToRender,
      end: renderedRowsIntervalCache.current.lastRowToRender
    });
    let hasChanged = false;
    const isInitialPage = renderedRowsIntervalCache.current.firstRowToRender === 0 && renderedRowsIntervalCache.current.lastRowToRender === 0;
    for (let i = 0; i < rootChildrenCount; i += 1) {
      if (isInitialPage) {
        break;
      }
      if (pageToSkip.start <= i && i <= pageToSkip.end || ((_a = tree[rootGroupChildren[i]]) == null ? void 0 : _a.type) === "skeletonRow" || // ignore rows that are already skeleton rows
      ((_b = tree[rootGroupChildren[i]]) == null ? void 0 : _b.id) === draggedRowId.current) {
        continue;
      }
      const rowId = tree[rootGroupChildren[i]].id;
      const skeletonRowNode = {
        type: "skeletonRow",
        id: rowId,
        parent: GRID_ROOT_GROUP_ID,
        depth: 0
      };
      tree[rowId] = skeletonRowNode;
      hasChanged = true;
    }
    if (loadingTrigger.current === LoadingTrigger.VIEWPORT) {
      for (let i = 0; i < pageRowCount - rootChildrenCount; i += 1) {
        const skeletonId = getSkeletonRowId2(i + rootChildrenCount);
        rootGroupChildren.push(skeletonId);
        const skeletonRowNode = {
          type: "skeletonRow",
          id: skeletonId,
          parent: GRID_ROOT_GROUP_ID,
          depth: 0
        };
        tree[skeletonId] = skeletonRowNode;
        hasChanged = true;
      }
    }
    if (!hasChanged) {
      return;
    }
    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {
      children: rootGroupChildren
    });
    privateApiRef.current.setState((state) => _extends({}, state, {
      rows: _extends({}, state.rows, {
        tree
      })
    }), "addSkeletonRows");
  }, [privateApiRef, adjustRowParams]);
  const updateLoadingTrigger = React42.useCallback((rowCount) => {
    const newLoadingTrigger = rowCount === -1 ? LoadingTrigger.SCROLL_END : LoadingTrigger.VIEWPORT;
    if (loadingTrigger.current !== null) {
      ensureValidRowCount(loadingTrigger.current, newLoadingTrigger);
    }
    if (loadingTrigger.current !== newLoadingTrigger) {
      loadingTrigger.current = newLoadingTrigger;
    }
  }, [ensureValidRowCount]);
  const handleDataUpdate = React42.useCallback((params) => {
    if ("error" in params) {
      return;
    }
    const {
      response,
      fetchParams
    } = params;
    const pageRowCount = privateApiRef.current.state.pagination.rowCount;
    const tree = privateApiRef.current.state.rows.tree;
    const dataRowIdToModelLookup = privateApiRef.current.state.rows.dataRowIdToModelLookup;
    if (response.rowCount !== void 0 || pageRowCount === void 0) {
      privateApiRef.current.setRowCount(response.rowCount === void 0 ? -1 : response.rowCount);
    }
    if (rowsStale.current && params.fetchParams.start === 0) {
      privateApiRef.current.scroll({
        top: 0
      });
      privateApiRef.current.setRows(response.rows);
    } else {
      const rootGroup = tree[GRID_ROOT_GROUP_ID];
      const rootGroupChildren = [...rootGroup.children];
      const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(privateApiRef);
      const startingIndex = typeof fetchParams.start === "string" ? Math.max(filteredSortedRowIds.indexOf(fetchParams.start), 0) : fetchParams.start;
      let duplicateRowCount = 0;
      response.rows.forEach((row) => {
        const rowId = gridRowIdSelector(privateApiRef, row);
        if (tree[rowId] || dataRowIdToModelLookup[rowId]) {
          const index = rootGroupChildren.indexOf(rowId);
          if (index !== -1) {
            const skeletonId = getSkeletonRowId2(index);
            rootGroupChildren[index] = skeletonId;
            tree[skeletonId] = {
              type: "skeletonRow",
              id: skeletonId,
              parent: GRID_ROOT_GROUP_ID,
              depth: 0
            };
          }
          delete tree[rowId];
          delete dataRowIdToModelLookup[rowId];
          duplicateRowCount += 1;
        }
      });
      if (duplicateRowCount > 0) {
        tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {
          children: rootGroupChildren
        });
        privateApiRef.current.setState((state) => _extends({}, state, {
          rows: _extends({}, state.rows, {
            tree,
            dataRowIdToModelLookup
          })
        }));
      }
      privateApiRef.current.unstable_replaceRows(startingIndex, response.rows);
    }
    rowsStale.current = false;
    if (loadingTrigger.current === null) {
      updateLoadingTrigger(privateApiRef.current.state.pagination.rowCount);
    }
    addSkeletonRows();
    privateApiRef.current.setLoading(false);
    privateApiRef.current.unstable_applyPipeProcessors("processDataSourceRows", {
      params: params.fetchParams,
      response
    }, false);
    privateApiRef.current.requestPipeProcessorsApplication("hydrateRows");
  }, [privateApiRef, updateLoadingTrigger, addSkeletonRows]);
  const handleRowCountChange = React42.useCallback(() => {
    if (rowsStale.current || loadingTrigger.current === null) {
      return;
    }
    updateLoadingTrigger(privateApiRef.current.state.pagination.rowCount);
    addSkeletonRows();
    privateApiRef.current.requestPipeProcessorsApplication("hydrateRows");
  }, [privateApiRef, updateLoadingTrigger, addSkeletonRows]);
  const handleIntersection = useEventCallback_default(() => {
    if (rowsStale.current || loadingTrigger.current !== LoadingTrigger.SCROLL_END) {
      return;
    }
    const renderContext = gridRenderContextSelector(privateApiRef);
    if (previousLastRowIndex.current >= renderContext.lastRowIndex) {
      return;
    }
    previousLastRowIndex.current = renderContext.lastRowIndex;
    const paginationModel = gridPaginationModelSelector(privateApiRef);
    const sortModel = gridSortModelSelector(privateApiRef);
    const filterModel = gridFilterModelSelector(privateApiRef);
    const getRowsParams = {
      start: renderContext.lastRowIndex,
      end: renderContext.lastRowIndex + paginationModel.pageSize - 1,
      sortModel,
      filterModel
    };
    privateApiRef.current.setLoading(true);
    fetchRows(adjustRowParams(getRowsParams));
  });
  const handleRenderedRowsIntervalChange = React42.useCallback((params) => {
    if (rowsStale.current) {
      return;
    }
    const sortModel = gridSortModelSelector(privateApiRef);
    const filterModel = gridFilterModelSelector(privateApiRef);
    const getRowsParams = {
      start: params.firstRowIndex,
      end: params.lastRowIndex - 1,
      sortModel,
      filterModel
    };
    if (renderedRowsIntervalCache.current.firstRowToRender === params.firstRowIndex && renderedRowsIntervalCache.current.lastRowToRender === params.lastRowIndex) {
      return;
    }
    renderedRowsIntervalCache.current = {
      firstRowToRender: params.firstRowIndex,
      lastRowToRender: params.lastRowIndex
    };
    const currentVisibleRows = getVisibleRows(privateApiRef);
    const skeletonRowsSection = findSkeletonRowsSection({
      apiRef: privateApiRef,
      visibleRows: currentVisibleRows.rows,
      range: {
        firstRowIndex: params.firstRowIndex,
        lastRowIndex: params.lastRowIndex - 1
      }
    });
    if (!skeletonRowsSection) {
      return;
    }
    getRowsParams.start = skeletonRowsSection.firstRowIndex;
    getRowsParams.end = skeletonRowsSection.lastRowIndex;
    fetchRows(adjustRowParams(getRowsParams));
  }, [privateApiRef, adjustRowParams, fetchRows]);
  const throttledHandleRenderedRowsIntervalChange = React42.useMemo(() => throttle(handleRenderedRowsIntervalChange, props.lazyLoadingRequestThrottleMs), [props.lazyLoadingRequestThrottleMs, handleRenderedRowsIntervalChange]);
  React42.useEffect(() => {
    return () => {
      throttledHandleRenderedRowsIntervalChange.clear();
    };
  }, [throttledHandleRenderedRowsIntervalChange]);
  const handleGridSortModelChange = React42.useCallback((newSortModel) => {
    rowsStale.current = true;
    throttledHandleRenderedRowsIntervalChange.clear();
    previousLastRowIndex.current = 0;
    const paginationModel = gridPaginationModelSelector(privateApiRef);
    const filterModel = gridFilterModelSelector(privateApiRef);
    const getRowsParams = {
      start: 0,
      end: paginationModel.pageSize - 1,
      sortModel: newSortModel,
      filterModel
    };
    privateApiRef.current.setLoading(true);
    debouncedFetchRows(getRowsParams);
  }, [privateApiRef, debouncedFetchRows, throttledHandleRenderedRowsIntervalChange]);
  const handleGridFilterModelChange = React42.useCallback((newFilterModel) => {
    rowsStale.current = true;
    throttledHandleRenderedRowsIntervalChange.clear();
    previousLastRowIndex.current = 0;
    const paginationModel = gridPaginationModelSelector(privateApiRef);
    const sortModel = gridSortModelSelector(privateApiRef);
    const getRowsParams = {
      start: 0,
      end: paginationModel.pageSize - 1,
      sortModel,
      filterModel: newFilterModel
    };
    privateApiRef.current.setLoading(true);
    debouncedFetchRows(getRowsParams);
  }, [privateApiRef, debouncedFetchRows, throttledHandleRenderedRowsIntervalChange]);
  const handleDragStart = React42.useCallback((row) => {
    draggedRowId.current = row.id;
  }, []);
  const handleDragEnd = React42.useCallback(() => {
    draggedRowId.current = null;
  }, []);
  const handleStrategyActivityChange = React42.useCallback(() => {
    setLazyLoadingRowsUpdateStrategyActive(privateApiRef.current.getActiveStrategy(GridStrategyGroup.DataSource) === DataSourceRowsUpdateStrategy.LazyLoading);
  }, [privateApiRef]);
  useGridRegisterStrategyProcessor(privateApiRef, DataSourceRowsUpdateStrategy.LazyLoading, "dataSourceRowsUpdate", handleDataUpdate);
  useGridEvent(privateApiRef, "strategyAvailabilityChange", handleStrategyActivityChange);
  useGridEvent(privateApiRef, "rowCountChange", runIf(lazyLoadingRowsUpdateStrategyActive, handleRowCountChange));
  useGridEvent(privateApiRef, "rowsScrollEndIntersection", runIf(lazyLoadingRowsUpdateStrategyActive, handleIntersection));
  useGridEvent(privateApiRef, "renderedRowsIntervalChange", runIf(lazyLoadingRowsUpdateStrategyActive, throttledHandleRenderedRowsIntervalChange));
  useGridEvent(privateApiRef, "sortModelChange", runIf(lazyLoadingRowsUpdateStrategyActive, handleGridSortModelChange));
  useGridEvent(privateApiRef, "filterModelChange", runIf(lazyLoadingRowsUpdateStrategyActive, handleGridFilterModelChange));
  useGridEvent(privateApiRef, "rowDragStart", runIf(lazyLoadingRowsUpdateStrategyActive, handleDragStart));
  useGridEvent(privateApiRef, "rowDragEnd", runIf(lazyLoadingRowsUpdateStrategyActive, handleDragEnd));
  React42.useEffect(() => {
    setStrategyAvailability();
  }, [setStrategyAvailability]);
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/serverSideLazyLoader/useGridInfiniteLoadingIntersection.js
var React43 = __toESM(require_react(), 1);
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
var InfiniteLoadingTriggerElement = styled_default2("div")({
  position: "sticky",
  left: 0,
  width: 0,
  height: 0
});
var useGridInfiniteLoadingIntersection = (apiRef, props) => {
  const isReady = useGridSelector(apiRef, gridDimensionsSelector).isReady;
  const observer = React43.useRef(null);
  const updateTargetTimeout = useTimeout();
  const triggerElement = React43.useRef(null);
  const isEnabledClientSide = props.rowsLoadingMode === "client" && !!props.onRowsScrollEnd;
  const isEnabledServerSide = props.dataSource && props.lazyLoading;
  const isEnabled = isEnabledClientSide || isEnabledServerSide;
  const handleIntersectionChange = useEventCallback_default(([entry]) => {
    var _a;
    const currentRatio = entry.intersectionRatio;
    const isIntersecting = entry.isIntersecting;
    if (isIntersecting && currentRatio === 1) {
      (_a = observer.current) == null ? void 0 : _a.disconnect();
      triggerElement.current = null;
      apiRef.current.publishEvent("rowsScrollEndIntersection");
    }
  });
  React43.useEffect(() => {
    var _a;
    const virtualScroller = apiRef.current.virtualScrollerRef.current;
    if (!isEnabled || !isReady || !virtualScroller) {
      return;
    }
    (_a = observer.current) == null ? void 0 : _a.disconnect();
    const horizontalScrollbarHeight = gridHorizontalScrollbarHeightSelector(apiRef);
    const marginBottom = props.scrollEndThreshold - horizontalScrollbarHeight;
    observer.current = new IntersectionObserver(handleIntersectionChange, {
      threshold: 1,
      root: virtualScroller,
      rootMargin: `0px 0px ${marginBottom}px 0px`
    });
    if (triggerElement.current) {
      observer.current.observe(triggerElement.current);
    }
  }, [apiRef, isReady, handleIntersectionChange, isEnabled, props.scrollEndThreshold]);
  const updateTarget = (node) => {
    var _a, _b;
    if (triggerElement.current !== node) {
      (_a = observer.current) == null ? void 0 : _a.disconnect();
      triggerElement.current = node;
      if (triggerElement.current) {
        (_b = observer.current) == null ? void 0 : _b.observe(triggerElement.current);
      }
    }
  };
  const triggerRef = React43.useCallback((node) => {
    if (!isEnabled) {
      return;
    }
    updateTargetTimeout.start(0, () => updateTarget(node));
  }, [isEnabled, updateTargetTimeout]);
  const getInfiniteLoadingTriggerElement = React43.useCallback(({
    lastRowId
  }) => {
    if (!isEnabled) {
      return null;
    }
    return (0, import_jsx_runtime21.jsx)(InfiniteLoadingTriggerElement, {
      ref: triggerRef,
      role: "presentation"
    }, `trigger-${lastRowId}`);
  }, [isEnabled, triggerRef]);
  const infiniteLoaderPrivateApi = {
    getInfiniteLoadingTriggerElement
  };
  useGridApiMethod(apiRef, infiniteLoaderPrivateApi, "private");
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourcePro.js
var React45 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourceBasePro.js
var React44 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/utils.js
var MAX_CONCURRENT_REQUESTS = Infinity;
var RequestStatus = (function(RequestStatus2) {
  RequestStatus2[RequestStatus2["QUEUED"] = 0] = "QUEUED";
  RequestStatus2[RequestStatus2["PENDING"] = 1] = "PENDING";
  RequestStatus2[RequestStatus2["SETTLED"] = 2] = "SETTLED";
  RequestStatus2[RequestStatus2["UNKNOWN"] = 3] = "UNKNOWN";
  return RequestStatus2;
})({});
var NestedDataManager = class {
  constructor(privateApiRef, maxConcurrentRequests = MAX_CONCURRENT_REQUESTS) {
    __publicField(this, "pendingRequests", /* @__PURE__ */ (() => /* @__PURE__ */ new Set())());
    __publicField(this, "queuedRequests", /* @__PURE__ */ (() => /* @__PURE__ */ new Set())());
    __publicField(this, "settledRequests", /* @__PURE__ */ (() => /* @__PURE__ */ new Set())());
    __publicField(this, "processQueue", async () => {
      if (this.queuedRequests.size === 0 || this.pendingRequests.size >= this.maxConcurrentRequests) {
        return;
      }
      const loopLength = Math.min(this.maxConcurrentRequests - this.pendingRequests.size, this.queuedRequests.size);
      if (loopLength === 0) {
        return;
      }
      const fetchQueue = Array.from(this.queuedRequests);
      for (let i = 0; i < loopLength; i += 1) {
        const id = fetchQueue[i];
        this.queuedRequests.delete(id);
        this.pendingRequests.add(id);
        this.api.fetchRowChildren(id);
      }
    });
    __publicField(this, "queue", async (ids) => {
      const loadingIds = {};
      ids.forEach((id) => {
        this.queuedRequests.add(id);
        loadingIds[id] = true;
      });
      this.api.setState((state) => _extends({}, state, {
        dataSource: _extends({}, state.dataSource, {
          loading: _extends({}, state.dataSource.loading, loadingIds)
        })
      }));
      this.processQueue();
    });
    __publicField(this, "setRequestSettled", (id) => {
      this.pendingRequests.delete(id);
      this.settledRequests.add(id);
      this.processQueue();
    });
    __publicField(this, "clear", () => {
      this.queuedRequests.clear();
      Array.from(this.pendingRequests).forEach((id) => this.clearPendingRequest(id));
    });
    __publicField(this, "clearPendingRequest", (id) => {
      this.api.dataSource.setChildrenLoading(id, false);
      this.pendingRequests.delete(id);
      this.processQueue();
    });
    __publicField(this, "getRequestStatus", (id) => {
      if (this.pendingRequests.has(id)) {
        return RequestStatus.PENDING;
      }
      if (this.queuedRequests.has(id)) {
        return RequestStatus.QUEUED;
      }
      if (this.settledRequests.has(id)) {
        return RequestStatus.SETTLED;
      }
      return RequestStatus.UNKNOWN;
    });
    __publicField(this, "getActiveRequestsCount", () => this.pendingRequests.size + this.queuedRequests.size);
    this.api = privateApiRef.current;
    this.maxConcurrentRequests = maxConcurrentRequests;
  }
};
var getGroupKeys = (tree, rowId) => {
  const rowNode = tree[rowId];
  let currentNodeId = rowNode.parent;
  const groupKeys = [];
  while (currentNodeId && currentNodeId !== GRID_ROOT_GROUP_ID) {
    const currentNode = tree[currentNodeId];
    groupKeys.push(currentNode.groupingKey ?? "");
    currentNodeId = currentNode.parent;
  }
  return groupKeys.reverse();
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourceBasePro.js
var INITIAL_STATE = {
  loading: {},
  errors: {}
};
var useGridDataSourceBasePro = (apiRef, props, options2 = {}) => {
  var _a;
  const groupsToAutoFetch = useGridSelector(apiRef, gridRowGroupsToFetchSelector);
  const nestedDataManager = useLazyRef(() => new NestedDataManager(apiRef)).current;
  const scheduledGroups = React44.useRef(0);
  const clearDataSourceState = React44.useCallback(() => {
    nestedDataManager.clear();
    scheduledGroups.current = 0;
    const dataSourceState = apiRef.current.state.dataSource;
    if (dataSourceState !== INITIAL_STATE) {
      apiRef.current.resetDataSourceState();
    }
    return null;
  }, [apiRef, nestedDataManager]);
  const handleEditRow = React44.useCallback((params, updatedRow) => {
    const groupKeys = getGroupKeys(gridRowTreeSelector(apiRef), params.rowId);
    apiRef.current.updateNestedRows([updatedRow], groupKeys);
    if (updatedRow && !isDeepEqual(updatedRow, params.previousRow)) {
      apiRef.current.dataSource.cache.clear();
    }
  }, [apiRef]);
  const {
    api,
    debouncedFetchRows,
    strategyProcessor,
    events,
    cacheChunkManager,
    cache
  } = useGridDataSourceBase(apiRef, props, _extends({
    fetchRowChildren: nestedDataManager.queue,
    clearDataSourceState,
    handleEditRow
  }, options2));
  const setStrategyAvailability = React44.useCallback(() => {
    apiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.Default, props.dataSource && !props.lazyLoading ? () => true : () => false);
  }, [apiRef, props.dataSource, props.lazyLoading]);
  const onDataSourceErrorProp = props.onDataSourceError;
  const fetchRowChildren = React44.useCallback(async (id) => {
    var _a2, _b;
    const pipedParams = apiRef.current.unstable_applyPipeProcessors("getRowsParams", {});
    if (!props.treeData && (((_a2 = pipedParams.groupFields) == null ? void 0 : _a2.length) ?? 0) === 0) {
      nestedDataManager.clearPendingRequest(id);
      return;
    }
    const getRows = (_b = props.dataSource) == null ? void 0 : _b.getRows;
    if (!getRows) {
      nestedDataManager.clearPendingRequest(id);
      return;
    }
    const rowNode = apiRef.current.getRowNode(id);
    if (!rowNode) {
      nestedDataManager.clearPendingRequest(id);
      return;
    }
    const fetchParams = _extends({}, gridGetRowsParamsSelector(apiRef), pipedParams, {
      groupKeys: rowNode.path
    });
    const cacheKeys = cacheChunkManager.getCacheKeys(fetchParams);
    const responses = cacheKeys.map((cacheKey) => cache.get(cacheKey));
    const cachedData = responses.some((response) => response === void 0) ? void 0 : CacheChunkManager.mergeResponses(responses);
    if (cachedData !== void 0) {
      const rows = cachedData.rows;
      nestedDataManager.setRequestSettled(id);
      apiRef.current.updateNestedRows(rows, rowNode.path);
      if (cachedData.rowCount !== void 0) {
        apiRef.current.setRowCount(cachedData.rowCount);
      }
      apiRef.current.setRowChildrenExpansion(id, true);
      apiRef.current.dataSource.setChildrenLoading(id, false);
      return;
    }
    const existingError = gridDataSourceErrorsSelector(apiRef)[id] ?? null;
    if (existingError) {
      apiRef.current.dataSource.setChildrenFetchError(id, null);
    }
    try {
      const getRowsResponse = await getRows(fetchParams);
      if (!apiRef.current.getRowNode(id)) {
        nestedDataManager.clearPendingRequest(id);
        return;
      }
      if (nestedDataManager.getRequestStatus(id) === RequestStatus.UNKNOWN) {
        apiRef.current.dataSource.setChildrenLoading(id, false);
        return;
      }
      nestedDataManager.setRequestSettled(id);
      const cacheResponses = cacheChunkManager.splitResponse(fetchParams, getRowsResponse);
      cacheResponses.forEach((response, key) => {
        cache.set(key, response);
      });
      if (getRowsResponse.rowCount !== void 0) {
        apiRef.current.setRowCount(getRowsResponse.rowCount);
      }
      const rowsToDelete = [];
      getRowsResponse.rows.forEach((row) => {
        const rowId = gridRowIdSelector(apiRef, row);
        const treeNode = gridRowNodeSelector(apiRef, rowId);
        if (treeNode) {
          rowsToDelete.push({
            id: rowId,
            _action: "delete"
          });
        }
      });
      if (rowsToDelete.length > 0) {
        apiRef.current.updateNestedRows(rowsToDelete, rowNode.path);
      }
      apiRef.current.updateNestedRows(getRowsResponse.rows, rowNode.path);
      apiRef.current.setRowChildrenExpansion(id, true);
    } catch (error) {
      const childrenFetchError = error;
      apiRef.current.dataSource.setChildrenFetchError(id, childrenFetchError);
      if (typeof onDataSourceErrorProp === "function") {
        onDataSourceErrorProp(new GridGetRowsError({
          message: childrenFetchError.message,
          params: fetchParams,
          cause: childrenFetchError
        }));
      } else if (true) {
        warnOnce(["MUI X: A call to `dataSource.getRows()` threw an error which was not handled because `onDataSourceError()` is missing.", "To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.", "For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling."], "error");
      }
    } finally {
      apiRef.current.dataSource.setChildrenLoading(id, false);
      nestedDataManager.setRequestSettled(id);
    }
  }, [nestedDataManager, cacheChunkManager, cache, onDataSourceErrorProp, apiRef, props.treeData, (_a = props.dataSource) == null ? void 0 : _a.getRows]);
  const setChildrenLoading = React44.useCallback((parentId, isLoading) => {
    apiRef.current.setState((state) => {
      if (!state.dataSource.loading[parentId] && isLoading === false) {
        return state;
      }
      const newLoadingState = _extends({}, state.dataSource.loading);
      if (isLoading === false) {
        delete newLoadingState[parentId];
      } else {
        newLoadingState[parentId] = isLoading;
      }
      return _extends({}, state, {
        dataSource: _extends({}, state.dataSource, {
          loading: newLoadingState
        })
      });
    });
  }, [apiRef]);
  const setChildrenFetchError = React44.useCallback((parentId, error) => {
    apiRef.current.setState((state) => {
      const newErrorsState = _extends({}, state.dataSource.errors);
      if (error === null && newErrorsState[parentId] !== void 0) {
        delete newErrorsState[parentId];
      } else {
        newErrorsState[parentId] = error;
      }
      return _extends({}, state, {
        dataSource: _extends({}, state.dataSource, {
          errors: newErrorsState
        })
      });
    });
  }, [apiRef]);
  const resetDataSourceState = React44.useCallback(() => {
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        dataSource: INITIAL_STATE
      });
    });
  }, [apiRef]);
  const removeChildrenRows = React44.useCallback((parentId) => {
    const rowNode = gridRowNodeSelector(apiRef, parentId);
    if (!rowNode || rowNode.type !== "group" || rowNode.children.length === 0) {
      return;
    }
    const removedRows = [];
    const traverse = (nodeId) => {
      const node = gridRowNodeSelector(apiRef, nodeId);
      if (!node) {
        return;
      }
      if (node.type === "group" && node.children.length > 0) {
        node.children.forEach(traverse);
      }
      removedRows.push({
        id: nodeId,
        _action: "delete"
      });
    };
    rowNode.children.forEach(traverse);
    if (removedRows.length > 0) {
      apiRef.current.updateNestedRows(removedRows, rowNode.path);
    }
  }, [apiRef]);
  const dataSourceApi = {
    dataSource: _extends({}, api.public.dataSource, {
      setChildrenLoading,
      setChildrenFetchError
    })
  };
  const dataSourcePrivateApi = {
    fetchRowChildren,
    resetDataSourceState,
    removeChildrenRows
  };
  React44.useEffect(() => {
    if (groupsToAutoFetch && groupsToAutoFetch.length && scheduledGroups.current < groupsToAutoFetch.length) {
      const groupsToSchedule = groupsToAutoFetch.slice(scheduledGroups.current);
      nestedDataManager.queue(groupsToSchedule);
      scheduledGroups.current = groupsToAutoFetch.length;
    }
  }, [apiRef, nestedDataManager, groupsToAutoFetch]);
  return {
    api: {
      public: dataSourceApi,
      private: dataSourcePrivateApi
    },
    debouncedFetchRows,
    strategyProcessor,
    events,
    setStrategyAvailability,
    cacheChunkManager,
    cache
  };
};

// node_modules/@mui/x-data-grid-pro/esm/hooks/features/dataSource/useGridDataSourcePro.js
function getKeyPro(params) {
  return JSON.stringify([params.filterModel, params.sortModel, params.groupKeys, params.start, params.end]);
}
var dataSourceStateInitializer = (state) => {
  return _extends({}, state, {
    dataSource: INITIAL_STATE
  });
};
var options = {
  cacheOptions: {
    getKey: getKeyPro
  }
};
var useGridDataSourcePro = (apiRef, props) => {
  const {
    api,
    strategyProcessor,
    events,
    setStrategyAvailability
  } = useGridDataSourceBasePro(apiRef, props, options);
  useGridApiMethod(apiRef, api.public, "public");
  useGridApiMethod(apiRef, api.private, "private");
  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);
  Object.entries(events).forEach(([event, handler]) => {
    useGridEvent(apiRef, event, handler);
  });
  React45.useEffect(() => {
    setStrategyAvailability();
  }, [setStrategyAvailability]);
};

// node_modules/@mui/x-data-grid-pro/esm/internals/propValidation.js
var propValidatorsDataGridPro = [...propValidatorsDataGrid, (props) => props.pagination && props.hideFooterRowCount && "MUI X: The `hideFooterRowCount` prop has no effect when the pagination is enabled." || void 0, (props) => props.treeData && props.filterMode === "server" && !props.dataSource && 'MUI X: The `filterMode="server"` prop is not available when the `treeData` is enabled.' || void 0, (props) => !props.pagination && props.checkboxSelectionVisibleOnly && "MUI X: The `checkboxSelectionVisibleOnly` prop has no effect when the pagination is not enabled." || void 0, (props) => props.signature !== GridSignature.DataGrid && props.paginationMode === "client" && props.rowsLoadingMode !== "server" && isNumber(props.rowCount) && 'MUI X: Usage of the `rowCount` prop with client side pagination (`paginationMode="client"`) has no effect. `rowCount` is only meant to be used with `paginationMode="server"`.' || void 0, (props) => props.signature !== GridSignature.DataGrid && (props.rowsLoadingMode === "server" || props.onRowsScrollEnd) && props.lazyLoading && 'MUI X: Usage of the client side lazy loading (`rowsLoadingMode="server"` or `onRowsScrollEnd=...`) cannot be used together with server side lazy loading `lazyLoading="true"`.' || void 0];

// node_modules/@mui/x-data-grid-pro/esm/internals/index.js
var RowGroupingStrategy = (function(RowGroupingStrategy2) {
  RowGroupingStrategy2["Default"] = "grouping-columns";
  RowGroupingStrategy2["DataSource"] = "grouping-columns-data-source";
  return RowGroupingStrategy2;
})({});

export {
  headerFilteringStateInitializer,
  useGridHeaderFiltering,
  TailwindDemoContainer,
  useGridInfiniteLoader,
  gridColumnReorderSelector,
  gridColumnReorderDragColSelector,
  columnReorderStateInitializer,
  useGridColumnReorder,
  useGridTreeData,
  useGridRootProps2 as useGridRootProps,
  useGridApiContext2 as useGridApiContext,
  GridTreeDataGroupingCell,
  GridColumnMenuPinningItem,
  gridDetailPanelExpandedRowIdsSelector,
  gridDetailPanelExpandedRowsContentCacheSelector,
  GridDetailPanelToggleCell,
  GridRowReorderCell,
  renderRowReorderCell,
  GridPushPinRightIcon,
  GridPushPinLeftIcon,
  GridHeaderFilterMenuContainer,
  Memoized,
  GridHeaderFilterMenu,
  getGroupRowIdFromPath,
  insertNodeInTree,
  removeNodeFromTree,
  getVisibleRowsLookup,
  createRowTree,
  sortRowTree,
  useGridColumnHeadersPro,
  GridColumnHeaders,
  GRID_COLUMN_MENU_SLOTS_PRO,
  GRID_COLUMN_MENU_SLOT_PROPS_PRO,
  GridProColumnMenu,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
  DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS,
  useGridAriaAttributesPro,
  useGridRowAriaAttributesPro,
  columnPinningStateInitializer,
  useGridColumnPinning,
  useGridColumnPinningPreProcessors,
  skipFiltering,
  skipSorting,
  getParentPath,
  gridDataSourceLoadingIdSelector,
  gridDataSourceErrorSelector,
  useGridDataSourceTreeDataPreProcessors,
  detailPanelStateInitializer,
  useGridDetailPanel,
  useGridDetailPanelPreProcessors,
  GRID_REORDER_COL_DEF,
  rowReorderStateInitializer,
  useGridRowReorder,
  useGridRowReorderPreProcessors,
  rowPinningStateInitializer,
  useGridRowPinning,
  addPinnedRow,
  useGridRowPinningPreProcessors,
  useGridLazyLoader,
  useGridLazyLoaderPreProcessors,
  useGridDataSourceLazyLoader,
  useGridInfiniteLoadingIntersection,
  getGroupKeys,
  useGridDataSourceBasePro,
  dataSourceStateInitializer,
  useGridDataSourcePro,
  propValidatorsDataGridPro,
  RowGroupingStrategy,
  updateRowTree,
  useGridTreeDataPreProcessors
};
//# sourceMappingURL=chunk-F22U6ZUN.js.map
