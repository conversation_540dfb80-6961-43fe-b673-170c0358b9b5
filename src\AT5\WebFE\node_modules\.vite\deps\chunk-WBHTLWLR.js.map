{"version": 3, "sources": ["../../@mui/material/esm/CardHeader/CardHeader.js", "../../@mui/material/esm/CardHeader/cardHeaderClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport cardHeaderClasses, { getCardHeaderUtilityClass } from \"./cardHeaderClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar'],\n    action: ['action'],\n    content: ['content'],\n    title: ['title'],\n    subheader: ['subheader']\n  };\n  return composeClasses(slots, getCardHeaderUtilityClass, classes);\n};\nconst CardHeaderRoot = styled('div', {\n  name: '<PERSON><PERSON><PERSON><PERSON>Header',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [{\n      [`& .${cardHeaderClasses.title}`]: styles.title\n    }, {\n      [`& .${cardHeaderClasses.subheader}`]: styles.subheader\n    }, styles.root];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 16\n});\nconst CardHeaderAvatar = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Avatar'\n})({\n  display: 'flex',\n  flex: '0 0 auto',\n  marginRight: 16\n});\nconst CardHeaderAction = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Action'\n})({\n  flex: '0 0 auto',\n  alignSelf: 'flex-start',\n  marginTop: -4,\n  marginRight: -8,\n  marginBottom: -4\n});\nconst CardHeaderContent = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Content'\n})({\n  flex: '1 1 auto',\n  [`.${typographyClasses.root}:where(& .${cardHeaderClasses.title})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${cardHeaderClasses.subheader})`]: {\n    display: 'block'\n  }\n});\nconst CardHeader = /*#__PURE__*/React.forwardRef(function CardHeader(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardHeader'\n  });\n  const {\n    action,\n    avatar,\n    component = 'div',\n    disableTypography = false,\n    subheader: subheaderProp,\n    subheaderTypographyProps,\n    title: titleProp,\n    titleTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    disableTypography\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      title: titleTypographyProps,\n      subheader: subheaderTypographyProps,\n      ...slotProps\n    }\n  };\n  let title = titleProp;\n  const [TitleSlot, titleSlotProps] = useSlot('title', {\n    className: classes.title,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      variant: avatar ? 'body2' : 'h5',\n      component: 'span'\n    }\n  });\n  if (title != null && title.type !== Typography && !disableTypography) {\n    title = /*#__PURE__*/_jsx(TitleSlot, {\n      ...titleSlotProps,\n      children: title\n    });\n  }\n  let subheader = subheaderProp;\n  const [SubheaderSlot, subheaderSlotProps] = useSlot('subheader', {\n    className: classes.subheader,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      variant: avatar ? 'body2' : 'body1',\n      color: 'textSecondary',\n      component: 'span'\n    }\n  });\n  if (subheader != null && subheader.type !== Typography && !disableTypography) {\n    subheader = /*#__PURE__*/_jsx(SubheaderSlot, {\n      ...subheaderSlotProps,\n      children: subheader\n    });\n  }\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: CardHeaderRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [AvatarSlot, avatarSlotProps] = useSlot('avatar', {\n    className: classes.avatar,\n    elementType: CardHeaderAvatar,\n    externalForwardedProps,\n    ownerState\n  });\n  const [ContentSlot, contentSlotProps] = useSlot('content', {\n    className: classes.content,\n    elementType: CardHeaderContent,\n    externalForwardedProps,\n    ownerState\n  });\n  const [ActionSlot, actionSlotProps] = useSlot('action', {\n    className: classes.action,\n    elementType: CardHeaderAction,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [avatar && /*#__PURE__*/_jsx(AvatarSlot, {\n      ...avatarSlotProps,\n      children: avatar\n    }), /*#__PURE__*/_jsxs(ContentSlot, {\n      ...contentSlotProps,\n      children: [title, subheader]\n    }), action && /*#__PURE__*/_jsx(ActionSlot, {\n      ...actionSlotProps,\n      children: action\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardHeader.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display in the card header.\n   */\n  action: PropTypes.node,\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.node,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, `subheader` and `title` won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `title` text, and optional `subheader` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    action: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    avatar: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    content: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    subheader: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    title: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    action: PropTypes.elementType,\n    avatar: PropTypes.elementType,\n    content: PropTypes.elementType,\n    root: PropTypes.elementType,\n    subheader: PropTypes.elementType,\n    title: PropTypes.elementType\n  }),\n  /**\n   * The content of the component.\n   */\n  subheader: PropTypes.node,\n  /**\n   * These props will be forwarded to the subheader\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.subheader` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  subheaderTypographyProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The content of the component.\n   */\n  title: PropTypes.node,\n  /**\n   * These props will be forwarded to the title\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.title` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  titleTypographyProps: PropTypes.object\n} : void 0;\nexport default CardHeader;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardHeaderUtilityClass(slot) {\n  return generateUtilityClass('MuiCardHeader', slot);\n}\nconst cardHeaderClasses = generateUtilityClasses('MuiCardHeader', ['root', 'avatar', 'action', 'content', 'title', 'subheader']);\nexport default cardHeaderClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACA,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,UAAU,UAAU,WAAW,SAAS,WAAW,CAAC;AAC/H,IAAO,4BAAQ;;;ADIf,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,QAAQ,CAAC,QAAQ;AAAA,IACjB,QAAQ,CAAC,QAAQ;AAAA,IACjB,SAAS,CAAC,SAAS;AAAA,IACnB,OAAO,CAAC,OAAO;AAAA,IACf,WAAW,CAAC,WAAW;AAAA,EACzB;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACA,IAAM,iBAAiB,eAAO,OAAO;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,0BAAkB,KAAK,EAAE,GAAG,OAAO;AAAA,IAC5C,GAAG;AAAA,MACD,CAAC,MAAM,0BAAkB,SAAS,EAAE,GAAG,OAAO;AAAA,IAChD,GAAG,OAAO,IAAI;AAAA,EAChB;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AACD,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,MAAM;AAAA,EACN,aAAa;AACf,CAAC;AACD,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAChB,CAAC;AACD,IAAM,oBAAoB,eAAO,OAAO;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,MAAM;AAAA,EACN,CAAC,IAAI,0BAAkB,IAAI,aAAa,0BAAkB,KAAK,GAAG,GAAG;AAAA,IACnE,SAAS;AAAA,EACX;AAAA,EACA,CAAC,IAAI,0BAAkB,IAAI,aAAa,0BAAkB,SAAS,GAAG,GAAG;AAAA,IACvE,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAM,aAAgC,iBAAW,SAASA,YAAW,SAAS,KAAK;AACjF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,EACF;AACA,MAAI,QAAQ;AACZ,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS,SAAS,UAAU;AAAA,MAC5B,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,SAAS,QAAQ,MAAM,SAAS,sBAAc,CAAC,mBAAmB;AACpE,gBAAqB,mBAAAC,KAAK,WAAW;AAAA,MACnC,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,MAAI,YAAY;AAChB,QAAM,CAAC,eAAe,kBAAkB,IAAI,QAAQ,aAAa;AAAA,IAC/D,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS,SAAS,UAAU;AAAA,MAC5B,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,aAAa,QAAQ,UAAU,SAAS,sBAAc,CAAC,mBAAmB;AAC5E,oBAAyB,mBAAAA,KAAK,eAAe;AAAA,MAC3C,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,YAAY,eAAe,IAAI,QAAQ,UAAU;AAAA,IACtD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,aAAa,gBAAgB,IAAI,QAAQ,WAAW;AAAA,IACzD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,YAAY,eAAe,IAAI,QAAQ,UAAU;AAAA,IACtD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,CAAC,cAAuB,mBAAAD,KAAK,YAAY;AAAA,MACjD,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC,OAAgB,mBAAAC,MAAM,aAAa;AAAA,MAClC,GAAG;AAAA,MACH,UAAU,CAAC,OAAO,SAAS;AAAA,IAC7B,CAAC,GAAG,cAAuB,mBAAAD,KAAK,YAAY;AAAA,MAC1C,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,WAAW,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpF,QAAQ,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC9D,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC9D,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC/D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,WAAW,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IACjE,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,QAAQ,kBAAAA,QAAU;AAAA,IAClB,QAAQ,kBAAAA,QAAU;AAAA,IAClB,SAAS,kBAAAA,QAAU;AAAA,IACnB,MAAM,kBAAAA,QAAU;AAAA,IAChB,WAAW,kBAAAA,QAAU;AAAA,IACrB,OAAO,kBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,0BAA0B,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,sBAAsB,kBAAAA,QAAU;AAClC,IAAI;AACJ,IAAO,qBAAQ;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "_jsx", "_jsxs", "PropTypes"]}