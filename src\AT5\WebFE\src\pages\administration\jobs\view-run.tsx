import * as React from "react";
import Box from "@mui/material/Box";
import Link from "@mui/material/Link";
import Stack from "@mui/material/Stack";
import { ArrowLeftIcon } from "@phosphor-icons/react/dist/ssr/ArrowLeft";
import { Helmet } from "react-helmet-async";

import type { Metadata } from "@/types/metadata";
import { appConfig } from "@/config/app";
import { paths } from "@/paths";
import { RouterLink } from "@/components/core/link";

import { Grid } from "@mui/material";
import { JobRunView } from "./components/job-run-view";
import { JobRunLogsList } from "./components/job-run-logs-list";

interface PageProps {
	readonly jobRunId?: number;
}

export function Page({jobRunId}: PageProps): React.JSX.Element {
	const metadata = {
		title: `Runs | Jobs | Dashboard | ${appConfig.name}`
	} satisfies Metadata;

	return (
		<React.Fragment>
			<Helmet>
				<title>{metadata.title}</title>
			</Helmet>
			<Box
				sx={{
					maxWidth: "var(--Content-maxWidth)",
					m: "var(--Content-margin)",
					p: "var(--Content-padding)",
					width: "var(--Content-width)",
				}}
			>
				<Stack spacing={4}>
					<Stack spacing={3}>
						<div>
							<Link
								color="text.primary"
								component={RouterLink}
								href={paths.administration.jobs.index(true)}
								sx={{ alignItems: "center", display: "inline-flex", gap: 1 }}
								variant="subtitle2"
							>
								<ArrowLeftIcon fontSize="var(--icon-fontSize-md)" />
								Browse Job Runs
							</Link>
						</div>
					</Stack>
                    <JobRunView jobRunId={jobRunId}/>
					<Grid
						size={{
							xs: 12,
						}}>
						<Stack direction={"column"}>
							{jobRunId && <JobRunLogsList jobRunId={jobRunId}/>}
						</Stack>
					</Grid>
				</Stack>
			</Box>
		</React.Fragment>
	);
}
