import { createApi } from '@reduxjs/toolkit/query/react';
import { createCustomBaseQuery } from './custom-base-query';

// Base API configuration that matches the existing backend API setup
// Use relative base URL so Vite dev server proxy handles CORS
const API_BASE_URL = '';

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: createCustomBaseQuery(API_BASE_URL),
  // Define tag types for cache invalidation
  tagTypes: ['Job', 'JobTrigger', 'User', 'Database', 'CurrentUser', 'CurrentTenant'],
  endpoints: () => ({}),
});
