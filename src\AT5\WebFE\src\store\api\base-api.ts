import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base API configuration that matches the existing backend API setup
const API_BASE_URL = 'https://localhost:7488';
const tenantId = 10;
const userId = 27219;

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      // Add the same headers that the existing API functions use
      if (tenantId) {
        headers.set('tenant-id', tenantId.toString());
      }
      if (userId) {
        headers.set('user-id', userId.toString());
      }
      return headers;
    },
  }),
  // Define tag types for cache invalidation
  tagTypes: ['Job', 'JobTrigger', 'User', 'Database', 'CurrentUser', 'CurrentTenant'],
  endpoints: () => ({}),
});
