import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsDatabaseDatabaseNamesEndpoint: build.query<
      AtApiServiceEndpointsDatabaseDatabaseNamesEndpointApiResponse,
      AtApiServiceEndpointsDatabaseDatabaseNamesEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/database/namestest` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as databaseGeneratedApi };
export type AtApiServiceEndpointsDatabaseDatabaseNamesEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsDatabaseModelsDatabaseNamesResponse;
export type AtApiServiceEndpointsDatabaseDatabaseNamesEndpointApiArg = void;
export type AtApiServiceEndpointsDatabaseModelsDatabaseNamesResponse = {
  masterDbName?: string;
  organizationDbName?: string;
};
export const { useAtApiServiceEndpointsDatabaseDatabaseNamesEndpointQuery } =
  injectedRtkApi;
