{"version": 3, "sources": ["../../@mui/x-date-pickers/esm/timeViewRenderers/timeViewRenderers.js", "../../@mui/x-date-pickers/esm/DigitalClock/DigitalClock.js", "../../@mui/x-date-pickers/esm/DigitalClock/digitalClockClasses.js", "../../@mui/x-date-pickers/esm/MultiSectionDigitalClock/MultiSectionDigitalClock.js", "../../@mui/x-date-pickers/esm/MultiSectionDigitalClock/multiSectionDigitalClockClasses.js", "../../@mui/x-date-pickers/esm/MultiSectionDigitalClock/MultiSectionDigitalClockSection.js", "../../@mui/x-date-pickers/esm/MultiSectionDigitalClock/multiSectionDigitalClockSectionClasses.js", "../../@mui/x-date-pickers/esm/MultiSectionDigitalClock/MultiSectionDigitalClock.utils.js"], "sourcesContent": ["import * as React from 'react';\nimport { TimeClock } from \"../TimeClock/index.js\";\nimport { DigitalClock } from \"../DigitalClock/index.js\";\nimport { MultiSectionDigitalClock } from \"../MultiSectionDigitalClock/index.js\";\nimport { isInternalTimeView, isTimeView } from \"../internals/utils/time-utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const renderTimeViewClock = ({\n  view,\n  onViewChange,\n  focusedView,\n  onFocusedViewChange,\n  views,\n  value,\n  defaultValue,\n  referenceDate,\n  onChange,\n  className,\n  classes,\n  disableFuture,\n  disablePast,\n  minTime,\n  maxTime,\n  shouldDisableTime,\n  minutesStep,\n  ampm,\n  ampmInClock,\n  slots,\n  slotProps,\n  readOnly,\n  disabled,\n  sx,\n  autoFocus,\n  showViewSwitcher,\n  disableIgnoringDatePartForTimeValidation,\n  timezone\n}) => /*#__PURE__*/_jsx(TimeClock, {\n  view: view,\n  onViewChange: onViewChange,\n  focusedView: focusedView && isTimeView(focusedView) ? focusedView : null,\n  onFocusedViewChange: onFocusedViewChange,\n  views: views.filter(isTimeView),\n  value: value,\n  defaultValue: defaultValue,\n  referenceDate: referenceDate,\n  onChange: onChange,\n  className: className,\n  classes: classes,\n  disableFuture: disableFuture,\n  disablePast: disablePast,\n  minTime: minTime,\n  maxTime: maxTime,\n  shouldDisableTime: shouldDisableTime,\n  minutesStep: minutesStep,\n  ampm: ampm,\n  ampmInClock: ampmInClock,\n  slots: slots,\n  slotProps: slotProps,\n  readOnly: readOnly,\n  disabled: disabled,\n  sx: sx,\n  autoFocus: autoFocus,\n  showViewSwitcher: showViewSwitcher,\n  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,\n  timezone: timezone\n});\nif (process.env.NODE_ENV !== \"production\") renderTimeViewClock.displayName = \"renderTimeViewClock\";\nexport const renderDigitalClockTimeView = ({\n  view,\n  onViewChange,\n  focusedView,\n  onFocusedViewChange,\n  views,\n  value,\n  defaultValue,\n  referenceDate,\n  onChange,\n  className,\n  classes,\n  disableFuture,\n  disablePast,\n  minTime,\n  maxTime,\n  shouldDisableTime,\n  minutesStep,\n  ampm,\n  slots,\n  slotProps,\n  readOnly,\n  disabled,\n  sx,\n  autoFocus,\n  disableIgnoringDatePartForTimeValidation,\n  timeSteps,\n  skipDisabled,\n  timezone\n}) => /*#__PURE__*/_jsx(DigitalClock, {\n  view: view,\n  onViewChange: onViewChange,\n  focusedView: focusedView && isTimeView(focusedView) ? focusedView : null,\n  onFocusedViewChange: onFocusedViewChange,\n  views: views.filter(isTimeView),\n  value: value,\n  defaultValue: defaultValue,\n  referenceDate: referenceDate,\n  onChange: onChange,\n  className: className,\n  classes: classes,\n  disableFuture: disableFuture,\n  disablePast: disablePast,\n  minTime: minTime,\n  maxTime: maxTime,\n  shouldDisableTime: shouldDisableTime,\n  minutesStep: minutesStep,\n  ampm: ampm,\n  slots: slots,\n  slotProps: slotProps,\n  readOnly: readOnly,\n  disabled: disabled,\n  sx: sx,\n  autoFocus: autoFocus,\n  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,\n  timeStep: timeSteps?.minutes,\n  skipDisabled: skipDisabled,\n  timezone: timezone\n});\nif (process.env.NODE_ENV !== \"production\") renderDigitalClockTimeView.displayName = \"renderDigitalClockTimeView\";\nexport const renderMultiSectionDigitalClockTimeView = ({\n  view,\n  onViewChange,\n  focusedView,\n  onFocusedViewChange,\n  views,\n  value,\n  defaultValue,\n  referenceDate,\n  onChange,\n  className,\n  classes,\n  disableFuture,\n  disablePast,\n  minTime,\n  maxTime,\n  shouldDisableTime,\n  minutesStep,\n  ampm,\n  slots,\n  slotProps,\n  readOnly,\n  disabled,\n  sx,\n  autoFocus,\n  disableIgnoringDatePartForTimeValidation,\n  timeSteps,\n  skipDisabled,\n  timezone\n}) => /*#__PURE__*/_jsx(MultiSectionDigitalClock, {\n  view: view,\n  onViewChange: onViewChange,\n  focusedView: focusedView && isInternalTimeView(focusedView) ? focusedView : null,\n  onFocusedViewChange: onFocusedViewChange,\n  views: views.filter(isTimeView),\n  value: value,\n  defaultValue: defaultValue,\n  referenceDate: referenceDate,\n  onChange: onChange,\n  className: className,\n  classes: classes,\n  disableFuture: disableFuture,\n  disablePast: disablePast,\n  minTime: minTime,\n  maxTime: maxTime,\n  shouldDisableTime: shouldDisableTime,\n  minutesStep: minutesStep,\n  ampm: ampm,\n  slots: slots,\n  slotProps: slotProps,\n  readOnly: readOnly,\n  disabled: disabled,\n  sx: sx,\n  autoFocus: autoFocus,\n  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,\n  timeSteps: timeSteps,\n  skipDisabled: skipDisabled,\n  timezone: timezone\n});\nif (process.env.NODE_ENV !== \"production\") renderMultiSectionDigitalClockTimeView.displayName = \"renderMultiSectionDigitalClockTimeView\";", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"timeStep\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"onChange\", \"view\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"views\", \"skipDisabled\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { alpha, styled, useThemeProps } from '@mui/material/styles';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport composeClasses from '@mui/utils/composeClasses';\nimport MenuItem from '@mui/material/MenuItem';\nimport MenuList from '@mui/material/MenuList';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getDigitalClockUtilityClass } from \"./digitalClockClasses.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { DIGITAL_CLOCK_VIEW_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { getFocusedListItemIndex } from \"../internals/utils/utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    list: ['list'],\n    item: ['item']\n  };\n  return composeClasses(slots, getDigitalClockUtilityClass, classes);\n};\nconst DigitalClockRoot = styled(PickerViewRoot, {\n  name: 'MuiDigitalClock',\n  slot: 'Root'\n})({\n  overflowY: 'auto',\n  width: '100%',\n  scrollbarWidth: 'thin',\n  '@media (prefers-reduced-motion: no-preference)': {\n    scrollBehavior: 'auto'\n  },\n  maxHeight: DIGITAL_CLOCK_VIEW_HEIGHT,\n  variants: [{\n    props: {\n      hasDigitalClockAlreadyBeenRendered: true\n    },\n    style: {\n      '@media (prefers-reduced-motion: no-preference)': {\n        scrollBehavior: 'smooth'\n      }\n    }\n  }]\n});\nconst DigitalClockList = styled(MenuList, {\n  name: 'MuiDigitalClock',\n  slot: 'List'\n})({\n  padding: 0\n});\nexport const DigitalClockItem = styled(MenuItem, {\n  name: 'MuiDigitalClock',\n  slot: 'Item',\n  shouldForwardProp: prop => prop !== 'itemValue' && prop !== 'formattedValue'\n})(({\n  theme\n}) => ({\n  padding: '8px 16px',\n  margin: '2px 4px',\n  '&:first-of-type': {\n    marginTop: 4\n  },\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.hoverOpacity)\n  },\n  '&.Mui-selected': {\n    backgroundColor: (theme.vars || theme).palette.primary.main,\n    color: (theme.vars || theme).palette.primary.contrastText,\n    '&:focus-visible, &:hover': {\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    }\n  },\n  '&.Mui-focusVisible': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.focusOpacity)\n  }\n}));\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [DigitalClock](https://mui.com/x/react-date-pickers/digital-clock/)\n *\n * API:\n *\n * - [DigitalClock API](https://mui.com/x/api/date-pickers/digital-clock/)\n */\nexport const DigitalClock = /*#__PURE__*/React.forwardRef(function DigitalClock(inProps, ref) {\n  const utils = useUtils();\n  const containerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, containerRef);\n  const listRef = React.useRef(null);\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDigitalClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      timeStep = 30,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      onChange,\n      view: inView,\n      openTo,\n      onViewChange,\n      focusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      views = ['hours'],\n      skipDisabled = false,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange: handleRawValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'DigitalClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const {\n    ownerState: pickerOwnerState\n  } = usePickerPrivateContext();\n  const ownerState = _extends({}, pickerOwnerState, {\n    hasDigitalClockAlreadyBeenRendered: !!containerRef.current\n  });\n  const classes = useUtilityClasses(classesProp);\n  const ClockItem = slots?.digitalClockItem ?? DigitalClockItem;\n  const clockItemProps = useSlotProps({\n    elementType: ClockItem,\n    externalSlotProps: slotProps?.digitalClockItem,\n    ownerState,\n    className: classes.item\n  });\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const handleValueChange = useEventCallback(newValue => handleRawValueChange(newValue, 'finish', 'hours'));\n  const {\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView,\n    onFocusedViewChange\n  });\n  const handleItemSelect = useEventCallback(newValue => {\n    setValueAndGoToNextView(newValue, 'finish');\n  });\n  useEnhancedEffect(() => {\n    if (containerRef.current === null) {\n      return;\n    }\n    const activeItem = containerRef.current.querySelector('[role=\"listbox\"] [role=\"option\"][tabindex=\"0\"], [role=\"listbox\"] [role=\"option\"][aria-selected=\"true\"]');\n    if (!activeItem) {\n      return;\n    }\n    const offsetTop = activeItem.offsetTop;\n    if (autoFocus || !!focusedView) {\n      activeItem.focus();\n    }\n\n    // Subtracting the 4px of extra margin intended for the first visible section item\n    containerRef.current.scrollTop = offsetTop - 4;\n  });\n  const isTimeDisabled = React.useCallback(valueToCheck => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const containsValidTime = () => {\n      if (minTime && isAfter(minTime, valueToCheck)) {\n        return false;\n      }\n      if (maxTime && isAfter(valueToCheck, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(valueToCheck, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, valueToCheck)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = () => {\n      if (utils.getMinutes(valueToCheck) % minutesStep !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        return !shouldDisableTime(valueToCheck, 'hours');\n      }\n      return true;\n    };\n    return !containsValidTime() || !isValidValue();\n  }, [disableIgnoringDatePartForTimeValidation, utils, minTime, maxTime, disableFuture, now, disablePast, minutesStep, shouldDisableTime]);\n  const timeOptions = React.useMemo(() => {\n    const result = [];\n    const startOfDay = utils.startOfDay(valueOrReferenceDate);\n    let nextTimeStepOption = startOfDay;\n    while (utils.isSameDay(valueOrReferenceDate, nextTimeStepOption)) {\n      result.push(nextTimeStepOption);\n      nextTimeStepOption = utils.addMinutes(nextTimeStepOption, timeStep);\n    }\n    return result;\n  }, [valueOrReferenceDate, timeStep, utils]);\n  const focusedOptionIndex = timeOptions.findIndex(option => utils.isEqual(option, valueOrReferenceDate));\n  const handleKeyDown = event => {\n    switch (event.key) {\n      case 'PageUp':\n        {\n          const newIndex = getFocusedListItemIndex(listRef.current) - 5;\n          const children = listRef.current.children;\n          const newFocusedIndex = Math.max(0, newIndex);\n          const childToFocus = children[newFocusedIndex];\n          if (childToFocus) {\n            childToFocus.focus();\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'PageDown':\n        {\n          const newIndex = getFocusedListItemIndex(listRef.current) + 5;\n          const children = listRef.current.children;\n          const newFocusedIndex = Math.min(children.length - 1, newIndex);\n          const childToFocus = children[newFocusedIndex];\n          if (childToFocus) {\n            childToFocus.focus();\n          }\n          event.preventDefault();\n          break;\n        }\n      default:\n    }\n  };\n  return /*#__PURE__*/_jsx(DigitalClockRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(DigitalClockList, {\n      ref: listRef,\n      role: \"listbox\",\n      \"aria-label\": translations.timePickerToolbarTitle,\n      className: classes.list,\n      onKeyDown: handleKeyDown,\n      children: timeOptions.map((option, index) => {\n        const optionDisabled = isTimeDisabled(option);\n        if (skipDisabled && optionDisabled) {\n          return null;\n        }\n        const isSelected = utils.isEqual(option, value);\n        const formattedValue = utils.format(option, ampm ? 'fullTime12h' : 'fullTime24h');\n        const isFocused = focusedOptionIndex === index || focusedOptionIndex === -1 && index === 0;\n        const tabIndex = isFocused ? 0 : -1;\n        return /*#__PURE__*/_jsx(ClockItem, _extends({\n          onClick: () => !readOnly && handleItemSelect(option),\n          selected: isSelected,\n          disabled: disabled || optionDisabled,\n          disableRipple: readOnly,\n          role: \"option\"\n          // aria-readonly is not supported here and does not have any effect\n          ,\n          \"aria-disabled\": readOnly,\n          \"aria-selected\": isSelected,\n          tabIndex: tabIndex,\n          itemValue: option,\n          formattedValue: formattedValue\n        }, clockItemProps, {\n          children: formattedValue\n        }), `${option.valueOf()}-${formattedValue}`);\n      })\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") DigitalClock.displayName = \"DigitalClock\";\nprocess.env.NODE_ENV !== \"production\" ? DigitalClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overrideable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The time steps between two time options.\n   * For example, if `timeStep = 45`, then the available time options will be `[00:00, 00:45, 01:30, 02:15, 03:00, etc.]`.\n   * @default 30\n   */\n  timeStep: PropTypes.number,\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours']),\n  /**\n   * Available views.\n   * @default ['hours']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours']))\n} : void 0;", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getDigitalClockUtilityClass(slot) {\n  return generateUtilityClass('MuiDigitalClock', slot);\n}\nexport const digitalClockClasses = generateUtilityClasses('MuiDigitalClock', ['root', 'list', 'item']);", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"timeSteps\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"skipDisabled\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getMultiSectionDigitalClockUtilityClass } from \"./multiSectionDigitalClockClasses.js\";\nimport { MultiSectionDigitalClockSection } from \"./MultiSectionDigitalClockSection.js\";\nimport { getHourSectionOptions, getTimeSectionOptions } from \"./MultiSectionDigitalClock.utils.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { formatMeridiem } from \"../internals/utils/date-utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getMultiSectionDigitalClockUtilityClass, classes);\n};\nconst MultiSectionDigitalClockRoot = styled(PickerViewRoot, {\n  name: 'MuiMultiSectionDigitalClock',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  flexDirection: 'row',\n  width: '100%',\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [DigitalClock](https://mui.com/x/react-date-pickers/digital-clock/)\n *\n * API:\n *\n * - [MultiSectionDigitalClock API](https://mui.com/x/api/date-pickers/multi-section-digital-clock/)\n */\nexport const MultiSectionDigitalClock = /*#__PURE__*/React.forwardRef(function MultiSectionDigitalClock(inProps, ref) {\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiMultiSectionDigitalClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      timeSteps: inTimeSteps,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      onChange,\n      view: inView,\n      views: inViews = ['hours', 'minutes'],\n      openTo,\n      onViewChange,\n      focusedView: inFocusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      skipDisabled = false,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange: handleRawValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'MultiSectionDigitalClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const timeSteps = React.useMemo(() => _extends({\n    hours: 1,\n    minutes: 5,\n    seconds: 5\n  }, inTimeSteps), [inTimeSteps]);\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const handleValueChange = useEventCallback((newValue, selectionState, selectedView) => handleRawValueChange(newValue, selectionState, selectedView));\n  const views = React.useMemo(() => {\n    if (!ampm || !inViews.includes('hours')) {\n      return inViews;\n    }\n    return inViews.includes('meridiem') ? inViews : [...inViews, 'meridiem'];\n  }, [ampm, inViews]);\n  const {\n    view,\n    setValueAndGoToNextView,\n    focusedView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView: inFocusedView,\n    onFocusedViewChange\n  });\n  const handleMeridiemValueChange = useEventCallback(newValue => {\n    setValueAndGoToNextView(newValue, 'finish', 'meridiem');\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, handleMeridiemValueChange, 'finish');\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          if (utils.getHours(dateWithNewHours) !== valueWithMeridiem) {\n            return true;\n          }\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const buildViewProps = React.useCallback(viewToBuild => {\n    switch (viewToBuild) {\n      case 'hours':\n        {\n          return {\n            onChange: hours => {\n              const valueWithMeridiem = convertValueToMeridiem(hours, meridiemMode, ampm);\n              setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), 'finish', 'hours');\n            },\n            items: getHourSectionOptions({\n              now,\n              value,\n              ampm,\n              utils,\n              isDisabled: hours => isTimeDisabled(hours, 'hours'),\n              timeStep: timeSteps.hours,\n              resolveAriaLabel: translations.hoursClockNumberText,\n              valueOrReferenceDate\n            })\n          };\n        }\n      case 'minutes':\n        {\n          return {\n            onChange: minutes => {\n              setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minutes), 'finish', 'minutes');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getMinutes(valueOrReferenceDate),\n              utils,\n              isDisabled: minutes => isTimeDisabled(minutes, 'minutes'),\n              resolveLabel: minutes => utils.format(utils.setMinutes(now, minutes), 'minutes'),\n              timeStep: timeSteps.minutes,\n              hasValue: !!value,\n              resolveAriaLabel: translations.minutesClockNumberText\n            })\n          };\n        }\n      case 'seconds':\n        {\n          return {\n            onChange: seconds => {\n              setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, seconds), 'finish', 'seconds');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getSeconds(valueOrReferenceDate),\n              utils,\n              isDisabled: seconds => isTimeDisabled(seconds, 'seconds'),\n              resolveLabel: seconds => utils.format(utils.setSeconds(now, seconds), 'seconds'),\n              timeStep: timeSteps.seconds,\n              hasValue: !!value,\n              resolveAriaLabel: translations.secondsClockNumberText\n            })\n          };\n        }\n      case 'meridiem':\n        {\n          const amLabel = formatMeridiem(utils, 'am');\n          const pmLabel = formatMeridiem(utils, 'pm');\n          return {\n            onChange: handleMeridiemChange,\n            items: [{\n              value: 'am',\n              label: amLabel,\n              isSelected: () => !!value && meridiemMode === 'am',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'am',\n              ariaLabel: amLabel\n            }, {\n              value: 'pm',\n              label: pmLabel,\n              isSelected: () => !!value && meridiemMode === 'pm',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'pm',\n              ariaLabel: pmLabel\n            }]\n          };\n        }\n      default:\n        throw new Error(`Unknown view: ${viewToBuild} found.`);\n    }\n  }, [now, value, ampm, utils, timeSteps.hours, timeSteps.minutes, timeSteps.seconds, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, handleMeridiemChange]);\n  const viewsToRender = React.useMemo(() => {\n    if (!isRtl) {\n      return views;\n    }\n    const digitViews = views.filter(v => v !== 'meridiem');\n    digitViews.reverse();\n    if (views.includes('meridiem')) {\n      digitViews.push('meridiem');\n    }\n    return digitViews;\n  }, [isRtl, views]);\n  const viewTimeOptions = React.useMemo(() => {\n    return views.reduce((result, currentView) => {\n      return _extends({}, result, {\n        [currentView]: buildViewProps(currentView)\n      });\n    }, {});\n  }, [views, buildViewProps]);\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const classes = useUtilityClasses(classesProp);\n  return /*#__PURE__*/_jsx(MultiSectionDigitalClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"group\"\n  }, other, {\n    children: viewsToRender.map(timeView => /*#__PURE__*/_jsx(MultiSectionDigitalClockSection, {\n      items: viewTimeOptions[timeView].items,\n      onChange: viewTimeOptions[timeView].onChange,\n      active: view === timeView,\n      autoFocus: autoFocus || focusedView === timeView,\n      disabled: disabled,\n      readOnly: readOnly,\n      slots: slots,\n      slotProps: slotProps,\n      skipDisabled: skipDisabled,\n      \"aria-label\": translations.selectViewText(timeView)\n    }, timeView))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") MultiSectionDigitalClock.displayName = \"MultiSectionDigitalClock\";\nprocess.env.NODE_ENV !== \"production\" ? MultiSectionDigitalClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overrideable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']).isRequired)\n} : void 0;", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClock', slot);\n}\nexport const multiSectionDigitalClockClasses = generateUtilityClasses('MuiMultiSectionDigitalClock', ['root']);", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"autoFocus\", \"onChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"items\", \"active\", \"slots\", \"slotProps\", \"skipDisabled\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { alpha, styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport MenuList from '@mui/material/MenuList';\nimport MenuItem from '@mui/material/MenuItem';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getMultiSectionDigitalClockSectionUtilityClass } from \"./multiSectionDigitalClockSectionClasses.js\";\nimport { DIGITAL_CLOCK_VIEW_HEIGHT, MULTI_SECTION_CLOCK_SECTION_WIDTH } from \"../internals/constants/dimensions.js\";\nimport { getFocusedListItemIndex } from \"../internals/utils/utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    item: ['item']\n  };\n  return composeClasses(slots, getMultiSectionDigitalClockSectionUtilityClass, classes);\n};\nconst MultiSectionDigitalClockSectionRoot = styled(MenuList, {\n  name: 'MuiMultiSectionDigitalClockSection',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  maxHeight: DIGITAL_CLOCK_VIEW_HEIGHT,\n  width: 56,\n  padding: 0,\n  overflow: 'hidden',\n  scrollbarWidth: 'thin',\n  '@media (prefers-reduced-motion: no-preference)': {\n    scrollBehavior: 'auto'\n  },\n  '@media (pointer: fine)': {\n    '&:hover': {\n      overflowY: 'auto'\n    }\n  },\n  '@media (pointer: none), (pointer: coarse)': {\n    overflowY: 'auto'\n  },\n  '&:not(:first-of-type)': {\n    borderLeft: `1px solid ${(theme.vars || theme).palette.divider}`\n  },\n  '&::after': {\n    display: 'block',\n    content: '\"\"',\n    // subtracting the height of one item, extra margin and borders to make sure the max height is correct\n    height: 'calc(100% - 40px - 6px)'\n  },\n  variants: [{\n    props: {\n      hasDigitalClockAlreadyBeenRendered: true\n    },\n    style: {\n      '@media (prefers-reduced-motion: no-preference)': {\n        scrollBehavior: 'smooth'\n      }\n    }\n  }]\n}));\nconst MultiSectionDigitalClockSectionItem = styled(MenuItem, {\n  name: 'MuiMultiSectionDigitalClockSection',\n  slot: 'Item'\n})(({\n  theme\n}) => ({\n  padding: 8,\n  margin: '2px 4px',\n  width: MULTI_SECTION_CLOCK_SECTION_WIDTH,\n  justifyContent: 'center',\n  '&:first-of-type': {\n    marginTop: 4\n  },\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.hoverOpacity)\n  },\n  '&.Mui-selected': {\n    backgroundColor: (theme.vars || theme).palette.primary.main,\n    color: (theme.vars || theme).palette.primary.contrastText,\n    '&:focus-visible, &:hover': {\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    }\n  },\n  '&.Mui-focusVisible': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.focusOpacity)\n  }\n}));\n/**\n * @ignore - internal component.\n */\nexport const MultiSectionDigitalClockSection = /*#__PURE__*/React.forwardRef(function MultiSectionDigitalClockSection(inProps, ref) {\n  const containerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, containerRef);\n  const previousActive = React.useRef(null);\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiMultiSectionDigitalClockSection'\n  });\n  const {\n      autoFocus,\n      onChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      items,\n      active,\n      slots,\n      slotProps,\n      skipDisabled\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    ownerState: pickerOwnerState\n  } = usePickerPrivateContext();\n  const ownerState = _extends({}, pickerOwnerState, {\n    hasDigitalClockAlreadyBeenRendered: !!containerRef.current\n  });\n  const classes = useUtilityClasses(classesProp);\n  const DigitalClockSectionItem = slots?.digitalClockSectionItem ?? MultiSectionDigitalClockSectionItem;\n  useEnhancedEffect(() => {\n    if (containerRef.current === null) {\n      return;\n    }\n    const activeItem = containerRef.current.querySelector('[role=\"option\"][tabindex=\"0\"], [role=\"option\"][aria-selected=\"true\"]');\n    if (active && autoFocus && activeItem) {\n      activeItem.focus();\n    }\n    if (!activeItem || previousActive.current === activeItem) {\n      return;\n    }\n    previousActive.current = activeItem;\n    const offsetTop = activeItem.offsetTop;\n\n    // Subtracting the 4px of extra margin intended for the first visible section item\n    containerRef.current.scrollTop = offsetTop - 4;\n  });\n  const focusedOptionIndex = items.findIndex(item => item.isFocused(item.value));\n  const handleKeyDown = event => {\n    switch (event.key) {\n      case 'PageUp':\n        {\n          const newIndex = getFocusedListItemIndex(containerRef.current) - 5;\n          const children = containerRef.current.children;\n          const newFocusedIndex = Math.max(0, newIndex);\n          const childToFocus = children[newFocusedIndex];\n          if (childToFocus) {\n            childToFocus.focus();\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'PageDown':\n        {\n          const newIndex = getFocusedListItemIndex(containerRef.current) + 5;\n          const children = containerRef.current.children;\n          const newFocusedIndex = Math.min(children.length - 1, newIndex);\n          const childToFocus = children[newFocusedIndex];\n          if (childToFocus) {\n            childToFocus.focus();\n          }\n          event.preventDefault();\n          break;\n        }\n      default:\n    }\n  };\n  return /*#__PURE__*/_jsx(MultiSectionDigitalClockSectionRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    autoFocusItem: autoFocus && active,\n    role: \"listbox\",\n    onKeyDown: handleKeyDown\n  }, other, {\n    children: items.map((option, index) => {\n      const isItemDisabled = option.isDisabled?.(option.value);\n      const isDisabled = disabled || isItemDisabled;\n      if (skipDisabled && isDisabled) {\n        return null;\n      }\n      const isSelected = option.isSelected(option.value);\n      const tabIndex = focusedOptionIndex === index || focusedOptionIndex === -1 && index === 0 ? 0 : -1;\n      return /*#__PURE__*/_jsx(DigitalClockSectionItem, _extends({\n        onClick: () => !readOnly && onChange(option.value),\n        selected: isSelected,\n        disabled: isDisabled,\n        disableRipple: readOnly,\n        role: \"option\"\n        // aria-readonly is not supported here and does not have any effect\n        ,\n        \"aria-disabled\": readOnly || isDisabled || undefined,\n        \"aria-label\": option.ariaLabel,\n        \"aria-selected\": isSelected,\n        tabIndex: tabIndex,\n        className: classes.item\n      }, slotProps?.digitalClockSectionItem, {\n        children: option.label\n      }), option.label);\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") MultiSectionDigitalClockSection.displayName = \"MultiSectionDigitalClockSection\";", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockSectionUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClockSection', slot);\n}\nexport const multiSectionDigitalClockSectionClasses = generateUtilityClasses('MuiMultiSectionDigitalClockSection', ['root', 'item']);", "export const getHourSectionOptions = ({\n  now,\n  value,\n  utils,\n  ampm,\n  isDisabled,\n  resolveAriaLabel,\n  timeStep,\n  valueOrReferenceDate\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const result = [];\n  const isSelected = (hour, overriddenCurrentHours) => {\n    const resolvedCurrentHours = overriddenCurrentHours ?? currentHours;\n    if (resolvedCurrentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return resolvedCurrentHours === 12 || resolvedCurrentHours === 0;\n      }\n      return resolvedCurrentHours === hour || resolvedCurrentHours - 12 === hour;\n    }\n    return resolvedCurrentHours === hour;\n  };\n  const isFocused = hour => {\n    return isSelected(hour, utils.getHours(valueOrReferenceDate));\n  };\n  const endHour = ampm ? 11 : 23;\n  for (let hour = 0; hour <= endHour; hour += timeStep) {\n    let label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');\n    const ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());\n    label = utils.formatNumber(label);\n    result.push({\n      value: hour,\n      label,\n      isSelected,\n      isDisabled,\n      isFocused,\n      ariaLabel\n    });\n  }\n  return result;\n};\nexport const getTimeSectionOptions = ({\n  value,\n  utils,\n  isDisabled,\n  timeStep,\n  resolveLabel,\n  resolveAriaLabel,\n  hasValue = true\n}) => {\n  const isSelected = timeValue => {\n    if (value === null) {\n      return false;\n    }\n    return hasValue && value === timeValue;\n  };\n  const isFocused = timeValue => {\n    return value === timeValue;\n  };\n  return [...Array.from({\n    length: Math.ceil(60 / timeStep)\n  }, (_, index) => {\n    const timeValue = timeStep * index;\n    return {\n      value: timeValue,\n      label: utils.formatNumber(resolveLabel(timeValue)),\n      isDisabled,\n      isSelected,\n      isFocused,\n      ariaLabel: resolveAriaLabel(timeValue.toString())\n    };\n  })];\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACKvB,YAAuB;AAEvB,wBAAsB;;;ACLf,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACO,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,QAAQ,QAAQ,MAAM,CAAC;;;ADuBrG,yBAA4B;AAxB5B,IAAM,YAAY,CAAC,QAAQ,YAAY,aAAa,SAAS,aAAa,SAAS,gBAAgB,iBAAiB,4CAA4C,WAAW,WAAW,iBAAiB,eAAe,eAAe,qBAAqB,YAAY,QAAQ,UAAU,gBAAgB,eAAe,uBAAuB,aAAa,WAAW,YAAY,YAAY,SAAS,gBAAgB,UAAU;AAyBja,IAAM,oBAAoB,aAAW;AACnC,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,MAAM,CAAC,MAAM;AAAA,IACb,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,6BAA6B,OAAO;AACnE;AACA,IAAM,mBAAmB,eAAO,gBAAgB;AAAA,EAC9C,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,kDAAkD;AAAA,IAChD,gBAAgB;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,EACX,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,oCAAoC;AAAA,IACtC;AAAA,IACA,OAAO;AAAA,MACL,kDAAkD;AAAA,QAChD,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,mBAAmB,eAAO,kBAAU;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,SAAS;AACX,CAAC;AACM,IAAM,mBAAmB,eAAO,kBAAU;AAAA,EAC/C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,UAAQ,SAAS,eAAe,SAAS;AAC9D,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,mBAAmB;AAAA,IACjB,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,YAAY;AAAA,EACnM;AAAA,EACA,kBAAkB;AAAA,IAChB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IACvD,QAAQ,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IAC7C,4BAA4B;AAAA,MAC1B,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IACzD;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,YAAY;AAAA,EACnM;AACF,EAAE;AAWK,IAAM,eAAkC,iBAAW,SAASC,cAAa,SAAS,KAAK;AAC5F,QAAM,QAAQ,SAAS;AACvB,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,YAAY,WAAW,KAAK,YAAY;AAC9C,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF,OAAO,MAAM,6BAA6B;AAAA,IAC1C,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf,2CAA2C;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,QAAQ,CAAC,OAAO;AAAA,IAChB,eAAe;AAAA,IACf,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,EACF,IAAI,mBAAmB;AAAA,IACrB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,eAAe,sBAAsB;AAC3C,QAAM,MAAM,OAAO,QAAQ;AAC3B,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI,wBAAwB;AAC5B,QAAM,aAAa,SAAS,CAAC,GAAG,kBAAkB;AAAA,IAChD,oCAAoC,CAAC,CAAC,aAAa;AAAA,EACrD,CAAC;AACD,QAAM,UAAU,kBAAkB,WAAW;AAC7C,QAAM,aAAY,+BAAO,qBAAoB;AAC7C,QAAM,iBAAiB,qBAAa;AAAA,IAClC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B;AAAA,IACA,WAAW,QAAQ;AAAA,EACrB,CAAC;AACD,QAAM,uBAAuB,sBAAsB;AAAA,IACjD;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,oBAAoB,yBAAiB,cAAY,qBAAqB,UAAU,UAAU,OAAO,CAAC;AACxG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,yBAAiB,cAAY;AACpD,4BAAwB,UAAU,QAAQ;AAAA,EAC5C,CAAC;AACD,4BAAkB,MAAM;AACtB,QAAI,aAAa,YAAY,MAAM;AACjC;AAAA,IACF;AACA,UAAM,aAAa,aAAa,QAAQ,cAAc,wGAAwG;AAC9J,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,YAAY,WAAW;AAC7B,QAAI,aAAa,CAAC,CAAC,aAAa;AAC9B,iBAAW,MAAM;AAAA,IACnB;AAGA,iBAAa,QAAQ,YAAY,YAAY;AAAA,EAC/C,CAAC;AACD,QAAM,iBAAuB,kBAAY,kBAAgB;AACvD,UAAM,UAAU,4BAA4B,0CAA0C,KAAK;AAC3F,UAAM,oBAAoB,MAAM;AAC9B,UAAI,WAAW,QAAQ,SAAS,YAAY,GAAG;AAC7C,eAAO;AAAA,MACT;AACA,UAAI,WAAW,QAAQ,cAAc,OAAO,GAAG;AAC7C,eAAO;AAAA,MACT;AACA,UAAI,iBAAiB,QAAQ,cAAc,GAAG,GAAG;AAC/C,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ,KAAK,YAAY,GAAG;AAC7C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,eAAe,MAAM;AACzB,UAAI,MAAM,WAAW,YAAY,IAAI,gBAAgB,GAAG;AACtD,eAAO;AAAA,MACT;AACA,UAAI,mBAAmB;AACrB,eAAO,CAAC,kBAAkB,cAAc,OAAO;AAAA,MACjD;AACA,aAAO;AAAA,IACT;AACA,WAAO,CAAC,kBAAkB,KAAK,CAAC,aAAa;AAAA,EAC/C,GAAG,CAAC,0CAA0C,OAAO,SAAS,SAAS,eAAe,KAAK,aAAa,aAAa,iBAAiB,CAAC;AACvI,QAAM,cAAoB,cAAQ,MAAM;AACtC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,MAAM,WAAW,oBAAoB;AACxD,QAAI,qBAAqB;AACzB,WAAO,MAAM,UAAU,sBAAsB,kBAAkB,GAAG;AAChE,aAAO,KAAK,kBAAkB;AAC9B,2BAAqB,MAAM,WAAW,oBAAoB,QAAQ;AAAA,IACpE;AACA,WAAO;AAAA,EACT,GAAG,CAAC,sBAAsB,UAAU,KAAK,CAAC;AAC1C,QAAM,qBAAqB,YAAY,UAAU,YAAU,MAAM,QAAQ,QAAQ,oBAAoB,CAAC;AACtG,QAAM,gBAAgB,WAAS;AAC7B,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK,UACH;AACE,cAAM,WAAW,wBAAwB,QAAQ,OAAO,IAAI;AAC5D,cAAM,WAAW,QAAQ,QAAQ;AACjC,cAAM,kBAAkB,KAAK,IAAI,GAAG,QAAQ;AAC5C,cAAM,eAAe,SAAS,eAAe;AAC7C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF,KAAK,YACH;AACE,cAAM,WAAW,wBAAwB,QAAQ,OAAO,IAAI;AAC5D,cAAM,WAAW,QAAQ,QAAQ;AACjC,cAAM,kBAAkB,KAAK,IAAI,SAAS,SAAS,GAAG,QAAQ;AAC9D,cAAM,eAAe,SAAS,eAAe;AAC7C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,kBAAkB,SAAS;AAAA,IAClD,KAAK;AAAA,IACL,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,EACF,GAAG,OAAO;AAAA,IACR,cAAuB,mBAAAA,KAAK,kBAAkB;AAAA,MAC5C,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc,aAAa;AAAA,MAC3B,WAAW,QAAQ;AAAA,MACnB,WAAW;AAAA,MACX,UAAU,YAAY,IAAI,CAAC,QAAQ,UAAU;AAC3C,cAAM,iBAAiB,eAAe,MAAM;AAC5C,YAAI,gBAAgB,gBAAgB;AAClC,iBAAO;AAAA,QACT;AACA,cAAM,aAAa,MAAM,QAAQ,QAAQ,KAAK;AAC9C,cAAM,iBAAiB,MAAM,OAAO,QAAQ,OAAO,gBAAgB,aAAa;AAChF,cAAM,YAAY,uBAAuB,SAAS,uBAAuB,MAAM,UAAU;AACzF,cAAM,WAAW,YAAY,IAAI;AACjC,mBAAoB,mBAAAA,KAAK,WAAW,SAAS;AAAA,UAC3C,SAAS,MAAM,CAAC,YAAY,iBAAiB,MAAM;AAAA,UACnD,UAAU;AAAA,UACV,UAAU,YAAY;AAAA,UACtB,eAAe;AAAA,UACf,MAAM;AAAA,UAGN,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB;AAAA,UACA,WAAW;AAAA,UACX;AAAA,QACF,GAAG,gBAAgB;AAAA,UACjB,UAAU;AAAA,QACZ,CAAC,GAAG,GAAG,OAAO,QAAQ,CAAC,IAAI,cAAc,EAAE;AAAA,MAC7C,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,cAAa,cAAc;AACtE,OAAwC,aAAa,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS/D,MAAM,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,SAAS,kBAAAA,QAAU;AAAA,EACnB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,0CAA0C,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,kBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASvB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,qBAAqB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,QAAQ,kBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtJ,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,MAAM,kBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,OAAO,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI;;;AE1dJ,IAAAC,SAAuB;AAEvB,IAAAC,qBAAsB;;;ACLf,SAAS,wCAAwC,MAAM;AAC5D,SAAO,qBAAqB,+BAA+B,IAAI;AACjE;AACO,IAAM,kCAAkC,uBAAuB,+BAA+B,CAAC,MAAM,CAAC;;;ACA7G,IAAAC,SAAuB;;;ACHhB,SAAS,+CAA+C,MAAM;AACnE,SAAO,qBAAqB,sCAAsC,IAAI;AACxE;AACO,IAAM,yCAAyC,uBAAuB,sCAAsC,CAAC,QAAQ,MAAM,CAAC;;;ADYnI,IAAAC,sBAA4B;AAb5B,IAAMC,aAAY,CAAC,aAAa,YAAY,aAAa,WAAW,YAAY,YAAY,SAAS,UAAU,SAAS,aAAa,cAAc;AAcnJ,IAAMC,qBAAoB,aAAW;AACnC,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,gDAAgD,OAAO;AACtF;AACA,IAAM,sCAAsC,eAAO,kBAAU;AAAA,EAC3D,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,kDAAkD;AAAA,IAChD,gBAAgB;AAAA,EAClB;AAAA,EACA,0BAA0B;AAAA,IACxB,WAAW;AAAA,MACT,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,6CAA6C;AAAA,IAC3C,WAAW;AAAA,EACb;AAAA,EACA,yBAAyB;AAAA,IACvB,YAAY,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAChE;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA;AAAA,IAET,QAAQ;AAAA,EACV;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,oCAAoC;AAAA,IACtC;AAAA,IACA,OAAO;AAAA,MACL,kDAAkD;AAAA,QAChD,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE;AACF,IAAM,sCAAsC,eAAO,kBAAU;AAAA,EAC3D,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,IACjB,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,YAAY;AAAA,EACnM;AAAA,EACA,kBAAkB;AAAA,IAChB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IACvD,QAAQ,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IAC7C,4BAA4B;AAAA,MAC1B,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IACzD;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,YAAY;AAAA,EACnM;AACF,EAAE;AAIK,IAAM,kCAAqD,kBAAW,SAASC,iCAAgC,SAAS,KAAK;AAClI,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,YAAY,WAAW,KAAK,YAAY;AAC9C,QAAM,iBAAuB,cAAO,IAAI;AACxC,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOF,UAAS;AACxD,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI,wBAAwB;AAC5B,QAAM,aAAa,SAAS,CAAC,GAAG,kBAAkB;AAAA,IAChD,oCAAoC,CAAC,CAAC,aAAa;AAAA,EACrD,CAAC;AACD,QAAM,UAAUC,mBAAkB,WAAW;AAC7C,QAAM,2BAA0B,+BAAO,4BAA2B;AAClE,4BAAkB,MAAM;AACtB,QAAI,aAAa,YAAY,MAAM;AACjC;AAAA,IACF;AACA,UAAM,aAAa,aAAa,QAAQ,cAAc,sEAAsE;AAC5H,QAAI,UAAU,aAAa,YAAY;AACrC,iBAAW,MAAM;AAAA,IACnB;AACA,QAAI,CAAC,cAAc,eAAe,YAAY,YAAY;AACxD;AAAA,IACF;AACA,mBAAe,UAAU;AACzB,UAAM,YAAY,WAAW;AAG7B,iBAAa,QAAQ,YAAY,YAAY;AAAA,EAC/C,CAAC;AACD,QAAM,qBAAqB,MAAM,UAAU,UAAQ,KAAK,UAAU,KAAK,KAAK,CAAC;AAC7E,QAAM,gBAAgB,WAAS;AAC7B,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK,UACH;AACE,cAAM,WAAW,wBAAwB,aAAa,OAAO,IAAI;AACjE,cAAM,WAAW,aAAa,QAAQ;AACtC,cAAM,kBAAkB,KAAK,IAAI,GAAG,QAAQ;AAC5C,cAAM,eAAe,SAAS,eAAe;AAC7C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF,KAAK,YACH;AACE,cAAM,WAAW,wBAAwB,aAAa,OAAO,IAAI;AACjE,cAAM,WAAW,aAAa,QAAQ;AACtC,cAAM,kBAAkB,KAAK,IAAI,SAAS,SAAS,GAAG,QAAQ;AAC9D,cAAM,eAAe,SAAS,eAAe;AAC7C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAoB,oBAAAE,KAAK,qCAAqC,SAAS;AAAA,IACrE,KAAK;AAAA,IACL,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,eAAe,aAAa;AAAA,IAC5B,MAAM;AAAA,IACN,WAAW;AAAA,EACb,GAAG,OAAO;AAAA,IACR,UAAU,MAAM,IAAI,CAAC,QAAQ,UAAU;AAtL3C;AAuLM,YAAM,kBAAiB,YAAO,eAAP,gCAAoB,OAAO;AAClD,YAAM,aAAa,YAAY;AAC/B,UAAI,gBAAgB,YAAY;AAC9B,eAAO;AAAA,MACT;AACA,YAAM,aAAa,OAAO,WAAW,OAAO,KAAK;AACjD,YAAM,WAAW,uBAAuB,SAAS,uBAAuB,MAAM,UAAU,IAAI,IAAI;AAChG,iBAAoB,oBAAAA,KAAK,yBAAyB,SAAS;AAAA,QACzD,SAAS,MAAM,CAAC,YAAY,SAAS,OAAO,KAAK;AAAA,QACjD,UAAU;AAAA,QACV,UAAU;AAAA,QACV,eAAe;AAAA,QACf,MAAM;AAAA,QAGN,iBAAiB,YAAY,cAAc;AAAA,QAC3C,cAAc,OAAO;AAAA,QACrB,iBAAiB;AAAA,QACjB;AAAA,QACA,WAAW,QAAQ;AAAA,MACrB,GAAG,uCAAW,yBAAyB;AAAA,QACrC,UAAU,OAAO;AAAA,MACnB,CAAC,GAAG,OAAO,KAAK;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,iCAAgC,cAAc;;;AEjNlF,IAAM,wBAAwB,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,eAAe,QAAQ,MAAM,SAAS,KAAK,IAAI;AACrD,QAAM,SAAS,CAAC;AAChB,QAAM,aAAa,CAAC,MAAM,2BAA2B;AACnD,UAAM,uBAAuB,0BAA0B;AACvD,QAAI,yBAAyB,MAAM;AACjC,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AACR,UAAI,SAAS,IAAI;AACf,eAAO,yBAAyB,MAAM,yBAAyB;AAAA,MACjE;AACA,aAAO,yBAAyB,QAAQ,uBAAuB,OAAO;AAAA,IACxE;AACA,WAAO,yBAAyB;AAAA,EAClC;AACA,QAAM,YAAY,UAAQ;AACxB,WAAO,WAAW,MAAM,MAAM,SAAS,oBAAoB,CAAC;AAAA,EAC9D;AACA,QAAM,UAAU,OAAO,KAAK;AAC5B,WAAS,OAAO,GAAG,QAAQ,SAAS,QAAQ,UAAU;AACpD,QAAI,QAAQ,MAAM,OAAO,MAAM,SAAS,KAAK,IAAI,GAAG,OAAO,aAAa,UAAU;AAClF,UAAM,YAAY,iBAAiB,SAAS,OAAO,EAAE,EAAE,SAAS,CAAC;AACjE,YAAQ,MAAM,aAAa,KAAK;AAChC,WAAO,KAAK;AAAA,MACV,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACO,IAAM,wBAAwB,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,aAAa,eAAa;AAC9B,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,WAAO,YAAY,UAAU;AAAA,EAC/B;AACA,QAAM,YAAY,eAAa;AAC7B,WAAO,UAAU;AAAA,EACnB;AACA,SAAO,CAAC,GAAG,MAAM,KAAK;AAAA,IACpB,QAAQ,KAAK,KAAK,KAAK,QAAQ;AAAA,EACjC,GAAG,CAAC,GAAG,UAAU;AACf,UAAM,YAAY,WAAW;AAC7B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO,MAAM,aAAa,aAAa,SAAS,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,iBAAiB,UAAU,SAAS,CAAC;AAAA,IAClD;AAAA,EACF,CAAC,CAAC;AACJ;;;AJjDA,IAAAC,sBAA4B;AAtB5B,IAAMC,aAAY,CAAC,QAAQ,aAAa,aAAa,SAAS,aAAa,SAAS,gBAAgB,iBAAiB,4CAA4C,WAAW,WAAW,iBAAiB,eAAe,eAAe,qBAAqB,YAAY,QAAQ,SAAS,UAAU,gBAAgB,eAAe,uBAAuB,aAAa,WAAW,YAAY,YAAY,gBAAgB,UAAU;AAuBla,IAAMC,qBAAoB,aAAW;AACnC,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,yCAAyC,OAAO;AAC/E;AACA,IAAM,+BAA+B,eAAO,gBAAgB;AAAA,EAC1D,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,eAAe;AAAA,EACf,OAAO;AAAA,EACP,cAAc,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAClE,EAAE;AAWK,IAAM,2BAA8C,kBAAW,SAASC,0BAAyB,SAAS,KAAK;AACpH,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF,OAAO,MAAM,6BAA6B;AAAA,IAC1C,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf,2CAA2C;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,OAAO,UAAU,CAAC,SAAS,SAAS;AAAA,IACpC;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAOF,UAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,EACF,IAAI,mBAAmB;AAAA,IACrB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,eAAe,sBAAsB;AAC3C,QAAM,MAAM,OAAO,QAAQ;AAC3B,QAAM,YAAkB,eAAQ,MAAM,SAAS;AAAA,IAC7C,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG,WAAW,GAAG,CAAC,WAAW,CAAC;AAC9B,QAAM,uBAAuB,sBAAsB;AAAA,IACjD;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,oBAAoB,yBAAiB,CAAC,UAAU,gBAAgB,iBAAiB,qBAAqB,UAAU,gBAAgB,YAAY,CAAC;AACnJ,QAAM,QAAc,eAAQ,MAAM;AAChC,QAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS,OAAO,GAAG;AACvC,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,SAAS,UAAU,IAAI,UAAU,CAAC,GAAG,SAAS,UAAU;AAAA,EACzE,GAAG,CAAC,MAAM,OAAO,CAAC;AAClB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,4BAA4B,yBAAiB,cAAY;AAC7D,4BAAwB,UAAU,UAAU,UAAU;AAAA,EACxD,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,sBAAsB,MAAM,2BAA2B,QAAQ;AACnF,QAAM,iBAAuB,mBAAY,CAAC,UAAU,aAAa;AAC/D,UAAM,UAAU,4BAA4B,0CAA0C,KAAK;AAC3F,UAAM,qBAAqB,aAAa,WAAW,aAAa,aAAa,MAAM,SAAS,SAAS;AACrG,UAAM,oBAAoB,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,WAAW,QAAQ,SAAS,GAAG,GAAG;AACpC,eAAO;AAAA,MACT;AACA,UAAI,WAAW,QAAQ,OAAO,OAAO,GAAG;AACtC,eAAO;AAAA,MACT;AACA,UAAI,iBAAiB,QAAQ,OAAO,GAAG,GAAG;AACxC,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ,KAAK,qBAAqB,MAAM,KAAK,GAAG;AACjE,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,eAAe,CAAC,WAAW,OAAO,MAAM;AAC5C,UAAI,YAAY,SAAS,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,mBAAmB;AACrB,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,CAAC,kBAAkB,MAAM,SAAS,sBAAsB,SAAS,GAAG,OAAO;AAAA,UACpF,KAAK;AACH,mBAAO,CAAC,kBAAkB,MAAM,WAAW,sBAAsB,SAAS,GAAG,SAAS;AAAA,UACxF,KAAK;AACH,mBAAO,CAAC,kBAAkB,MAAM,WAAW,sBAAsB,SAAS,GAAG,SAAS;AAAA,UACxF;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,YAAQ,UAAU;AAAA,MAChB,KAAK,SACH;AACE,cAAM,oBAAoB,uBAAuB,UAAU,cAAc,IAAI;AAC7E,cAAM,mBAAmB,MAAM,SAAS,sBAAsB,iBAAiB;AAC/E,YAAI,MAAM,SAAS,gBAAgB,MAAM,mBAAmB;AAC1D,iBAAO;AAAA,QACT;AACA,cAAM,QAAQ,MAAM,WAAW,MAAM,WAAW,kBAAkB,CAAC,GAAG,CAAC;AACvE,cAAM,MAAM,MAAM,WAAW,MAAM,WAAW,kBAAkB,EAAE,GAAG,EAAE;AACvE,eAAO,CAAC,kBAAkB;AAAA,UACxB;AAAA,UACA;AAAA,QACF,CAAC,KAAK,CAAC,aAAa,iBAAiB;AAAA,MACvC;AAAA,MACF,KAAK,WACH;AACE,cAAM,qBAAqB,MAAM,WAAW,sBAAsB,QAAQ;AAC1E,cAAM,QAAQ,MAAM,WAAW,oBAAoB,CAAC;AACpD,cAAM,MAAM,MAAM,WAAW,oBAAoB,EAAE;AACnD,eAAO,CAAC,kBAAkB;AAAA,UACxB;AAAA,UACA;AAAA,QACF,CAAC,KAAK,CAAC,aAAa,UAAU,WAAW;AAAA,MAC3C;AAAA,MACF,KAAK,WACH;AACE,cAAM,qBAAqB,MAAM,WAAW,sBAAsB,QAAQ;AAC1E,cAAM,QAAQ;AACd,cAAM,MAAM;AACZ,eAAO,CAAC,kBAAkB;AAAA,UACxB;AAAA,UACA;AAAA,QACF,CAAC,KAAK,CAAC,aAAa,QAAQ;AAAA,MAC9B;AAAA,MACF;AACE,cAAM,IAAI,MAAM,eAAe;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,MAAM,sBAAsB,0CAA0C,SAAS,cAAc,SAAS,aAAa,mBAAmB,OAAO,eAAe,aAAa,KAAK,KAAK,CAAC;AACxL,QAAM,iBAAuB,mBAAY,iBAAe;AACtD,YAAQ,aAAa;AAAA,MACnB,KAAK,SACH;AACE,eAAO;AAAA,UACL,UAAU,WAAS;AACjB,kBAAM,oBAAoB,uBAAuB,OAAO,cAAc,IAAI;AAC1E,oCAAwB,MAAM,SAAS,sBAAsB,iBAAiB,GAAG,UAAU,OAAO;AAAA,UACpG;AAAA,UACA,OAAO,sBAAsB;AAAA,YAC3B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,YAAY,WAAS,eAAe,OAAO,OAAO;AAAA,YAClD,UAAU,UAAU;AAAA,YACpB,kBAAkB,aAAa;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACF,KAAK,WACH;AACE,eAAO;AAAA,UACL,UAAU,aAAW;AACnB,oCAAwB,MAAM,WAAW,sBAAsB,OAAO,GAAG,UAAU,SAAS;AAAA,UAC9F;AAAA,UACA,OAAO,sBAAsB;AAAA,YAC3B,OAAO,MAAM,WAAW,oBAAoB;AAAA,YAC5C;AAAA,YACA,YAAY,aAAW,eAAe,SAAS,SAAS;AAAA,YACxD,cAAc,aAAW,MAAM,OAAO,MAAM,WAAW,KAAK,OAAO,GAAG,SAAS;AAAA,YAC/E,UAAU,UAAU;AAAA,YACpB,UAAU,CAAC,CAAC;AAAA,YACZ,kBAAkB,aAAa;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACF,KAAK,WACH;AACE,eAAO;AAAA,UACL,UAAU,aAAW;AACnB,oCAAwB,MAAM,WAAW,sBAAsB,OAAO,GAAG,UAAU,SAAS;AAAA,UAC9F;AAAA,UACA,OAAO,sBAAsB;AAAA,YAC3B,OAAO,MAAM,WAAW,oBAAoB;AAAA,YAC5C;AAAA,YACA,YAAY,aAAW,eAAe,SAAS,SAAS;AAAA,YACxD,cAAc,aAAW,MAAM,OAAO,MAAM,WAAW,KAAK,OAAO,GAAG,SAAS;AAAA,YAC/E,UAAU,UAAU;AAAA,YACpB,UAAU,CAAC,CAAC;AAAA,YACZ,kBAAkB,aAAa;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACF,KAAK,YACH;AACE,cAAM,UAAU,eAAe,OAAO,IAAI;AAC1C,cAAM,UAAU,eAAe,OAAO,IAAI;AAC1C,eAAO;AAAA,UACL,UAAU;AAAA,UACV,OAAO,CAAC;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,YACP,YAAY,MAAM,CAAC,CAAC,SAAS,iBAAiB;AAAA,YAC9C,WAAW,MAAM,CAAC,CAAC,wBAAwB,iBAAiB;AAAA,YAC5D,WAAW;AAAA,UACb,GAAG;AAAA,YACD,OAAO;AAAA,YACP,OAAO;AAAA,YACP,YAAY,MAAM,CAAC,CAAC,SAAS,iBAAiB;AAAA,YAC9C,WAAW,MAAM,CAAC,CAAC,wBAAwB,iBAAiB;AAAA,YAC5D,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACF;AACE,cAAM,IAAI,MAAM,iBAAiB,WAAW,SAAS;AAAA,IACzD;AAAA,EACF,GAAG,CAAC,KAAK,OAAO,MAAM,OAAO,UAAU,OAAO,UAAU,SAAS,UAAU,SAAS,aAAa,sBAAsB,aAAa,wBAAwB,aAAa,wBAAwB,cAAc,yBAAyB,sBAAsB,gBAAgB,oBAAoB,CAAC;AACnS,QAAM,gBAAsB,eAAQ,MAAM;AACxC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,UAAM,aAAa,MAAM,OAAO,OAAK,MAAM,UAAU;AACrD,eAAW,QAAQ;AACnB,QAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,iBAAW,KAAK,UAAU;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,KAAK,CAAC;AACjB,QAAM,kBAAwB,eAAQ,MAAM;AAC1C,WAAO,MAAM,OAAO,CAAC,QAAQ,gBAAgB;AAC3C,aAAO,SAAS,CAAC,GAAG,QAAQ;AAAA,QAC1B,CAAC,WAAW,GAAG,eAAe,WAAW;AAAA,MAC3C,CAAC;AAAA,IACH,GAAG,CAAC,CAAC;AAAA,EACP,GAAG,CAAC,OAAO,cAAc,CAAC;AAC1B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,wBAAwB;AAC5B,QAAM,UAAUC,mBAAkB,WAAW;AAC7C,aAAoB,oBAAAE,KAAK,8BAA8B,SAAS;AAAA,IAC9D;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,MAAM;AAAA,EACR,GAAG,OAAO;AAAA,IACR,UAAU,cAAc,IAAI,kBAAyB,oBAAAA,KAAK,iCAAiC;AAAA,MACzF,OAAO,gBAAgB,QAAQ,EAAE;AAAA,MACjC,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACpC,QAAQ,SAAS;AAAA,MACjB,WAAW,aAAa,gBAAgB;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,aAAa,eAAe,QAAQ;AAAA,IACpD,GAAG,QAAQ,CAAC;AAAA,EACd,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,0BAAyB,cAAc;AAClF,OAAwC,yBAAyB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3E,MAAM,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,SAAS,mBAAAA,QAAU;AAAA,EACnB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,0CAA0C,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,mBAAAA,QAAU,MAAM,CAAC,SAAS,YAAY,WAAW,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASvB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,QAAQ,mBAAAA,QAAU,MAAM,CAAC,SAAS,YAAY,WAAW,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnE,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtJ,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,OAAO,mBAAAA,QAAU;AAAA,IACjB,SAAS,mBAAAA,QAAU;AAAA,IACnB,SAAS,mBAAAA,QAAU;AAAA,EACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,MAAM,mBAAAA,QAAU,MAAM,CAAC,SAAS,YAAY,WAAW,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjE,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,SAAS,YAAY,WAAW,SAAS,CAAC,EAAE,UAAU;AAClG,IAAI;;;AH3fJ,IAAAC,sBAA4B;AACrB,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,UAAmB,oBAAAC,KAAK,WAAW;AAAA,EACjC;AAAA,EACA;AAAA,EACA,aAAa,eAAe,WAAW,WAAW,IAAI,cAAc;AAAA,EACpE;AAAA,EACA,OAAO,MAAM,OAAO,UAAU;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,KAAuC,qBAAoB,cAAc;AACtE,IAAM,6BAA6B,CAAC;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,UAAmB,oBAAAA,KAAK,cAAc;AAAA,EACpC;AAAA,EACA;AAAA,EACA,aAAa,eAAe,WAAW,WAAW,IAAI,cAAc;AAAA,EACpE;AAAA,EACA,OAAO,MAAM,OAAO,UAAU;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,uCAAW;AAAA,EACrB;AAAA,EACA;AACF,CAAC;AACD,IAAI,KAAuC,4BAA2B,cAAc;AAC7E,IAAM,yCAAyC,CAAC;AAAA,EACrD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,UAAmB,oBAAAA,KAAK,0BAA0B;AAAA,EAChD;AAAA,EACA;AAAA,EACA,aAAa,eAAe,mBAAmB,WAAW,IAAI,cAAc;AAAA,EAC5E;AAAA,EACA,OAAO,MAAM,OAAO,UAAU;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,KAAuC,wCAAuC,cAAc;", "names": ["React", "DigitalClock", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_jsx_runtime", "_excluded", "useUtilityClasses", "MultiSectionDigitalClockSection", "_jsx", "import_jsx_runtime", "_excluded", "useUtilityClasses", "MultiSectionDigitalClock", "_jsx", "PropTypes", "import_jsx_runtime", "_jsx"]}