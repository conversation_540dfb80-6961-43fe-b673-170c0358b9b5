import {
  administrationGenerated<PERSON>pi,
  useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation,
  useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery,
  useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery,
} from './generated/administration.generated';

// Re-export only types and the enhanced API, not the verbose hooks
export type * from './generated/administration.generated';

// Enhanced API with proper cache tags using enhanceEndpoints
export const administrationApi = administrationGeneratedApi.enhanceEndpoints({
  addTagTypes: ['Job', 'JobTrigger'],
  endpoints: {
    // Job Management Endpoints
    atApiServiceEndpointsAdministrationJobsGetJobsEndpoint: {
      providesTags: ['Job'],
    },
    atApiServiceEndpointsAdministrationJobsGetJobEndpoint: {
      providesTags: (result, error, arg) => [{ type: 'Job', id: arg.rootEntityId }],
    },
    atApiServiceEndpointsAdministrationJobsPostJobEndpoint: {
      invalidatesTags: ['Job'],
    },
    atApiServiceEndpointsAdministrationJobsPutJobEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'Job', id: arg.entityId },
        'Job',
      ],
    },
    atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'Job', id: arg.entityId },
        'Job',
      ],
    },

    // Job Trigger Management Endpoints
    atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint: {
      providesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: `job-${arg.jobId}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint: {
      providesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.subEntityId1 },
      ],
    },
    atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: `job-${arg.jobId}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.entityId2 },
        { type: 'JobTrigger', id: `job-${arg.entityId1}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.entityId2 },
        { type: 'JobTrigger', id: `job-${arg.entityId1}` },
        'JobTrigger',
      ],
    },
  },
});

// Clean hook exports that map to the verbose generated hook names
export const useGetJobsQuery = useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery;
export const useGetJobQuery = useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery;
export const useGetJobRunQuery = useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery;
export const useGetJobRunLogsQuery = useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery;
export const useCreateJobMutation = useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation;
export const useUpdateJobMutation = useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation;
export const useDeleteJobMutation = useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation;

// Job Trigger hooks
export const useGetJobTriggersQuery = useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery;
export const useGetJobTriggerQuery = useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery;
export const useCreateJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation;
export const useUpdateJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation;
export const useDeleteJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation;