import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { GridRowId, GridFilterModel } from '@mui/x-data-grid-premium';

// Types for the jobRuns UI state
export interface JobRunsGridConfig {
  columnVisibility: Record<string, boolean>;
  sortModel: Array<{ field: string; sort: 'asc' | 'desc' | null }>;
  filterModel: GridFilterModel;
  pageSize: number;
  page: number;
}

export interface JobRunsSelection {
  selectedIds: GridRowId[];
}

export interface JobRunsUIState {
  activeTab: number;
}

export interface JobRunsState {
  gridConfig: JobRunsGridConfig;
  selection: JobRunsSelection;
  ui: JobRunsUIState;
}

// Initial state matching current component defaults
const initialState: JobRunsState = {
  gridConfig: {
    columnVisibility: {},
    sortModel: [{ field: 'triggered', sort: 'desc' }], 
    filterModel: { items: [] },
    pageSize: 10,
    page: 0,
  },
  selection: {
    selectedIds: [],
  },
  ui: {
    activeTab: 0,
  },
};

const jobRunsSlice = createSlice({
  name: 'jobRuns',
  initialState,
  reducers: {
    // Grid configuration actions
    setPageSize: (state, action: PayloadAction<number>) => {
      state.gridConfig.pageSize = action.payload;
      state.gridConfig.page = 0; // Reset to first page when page size changes
    },
    
    setPage: (state, action: PayloadAction<number>) => {
      state.gridConfig.page = action.payload;
    },
    
    setSortModel: (state, action: PayloadAction<Array<{ field: string; sort: 'asc' | 'desc' | null }>>) => {
      state.gridConfig.sortModel = action.payload;
    },

    setFilterModel: (state, action: PayloadAction<GridFilterModel>) => {
      state.gridConfig.filterModel = action.payload;
      state.gridConfig.page = 0; // Reset to first page when filters change
    },

    setColumnVisibility: (state, action: PayloadAction<Record<string, boolean>>) => {
      state.gridConfig.columnVisibility = action.payload;
    },
    
    // Selection actions
    setSelectedIds: (state, action: PayloadAction<GridRowId[]>) => {
      state.selection.selectedIds = action.payload;
    },

    clearSelection: (state) => {
      state.selection.selectedIds = [];
    },
    
    // UI actions
    setActiveTab: (state, action: PayloadAction<number>) => {
      state.ui.activeTab = action.payload;
    },
    
    // Reset all state
    resetJobRunsState: () => initialState,
  },
});

export const {
  setPageSize,
  setPage,
  setSortModel,
  setFilterModel,
  setColumnVisibility,
  setSelectedIds,
  clearSelection,
  setActiveTab,
  resetJobRunsState: resetJobLogsState,
} = jobRunsSlice.actions;

// Selectors
export const selectJobRunsGridConfig = (state: { jobRuns: JobRunsState }) => state.jobRuns.gridConfig;
export const selectJobRunsSelection = (state: { jobRuns: JobRunsState }) => state.jobRuns.selection;
export const selectJobRunsUI = (state: { jobRuns: JobRunsState }) => state.jobRuns.ui;

// Specific selectors for common use cases
export const selectPageSize = (state: { jobRuns: JobRunsState }) => state.jobRuns.gridConfig.pageSize;
export const selectPage = (state: { jobRuns: JobRunsState }) => state.jobRuns.gridConfig.page;
export const selectSortModel = (state: { jobRuns: JobRunsState }) => state.jobRuns.gridConfig.sortModel;
export const selectFilterModel = (state: { jobRuns: JobRunsState }) => state.jobRuns.gridConfig.filterModel;
export const selectSelectedIds = (state: { jobRuns: JobRunsState }) => state.jobRuns.selection.selectedIds;
export const selectActiveTab = (state: { jobRuns: JobRunsState }) => state.jobRuns.ui.activeTab;

export default jobRunsSlice.reducer;
