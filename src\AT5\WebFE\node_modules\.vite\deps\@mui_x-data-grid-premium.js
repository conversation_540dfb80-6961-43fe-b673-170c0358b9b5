import {
  DATA_GRID_PRO_PROPS_DEFAULT_VALUES,
  MemoizedWatermark,
  useLicenseVerifier
} from "./chunk-Z75LME4T.js";
import {
  DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS,
  GRID_COLUMN_MENU_SLOTS_PRO,
  GRID_COLUMN_MENU_SLOT_PROPS_PRO,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
  GRID_REORDER_COL_DEF,
  GridColumnHeaders,
  GridColumnMenuPinningItem,
  GridDetailPanelToggleCell,
  GridHeaderFilterMenu,
  GridHeaderFilterMenuContainer,
  GridPushPinLeftIcon,
  GridPushPinRightIcon,
  GridRowReorderCell,
  GridTreeDataGroupingCell,
  Memoized as Memoized3,
  RowGroupingStrategy,
  addPinnedRow,
  columnPinningStateInitializer,
  columnReorderStateInitializer,
  createRowTree,
  dataSourceStateInitializer,
  detailPanelStateInitializer,
  getGroupRowIdFromPath,
  getParentPath,
  getVisibleRowsLookup,
  gridColumnReorderDragColSelector,
  gridColumnReorderSelector,
  gridDataSourceErrorSelector,
  gridDataSourceLoadingIdSelector,
  gridDetailPanelExpandedRowIdsSelector,
  gridDetailPanelExpandedRowsContentCacheSelector,
  headerFilteringStateInitializer,
  insertNodeInTree,
  propValidatorsDataGridPro,
  removeNodeFromTree,
  renderRowReorderCell,
  rowPinningStateInitializer,
  rowReorderStateInitializer,
  skipFiltering,
  skipSorting,
  sortRowTree,
  updateRowTree,
  useGridAriaAttributesPro,
  useGridColumnPinning,
  useGridColumnPinningPreProcessors,
  useGridColumnReorder,
  useGridDataSourceBasePro,
  useGridDataSourceLazyLoader,
  useGridDataSourceTreeDataPreProcessors,
  useGridDetailPanel,
  useGridDetailPanelPreProcessors,
  useGridHeaderFiltering,
  useGridInfiniteLoader,
  useGridInfiniteLoadingIntersection,
  useGridLazyLoader,
  useGridLazyLoaderPreProcessors,
  useGridRootProps,
  useGridRowAriaAttributesPro,
  useGridRowPinning,
  useGridRowPinningPreProcessors,
  useGridRowReorder,
  useGridRowReorderPreProcessors,
  useGridTreeData,
  useGridTreeDataPreProcessors
} from "./chunk-F22U6ZUN.js";
import {
  COMFORTABLE_DENSITY_FACTOR,
  COMPACT_DENSITY_FACTOR,
  ColumnsPanelTrigger,
  DEFAULT_GRID_AUTOSIZE_OPTIONS,
  DEFAULT_GRID_COL_TYPE_KEY,
  EMPTY_PINNED_COLUMN_FIELDS,
  EMPTY_RENDER_CONTEXT,
  ExportCsv,
  ExportPrint,
  FilterPanelTrigger,
  GRID_ACTIONS_COLUMN_TYPE,
  GRID_ACTIONS_COL_DEF,
  GRID_BOOLEAN_COL_DEF,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GRID_CHECKBOX_SELECTION_FIELD,
  GRID_DATETIME_COL_DEF,
  GRID_DATE_COL_DEF,
  GRID_DEFAULT_LOCALE_TEXT,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GRID_EXPERIMENTAL_ENABLED,
  GRID_ID_AUTOGENERATED,
  GRID_NUMERIC_COL_DEF,
  GRID_ROOT_GROUP_ID,
  GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD,
  GRID_SINGLE_SELECT_COL_DEF,
  GRID_STRING_COL_DEF,
  GRID_TREE_DATA_GROUPING_FIELD,
  GridActionsCell,
  GridActionsCellItem,
  GridAddIcon,
  GridApiContext,
  GridArrowDownwardIcon,
  GridArrowUpwardIcon,
  GridBooleanCell,
  GridCellCheckboxForwardRef,
  GridCellCheckboxRenderer,
  GridCellEditStartReasons,
  GridCellEditStopReasons,
  GridCellModes,
  GridCheckCircleIcon,
  GridCheckIcon,
  GridClearIcon,
  GridCloseIcon,
  GridColumnHeaderFilterIconButtonWrapped,
  GridColumnHeaderMenu,
  GridColumnHeaderSeparator,
  GridColumnHeaderSeparatorSides,
  GridColumnHeaderSortIcon,
  GridColumnHeaderTitle,
  GridColumnIcon,
  GridColumnMenuColumnsItem,
  GridColumnMenuContainer,
  GridColumnMenuFilterItem,
  GridColumnMenuHideItem,
  GridColumnMenuManageItem,
  GridColumnMenuSortItem,
  GridColumnSortButton,
  GridColumnsManagement,
  GridColumnsPanel,
  GridContextProvider,
  GridCsvExportMenuItem,
  GridDataSourceCacheDefault,
  GridDeleteForeverIcon,
  GridDeleteIcon,
  GridDownloadIcon,
  GridDragIcon,
  GridEditBooleanCell,
  GridEditDateCell,
  GridEditInputCell,
  GridEditModes,
  GridEditSingleSelectCell,
  GridExpandMoreIcon,
  GridFilterAltIcon,
  GridFilterForm,
  GridFilterInputBoolean,
  GridFilterInputDate,
  GridFilterInputMultipleSingleSelect,
  GridFilterInputMultipleValue,
  GridFilterInputSingleSelect,
  GridFilterInputValue,
  GridFilterListIcon,
  GridFilterPanel,
  GridFooter,
  GridFooterContainer,
  GridFooterPlaceholder,
  GridGenericColumnMenu,
  GridGetRowsError,
  GridHeader,
  GridHeaderCheckbox,
  GridKeyboardArrowRight,
  GridLoadIcon,
  GridLoadingOverlay,
  GridLogicOperator,
  GridMenu,
  GridMenuIcon,
  GridMoreVertIcon,
  GridNoColumnsOverlay,
  GridNoRowsOverlay,
  GridOverlay,
  GridPagination,
  GridPanel,
  GridPanelContent,
  GridPanelFooter,
  GridPanelHeader,
  GridPanelWrapper,
  GridPinnedColumnPosition,
  GridPortalWrapper,
  GridPreferencePanelsValue,
  GridPrintExportMenuItem,
  GridRemoveIcon,
  GridRowCount,
  GridRowEditStartReasons,
  GridRowEditStopReasons,
  GridRowModes,
  GridSearchIcon,
  GridSelectedRowCount,
  GridSeparatorIcon,
  GridShadowScrollArea,
  GridSignature,
  GridSkeletonLoadingOverlayInner,
  GridStrategyGroup,
  GridTableRowsIcon,
  GridToolbar,
  GridToolbar2,
  GridToolbarColumnsButton,
  GridToolbarContainer,
  GridToolbarDensitySelector,
  GridToolbarExport,
  GridToolbarExportContainer,
  GridToolbarFilterButton,
  GridToolbarQuickFilter,
  GridTripleDotsVerticalIcon,
  GridUpdateRowError,
  GridViewColumnIcon,
  GridViewHeadlineIcon,
  GridViewStreamIcon,
  GridVirtualScroller,
  GridVisibilityOffIcon,
  Memoized,
  Memoized2,
  MemoizedGridCell,
  MemoizedGridRoot,
  MemoizedGridRow,
  NotRendered,
  QuickFilter,
  QuickFilterClear,
  QuickFilterControl,
  QuickFilterTrigger,
  Toolbar,
  ToolbarButton,
  checkGridRowIdIsValid,
  columnGroupsStateInitializer,
  columnMenuStateInitializer,
  columnResizeStateInitializer,
  columnsStateInitializer,
  computeSlots,
  createRootSelector,
  createRowSelectionManager,
  createSelector,
  createSelectorMemoized,
  createSvgIcon,
  defaultGetRowsToExport,
  densityStateInitializer,
  dimensionsStateInitializer,
  editingStateInitializer,
  exportAs,
  filterStateInitializer,
  focusStateInitializer,
  forwardRef,
  getActiveElement,
  getColumnsToExport,
  getDataGridUtilityClass,
  getDefaultColTypeDef,
  getDefaultGridFilterModel,
  getGridBooleanOperators,
  getGridDateOperators,
  getGridDefaultColumnTypes,
  getGridNumericOperators,
  getGridNumericQuickFilterFn,
  getGridSingleSelectOperators,
  getGridStringOperators,
  getGridStringQuickFilterFn,
  getPublicApiRef,
  getRowGroupingCriteriaFromGroupingField,
  getRowIdFromRowModel,
  getRowValue,
  getTotalHeaderHeight,
  getValueOptions,
  getVisibleRows,
  gridClasses,
  gridColumnDefinitionsSelector,
  gridColumnFieldsSelector,
  gridColumnGroupingSelector,
  gridColumnGroupsHeaderMaxDepthSelector,
  gridColumnGroupsHeaderStructureSelector,
  gridColumnGroupsLookupSelector,
  gridColumnGroupsUnwrappedModelSelector,
  gridColumnLookupSelector,
  gridColumnMenuSelector,
  gridColumnPositionsSelector,
  gridColumnResizeSelector,
  gridColumnVisibilityModelSelector,
  gridColumnsStateSelector,
  gridColumnsTotalWidthSelector,
  gridDataRowIdsSelector,
  gridDateComparator,
  gridDateFormatter,
  gridDateTimeFormatter,
  gridDensityFactorSelector,
  gridDensitySelector,
  gridDimensionsSelector,
  gridEditCellStateSelector,
  gridEditRowsStateSelector,
  gridExpandedRowCountSelector,
  gridExpandedSortedRowEntriesSelector,
  gridExpandedSortedRowIdsSelector,
  gridExpandedSortedRowIndexLookupSelector,
  gridFilterActiveItemsLookupSelector,
  gridFilterActiveItemsSelector,
  gridFilterModelSelector,
  gridFilterableColumnDefinitionsSelector,
  gridFilterableColumnLookupSelector,
  gridFilteredDescendantCountLookupSelector,
  gridFilteredDescendantRowCountSelector,
  gridFilteredRowCountSelector,
  gridFilteredRowsLookupSelector,
  gridFilteredSortedRowEntriesSelector,
  gridFilteredSortedRowIdsSelector,
  gridFilteredSortedTopLevelRowEntriesSelector,
  gridFilteredTopLevelRowCountSelector,
  gridFocusCellSelector,
  gridFocusColumnGroupHeaderSelector,
  gridFocusColumnHeaderFilterSelector,
  gridFocusColumnHeaderSelector,
  gridFocusStateSelector,
  gridHasColSpanSelector,
  gridHeaderFilteringEditFieldSelector,
  gridHeaderFilteringEnabledSelector,
  gridHeaderFilteringMenuSelector,
  gridHeaderFilteringStateSelector,
  gridListColumnSelector,
  gridListViewSelector,
  gridNumberComparator,
  gridPageCountSelector,
  gridPageSelector,
  gridPageSizeSelector,
  gridPaginatedVisibleSortedGridRowEntriesSelector,
  gridPaginatedVisibleSortedGridRowIdsSelector,
  gridPaginationEnabledClientSideSelector,
  gridPaginationMetaSelector,
  gridPaginationModelSelector,
  gridPaginationRowCountSelector,
  gridPaginationRowRangeSelector,
  gridPaginationSelector,
  gridPanelClasses,
  gridPinnedColumnsSelector,
  gridPivotActiveSelector,
  gridPivotInitialColumnsSelector,
  gridPreferencePanelStateSelector,
  gridQuickFilterValuesSelector,
  gridRenderContextColumnsSelector,
  gridRenderContextSelector,
  gridResizingColumnFieldSelector,
  gridRowCountSelector,
  gridRowGroupingNameSelector,
  gridRowIdSelector,
  gridRowIsEditingSelector,
  gridRowMaximumTreeDepthSelector,
  gridRowNodeSelector,
  gridRowSelectionCountSelector,
  gridRowSelectionIdsSelector,
  gridRowSelectionManagerSelector,
  gridRowSelectionStateSelector,
  gridRowSelector,
  gridRowTreeDepthsSelector,
  gridRowTreeSelector,
  gridRowsLoadingSelector,
  gridRowsLookupSelector,
  gridRowsMetaSelector,
  gridSortColumnLookupSelector,
  gridSortModelSelector,
  gridSortedRowEntriesSelector,
  gridSortedRowIdsSelector,
  gridStringOrNumberComparator,
  gridTabIndexCellSelector,
  gridTabIndexColumnGroupHeaderSelector,
  gridTabIndexColumnHeaderFilterSelector,
  gridTabIndexColumnHeaderSelector,
  gridTabIndexStateSelector,
  gridTopLevelRowCountSelector,
  gridVirtualizationColumnEnabledSelector,
  gridVirtualizationEnabledSelector,
  gridVirtualizationRowEnabledSelector,
  gridVirtualizationSelector,
  gridVisibleColumnDefinitionsSelector,
  gridVisibleColumnFieldsSelector,
  gridVisiblePinnedColumnDefinitionsSelector,
  gridVisibleRowsLookupSelector,
  gridVisibleRowsSelector,
  isAutogeneratedRow,
  isDeepEqual,
  isGroupingColumn,
  isLeaf,
  isNavigationKey,
  isNumber,
  isObject,
  isPasteShortcut,
  isSingleSelectColDef,
  listViewStateInitializer,
  paginationStateInitializer,
  passFilterLogic,
  preferencePanelStateInitializer,
  propValidatorsDataGrid,
  propsStateInitializer,
  renderActionsCell,
  renderBooleanCell,
  renderEditBooleanCell,
  renderEditDateCell,
  renderEditInputCell,
  renderEditSingleSelectCell,
  rowSelectionStateInitializer,
  rowSpanningStateInitializer,
  rowsMetaStateInitializer,
  rowsStateInitializer,
  serializeCellValue,
  sortingStateInitializer,
  toPropertyKey,
  unstable_resetCleanupTracking,
  useComponentRenderer,
  useFirstRender,
  useGridApiContext,
  useGridApiInitialization,
  useGridApiMethod,
  useGridApiRef,
  useGridClipboard,
  useGridColumnGrouping,
  useGridColumnMenu,
  useGridColumnResize,
  useGridColumnSpanning,
  useGridColumns,
  useGridCsvExport,
  useGridDensity,
  useGridDimensions,
  useGridEditing,
  useGridEvent,
  useGridEventPriority,
  useGridEvents,
  useGridFilter,
  useGridFocus,
  useGridInitialization,
  useGridInitializeState,
  useGridKeyboardNavigation,
  useGridListView,
  useGridLogger,
  useGridNativeEventListener,
  useGridPagination,
  useGridPanelContext,
  useGridParamsApi,
  useGridPreferencesPanel,
  useGridPrintExport,
  useGridPrivateApiContext,
  useGridRegisterPipeProcessor,
  useGridRegisterStrategyProcessor,
  useGridRowSelection,
  useGridRowSelectionPreProcessors,
  useGridRowSpanning,
  useGridRows,
  useGridRowsMeta,
  useGridRowsPreProcessors,
  useGridScroll,
  useGridSelector,
  useGridSorting,
  useGridStatePersistence,
  useGridVirtualization,
  useGridVirtualizer,
  useMaterialCSSVariables,
  useOnMount,
  useRunOnce,
  useRunOncePerLoop,
  validateProps,
  vars,
  virtualizationStateInitializer,
  warnOnce
} from "./chunk-MC3F52YZ.js";
import "./chunk-IIUSGDYG.js";
import {
  getThemeProps,
  styled_default as styled_default2
} from "./chunk-K3WPA3ZI.js";
import "./chunk-TWYH2CSE.js";
import "./chunk-ZG6HMRMM.js";
import "./chunk-PV4FMBZO.js";
import "./chunk-242P2TNS.js";
import "./chunk-QANYZCDJ.js";
import "./chunk-HT2YSOKA.js";
import "./chunk-57DMPVAT.js";
import "./chunk-GA2JHSPG.js";
import "./chunk-C2GYKKIV.js";
import "./chunk-O4EN72SE.js";
import "./chunk-5SS734A6.js";
import "./chunk-WKLPWJDB.js";
import "./chunk-ABKDQR2I.js";
import "./chunk-BSVA4ZRV.js";
import "./chunk-VXND5AAA.js";
import "./chunk-V3MHY6M4.js";
import "./chunk-FM3CYTQT.js";
import "./chunk-VHPMIWLX.js";
import "./chunk-JAESOEEL.js";
import "./chunk-A6MFVUUX.js";
import "./chunk-4DLCZEAF.js";
import "./chunk-4KAUVY4E.js";
import "./chunk-3OTPBVKS.js";
import "./chunk-ZEPMJIZH.js";
import "./chunk-PWMJGB5P.js";
import "./chunk-WGJAVVDM.js";
import "./chunk-ZCLIDCLD.js";
import "./chunk-YN27PUKW.js";
import "./chunk-QFBRDMUT.js";
import "./chunk-TLBGFVED.js";
import "./chunk-CUZUNDKH.js";
import "./chunk-NV7PZCJM.js";
import "./chunk-RXTWTMI6.js";
import "./chunk-VN7OJYOH.js";
import "./chunk-MBP7TPJO.js";
import "./chunk-5IEXKNXB.js";
import "./chunk-6CV5C7MM.js";
import "./chunk-ZPFGGG6H.js";
import "./chunk-CE5ETJ3V.js";
import "./chunk-4V3NUAZW.js";
import "./chunk-IUQBLD37.js";
import "./chunk-KQSKNSZC.js";
import "./chunk-47OS7YOP.js";
import "./chunk-TFSCNABI.js";
import "./chunk-QFC2NEOE.js";
import "./chunk-7KDIBPEB.js";
import "./chunk-WKQ7WKDH.js";
import "./chunk-CK2DE3UP.js";
import "./chunk-SHGWGNLR.js";
import "./chunk-2GLHLTFV.js";
import "./chunk-SPQF2UUW.js";
import "./chunk-OM6NJFIX.js";
import "./chunk-LNCH2BSV.js";
import "./chunk-RQ3IP32G.js";
import "./chunk-BT7TH4LS.js";
import "./chunk-M6STLWMS.js";
import "./chunk-UON5SOHO.js";
import "./chunk-OKOP3THR.js";
import "./chunk-ZCSSBJ3Q.js";
import "./chunk-5JG66LKK.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-BFL632LT.js";
import "./chunk-UQLMMC2X.js";
import "./chunk-JBOM32NB.js";
import "./chunk-7GP3IO6K.js";
import "./chunk-LHFENOTP.js";
import "./chunk-5UDJL72O.js";
import "./chunk-XWVFXRID.js";
import "./chunk-6PIYS4D7.js";
import "./chunk-SC3ENGJE.js";
import "./chunk-LOFJHG74.js";
import "./chunk-JAX6LN4S.js";
import "./chunk-FEQY4JZG.js";
import "./chunk-TSUFHXW5.js";
import "./chunk-F6NIRWWU.js";
import "./chunk-66CVMEPJ.js";
import "./chunk-CCNLC3K7.js";
import "./chunk-AYKJSJWS.js";
import "./chunk-5XVM6S7M.js";
import "./chunk-YOECTZH7.js";
import "./chunk-FNIB3BFK.js";
import {
  useForkRef_default
} from "./chunk-BITCO4ZF.js";
import "./chunk-WQ453J3Z.js";
import "./chunk-P2MDZNQE.js";
import "./chunk-Z3OIR7Y2.js";
import "./chunk-HLJDXRDF.js";
import {
  useTheme
} from "./chunk-Y5VMRVGE.js";
import {
  styled_default
} from "./chunk-3OAPAV2J.js";
import "./chunk-QMOJV6NA.js";
import "./chunk-UBUXC2RB.js";
import {
  clsx_default,
  require_prop_types
} from "./chunk-6ZYRDDF6.js";
import {
  keyframes
} from "./chunk-VEBRIJKA.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import "./chunk-7NQIRYQS.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __publicField,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/DataGrid.js
function DataGrid() {
  if (false) {
    return null;
  }
  throw new Error(["You try to import Data Grid from @mui/x-data-grid-premium but this module isn't exported from this npm package.", "", "Instead, you can do `import { DataGridPremium } from '@mui/x-data-grid-premium'`.", ""].join("\n"));
}
function DataGridPro() {
  if (false) {
    return null;
  }
  throw new Error(["You try to import Data Grid Pro from @mui/x-data-grid-premium but this module isn't exported from this npm package.", "", "Instead, you can do `import { DataGridPremium } from '@mui/x-data-grid-premium'`.", ""].join("\n"));
}

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/DataGridPremium.js
var React71 = __toESM(require_react(), 1);
var import_prop_types11 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/useDataGridPremiumComponent.js
var React64 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/dataSource/useGridDataSourcePremium.js
var React = __toESM(require_react(), 1);
function getKeyPremium(params) {
  return JSON.stringify([params.filterModel, params.sortModel, params.groupKeys, params.groupFields, params.start, params.end, params.aggregationModel]);
}
var options = {
  cacheOptions: {
    getKey: getKeyPremium
  }
};
var useGridDataSourcePremium = (apiRef, props) => {
  const {
    api,
    debouncedFetchRows,
    strategyProcessor,
    events,
    setStrategyAvailability: setStrategyAvailability2
  } = useGridDataSourceBasePro(apiRef, props, options);
  const aggregateRowRef = React.useRef({});
  const processDataSourceRows = React.useCallback(({
    params,
    response
  }, applyRowHydration) => {
    if (response.aggregateRow) {
      aggregateRowRef.current = response.aggregateRow;
    }
    if (Object.keys(params.aggregationModel || {}).length > 0) {
      if (applyRowHydration) {
        apiRef.current.requestPipeProcessorsApplication("hydrateRows");
      }
      apiRef.current.applyAggregation();
    }
    return {
      params,
      response
    };
  }, [apiRef]);
  const resolveGroupAggregation = React.useCallback((groupId, field) => {
    var _a, _b, _c, _d;
    if (groupId === GRID_ROOT_GROUP_ID) {
      return (_b = (_a = props.dataSource) == null ? void 0 : _a.getAggregatedValue) == null ? void 0 : _b.call(_a, aggregateRowRef.current, field);
    }
    const row = apiRef.current.getRow(groupId);
    return (_d = (_c = props.dataSource) == null ? void 0 : _c.getAggregatedValue) == null ? void 0 : _d.call(_c, row, field);
  }, [apiRef, props.dataSource]);
  const privateApi = _extends({}, api.private, {
    resolveGroupAggregation
  });
  useGridApiMethod(apiRef, api.public, "public");
  useGridApiMethod(apiRef, privateApi, "private");
  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);
  useGridRegisterPipeProcessor(apiRef, "processDataSourceRows", processDataSourceRows);
  Object.entries(events).forEach(([event, handler]) => {
    useGridEvent(apiRef, event, handler);
  });
  useGridEvent(apiRef, "rowGroupingModelChange", () => debouncedFetchRows());
  useGridEvent(apiRef, "aggregationModelChange", () => debouncedFetchRows());
  React.useEffect(() => {
    setStrategyAvailability2();
  }, [setStrategyAvailability2]);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/useGridAggregation.js
var React2 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/gridAggregationSelectors.js
var gridAggregationStateSelector = createRootSelector((state) => state.aggregation);
var gridAggregationModelSelector = createSelector(gridAggregationStateSelector, (aggregationState) => aggregationState.model);
var gridAggregationLookupSelector = createSelector(gridAggregationStateSelector, (aggregationState) => aggregationState.lookup);
var gridCellAggregationResultSelector = createSelector(gridRowTreeSelector, gridAggregationLookupSelector, (rowTree, aggregationLookup, {
  id,
  field
}) => {
  var _a;
  let cellAggregationPosition = null;
  const rowNode = rowTree[id];
  if (!rowNode) {
    return null;
  }
  if (rowNode.type === "group") {
    cellAggregationPosition = "inline";
  } else if (id.toString().startsWith("auto-generated-group-footer-")) {
    cellAggregationPosition = "footer";
  }
  if (cellAggregationPosition == null) {
    return null;
  }
  const groupId = cellAggregationPosition === "inline" ? id : rowNode.parent ?? "";
  const aggregationResult = (_a = aggregationLookup == null ? void 0 : aggregationLookup[groupId]) == null ? void 0 : _a[field];
  if (!aggregationResult || aggregationResult.position !== cellAggregationPosition) {
    return null;
  }
  return aggregationResult;
});

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/capitalize/capitalize.js
function capitalize(string) {
  if (typeof string !== "string") {
    throw new Error(true ? "MUI: `capitalize(string)` expects a string argument." : formatMuiErrorMessage(7));
  }
  return string.charAt(0).toUpperCase() + string.slice(1);
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/gridAggregationUtils.js
var GRID_AGGREGATION_ROOT_FOOTER_ROW_ID = "auto-generated-group-footer-root";
var getAggregationFooterRowIdFromGroupId = (groupId) => {
  if (groupId == null) {
    return GRID_AGGREGATION_ROOT_FOOTER_ROW_ID;
  }
  return `auto-generated-group-footer-${groupId}`;
};
var isClientSideAggregateFunction = (aggregationFunction) => !!aggregationFunction && "apply" in aggregationFunction;
var canColumnHaveAggregationFunction = ({
  colDef,
  aggregationFunctionName,
  aggregationFunction,
  isDataSource
}) => {
  if (!colDef) {
    return false;
  }
  if (!isClientSideAggregateFunction(aggregationFunction) && !isDataSource) {
    return false;
  }
  if (colDef.availableAggregationFunctions != null) {
    return colDef.availableAggregationFunctions.includes(aggregationFunctionName);
  }
  if (!(aggregationFunction == null ? void 0 : aggregationFunction.columnTypes)) {
    return true;
  }
  return aggregationFunction.columnTypes.includes(colDef.type);
};
var getAvailableAggregationFunctions = ({
  aggregationFunctions,
  colDef,
  isDataSource
}) => Object.keys(aggregationFunctions).filter((aggregationFunctionName) => canColumnHaveAggregationFunction({
  colDef,
  aggregationFunctionName,
  aggregationFunction: aggregationFunctions[aggregationFunctionName],
  isDataSource
}));
var mergeStateWithAggregationModel = (aggregationModel) => (state) => _extends({}, state, {
  aggregation: _extends({}, state.aggregation, {
    model: aggregationModel
  })
});
var getAggregationRules = (columnsLookup, aggregationModel, aggregationFunctions, isDataSource) => {
  const aggregationRules = {};
  for (const field in aggregationModel) {
    const columnItem = aggregationModel[field];
    if (columnsLookup[field] && canColumnHaveAggregationFunction({
      colDef: columnsLookup[field],
      aggregationFunctionName: columnItem,
      aggregationFunction: aggregationFunctions[columnItem],
      isDataSource
    })) {
      aggregationRules[field] = {
        aggregationFunctionName: columnItem,
        aggregationFunction: aggregationFunctions[columnItem]
      };
    }
  }
  return aggregationRules;
};
var addFooterRows = ({
  groupingParams,
  apiRef,
  getAggregationPosition,
  hasAggregationRule
}) => {
  let newGroupingParams = _extends({}, groupingParams, {
    tree: _extends({}, groupingParams.tree),
    treeDepths: _extends({}, groupingParams.treeDepths)
  });
  const updateChildGroupFooter = (groupNode) => {
    const shouldHaveFooter = hasAggregationRule && getAggregationPosition(groupNode) === "footer";
    if (shouldHaveFooter) {
      const footerId = getAggregationFooterRowIdFromGroupId(groupNode.id);
      if (groupNode.footerId !== footerId) {
        if (groupNode.footerId != null) {
          removeNodeFromTree({
            node: newGroupingParams.tree[groupNode.footerId],
            tree: newGroupingParams.tree,
            treeDepths: newGroupingParams.treeDepths
          });
        }
        const footerNode = {
          id: footerId,
          parent: groupNode.id,
          depth: groupNode ? groupNode.depth + 1 : 0,
          type: "footer"
        };
        insertNodeInTree(footerNode, newGroupingParams.tree, newGroupingParams.treeDepths, null);
      }
    } else if (groupNode.footerId != null) {
      removeNodeFromTree({
        node: newGroupingParams.tree[groupNode.footerId],
        tree: newGroupingParams.tree,
        treeDepths: newGroupingParams.treeDepths
      });
      newGroupingParams.tree[groupNode.id] = _extends({}, newGroupingParams.tree[groupNode.id], {
        footerId: null
      });
    }
  };
  const updateRootGroupFooter = (groupNode) => {
    const shouldHaveFooter = hasAggregationRule && getAggregationPosition(groupNode) === "footer" && groupNode.children.length > 0;
    if (shouldHaveFooter) {
      const rowId = getAggregationFooterRowIdFromGroupId(null);
      newGroupingParams = addPinnedRow({
        groupingParams: newGroupingParams,
        rowModel: {
          [GRID_ID_AUTOGENERATED]: rowId
        },
        rowId,
        position: "bottom",
        apiRef,
        isAutoGenerated: true
      });
    }
  };
  const updateGroupFooter = (groupNode) => {
    if (groupNode.id === GRID_ROOT_GROUP_ID) {
      updateRootGroupFooter(groupNode);
    } else {
      updateChildGroupFooter(groupNode);
    }
    groupNode.children.forEach((childId) => {
      const childNode = newGroupingParams.tree[childId];
      if (childNode.type === "group") {
        updateGroupFooter(childNode);
      }
    });
  };
  updateGroupFooter(newGroupingParams.tree[GRID_ROOT_GROUP_ID]);
  return newGroupingParams;
};
var areAggregationRulesEqual = (previousValue, newValue) => {
  const previousFields = Object.keys(previousValue ?? {});
  const newFields = Object.keys(newValue);
  if (!isDeepEqual(previousFields, newFields)) {
    return false;
  }
  return newFields.every((field) => {
    const previousRule = previousValue == null ? void 0 : previousValue[field];
    const newRule = newValue[field];
    if ((previousRule == null ? void 0 : previousRule.aggregationFunction) !== (newRule == null ? void 0 : newRule.aggregationFunction)) {
      return false;
    }
    if ((previousRule == null ? void 0 : previousRule.aggregationFunctionName) !== (newRule == null ? void 0 : newRule.aggregationFunctionName)) {
      return false;
    }
    return true;
  });
};
var getAggregationFunctionLabel = ({
  apiRef,
  aggregationRule
}) => {
  if (aggregationRule.aggregationFunction.label != null) {
    return aggregationRule.aggregationFunction.label;
  }
  try {
    return apiRef.current.getLocaleText(`aggregationFunctionLabel${capitalize(aggregationRule.aggregationFunctionName)}`);
  } catch {
    return aggregationRule.aggregationFunctionName;
  }
};
var defaultGetAggregationPosition = (groupNode) => groupNode.depth === -1 ? "footer" : "inline";

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/createAggregationLookup.js
var shouldApplySorting = (aggregationRules, aggregatedFields) => {
  return aggregatedFields.some((field) => aggregationRules[field].aggregationFunction.applySorting);
};
var getGroupAggregatedValue = (groupId, apiRef, aggregationRowsScope, aggregatedFields, aggregationRules, position, applySorting, valueGetters, publicApi, groupAggregatedValuesLookup) => {
  const groupAggregationLookup = {};
  const aggregatedValues = [];
  for (let i = 0; i < aggregatedFields.length; i += 1) {
    aggregatedValues[i] = {
      aggregatedField: aggregatedFields[i],
      values: []
    };
  }
  const rowTree = gridRowTreeSelector(apiRef);
  const rowLookup = gridRowsLookupSelector(apiRef);
  const isPivotActive = apiRef.current.state.pivoting.active;
  const rowIds = apiRef.current.getRowGroupChildren({
    groupId,
    applySorting,
    directChildrenOnly: true,
    skipAutoGeneratedRows: false,
    applyFiltering: aggregationRowsScope === "filtered"
  });
  for (let i = 0; i < rowIds.length; i += 1) {
    const rowId = rowIds[i];
    const rowNode = rowTree[rowId];
    if (rowNode.type === "group") {
      const childGroupValues = groupAggregatedValuesLookup.get(rowId);
      if (childGroupValues) {
        for (let j = 0; j < aggregatedFields.length; j += 1) {
          aggregatedValues[j].values = aggregatedValues[j].values.concat(childGroupValues[j].values);
        }
      }
      continue;
    }
    const row = rowLookup[rowId];
    for (let j = 0; j < aggregatedFields.length; j += 1) {
      const aggregatedField = aggregatedFields[j];
      const columnAggregationRules = aggregationRules[aggregatedField];
      const aggregationFunction = columnAggregationRules.aggregationFunction;
      const field = aggregatedField;
      let value;
      if (typeof aggregationFunction.getCellValue === "function") {
        value = aggregationFunction.getCellValue({
          field,
          row
        });
      } else if (isPivotActive) {
        value = row[field];
      } else {
        if (!row) {
          continue;
        }
        const valueGetter = valueGetters[aggregatedField];
        value = valueGetter(row);
      }
      if (value !== void 0) {
        aggregatedValues[j].values.push(value);
      }
    }
  }
  for (let i = 0; i < aggregatedValues.length; i += 1) {
    const {
      aggregatedField,
      values
    } = aggregatedValues[i];
    const aggregationFunction = aggregationRules[aggregatedField].aggregationFunction;
    const value = aggregationFunction.apply({
      values,
      groupId,
      field: aggregatedField
      // Added per user request in https://github.com/mui/mui-x/issues/6995#issuecomment-1327423455
    }, publicApi);
    groupAggregationLookup[aggregatedField] = {
      position,
      value
    };
  }
  return {
    groupAggregationLookup,
    aggregatedValues
  };
};
var getGroupAggregatedValueDataSource = (groupId, apiRef, aggregatedFields, position) => {
  var _a, _b;
  const groupAggregationLookup = {};
  for (let j = 0; j < aggregatedFields.length; j += 1) {
    const aggregatedField = aggregatedFields[j];
    groupAggregationLookup[aggregatedField] = {
      position,
      value: ((_b = (_a = apiRef.current).resolveGroupAggregation) == null ? void 0 : _b.call(_a, groupId, aggregatedField)) ?? ""
    };
  }
  return groupAggregationLookup;
};
var createAggregationLookup = ({
  apiRef,
  aggregationRules,
  aggregatedFields,
  aggregationRowsScope,
  getAggregationPosition,
  isDataSource,
  applySorting = false
}) => {
  if (aggregatedFields.length === 0) {
    return {};
  }
  const columnsLookup = gridColumnLookupSelector(apiRef);
  const valueGetters = {};
  for (let i = 0; i < aggregatedFields.length; i += 1) {
    const field = aggregatedFields[i];
    const column = columnsLookup[field];
    const valueGetter = (row) => apiRef.current.getRowValue(row, column);
    valueGetters[field] = valueGetter;
  }
  const aggregationLookup = {};
  const rowTree = gridRowTreeSelector(apiRef);
  const groupAggregatedValuesLookup = /* @__PURE__ */ new Map();
  const {
    rowIdToIndexMap
  } = getVisibleRows(apiRef);
  const createGroupAggregationLookup = (groupNode) => {
    let children = groupNode.children;
    if (applySorting) {
      children = children.toSorted((a, b) => rowIdToIndexMap.get(a) - rowIdToIndexMap.get(b));
    }
    for (let i = 0; i < children.length; i += 1) {
      const childId = children[i];
      const childNode = rowTree[childId];
      if (childNode.type === "group") {
        createGroupAggregationLookup(childNode);
      }
    }
    const position = getAggregationPosition(groupNode);
    if (position !== null) {
      if (isDataSource) {
        aggregationLookup[groupNode.id] = getGroupAggregatedValueDataSource(groupNode.id, apiRef, aggregatedFields, position);
      } else if (groupNode.children.length) {
        const result = getGroupAggregatedValue(groupNode.id, apiRef, aggregationRowsScope, aggregatedFields, aggregationRules, position, applySorting, valueGetters, apiRef.current, groupAggregatedValuesLookup);
        aggregationLookup[groupNode.id] = result.groupAggregationLookup;
        groupAggregatedValuesLookup.set(groupNode.id, result.aggregatedValues);
      }
    }
  };
  createGroupAggregationLookup(rowTree[GRID_ROOT_GROUP_ID]);
  return aggregationLookup;
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/useGridAggregation.js
var aggregationStateInitializer = (state, props, apiRef) => {
  var _a, _b;
  apiRef.current.caches.aggregation = {
    rulesOnLastColumnHydration: {},
    rulesOnLastRowHydration: {}
  };
  return _extends({}, state, {
    aggregation: {
      model: props.aggregationModel ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.aggregation) == null ? void 0 : _b.model) ?? {}
    }
  });
};
var useGridAggregation = (apiRef, props) => {
  apiRef.current.registerControlState({
    stateId: "aggregation",
    propModel: props.aggregationModel,
    propOnChange: props.onAggregationModelChange,
    stateSelector: gridAggregationModelSelector,
    changeEvent: "aggregationModelChange"
  });
  const setAggregationModel = React2.useCallback((model) => {
    const currentModel = gridAggregationModelSelector(apiRef);
    if (currentModel !== model) {
      apiRef.current.setState(mergeStateWithAggregationModel(model));
    }
  }, [apiRef]);
  const abortControllerRef = React2.useRef(null);
  const applyAggregation = React2.useCallback((reason) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    const aggregationRules = getAggregationRules(gridColumnLookupSelector(apiRef), gridAggregationModelSelector(apiRef), props.aggregationFunctions, !!props.dataSource);
    const aggregatedFields = Object.keys(aggregationRules);
    const needsSorting = shouldApplySorting(aggregationRules, aggregatedFields);
    if (reason === "sort" && !needsSorting) {
      return;
    }
    const renderContext = gridRenderContextSelector(apiRef);
    const visibleColumns = gridVisibleColumnFieldsSelector(apiRef);
    const chunks = [];
    const visibleAggregatedFields = visibleColumns.slice(renderContext.firstColumnIndex, renderContext.lastColumnIndex + 1).filter((field) => aggregatedFields.includes(field));
    if (visibleAggregatedFields.length > 0) {
      chunks.push(visibleAggregatedFields);
    }
    const otherAggregatedFields = aggregatedFields.filter((field) => !visibleAggregatedFields.includes(field));
    const chunkSize = 20;
    for (let i = 0; i < otherAggregatedFields.length; i += chunkSize) {
      chunks.push(otherAggregatedFields.slice(i, i + chunkSize));
    }
    let chunkIndex = 0;
    const aggregationLookup = {};
    let chunkStartTime = performance.now();
    const timeLimit = 1e3 / 120;
    const processChunk = () => {
      if (abortController.signal.aborted) {
        return;
      }
      const currentChunk = chunks[chunkIndex];
      if (!currentChunk) {
        const sortModel = gridSortModelSelector(apiRef).map((s) => s.field);
        const hasAggregatedSorting = sortModel.some((field) => aggregationRules[field]);
        if (hasAggregatedSorting) {
          apiRef.current.applySorting();
        }
        abortControllerRef.current = null;
        return;
      }
      const applySorting = shouldApplySorting(aggregationRules, currentChunk);
      const partialLookup = createAggregationLookup({
        apiRef,
        getAggregationPosition: props.getAggregationPosition,
        aggregatedFields: currentChunk,
        aggregationRules,
        aggregationRowsScope: props.aggregationRowsScope,
        isDataSource: !!props.dataSource,
        applySorting
      });
      for (const key of Object.keys(partialLookup)) {
        for (const field of Object.keys(partialLookup[key])) {
          aggregationLookup[key] ?? (aggregationLookup[key] = {});
          aggregationLookup[key][field] = partialLookup[key][field];
        }
      }
      apiRef.current.setState((state) => _extends({}, state, {
        aggregation: _extends({}, state.aggregation, {
          lookup: _extends({}, aggregationLookup)
        })
      }));
      chunkIndex += 1;
      if (performance.now() - chunkStartTime < timeLimit) {
        processChunk();
        return;
      }
      setTimeout(() => {
        chunkStartTime = performance.now();
        processChunk();
      }, 0);
    };
    processChunk();
  }, [apiRef, props.getAggregationPosition, props.aggregationFunctions, props.aggregationRowsScope, props.dataSource]);
  React2.useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);
  const deferredApplyAggregation = useRunOncePerLoop(applyAggregation);
  const aggregationApi = {
    setAggregationModel
  };
  const aggregationPrivateApi = {
    applyAggregation
  };
  useGridApiMethod(apiRef, aggregationApi, "public");
  useGridApiMethod(apiRef, aggregationPrivateApi, "private");
  const addGetRowsParams = React2.useCallback((params) => {
    return _extends({}, params, {
      aggregationModel: gridAggregationModelSelector(apiRef)
    });
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "getRowsParams", addGetRowsParams);
  const checkAggregationRulesDiff = React2.useCallback(() => {
    const {
      rulesOnLastRowHydration,
      rulesOnLastColumnHydration
    } = apiRef.current.caches.aggregation;
    const aggregationRules = props.disableAggregation ? {} : getAggregationRules(gridColumnLookupSelector(apiRef), gridAggregationModelSelector(apiRef), props.aggregationFunctions, !!props.dataSource);
    if (!props.dataSource && !areAggregationRulesEqual(rulesOnLastRowHydration, aggregationRules)) {
      apiRef.current.requestPipeProcessorsApplication("hydrateRows");
      deferredApplyAggregation();
    }
    if (!areAggregationRulesEqual(rulesOnLastColumnHydration, aggregationRules)) {
      apiRef.current.requestPipeProcessorsApplication("hydrateColumns");
    }
  }, [apiRef, deferredApplyAggregation, props.aggregationFunctions, props.disableAggregation, props.dataSource]);
  useGridEvent(apiRef, "aggregationModelChange", checkAggregationRulesDiff);
  useGridEvent(apiRef, "columnsChange", checkAggregationRulesDiff);
  useGridEvent(apiRef, "filteredRowsSet", deferredApplyAggregation);
  useGridEvent(apiRef, "sortedRowsSet", () => deferredApplyAggregation("sort"));
  React2.useEffect(() => {
    if (props.aggregationModel !== void 0) {
      apiRef.current.setAggregationModel(props.aggregationModel);
    }
  }, [apiRef, props.aggregationModel]);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/useGridAggregationPreProcessors.js
var React6 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/wrapColumnWithAggregation.js
var React5 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/GridFooterCell.js
var React3 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/composeClasses/composeClasses.js
function composeClasses(slots, getUtilityClass, classes = void 0) {
  const output = {};
  for (const slotName in slots) {
    const slot = slots[slotName];
    let buffer = "";
    let start = true;
    for (let i = 0; i < slot.length; i += 1) {
      const value = slot[i];
      if (value) {
        buffer += (start === true ? "" : " ") + getUtilityClass(value);
        start = false;
        if (classes && classes[value]) {
          buffer += " " + classes[value];
        }
      }
    }
    output[slotName] = buffer;
  }
  return output;
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useGridRootProps.js
var useGridRootProps2 = useGridRootProps;

// node_modules/@mui/x-data-grid-premium/esm/components/GridFooterCell.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var _excluded = ["formattedValue", "colDef", "cellMode", "row", "api", "id", "value", "rowNode", "field", "focusElementRef", "hasFocus", "tabIndex", "isEditable"];
var GridFooterCellRoot = styled_default("div", {
  name: "MuiDataGrid",
  slot: "FooterCell"
})({
  fontWeight: vars.typography.fontWeight.medium,
  color: vars.colors.foreground.accent
});
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["footerCell"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridFooterCell(props) {
  const {
    formattedValue
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const rootProps = useGridRootProps2();
  const ownerState = rootProps;
  const classes = useUtilityClasses(ownerState);
  return (0, import_jsx_runtime.jsx)(GridFooterCellRoot, _extends({
    ownerState,
    className: classes.root
  }, other, {
    children: formattedValue
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridAggregationHeader.js
var React4 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useGridApiContext.js
var useGridApiContext2 = useGridApiContext;

// node_modules/@mui/x-data-grid-premium/esm/components/GridAggregationHeader.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var _excluded2 = ["renderHeader"];
var GridAggregationHeaderRoot = styled_default("div", {
  name: "MuiDataGrid",
  slot: "AggregationColumnHeader",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.aggregationColumnHeader, ownerState.colDef.headerAlign === "left" && styles["aggregationColumnHeader--alignLeft"], ownerState.colDef.headerAlign === "center" && styles["aggregationColumnHeader--alignCenter"], ownerState.colDef.headerAlign === "right" && styles["aggregationColumnHeader--alignRight"]];
  }
})({
  display: "flex",
  flexDirection: "column",
  [`&.${gridClasses["aggregationColumnHeader--alignRight"]}`]: {
    alignItems: "flex-end"
  },
  [`&.${gridClasses["aggregationColumnHeader--alignCenter"]}`]: {
    alignItems: "center"
  }
});
var GridAggregationFunctionLabel = styled_default("div", {
  name: "MuiDataGrid",
  slot: "AggregationColumnHeaderLabel"
})({
  font: vars.typography.font.small,
  lineHeight: "normal",
  color: vars.colors.foreground.muted,
  marginTop: -1
});
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    colDef
  } = ownerState;
  const slots = {
    root: ["aggregationColumnHeader", colDef.headerAlign === "left" && "aggregationColumnHeader--alignLeft", colDef.headerAlign === "center" && "aggregationColumnHeader--alignCenter", colDef.headerAlign === "right" && "aggregationColumnHeader--alignRight"],
    aggregationLabel: ["aggregationColumnHeaderLabel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridAggregationHeader(props) {
  const {
    renderHeader
  } = props, params = _objectWithoutPropertiesLoose(props, _excluded2);
  const {
    colDef,
    aggregation
  } = params;
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps2();
  const ownerState = _extends({}, rootProps, {
    classes: rootProps.classes,
    colDef
  });
  const classes = useUtilityClasses2(ownerState);
  if (!aggregation) {
    return null;
  }
  const aggregationLabel = getAggregationFunctionLabel({
    apiRef,
    aggregationRule: aggregation.aggregationRule
  });
  return (0, import_jsx_runtime2.jsxs)(GridAggregationHeaderRoot, {
    ownerState,
    className: classes.root,
    children: [renderHeader ? renderHeader(params) : (0, import_jsx_runtime2.jsx)(GridColumnHeaderTitle, {
      label: colDef.headerName ?? colDef.field,
      description: colDef.description,
      columnWidth: colDef.computedWidth
    }), (0, import_jsx_runtime2.jsx)(GridAggregationFunctionLabel, {
      ownerState,
      className: classes.aggregationLabel,
      children: aggregationLabel
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/sidebar/gridSidebarInterfaces.js
var GridSidebarValue = (function(GridSidebarValue2) {
  GridSidebarValue2["Pivot"] = "pivot";
  return GridSidebarValue2;
})({});

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/sidebar/gridSidebarSelector.js
var gridSidebarStateSelector = createRootSelector((state) => state.sidebar);
var gridSidebarOpenSelector = createSelector(gridSidebarStateSelector, (state) => state.open);
var gridSidebarContentSelector = createSelector(gridSidebarStateSelector, ({
  sidebarId,
  labelId,
  value
}) => ({
  sidebarId,
  labelId,
  value
}));

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/pivoting/gridPivotingSelectors.js
var gridPivotingStateSelector = createRootSelector((state) => state.pivoting);
var gridPivotPanelOpenSelector = createSelector(gridSidebarStateSelector, (sidebar) => sidebar.value === GridSidebarValue.Pivot && sidebar.open);
var gridPivotModelSelector = createSelector(gridPivotingStateSelector, (pivoting) => pivoting == null ? void 0 : pivoting.model);
var gridPivotPropsOverridesSelector = createSelector(gridPivotingStateSelector, (pivoting) => (pivoting == null ? void 0 : pivoting.active) ? pivoting.propsOverrides : void 0);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/wrapColumnWithAggregation.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var _excluded3 = ["aggregationWrappedProperties"];
var getAggregationValueWrappedValueGetter = ({
  value: valueGetter,
  getCellAggregationResult
}) => {
  const wrappedValueGetter = (value, row, column, apiRef) => {
    const rowId = gridRowIdSelector(apiRef, row);
    const cellAggregationResult = rowId ? getCellAggregationResult(rowId, column.field) : null;
    if (cellAggregationResult != null) {
      return (cellAggregationResult == null ? void 0 : cellAggregationResult.value) ?? null;
    }
    if (valueGetter) {
      return valueGetter(value, row, column, apiRef);
    }
    return row[column.field];
  };
  return wrappedValueGetter;
};
var getAggregationValueWrappedValueFormatter = ({
  value: valueFormatter,
  aggregationRule,
  getCellAggregationResult
}) => {
  if (!aggregationRule.aggregationFunction.valueFormatter) {
    return valueFormatter;
  }
  const wrappedValueFormatter = (value, row, column, apiRef) => {
    var _a, _b;
    const rowId = gridRowIdSelector(apiRef, row);
    if (rowId != null) {
      const cellAggregationResult = getCellAggregationResult(rowId, column.field);
      if (cellAggregationResult != null) {
        return (_b = (_a = aggregationRule.aggregationFunction).valueFormatter) == null ? void 0 : _b.call(_a, value, row, column, apiRef);
      }
    }
    if (valueFormatter) {
      return valueFormatter(value, row, column, apiRef);
    }
    return value;
  };
  return wrappedValueFormatter;
};
var getAggregationValueWrappedRenderCell = ({
  value: renderCell,
  aggregationRule,
  getCellAggregationResult,
  apiRef
}) => {
  const pivotActive = gridPivotActiveSelector(apiRef);
  const wrappedRenderCell = (params) => {
    const cellAggregationResult = getCellAggregationResult(params.id, params.field);
    if (cellAggregationResult != null) {
      if (!renderCell) {
        if (cellAggregationResult.position === "footer") {
          return (0, import_jsx_runtime3.jsx)(GridFooterCell, _extends({}, params));
        }
        if (pivotActive && cellAggregationResult.value === 0) {
          return null;
        }
        return params.formattedValue;
      }
      if (pivotActive && cellAggregationResult.value === 0) {
        return null;
      }
      const aggregationMeta = {
        hasCellUnit: aggregationRule.aggregationFunction.hasCellUnit ?? true,
        aggregationFunctionName: aggregationRule.aggregationFunctionName
      };
      return renderCell(_extends({}, params, {
        aggregation: aggregationMeta
      }));
    }
    if (!renderCell) {
      return params.formattedValue;
    }
    return renderCell(params);
  };
  return wrappedRenderCell;
};
var getWrappedRenderHeader = ({
  value: renderHeader,
  aggregationRule
}) => {
  const wrappedRenderHeader = (params) => {
    if (!params.colDef) {
      return null;
    }
    return (0, import_jsx_runtime3.jsx)(GridAggregationHeader, _extends({}, params, {
      aggregation: {
        aggregationRule
      },
      renderHeader
    }));
  };
  if (true) wrappedRenderHeader.displayName = "wrappedRenderHeader";
  return wrappedRenderHeader;
};
var wrapColumnWithAggregationValue = (column, aggregationRule, apiRef) => {
  const getCellAggregationResult = (id, field) => {
    var _a, _b;
    let cellAggregationPosition = null;
    const rowNode = gridRowNodeSelector(apiRef, id);
    if (!rowNode) {
      return null;
    }
    if (rowNode.type === "group") {
      cellAggregationPosition = "inline";
    } else if (id.toString().startsWith("auto-generated-group-footer-")) {
      cellAggregationPosition = "footer";
    }
    if (cellAggregationPosition == null) {
      return null;
    }
    const groupId = cellAggregationPosition === "inline" ? id : rowNode.parent ?? "";
    const aggregationResult = (_b = (_a = gridAggregationLookupSelector(apiRef)) == null ? void 0 : _a[groupId]) == null ? void 0 : _b[field];
    if (!aggregationResult || aggregationResult.position !== cellAggregationPosition) {
      return null;
    }
    return aggregationResult;
  };
  let didWrapSomeProperty = false;
  const wrappedColumn = _extends({}, column, {
    aggregationWrappedProperties: []
  });
  const wrapColumnProperty = (property, wrapper) => {
    const originalValue = column[property];
    const wrappedProperty = wrapper({
      apiRef,
      value: originalValue,
      colDef: column,
      aggregationRule,
      getCellAggregationResult
    });
    if (wrappedProperty !== originalValue) {
      didWrapSomeProperty = true;
      wrappedColumn[property] = wrappedProperty;
      wrappedColumn.aggregationWrappedProperties.push({
        name: property,
        originalValue,
        wrappedValue: wrappedProperty
      });
    }
  };
  wrapColumnProperty("valueGetter", getAggregationValueWrappedValueGetter);
  wrapColumnProperty("valueFormatter", getAggregationValueWrappedValueFormatter);
  wrapColumnProperty("renderCell", getAggregationValueWrappedRenderCell);
  wrapColumnProperty("renderHeader", getWrappedRenderHeader);
  if (!didWrapSomeProperty) {
    return column;
  }
  return wrappedColumn;
};
var isColumnWrappedWithAggregation = (column) => {
  return typeof column.aggregationWrappedProperties !== "undefined";
};
var unwrapColumnFromAggregation = (column) => {
  if (!isColumnWrappedWithAggregation(column)) {
    return column;
  }
  const _ref = column, {
    aggregationWrappedProperties
  } = _ref, unwrappedColumn = _objectWithoutPropertiesLoose(_ref, _excluded3);
  aggregationWrappedProperties.forEach(({
    name,
    originalValue,
    wrappedValue
  }) => {
    if (wrappedValue !== unwrappedColumn[name]) {
      return;
    }
    unwrappedColumn[name] = originalValue;
  });
  return unwrappedColumn;
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/useGridAggregationPreProcessors.js
var useGridAggregationPreProcessors = (apiRef, props) => {
  const rulesOnLastColumnHydration = React6.useRef({});
  const updateAggregatedColumns = React6.useCallback((columnsState) => {
    const aggregationRules = props.disableAggregation ? {} : getAggregationRules(columnsState.lookup, gridAggregationModelSelector(apiRef), props.aggregationFunctions, !!props.dataSource);
    columnsState.orderedFields.forEach((field) => {
      const shouldHaveAggregationValue = !!aggregationRules[field];
      const haveAggregationColumnValue = !!rulesOnLastColumnHydration.current[field];
      let column = columnsState.lookup[field];
      if (haveAggregationColumnValue) {
        column = unwrapColumnFromAggregation(column);
      }
      if (shouldHaveAggregationValue) {
        column = wrapColumnWithAggregationValue(column, aggregationRules[field], apiRef);
      }
      columnsState.lookup[field] = column;
    });
    rulesOnLastColumnHydration.current = aggregationRules;
    apiRef.current.caches.aggregation.rulesOnLastColumnHydration = aggregationRules;
    return columnsState;
  }, [apiRef, props.aggregationFunctions, props.disableAggregation, props.dataSource]);
  const addGroupFooterRows = React6.useCallback((value) => {
    const aggregationRules = props.disableAggregation ? {} : getAggregationRules(gridColumnLookupSelector(apiRef), gridAggregationModelSelector(apiRef), props.aggregationFunctions, !!props.dataSource);
    const hasAggregationRule = Object.keys(aggregationRules).length > 0;
    if (Object.keys(apiRef.current.caches.aggregation.rulesOnLastRowHydration).length === 0 && !hasAggregationRule) {
      return value;
    }
    apiRef.current.caches.aggregation.rulesOnLastRowHydration = aggregationRules;
    return addFooterRows({
      apiRef,
      groupingParams: value,
      getAggregationPosition: props.getAggregationPosition,
      hasAggregationRule
    });
  }, [apiRef, props.disableAggregation, props.getAggregationPosition, props.aggregationFunctions, props.dataSource]);
  const addColumnMenuButtons = React6.useCallback((columnMenuItems, colDef) => {
    if (props.disableAggregation || !colDef.aggregable) {
      return columnMenuItems;
    }
    const availableAggregationFunctions = getAvailableAggregationFunctions({
      aggregationFunctions: props.aggregationFunctions,
      colDef,
      isDataSource: !!props.dataSource
    });
    if (availableAggregationFunctions.length === 0) {
      return columnMenuItems;
    }
    return [...columnMenuItems, "columnMenuAggregationItem"];
  }, [props.aggregationFunctions, props.disableAggregation, props.dataSource]);
  const stateExportPreProcessing = React6.useCallback((prevState) => {
    if (props.disableAggregation) {
      return prevState;
    }
    const aggregationModelToExport = gridAggregationModelSelector(apiRef);
    if (Object.values(aggregationModelToExport).length === 0) {
      return prevState;
    }
    return _extends({}, prevState, {
      aggregation: {
        model: aggregationModelToExport
      }
    });
  }, [apiRef, props.disableAggregation]);
  const stateRestorePreProcessing = React6.useCallback((params, context) => {
    var _a;
    if (props.disableAggregation) {
      return params;
    }
    const aggregationModel = (_a = context.stateToRestore.aggregation) == null ? void 0 : _a.model;
    if (aggregationModel != null) {
      apiRef.current.setState(mergeStateWithAggregationModel(aggregationModel));
    }
    return params;
  }, [apiRef, props.disableAggregation]);
  useGridRegisterPipeProcessor(apiRef, "hydrateColumns", updateAggregatedColumns);
  useGridRegisterPipeProcessor(apiRef, "hydrateRows", addGroupFooterRows);
  useGridRegisterPipeProcessor(apiRef, "columnMenu", addColumnMenuButtons);
  useGridRegisterPipeProcessor(apiRef, "exportState", stateExportPreProcessing);
  useGridRegisterPipeProcessor(apiRef, "restoreState", stateRestorePreProcessing);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/useGridRowGrouping.js
var React7 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/gridRowGroupingSelector.js
var gridRowGroupingStateSelector = createRootSelector((state) => state.rowGrouping);
var gridRowGroupingModelSelector = createSelector(gridRowGroupingStateSelector, (rowGrouping) => rowGrouping.model);
var gridRowGroupingSanitizedModelSelector = createSelectorMemoized(gridRowGroupingModelSelector, gridColumnLookupSelector, (model, columnsLookup) => model.filter((field) => !!columnsLookup[field]));

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/gridRowGroupingUtils.js
var getRowGroupingFieldFromGroupingCriteria = (groupingCriteria) => {
  if (groupingCriteria === null) {
    return GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD;
  }
  return `__row_group_by_columns_group_${groupingCriteria}__`;
};
var shouldApplyFilterItemOnGroup = (columnField, node) => {
  if (columnField === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD) {
    return true;
  }
  const groupingCriteriaField = getRowGroupingCriteriaFromGroupingField(columnField);
  return groupingCriteriaField === node.groupingField;
};
var filterRowTreeFromGroupingColumns = (params) => {
  const {
    apiRef,
    rowTree,
    isRowMatchingFilters,
    filterModel
  } = params;
  const filteredRowsLookup = {};
  const filteredChildrenCountLookup = {};
  const filteredDescendantCountLookup = {};
  const filterCache = {};
  const filterTreeNode = (node, areAncestorsExpanded, ancestorsResults) => {
    const filterResults = {
      passingFilterItems: null,
      passingQuickFilterValues: null
    };
    let isPassingFiltering = false;
    if (isRowMatchingFilters && node.type !== "footer") {
      const shouldApplyItem = node.type === "group" && node.isAutoGenerated ? (columnField) => shouldApplyFilterItemOnGroup(columnField, node) : void 0;
      const row = apiRef.current.getRow(node.id);
      isRowMatchingFilters(row, shouldApplyItem, filterResults);
    } else {
      isPassingFiltering = true;
    }
    let filteredChildrenCount = 0;
    let filteredDescendantCount = 0;
    if (node.type === "group") {
      node.children.forEach((childId) => {
        const childNode = rowTree[childId];
        const childSubTreeSize = filterTreeNode(childNode, areAncestorsExpanded && !!node.childrenExpanded, [...ancestorsResults, filterResults]);
        filteredDescendantCount += childSubTreeSize;
        if (childSubTreeSize > 0) {
          filteredChildrenCount += 1;
        }
      });
    }
    if (isPassingFiltering === false) {
      if (node.type === "group") {
        isPassingFiltering = filteredDescendantCount > 0;
      } else {
        const allResults = [...ancestorsResults, filterResults];
        isPassingFiltering = passFilterLogic(allResults.map((result) => result.passingFilterItems), allResults.map((result) => result.passingQuickFilterValues), filterModel, params.apiRef, filterCache);
      }
    }
    if (!isPassingFiltering) {
      filteredRowsLookup[node.id] = false;
    }
    if (!isPassingFiltering) {
      return 0;
    }
    filteredChildrenCountLookup[node.id] = filteredChildrenCount;
    filteredDescendantCountLookup[node.id] = filteredDescendantCount;
    if (node.type !== "group") {
      return filteredDescendantCount + 1;
    }
    return filteredDescendantCount;
  };
  const nodes = Object.values(rowTree);
  for (let i = 0; i < nodes.length; i += 1) {
    const node = nodes[i];
    if (node.depth === 0) {
      filterTreeNode(node, true, []);
    }
  }
  return {
    filteredRowsLookup,
    filteredChildrenCountLookup,
    filteredDescendantCountLookup
  };
};
var getColDefOverrides = (groupingColDefProp, fields, strategy) => {
  if (typeof groupingColDefProp === "function") {
    return groupingColDefProp({
      groupingName: strategy ?? RowGroupingStrategy.Default,
      fields
    });
  }
  return groupingColDefProp;
};
var mergeStateWithRowGroupingModel = (rowGroupingModel) => (state) => _extends({}, state, {
  rowGrouping: _extends({}, state.rowGrouping, {
    model: rowGroupingModel
  })
});
var setStrategyAvailability = (privateApiRef, disableRowGrouping, dataSource) => {
  const strategy = dataSource ? RowGroupingStrategy.DataSource : RowGroupingStrategy.Default;
  if (privateApiRef.current.getActiveStrategy(GridStrategyGroup.RowTree) === strategy) {
    return;
  }
  let isAvailable;
  if (disableRowGrouping) {
    isAvailable = () => false;
  } else {
    isAvailable = () => {
      const rowGroupingSanitizedModel = gridRowGroupingSanitizedModelSelector(privateApiRef);
      return rowGroupingSanitizedModel.length > 0;
    };
  }
  privateApiRef.current.setStrategyAvailability(GridStrategyGroup.RowTree, strategy, isAvailable);
};
var getCellGroupingCriteria = ({
  row,
  colDef,
  groupingRule,
  apiRef
}) => {
  let key;
  if (groupingRule.groupingValueGetter) {
    key = groupingRule.groupingValueGetter(row[groupingRule.field], row, colDef, apiRef);
  } else {
    key = getRowValue(row, colDef, apiRef);
  }
  return {
    key,
    field: groupingRule.field
  };
};
var getGroupingRules = ({
  sanitizedRowGroupingModel,
  columnsLookup
}) => sanitizedRowGroupingModel.map((field) => {
  var _a, _b;
  return {
    field,
    groupingValueGetter: (_a = columnsLookup[field]) == null ? void 0 : _a.groupingValueGetter,
    groupingValueSetter: (_b = columnsLookup[field]) == null ? void 0 : _b.groupingValueSetter
  };
});
var areGroupingRulesEqual = (newValue, previousValue) => {
  if (previousValue.length !== newValue.length) {
    return false;
  }
  return newValue.every((newRule, newRuleIndex) => {
    const previousRule = previousValue[newRuleIndex];
    if (previousRule.groupingValueGetter !== newRule.groupingValueGetter) {
      return false;
    }
    if (previousRule.groupingValueSetter !== newRule.groupingValueSetter) {
      return false;
    }
    if (previousRule.field !== newRule.field) {
      return false;
    }
    return true;
  });
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowReorder/utils.js
var conditions = {
  // Node type checks
  isGroupToGroup: (ctx) => ctx.sourceNode.type === "group" && ctx.targetNode.type === "group",
  isLeafToLeaf: (ctx) => ctx.sourceNode.type === "leaf" && ctx.targetNode.type === "leaf",
  isLeafToGroup: (ctx) => ctx.sourceNode.type === "leaf" && ctx.targetNode.type === "group",
  isGroupToLeaf: (ctx) => ctx.sourceNode.type === "group" && ctx.targetNode.type === "leaf",
  // Drop position checks
  isDropAbove: (ctx) => ctx.dropPosition === "above",
  isDropBelow: (ctx) => ctx.dropPosition === "below",
  // Depth checks
  sameDepth: (ctx) => ctx.sourceNode.depth === ctx.targetNode.depth,
  sourceDepthGreater: (ctx) => ctx.sourceNode.depth > ctx.targetNode.depth,
  targetDepthIsSourceMinusOne: (ctx) => ctx.targetNode.depth === ctx.sourceNode.depth - 1,
  // Parent checks
  sameParent: (ctx) => ctx.sourceNode.parent === ctx.targetNode.parent,
  // Node state checks
  targetGroupExpanded: (ctx) => (ctx.targetNode.type === "group" && ctx.targetNode.childrenExpanded) ?? false,
  targetGroupCollapsed: (ctx) => ctx.targetNode.type === "group" && !ctx.targetNode.childrenExpanded,
  // Previous/Next node checks
  hasPrevNode: (ctx) => ctx.prevNode !== null,
  hasNextNode: (ctx) => ctx.nextNode !== null,
  prevIsLeaf: (ctx) => {
    var _a;
    return ((_a = ctx.prevNode) == null ? void 0 : _a.type) === "leaf";
  },
  prevIsGroup: (ctx) => {
    var _a;
    return ((_a = ctx.prevNode) == null ? void 0 : _a.type) === "group";
  },
  nextIsLeaf: (ctx) => {
    var _a;
    return ((_a = ctx.nextNode) == null ? void 0 : _a.type) === "leaf";
  },
  nextIsGroup: (ctx) => {
    var _a;
    return ((_a = ctx.nextNode) == null ? void 0 : _a.type) === "group";
  },
  prevDepthEquals: (ctx, depth) => {
    var _a;
    return ((_a = ctx.prevNode) == null ? void 0 : _a.depth) === depth;
  },
  prevDepthEqualsSource: (ctx) => {
    var _a;
    return ((_a = ctx.prevNode) == null ? void 0 : _a.depth) === ctx.sourceNode.depth;
  },
  // Complex checks
  prevBelongsToSource: (ctx) => {
    if (!ctx.prevNode) {
      return false;
    }
    let currentId = ctx.prevNode.parent;
    while (currentId) {
      if (currentId === ctx.sourceNode.id) {
        return true;
      }
      const node = ctx.rowTree[currentId];
      if (!node) {
        break;
      }
      currentId = node.parent;
    }
    return false;
  },
  // Position checks
  isAdjacentPosition: (ctx) => {
    const {
      sourceRowIndex,
      targetRowIndex,
      dropPosition
    } = ctx;
    return dropPosition === "above" && targetRowIndex === sourceRowIndex + 1 || dropPosition === "below" && targetRowIndex === sourceRowIndex - 1;
  },
  // First child check
  targetFirstChildIsGroupWithSourceDepth: (ctx) => {
    var _a;
    if (ctx.targetNode.type !== "group") {
      return false;
    }
    const targetGroup = ctx.targetNode;
    const firstChild = ((_a = targetGroup.children) == null ? void 0 : _a[0]) ? ctx.rowTree[targetGroup.children[0]] : null;
    return (firstChild == null ? void 0 : firstChild.type) === "group" && firstChild.depth === ctx.sourceNode.depth;
  },
  targetFirstChildDepthEqualsSource: (ctx) => {
    var _a;
    if (ctx.targetNode.type !== "group") {
      return false;
    }
    const targetGroup = ctx.targetNode;
    const firstChild = ((_a = targetGroup.children) == null ? void 0 : _a[0]) ? ctx.rowTree[targetGroup.children[0]] : null;
    return firstChild ? firstChild.depth === ctx.sourceNode.depth : false;
  }
};
function determineOperationType(sourceNode, targetNode) {
  if (sourceNode.parent === targetNode.parent) {
    return "same-parent-swap";
  }
  if (sourceNode.type === "leaf") {
    return "cross-parent-leaf";
  }
  return "cross-parent-group";
}
function calculateTargetIndex(sourceNode, targetNode, isLastChild, rowTree) {
  if (sourceNode.parent === targetNode.parent && !isLastChild) {
    const parent = rowTree[sourceNode.parent];
    return parent.children.findIndex((id) => id === targetNode.id);
  }
  if (isLastChild) {
    const targetParent2 = rowTree[targetNode.parent];
    return targetParent2.children.length;
  }
  const targetParent = rowTree[targetNode.parent];
  const targetIndex = targetParent.children.findIndex((id) => id === targetNode.id);
  return targetIndex >= 0 ? targetIndex : 0;
}
var getNodePathInTree = ({
  id,
  tree
}) => {
  const path = [];
  let node = tree[id];
  while (node.id !== GRID_ROOT_GROUP_ID) {
    path.push({
      field: node.type === "leaf" ? null : node.groupingField,
      key: node.groupingKey
    });
    node = tree[node.parent];
  }
  path.reverse();
  return path;
};
var collectAllLeafDescendants = (groupNode, tree) => {
  const leafIds = [];
  const collectFromNode = (nodeId) => {
    const node = tree[nodeId];
    if (node.type === "leaf") {
      leafIds.push(nodeId);
    } else if (node.type === "group") {
      node.children.forEach(collectFromNode);
    }
  };
  groupNode.children.forEach(collectFromNode);
  return leafIds;
};
function findExistingGroupWithSameKey(parentNode, groupingKey, groupingField, tree) {
  for (const childId of parentNode.children) {
    const childNode = tree[childId];
    if (childNode && childNode.type === "group" && childNode.groupingKey === groupingKey && childNode.groupingField === groupingField) {
      return childNode;
    }
  }
  return null;
}
function removeEmptyAncestors(groupId, tree, removedGroups) {
  let rootLevelRemovals = 0;
  let currentGroupId = groupId;
  while (currentGroupId && currentGroupId !== GRID_ROOT_GROUP_ID) {
    const group = tree[currentGroupId];
    if (!group) {
      break;
    }
    const remainingChildren = group.children.filter((childId) => !removedGroups.has(childId));
    if (remainingChildren.length > 0) {
      break;
    }
    if (group.depth === 0) {
      rootLevelRemovals += 1;
    }
    removedGroups.add(currentGroupId);
    currentGroupId = group.parent;
  }
  return rootLevelRemovals;
}
function handleProcessRowUpdateError(error, onProcessRowUpdateError) {
  if (onProcessRowUpdateError) {
    onProcessRowUpdateError(error);
  } else if (true) {
    warnOnce(["MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.", "To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.", "For more detail, see https://mui.com/x/react-data-grid/editing/persistence/."], "error");
  }
}
var BatchRowUpdater = class {
  constructor(processRowUpdate, onProcessRowUpdateError) {
    __publicField(this, "rowsToUpdate", /* @__PURE__ */ (() => /* @__PURE__ */ new Map())());
    __publicField(this, "originalRows", /* @__PURE__ */ (() => /* @__PURE__ */ new Map())());
    __publicField(this, "successfulRowIds", /* @__PURE__ */ (() => /* @__PURE__ */ new Set())());
    __publicField(this, "failedRowIds", /* @__PURE__ */ (() => /* @__PURE__ */ new Set())());
    __publicField(this, "pendingRowUpdates", []);
    this.processRowUpdate = processRowUpdate;
    this.onProcessRowUpdateError = onProcessRowUpdateError;
  }
  queueUpdate(rowId, originalRow, updatedRow) {
    this.originalRows.set(rowId, originalRow);
    this.rowsToUpdate.set(rowId, updatedRow);
  }
  async executeAll() {
    const rowIds = Array.from(this.rowsToUpdate.keys());
    if (rowIds.length === 0) {
      return {
        successful: [],
        failed: [],
        updates: []
      };
    }
    const handleRowUpdate = async (rowId) => {
      const newRow = this.rowsToUpdate.get(rowId);
      const oldRow = this.originalRows.get(rowId);
      try {
        if (typeof this.processRowUpdate === "function") {
          const params = {
            rowId,
            previousRow: oldRow,
            updatedRow: newRow
          };
          const finalRow = await this.processRowUpdate(newRow, oldRow, params);
          this.pendingRowUpdates.push(finalRow || newRow);
          this.successfulRowIds.add(rowId);
        } else {
          this.pendingRowUpdates.push(newRow);
          this.successfulRowIds.add(rowId);
        }
      } catch (error) {
        this.failedRowIds.add(rowId);
        handleProcessRowUpdateError(error, this.onProcessRowUpdateError);
      }
    };
    const promises = rowIds.map((rowId) => {
      return new Promise((resolve) => {
        handleRowUpdate(rowId).then(resolve).catch(resolve);
      });
    });
    await Promise.all(promises);
    return {
      successful: Array.from(this.successfulRowIds),
      failed: Array.from(this.failedRowIds),
      updates: this.pendingRowUpdates
    };
  }
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowReorder/reorderValidator.js
var validationRules = [
  // ===== Basic invalid cases =====
  {
    name: "same-position",
    applies: (ctx) => ctx.sourceRowIndex === ctx.targetRowIndex,
    isInvalid: () => true,
    message: "Source and target are the same"
  },
  {
    name: "adjacent-position",
    applies: (ctx) => conditions.isAdjacentPosition(ctx),
    isInvalid: () => true,
    message: "Source and target are adjacent"
  },
  {
    name: "group-to-leaf",
    applies: conditions.isGroupToLeaf,
    isInvalid: () => true,
    message: "Cannot drop group on leaf"
  },
  // ===== Group to Group Rules =====
  {
    name: "group-to-group-above-leaf-belongs-to-source",
    applies: (ctx) => conditions.isGroupToGroup(ctx) && conditions.isDropAbove(ctx) && conditions.prevIsLeaf(ctx),
    isInvalid: conditions.prevBelongsToSource,
    message: "Previous leaf belongs to source group or its descendants"
  },
  {
    name: "group-to-group-above-invalid-depth",
    applies: (ctx) => conditions.isGroupToGroup(ctx) && conditions.isDropAbove(ctx) && !conditions.sameDepth(ctx) && !(ctx.targetNode.depth < ctx.sourceNode.depth && (conditions.prevIsLeaf(ctx) || conditions.prevIsGroup(ctx) && conditions.prevDepthEqualsSource(ctx))),
    isInvalid: () => true,
    message: "Invalid depth configuration for group above group"
  },
  {
    name: "group-to-group-above-different-parent-depth",
    applies: (ctx) => conditions.isGroupToGroup(ctx) && conditions.isDropAbove(ctx) && conditions.prevIsGroup(ctx) && conditions.prevDepthEqualsSource(ctx) && conditions.targetGroupExpanded(ctx),
    isInvalid: (ctx) => ctx.prevNode.depth !== ctx.sourceNode.depth,
    message: "Cannot reorder groups with different depths"
  },
  {
    name: "group-to-group-below-invalid-config",
    applies: (ctx) => conditions.isGroupToGroup(ctx) && conditions.isDropBelow(ctx),
    isInvalid: (ctx) => {
      if (conditions.sameDepth(ctx) && conditions.targetGroupCollapsed(ctx)) {
        return false;
      }
      if (conditions.targetDepthIsSourceMinusOne(ctx) && conditions.targetGroupExpanded(ctx) && conditions.targetFirstChildIsGroupWithSourceDepth(ctx)) {
        return false;
      }
      return true;
    },
    message: "Invalid group below group configuration"
  },
  // ===== Leaf to Leaf Rules =====
  {
    name: "leaf-to-leaf-different-depth",
    applies: (ctx) => conditions.isLeafToLeaf(ctx) && !conditions.sameDepth(ctx),
    isInvalid: () => true,
    message: "Leaves at different depths cannot be reordered"
  },
  {
    name: "leaf-to-leaf-invalid-below",
    applies: (ctx) => conditions.isLeafToLeaf(ctx) && conditions.sameDepth(ctx) && !conditions.sameParent(ctx) && conditions.isDropBelow(ctx),
    isInvalid: (ctx) => !(conditions.nextIsGroup(ctx) && ctx.sourceNode.depth > ctx.nextNode.depth) && !conditions.nextIsLeaf(ctx),
    message: "Invalid leaf below leaf configuration"
  },
  // ===== Leaf to Group Rules =====
  {
    name: "leaf-to-group-above-no-prev-leaf",
    applies: (ctx) => conditions.isLeafToGroup(ctx) && conditions.isDropAbove(ctx),
    isInvalid: (ctx) => !conditions.hasPrevNode(ctx) || !conditions.prevIsLeaf(ctx),
    message: "No valid previous leaf for leaf above group"
  },
  {
    name: "leaf-to-group-above-depth-mismatch",
    applies: (ctx) => conditions.isLeafToGroup(ctx) && conditions.isDropAbove(ctx) && conditions.prevIsLeaf(ctx) && !(ctx.sourceNode.depth > ctx.targetNode.depth && ctx.targetNode.depth === 0),
    isInvalid: (ctx) => ctx.prevNode.depth !== ctx.sourceNode.depth,
    message: "Previous node depth mismatch for leaf above group"
  },
  {
    name: "leaf-to-group-below-collapsed",
    applies: (ctx) => conditions.isLeafToGroup(ctx) && conditions.isDropBelow(ctx),
    isInvalid: conditions.targetGroupCollapsed,
    message: "Cannot drop below collapsed group"
  },
  {
    name: "leaf-to-group-below-invalid-depth",
    applies: (ctx) => conditions.isLeafToGroup(ctx) && conditions.isDropBelow(ctx) && conditions.targetGroupExpanded(ctx),
    isInvalid: (ctx) => {
      if (ctx.sourceNode.depth > ctx.targetNode.depth && ctx.targetNode.depth === ctx.sourceNode.depth - 1) {
        return false;
      }
      if (conditions.targetFirstChildDepthEqualsSource(ctx)) {
        return false;
      }
      return true;
    },
    message: "Invalid depth configuration for leaf below group"
  }
];
var RowReorderValidator = class {
  constructor(rules = validationRules) {
    this.rules = rules;
  }
  addRule(rule) {
    this.rules.push(rule);
  }
  removeRule(ruleName) {
    this.rules = this.rules.filter((r) => r.name !== ruleName);
  }
  validate(context) {
    for (const rule of this.rules) {
      if (rule.applies(context) && rule.isInvalid(context)) {
        return false;
      }
    }
    return true;
  }
};
var rowGroupingReorderValidator = new RowReorderValidator(validationRules);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/useGridRowGrouping.js
var rowGroupingStateInitializer = (state, props, apiRef) => {
  var _a, _b;
  apiRef.current.caches.rowGrouping = {
    rulesOnLastRowTreeCreation: []
  };
  return _extends({}, state, {
    rowGrouping: {
      model: props.rowGroupingModel ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.rowGrouping) == null ? void 0 : _b.model) ?? []
    }
  });
};
var useGridRowGrouping = (apiRef, props) => {
  var _a, _b;
  apiRef.current.registerControlState({
    stateId: "rowGrouping",
    propModel: props.rowGroupingModel,
    propOnChange: props.onRowGroupingModelChange,
    stateSelector: gridRowGroupingModelSelector,
    changeEvent: "rowGroupingModelChange"
  });
  const setRowGroupingModel = React7.useCallback((model) => {
    const currentModel = gridRowGroupingModelSelector(apiRef);
    if (currentModel !== model) {
      apiRef.current.setState(mergeStateWithRowGroupingModel(model));
      setStrategyAvailability(apiRef, props.disableRowGrouping);
    }
  }, [apiRef, props.disableRowGrouping]);
  const addRowGroupingCriteria = React7.useCallback((field, groupingIndex) => {
    const currentModel = gridRowGroupingModelSelector(apiRef);
    if (currentModel.includes(field)) {
      return;
    }
    const cleanGroupingIndex = groupingIndex ?? currentModel.length;
    const updatedModel = [...currentModel.slice(0, cleanGroupingIndex), field, ...currentModel.slice(cleanGroupingIndex)];
    apiRef.current.setRowGroupingModel(updatedModel);
  }, [apiRef]);
  const removeRowGroupingCriteria = React7.useCallback((field) => {
    const currentModel = gridRowGroupingModelSelector(apiRef);
    if (!currentModel.includes(field)) {
      return;
    }
    apiRef.current.setRowGroupingModel(currentModel.filter((el) => el !== field));
  }, [apiRef]);
  const setRowGroupingCriteriaIndex = React7.useCallback((field, targetIndex) => {
    const currentModel = gridRowGroupingModelSelector(apiRef);
    const currentTargetIndex = currentModel.indexOf(field);
    if (currentTargetIndex === -1) {
      return;
    }
    const updatedModel = [...currentModel];
    updatedModel.splice(targetIndex, 0, updatedModel.splice(currentTargetIndex, 1)[0]);
    apiRef.current.setRowGroupingModel(updatedModel);
  }, [apiRef]);
  const rowGroupingApi = {
    setRowGroupingModel,
    addRowGroupingCriteria,
    removeRowGroupingCriteria,
    setRowGroupingCriteriaIndex
  };
  useGridApiMethod(apiRef, rowGroupingApi, "public");
  const addColumnMenuButtons = React7.useCallback((columnMenuItems, colDef) => {
    if (props.disableRowGrouping) {
      return columnMenuItems;
    }
    if (isGroupingColumn(colDef.field) || colDef.groupable) {
      return [...columnMenuItems, "columnMenuGroupingItem"];
    }
    return columnMenuItems;
  }, [props.disableRowGrouping]);
  const addGetRowsParams = React7.useCallback((params) => {
    return _extends({}, params, {
      groupFields: gridRowGroupingModelSelector(apiRef)
    });
  }, [apiRef]);
  const stateExportPreProcessing = React7.useCallback((prevState, context) => {
    var _a2, _b2;
    const rowGroupingModelToExport = gridRowGroupingModelSelector(apiRef);
    const shouldExportRowGroupingModel = (
      // Always export if the `exportOnlyDirtyModels` property is not activated
      !context.exportOnlyDirtyModels || // Always export if the model is controlled
      props.rowGroupingModel != null || // Always export if the model has been initialized
      ((_b2 = (_a2 = props.initialState) == null ? void 0 : _a2.rowGrouping) == null ? void 0 : _b2.model) != null || // Export if the model is not empty
      Object.keys(rowGroupingModelToExport).length > 0
    );
    if (!shouldExportRowGroupingModel) {
      return prevState;
    }
    return _extends({}, prevState, {
      rowGrouping: {
        model: rowGroupingModelToExport
      }
    });
  }, [apiRef, props.rowGroupingModel, (_b = (_a = props.initialState) == null ? void 0 : _a.rowGrouping) == null ? void 0 : _b.model]);
  const stateRestorePreProcessing = React7.useCallback((params, context) => {
    var _a2;
    if (props.disableRowGrouping) {
      return params;
    }
    const rowGroupingModel = (_a2 = context.stateToRestore.rowGrouping) == null ? void 0 : _a2.model;
    if (rowGroupingModel != null) {
      apiRef.current.setState(mergeStateWithRowGroupingModel(rowGroupingModel));
    }
    return params;
  }, [apiRef, props.disableRowGrouping]);
  useGridRegisterPipeProcessor(apiRef, "columnMenu", addColumnMenuButtons);
  useGridRegisterPipeProcessor(apiRef, "getRowsParams", addGetRowsParams);
  useGridRegisterPipeProcessor(apiRef, "exportState", stateExportPreProcessing);
  useGridRegisterPipeProcessor(apiRef, "restoreState", stateRestorePreProcessing);
  const handleCellKeyDown = React7.useCallback((params, event) => {
    const cellParams = apiRef.current.getCellParams(params.id, params.field);
    if (isGroupingColumn(cellParams.field) && event.key === " " && !event.shiftKey) {
      event.stopPropagation();
      event.preventDefault();
      if (params.rowNode.type !== "group") {
        return;
      }
      const isOnGroupingCell = props.rowGroupingColumnMode === "single" || getRowGroupingFieldFromGroupingCriteria(params.rowNode.groupingField) === params.field;
      if (!isOnGroupingCell) {
        return;
      }
      if (props.dataSource && !params.rowNode.childrenExpanded) {
        apiRef.current.dataSource.fetchRows(params.id);
        return;
      }
      apiRef.current.setRowChildrenExpansion(params.id, !params.rowNode.childrenExpanded);
    }
  }, [apiRef, props.rowGroupingColumnMode, props.dataSource]);
  const checkGroupingColumnsModelDiff = React7.useCallback(() => {
    const sanitizedRowGroupingModel = gridRowGroupingSanitizedModelSelector(apiRef);
    const rulesOnLastRowTreeCreation = apiRef.current.caches.rowGrouping.rulesOnLastRowTreeCreation || [];
    const groupingRules = getGroupingRules({
      sanitizedRowGroupingModel,
      columnsLookup: gridColumnLookupSelector(apiRef)
    });
    if (!areGroupingRulesEqual(rulesOnLastRowTreeCreation, groupingRules)) {
      apiRef.current.caches.rowGrouping.rulesOnLastRowTreeCreation = groupingRules;
      apiRef.current.requestPipeProcessorsApplication("hydrateColumns");
      setStrategyAvailability(apiRef, props.disableRowGrouping);
      if (apiRef.current.getActiveStrategy(GridStrategyGroup.RowTree) === RowGroupingStrategy.Default) {
        apiRef.current.publishEvent("activeStrategyProcessorChange", "rowTreeCreation");
      }
    }
  }, [apiRef, props.disableRowGrouping]);
  const getRowReorderTargetIndex = React7.useCallback((initialValue, {
    sourceRowId,
    targetRowId,
    dropPosition,
    dragDirection
  }) => {
    if (gridRowMaximumTreeDepthSelector(apiRef) === 1 || props.treeData) {
      return initialValue;
    }
    const expandedSortedRowIndexLookup = gridExpandedSortedRowIndexLookupSelector(apiRef);
    const expandedSortedRowIds = gridExpandedSortedRowIdsSelector(apiRef);
    const rowTree = gridRowTreeSelector(apiRef);
    const sourceRowIndex = expandedSortedRowIndexLookup[sourceRowId];
    const targetRowIndex = expandedSortedRowIndexLookup[targetRowId];
    const sourceNode = rowTree[sourceRowId];
    const targetNode = rowTree[targetRowId];
    const prevNode = targetRowIndex > 0 ? rowTree[expandedSortedRowIds[targetRowIndex - 1]] : null;
    const nextNode = targetRowIndex < expandedSortedRowIds.length - 1 ? rowTree[expandedSortedRowIds[targetRowIndex + 1]] : null;
    if (!sourceNode || !targetNode) {
      return -1;
    }
    const context = {
      sourceNode,
      targetNode,
      prevNode,
      nextNode,
      rowTree,
      dropPosition,
      dragDirection,
      targetRowIndex,
      sourceRowIndex,
      expandedSortedRowIndexLookup
    };
    if (rowGroupingReorderValidator.validate(context)) {
      return dropPosition === "below" ? targetRowIndex + 1 : targetRowIndex;
    }
    return -1;
  }, [apiRef, props.treeData]);
  useGridRegisterPipeProcessor(apiRef, "getRowReorderTargetIndex", getRowReorderTargetIndex);
  useGridEvent(apiRef, "cellKeyDown", handleCellKeyDown);
  useGridEvent(apiRef, "columnsChange", checkGroupingColumnsModelDiff);
  useGridEvent(apiRef, "rowGroupingModelChange", checkGroupingColumnsModelDiff);
  React7.useEffect(() => {
    if (props.rowGroupingModel !== void 0) {
      apiRef.current.setRowGroupingModel(props.rowGroupingModel);
    }
  }, [apiRef, props.rowGroupingModel]);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/useGridRowGroupingPreProcessors.js
var React13 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/createGroupingColDef.js
var React12 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/GridGroupingColumnFooterCell.js
var React8 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
function GridGroupingColumnFooterCell(props) {
  const rootProps = useGridRootProps2();
  const sx = {
    ml: 0
  };
  if (props.rowNode.parent == null) {
    sx.ml = 0;
  } else if (rootProps.rowGroupingColumnMode === "multiple") {
    sx.ml = 2;
  } else {
    sx.ml = `calc(var(--DataGrid-cellOffsetMultiplier) * ${vars.spacing(props.rowNode.depth)})`;
  }
  return (0, import_jsx_runtime4.jsx)(GridFooterCell, _extends({
    sx
  }, props));
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridGroupingCriteriaCell.js
var React9 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses3 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["groupingCriteriaCell"],
    toggle: ["groupingCriteriaCellToggle"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridGroupingCriteriaCell(props) {
  var _a;
  const {
    id,
    field,
    rowNode,
    hideDescendantCount,
    formattedValue
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses3(ownerState);
  const filteredDescendantCountLookup = useGridSelector(apiRef, gridFilteredDescendantCountLookupSelector);
  const filteredDescendantCount = filteredDescendantCountLookup[rowNode.id] ?? 0;
  const pivotActive = useGridSelector(apiRef, gridPivotActiveSelector);
  const maxTreeDepth = gridRowMaximumTreeDepthSelector(apiRef);
  const shouldShowToggleContainer = !pivotActive || maxTreeDepth > 2;
  const shouldShowToggleButton = !pivotActive || rowNode.depth < maxTreeDepth - 2;
  const Icon = rowNode.childrenExpanded ? rootProps.slots.groupingCriteriaCollapseIcon : rootProps.slots.groupingCriteriaExpandIcon;
  const handleKeyDown = (event) => {
    if (event.key === " ") {
      event.stopPropagation();
    }
    apiRef.current.publishEvent("cellKeyDown", props, event);
  };
  const handleClick = (event) => {
    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };
  let cellContent;
  const colDef = apiRef.current.getColumn(rowNode.groupingField);
  if (typeof colDef.renderCell === "function") {
    cellContent = colDef.renderCell(props);
  } else if (typeof formattedValue !== "undefined") {
    cellContent = (0, import_jsx_runtime5.jsx)("span", {
      children: formattedValue
    });
  } else {
    cellContent = (0, import_jsx_runtime5.jsx)("span", {
      children: rowNode.groupingKey
    });
  }
  return (0, import_jsx_runtime5.jsxs)("div", {
    className: classes.root,
    style: {
      marginLeft: rootProps.rowGroupingColumnMode === "multiple" ? 0 : `calc(var(--DataGrid-cellOffsetMultiplier) * ${rowNode.depth} * ${vars.spacing(1)})`
    },
    children: [shouldShowToggleContainer ? (0, import_jsx_runtime5.jsx)("div", {
      className: classes.toggle,
      children: shouldShowToggleButton && filteredDescendantCount > 0 && (0, import_jsx_runtime5.jsx)(rootProps.slots.baseIconButton, _extends({
        size: "small",
        onClick: handleClick,
        onKeyDown: handleKeyDown,
        tabIndex: -1,
        "aria-label": rowNode.childrenExpanded ? apiRef.current.getLocaleText("treeDataCollapse") : apiRef.current.getLocaleText("treeDataExpand")
      }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
        children: (0, import_jsx_runtime5.jsx)(Icon, {
          fontSize: "inherit"
        })
      }))
    }) : null, cellContent, !hideDescendantCount && filteredDescendantCount > 0 ? (0, import_jsx_runtime5.jsxs)("span", {
      style: {
        whiteSpace: "pre"
      },
      children: [" (", filteredDescendantCount, ")"]
    }) : null]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridDataSourceGroupingCriteriaCell.js
var React10 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses4 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["groupingCriteriaCell"],
    toggle: ["groupingCriteriaCellToggle"],
    loadingContainer: ["groupingCriteriaCellLoadingContainer"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridGroupingCriteriaCellIcon(props) {
  var _a;
  const apiRef = useGridPrivateApiContext();
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses4(rootProps);
  const {
    rowNode,
    id,
    field,
    descendantCount
  } = props;
  const isDataLoading = useGridSelector(apiRef, gridDataSourceLoadingIdSelector, id);
  const error = useGridSelector(apiRef, gridDataSourceErrorSelector, id);
  const handleClick = (event) => {
    if (!rowNode.childrenExpanded) {
      apiRef.current.dataSource.fetchRows(id);
    } else {
      apiRef.current.setRowChildrenExpansion(id, false);
      apiRef.current.removeChildrenRows(id);
    }
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };
  const Icon = rowNode.childrenExpanded ? rootProps.slots.groupingCriteriaCollapseIcon : rootProps.slots.groupingCriteriaExpandIcon;
  if (isDataLoading) {
    return (0, import_jsx_runtime6.jsx)("div", {
      className: classes.loadingContainer,
      children: (0, import_jsx_runtime6.jsx)(rootProps.slots.baseCircularProgress, {
        size: "1rem",
        color: "inherit"
      })
    });
  }
  return descendantCount === -1 || descendantCount > 0 ? (0, import_jsx_runtime6.jsx)(rootProps.slots.baseIconButton, _extends({
    size: "small",
    onClick: handleClick,
    tabIndex: -1,
    "aria-label": rowNode.childrenExpanded ? apiRef.current.getLocaleText("treeDataCollapse") : apiRef.current.getLocaleText("treeDataExpand")
  }, (_a = rootProps == null ? void 0 : rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
    children: (0, import_jsx_runtime6.jsx)(rootProps.slots.baseTooltip, {
      title: (error == null ? void 0 : error.message) ?? null,
      children: (0, import_jsx_runtime6.jsx)(rootProps.slots.baseBadge, {
        variant: "dot",
        color: "error",
        invisible: !error,
        children: (0, import_jsx_runtime6.jsx)(Icon, {
          fontSize: "inherit"
        })
      })
    })
  })) : null;
}
function GridDataSourceGroupingCriteriaCell(props) {
  var _a, _b;
  const {
    id,
    field,
    rowNode,
    hideDescendantCount,
    formattedValue
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const row = useGridSelector(apiRef, gridRowSelector, id);
  const classes = useUtilityClasses4(rootProps);
  let descendantCount = 0;
  if (row) {
    descendantCount = ((_b = (_a = rootProps.dataSource) == null ? void 0 : _a.getChildrenCount) == null ? void 0 : _b.call(_a, row)) ?? 0;
  }
  let cellContent;
  const colDef = apiRef.current.getColumn(rowNode.groupingField);
  if (typeof (colDef == null ? void 0 : colDef.renderCell) === "function") {
    cellContent = colDef.renderCell(props);
  } else if (typeof formattedValue !== "undefined") {
    cellContent = (0, import_jsx_runtime6.jsx)("span", {
      children: formattedValue
    });
  } else {
    cellContent = (0, import_jsx_runtime6.jsx)("span", {
      children: rowNode.groupingKey
    });
  }
  return (0, import_jsx_runtime6.jsxs)("div", {
    className: classes.root,
    style: {
      marginLeft: rootProps.rowGroupingColumnMode === "multiple" ? 0 : `calc(var(--DataGrid-cellOffsetMultiplier) * ${vars.spacing(rowNode.depth)})`
    },
    children: [(0, import_jsx_runtime6.jsx)("div", {
      className: classes.toggle,
      children: (0, import_jsx_runtime6.jsx)(GridGroupingCriteriaCellIcon, {
        id,
        field,
        rowNode,
        row,
        descendantCount
      })
    }), cellContent, !hideDescendantCount && descendantCount > 0 ? (0, import_jsx_runtime6.jsxs)("span", {
      style: {
        whiteSpace: "pre"
      },
      children: [" (", descendantCount, ")"]
    }) : null]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridGroupingColumnLeafCell.js
var React11 = __toESM(require_react(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
function GridGroupingColumnLeafCell(props) {
  const {
    rowNode
  } = props;
  const rootProps = useGridRootProps2();
  return (0, import_jsx_runtime7.jsx)("div", {
    style: {
      marginLeft: rootProps.rowGroupingColumnMode === "multiple" ? vars.spacing(1) : `calc(var(--DataGrid-cellOffsetMultiplier) * ${vars.spacing(rowNode.depth)})`
    },
    children: props.formattedValue ?? props.value
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/createGroupingColDef.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var _excluded4 = ["leafField", "mainGroupingCriteria", "hideDescendantCount"];
var _excluded22 = ["leafField", "mainGroupingCriteria", "hideDescendantCount"];
var GROUPING_COL_DEF_DEFAULT_PROPERTIES = _extends({}, GRID_STRING_COL_DEF, {
  type: "custom",
  disableReorder: true
});
var GROUPING_COL_DEF_FORCED_PROPERTIES_DEFAULT = {
  editable: false,
  groupable: false
};
var GROUPING_COL_DEF_FORCED_PROPERTIES_DATA_SOURCE = _extends({}, GROUPING_COL_DEF_FORCED_PROPERTIES_DEFAULT, {
  // TODO: Support these features on the grouping column(s)
  filterable: false,
  sortable: false
});
var groupingFieldIndexComparator = (v1, v2, cellParams1, cellParams2) => {
  const model = gridRowGroupingSanitizedModelSelector({
    current: cellParams1.api
  });
  const groupingField1 = cellParams1.rowNode.groupingField ?? null;
  const groupingField2 = cellParams2.rowNode.groupingField ?? null;
  if (groupingField1 === groupingField2) {
    return 0;
  }
  if (groupingField1 == null) {
    return -1;
  }
  if (groupingField2 == null) {
    return 1;
  }
  if (model.indexOf(groupingField1) < model.indexOf(groupingField2)) {
    return -1;
  }
  return 1;
};
var getLeafProperties = (leafColDef) => ({
  headerName: leafColDef.headerName ?? leafColDef.field,
  sortable: leafColDef.sortable,
  filterable: leafColDef.filterable,
  valueOptions: isSingleSelectColDef(leafColDef) ? leafColDef.valueOptions : void 0,
  filterOperators: leafColDef.filterOperators,
  sortComparator: (v1, v2, cellParams1, cellParams2) => {
    if (cellParams1.rowNode.type === "leaf" && cellParams2.rowNode.type === "leaf") {
      return leafColDef.sortComparator(v1, v2, cellParams1, cellParams2);
    }
    return groupingFieldIndexComparator(v1, v2, cellParams1, cellParams2);
  }
});
var groupedByColValueFormatter = (groupedByColDef) => (value, row, _, apiRef) => {
  const rowId = gridRowIdSelector(apiRef, row);
  const rowNode = gridRowNodeSelector(apiRef, rowId);
  if (rowNode.type === "group" && rowNode.groupingField === groupedByColDef.field) {
    return groupedByColDef.valueFormatter(value, row, groupedByColDef, apiRef);
  }
  return value;
};
function getGroupingCriteriaProperties(groupedByColDef, rowGroupingColumnMode, rowGroupingModel = [], columnsLookup = {}) {
  let valueFormatter;
  if (rowGroupingColumnMode === "single" && rowGroupingModel.length > 1) {
    valueFormatter = (value, row, column, apiRef) => {
      const rowId = gridRowIdSelector(apiRef, row);
      const rowNode = gridRowNodeSelector(apiRef, rowId);
      if (rowNode.type === "group") {
        const originalColDef = columnsLookup[rowNode.groupingField];
        if (originalColDef.type === "singleSelect") {
          return value;
        }
        const columnValueFormatter = originalColDef.valueFormatter;
        if (typeof columnValueFormatter === "function") {
          return columnValueFormatter(value, row, column, apiRef);
        }
      }
      return value;
    };
  } else {
    valueFormatter = groupedByColDef.valueFormatter ? groupedByColValueFormatter(groupedByColDef) : void 0;
  }
  const properties = {
    sortable: groupedByColDef.sortable,
    filterable: groupedByColDef.filterable,
    valueFormatter,
    valueOptions: isSingleSelectColDef(groupedByColDef) ? groupedByColDef.valueOptions : void 0,
    sortComparator: (v1, v2, cellParams1, cellParams2) => {
      if (cellParams1.rowNode.type === "group" && cellParams2.rowNode.type === "group" && cellParams1.rowNode.groupingField === cellParams2.rowNode.groupingField) {
        const colDef = cellParams1.api.getColumn(cellParams1.rowNode.groupingField);
        return colDef.sortComparator(v1, v2, cellParams1, cellParams2);
      }
      return groupingFieldIndexComparator(v1, v2, cellParams1, cellParams2);
    },
    filterOperators: groupedByColDef.filterOperators
  };
  const applyHeaderName = !(rowGroupingColumnMode === "single" && rowGroupingModel.length > 1);
  if (applyHeaderName) {
    properties.headerName = groupedByColDef.headerName ?? groupedByColDef.field;
  }
  return properties;
}
var createGroupingColDefForOneGroupingCriteria = ({
  columnsLookup,
  groupedByColDef,
  groupingCriteria,
  colDefOverride,
  strategy = RowGroupingStrategy.Default
}) => {
  const _ref = colDefOverride ?? {}, {
    leafField,
    mainGroupingCriteria,
    hideDescendantCount
  } = _ref, colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref, _excluded4);
  const leafColDef = leafField ? columnsLookup[leafField] : null;
  const CriteriaCell = strategy === RowGroupingStrategy.Default ? GridGroupingCriteriaCell : GridDataSourceGroupingCriteriaCell;
  const commonProperties = {
    width: Math.max((groupedByColDef.width ?? GRID_STRING_COL_DEF.width) + 40, (leafColDef == null ? void 0 : leafColDef.width) ?? 0),
    renderCell: (params) => {
      if (params.rowNode.type === "footer" || params.rowNode.type === "pinnedRow") {
        return (0, import_jsx_runtime8.jsx)(GridGroupingColumnFooterCell, _extends({}, params));
      }
      if (params.rowNode.type === "leaf") {
        if (leafColDef) {
          const leafParams = _extends({}, params.api.getCellParams(params.id, leafField), {
            api: params.api,
            hasFocus: params.hasFocus
          });
          if (leafColDef.renderCell) {
            return leafColDef.renderCell(leafParams);
          }
          return (0, import_jsx_runtime8.jsx)(GridGroupingColumnLeafCell, _extends({}, leafParams));
        }
        return "";
      }
      if (params.rowNode.groupingField === groupingCriteria) {
        return (0, import_jsx_runtime8.jsx)(CriteriaCell, _extends({}, params, {
          hideDescendantCount
        }));
      }
      return "";
    },
    valueGetter: (value, row, column, apiRef) => {
      const rowId = gridRowIdSelector(apiRef, row);
      const rowNode = gridRowNodeSelector(apiRef, rowId);
      if (!rowNode || rowNode.type === "footer" || rowNode.type === "pinnedRow") {
        return void 0;
      }
      if (rowNode.type === "leaf") {
        if (leafColDef) {
          return apiRef.current.getCellValue(rowId, leafField);
        }
        return void 0;
      }
      if (rowNode.groupingField === groupingCriteria) {
        return rowNode.groupingKey;
      }
      return void 0;
    }
  };
  let sourceProperties;
  if (mainGroupingCriteria && mainGroupingCriteria === groupingCriteria) {
    sourceProperties = getGroupingCriteriaProperties(groupedByColDef, "multiple");
  } else if (leafColDef) {
    sourceProperties = getLeafProperties(leafColDef);
  } else {
    sourceProperties = getGroupingCriteriaProperties(groupedByColDef, "multiple");
  }
  const forcedProperties = _extends({
    field: getRowGroupingFieldFromGroupingCriteria(groupingCriteria)
  }, GROUPING_COL_DEF_FORCED_PROPERTIES_DEFAULT);
  return _extends({}, GROUPING_COL_DEF_DEFAULT_PROPERTIES, commonProperties, sourceProperties, colDefOverrideProperties, forcedProperties);
};
var createGroupingColDefForAllGroupingCriteria = ({
  apiRef,
  columnsLookup,
  rowGroupingModel,
  colDefOverride,
  strategy = RowGroupingStrategy.Default
}) => {
  const _ref2 = colDefOverride ?? {}, {
    leafField,
    mainGroupingCriteria,
    hideDescendantCount
  } = _ref2, colDefOverrideProperties = _objectWithoutPropertiesLoose(_ref2, _excluded22);
  const leafColDef = leafField ? columnsLookup[leafField] : null;
  const CriteriaCell = strategy === RowGroupingStrategy.Default ? GridGroupingCriteriaCell : GridDataSourceGroupingCriteriaCell;
  const commonProperties = {
    headerName: apiRef.current.getLocaleText("groupingColumnHeaderName"),
    width: Math.max(...rowGroupingModel.map((field) => (columnsLookup[field].width ?? GRID_STRING_COL_DEF.width) + 40), (leafColDef == null ? void 0 : leafColDef.width) ?? 0),
    renderCell: (params) => {
      if (params.rowNode.type === "footer" || params.rowNode.type === "pinnedRow") {
        return (0, import_jsx_runtime8.jsx)(GridGroupingColumnFooterCell, _extends({}, params));
      }
      if (params.rowNode.type === "leaf") {
        if (leafColDef) {
          const leafParams = _extends({}, params.api.getCellParams(params.id, leafField), {
            api: params.api,
            hasFocus: params.hasFocus
          });
          if (leafColDef.renderCell) {
            return leafColDef.renderCell(leafParams);
          }
          return (0, import_jsx_runtime8.jsx)(GridGroupingColumnLeafCell, _extends({}, leafParams));
        }
        return "";
      }
      return (0, import_jsx_runtime8.jsx)(CriteriaCell, _extends({}, params, {
        hideDescendantCount
      }));
    },
    valueGetter: (value, row) => {
      const rowId = gridRowIdSelector(apiRef, row);
      const rowNode = gridRowNodeSelector(apiRef, rowId);
      if (!rowNode || rowNode.type === "footer" || rowNode.type === "pinnedRow") {
        return void 0;
      }
      if (rowNode.type === "leaf") {
        if (leafColDef) {
          return apiRef.current.getCellValue(rowId, leafField);
        }
        return void 0;
      }
      return rowNode.groupingKey;
    }
  };
  if (true) commonProperties.renderCell.displayName = "commonProperties.renderCell";
  let sourceProperties;
  if (mainGroupingCriteria && rowGroupingModel.includes(mainGroupingCriteria)) {
    sourceProperties = getGroupingCriteriaProperties(columnsLookup[mainGroupingCriteria], "single", rowGroupingModel, columnsLookup);
  } else if (leafColDef) {
    sourceProperties = getLeafProperties(leafColDef);
  } else {
    sourceProperties = getGroupingCriteriaProperties(columnsLookup[rowGroupingModel[0]], "single", rowGroupingModel, columnsLookup);
  }
  const forcedProperties = _extends({
    field: GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD
  }, strategy === RowGroupingStrategy.Default ? GROUPING_COL_DEF_FORCED_PROPERTIES_DEFAULT : GROUPING_COL_DEF_FORCED_PROPERTIES_DATA_SOURCE);
  return _extends({}, GROUPING_COL_DEF_DEFAULT_PROPERTIES, commonProperties, sourceProperties, colDefOverrideProperties, forcedProperties);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/useGridRowGroupingPreProcessors.js
var useGridRowGroupingPreProcessors = (apiRef, props) => {
  const getGroupingColDefs = React13.useCallback((columnsState) => {
    if (props.disableRowGrouping) {
      return [];
    }
    const strategy = props.dataSource ? RowGroupingStrategy.DataSource : RowGroupingStrategy.Default;
    const groupingColDefProp = props.groupingColDef;
    const rowGroupingModel = gridRowGroupingModelSelector(apiRef).filter((field) => !!columnsState.lookup[field]);
    if (rowGroupingModel.length === 0) {
      return [];
    }
    switch (props.rowGroupingColumnMode) {
      case "single": {
        return [createGroupingColDefForAllGroupingCriteria({
          apiRef,
          rowGroupingModel,
          colDefOverride: getColDefOverrides(groupingColDefProp, rowGroupingModel, strategy),
          columnsLookup: columnsState.lookup,
          strategy
        })];
      }
      case "multiple": {
        return rowGroupingModel.map((groupingCriteria) => createGroupingColDefForOneGroupingCriteria({
          groupingCriteria,
          colDefOverride: getColDefOverrides(groupingColDefProp, [groupingCriteria]),
          groupedByColDef: columnsState.lookup[groupingCriteria],
          columnsLookup: columnsState.lookup,
          strategy
        }));
      }
      default: {
        return [];
      }
    }
  }, [apiRef, props.groupingColDef, props.rowGroupingColumnMode, props.disableRowGrouping, props.dataSource]);
  const updateGroupingColumn = React13.useCallback((columnsState) => {
    const groupingColDefs = getGroupingColDefs(columnsState);
    let newColumnFields = [];
    const newColumnsLookup = {};
    columnsState.orderedFields.forEach((field) => {
      if (!isGroupingColumn(field)) {
        newColumnFields.push(field);
        newColumnsLookup[field] = columnsState.lookup[field];
      }
    });
    groupingColDefs.forEach((groupingColDef) => {
      const matchingGroupingColDef = columnsState.lookup[groupingColDef.field];
      if (matchingGroupingColDef) {
        groupingColDef.width = matchingGroupingColDef.width;
        groupingColDef.flex = matchingGroupingColDef.flex;
      }
      newColumnsLookup[groupingColDef.field] = groupingColDef;
    });
    newColumnFields = [...groupingColDefs.map((colDef) => colDef.field), ...newColumnFields];
    columnsState.orderedFields = newColumnFields;
    columnsState.lookup = newColumnsLookup;
    return columnsState;
  }, [getGroupingColDefs]);
  const createRowTreeForRowGrouping = React13.useCallback((params) => {
    const sanitizedRowGroupingModel = gridRowGroupingSanitizedModelSelector(apiRef);
    const columnsLookup = gridColumnLookupSelector(apiRef);
    const groupingRules = getGroupingRules({
      sanitizedRowGroupingModel,
      columnsLookup
    });
    apiRef.current.caches.rowGrouping.rulesOnLastRowTreeCreation = groupingRules;
    const getRowTreeBuilderNode = (rowId) => {
      const row = params.dataRowIdToModelLookup[rowId];
      const parentPath = groupingRules.map((groupingRule) => getCellGroupingCriteria({
        row,
        groupingRule,
        colDef: columnsLookup[groupingRule.field],
        apiRef
      })).filter((cell) => cell.key != null);
      const leafGroupingCriteria = {
        key: rowId.toString(),
        field: null
      };
      return {
        path: [...parentPath, leafGroupingCriteria],
        id: rowId
      };
    };
    if (params.updates.type === "full") {
      return createRowTree({
        previousTree: params.previousTree,
        nodes: params.updates.rows.map(getRowTreeBuilderNode),
        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
        isGroupExpandedByDefault: props.isGroupExpandedByDefault,
        groupingName: RowGroupingStrategy.Default
      });
    }
    return updateRowTree({
      nodes: {
        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),
        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),
        removed: params.updates.actions.remove
      },
      previousTree: params.previousTree,
      previousTreeDepth: params.previousTreeDepths,
      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
      isGroupExpandedByDefault: props.isGroupExpandedByDefault,
      groupingName: RowGroupingStrategy.Default
    });
  }, [apiRef, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);
  const filterRows = React13.useCallback((params) => {
    const rowTree = gridRowTreeSelector(apiRef);
    return filterRowTreeFromGroupingColumns({
      rowTree,
      isRowMatchingFilters: params.isRowMatchingFilters,
      filterModel: params.filterModel,
      apiRef
    });
  }, [apiRef]);
  const sortRows = React13.useCallback((params) => {
    const rowTree = gridRowTreeSelector(apiRef);
    return sortRowTree({
      rowTree,
      sortRowList: params.sortRowList,
      disableChildrenSorting: false,
      shouldRenderGroupBelowLeaves: true
    });
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "hydrateColumns", updateGroupingColumn);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.Default, "rowTreeCreation", createRowTreeForRowGrouping);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.Default, "filtering", filterRows);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.Default, "sorting", sortRows);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.Default, "visibleRowsLookupCreation", getVisibleRowsLookup);
  useFirstRender(() => {
    setStrategyAvailability(apiRef, props.disableRowGrouping, props.dataSource);
  });
  const isFirstRender = React13.useRef(true);
  React13.useEffect(() => {
    if (!isFirstRender.current) {
      setStrategyAvailability(apiRef, props.disableRowGrouping, props.dataSource);
    } else {
      isFirstRender.current = false;
    }
  }, [apiRef, props.disableRowGrouping, props.dataSource]);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowGrouping/useGridDataSourceRowGroupingPreProcessors.js
var React14 = __toESM(require_react(), 1);
var useGridDataSourceRowGroupingPreProcessors = (apiRef, props) => {
  const createRowTreeForRowGrouping = React14.useCallback((params) => {
    var _a, _b;
    const getGroupKey = (_a = props.dataSource) == null ? void 0 : _a.getGroupKey;
    if (!getGroupKey) {
      throw new Error("MUI X: No `getGroupKey` method provided with the dataSource.");
    }
    const getChildrenCount = (_b = props.dataSource) == null ? void 0 : _b.getChildrenCount;
    if (!getChildrenCount) {
      throw new Error("MUI X: No `getChildrenCount` method provided with the dataSource.");
    }
    const sanitizedRowGroupingModel = gridRowGroupingSanitizedModelSelector(apiRef);
    const columnsLookup = gridColumnLookupSelector(apiRef);
    const groupingRules = getGroupingRules({
      sanitizedRowGroupingModel,
      columnsLookup
    });
    apiRef.current.caches.rowGrouping.rulesOnLastRowTreeCreation = groupingRules;
    const getRowTreeBuilderNode = (rowId) => {
      const parentPath = params.updates.groupKeys ?? getParentPath(rowId, params);
      const leafKey = getGroupKey(params.dataRowIdToModelLookup[rowId]);
      return {
        id: rowId,
        path: [...parentPath, leafKey ?? rowId.toString()].map((key, i) => {
          var _a2;
          return {
            key,
            field: ((_a2 = groupingRules[i]) == null ? void 0 : _a2.field) ?? null
          };
        }),
        serverChildrenCount: getChildrenCount(params.dataRowIdToModelLookup[rowId]) ?? 0
      };
    };
    if (params.updates.type === "full") {
      return createRowTree({
        previousTree: params.previousTree,
        nodes: params.updates.rows.map(getRowTreeBuilderNode),
        defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
        isGroupExpandedByDefault: props.isGroupExpandedByDefault,
        groupingName: RowGroupingStrategy.DataSource
      });
    }
    return updateRowTree({
      nodes: {
        inserted: params.updates.actions.insert.map(getRowTreeBuilderNode),
        modified: params.updates.actions.modify.map(getRowTreeBuilderNode),
        removed: params.updates.actions.remove
      },
      previousTree: params.previousTree,
      previousGroupsToFetch: params.previousGroupsToFetch,
      previousTreeDepth: params.previousTreeDepths,
      defaultGroupingExpansionDepth: props.defaultGroupingExpansionDepth,
      isGroupExpandedByDefault: props.isGroupExpandedByDefault,
      groupingName: RowGroupingStrategy.DataSource
    });
  }, [apiRef, props.dataSource, props.defaultGroupingExpansionDepth, props.isGroupExpandedByDefault]);
  const filterRows = React14.useCallback(() => {
    const rowTree = gridRowTreeSelector(apiRef);
    return skipFiltering(rowTree);
  }, [apiRef]);
  const sortRows = React14.useCallback(() => {
    const rowTree = gridRowTreeSelector(apiRef);
    return skipSorting(rowTree);
  }, [apiRef]);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.DataSource, "rowTreeCreation", createRowTreeForRowGrouping);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.DataSource, "filtering", filterRows);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.DataSource, "sorting", sortRows);
  useGridRegisterStrategyProcessor(apiRef, RowGroupingStrategy.DataSource, "visibleRowsLookupCreation", getVisibleRowsLookup);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/export/useGridExcelExport.js
var React56 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/export/serializer/utils.js
var getExcelJs = async () => {
  const excelJsModule = await import("./exceljs.min-GQZ35XTL.js");
  return excelJsModule.default ?? excelJsModule;
};
var addColumnGroupingHeaders = (worksheet, columns, columnGroupPaths, columnGroupDetails) => {
  const maxDepth = Math.max(...columns.map(({
    key
  }) => {
    var _a;
    return ((_a = columnGroupPaths[key]) == null ? void 0 : _a.length) ?? 0;
  }));
  if (maxDepth === 0) {
    return;
  }
  for (let rowIndex = 0; rowIndex < maxDepth; rowIndex += 1) {
    const row = columns.map(({
      key
    }) => {
      const groupingPath = columnGroupPaths[key];
      if (groupingPath.length <= rowIndex) {
        return {
          groupId: null,
          parents: groupingPath
        };
      }
      return _extends({}, columnGroupDetails[groupingPath[rowIndex]], {
        parents: groupingPath.slice(0, rowIndex)
      });
    });
    const newRow = worksheet.addRow(row.map((group) => group.groupId === null ? null : (group == null ? void 0 : group.headerName) ?? group.groupId));
    const lastRowIndex = newRow.worksheet.rowCount;
    let leftIndex = 0;
    let rightIndex = 1;
    while (rightIndex < columns.length) {
      const {
        groupId: leftGroupId,
        parents: leftParents
      } = row[leftIndex];
      const {
        groupId: rightGroupId,
        parents: rightParents
      } = row[rightIndex];
      const areInSameGroup = leftGroupId === rightGroupId && leftParents.length === rightParents.length && leftParents.every((leftParent, index) => rightParents[index] === leftParent);
      if (areInSameGroup) {
        rightIndex += 1;
      } else {
        if (rightIndex - leftIndex > 1) {
          worksheet.mergeCells(lastRowIndex, leftIndex + 1, lastRowIndex, rightIndex);
        }
        leftIndex = rightIndex;
        rightIndex += 1;
      }
    }
    if (rightIndex - leftIndex > 1) {
      worksheet.mergeCells(lastRowIndex, leftIndex + 1, lastRowIndex, rightIndex);
    }
  }
};
function addSerializedRowToWorksheet(serializedRow, worksheet) {
  const {
    row,
    dataValidation,
    outlineLevel,
    mergedCells
  } = serializedRow;
  const newRow = worksheet.addRow(row);
  Object.keys(dataValidation).forEach((field) => {
    newRow.getCell(field).dataValidation = _extends({}, dataValidation[field]);
  });
  if (outlineLevel) {
    newRow.outlineLevel = outlineLevel;
  }
  const lastRowIndex = newRow.worksheet.rowCount;
  mergedCells.forEach((mergedCell) => {
    worksheet.mergeCells(lastRowIndex, mergedCell.leftIndex, lastRowIndex, mergedCell.rightIndex);
  });
}
async function createValueOptionsSheetIfNeeded(valueOptionsData, sheetName, workbook) {
  if (Object.keys(valueOptionsData).length === 0) {
    return;
  }
  const valueOptionsWorksheet = workbook.addWorksheet(sheetName);
  valueOptionsWorksheet.columns = Object.keys(valueOptionsData).map((key) => ({
    key
  }));
  Object.entries(valueOptionsData).forEach(([field, {
    values
  }]) => {
    valueOptionsWorksheet.getColumn(field).values = values;
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/export/serializer/excelSerializer.js
var getFormattedValueOptions = (colDef, row, valueOptions, api, callback) => {
  if (!colDef.valueOptions) {
    return;
  }
  const valueFormatter = colDef.valueFormatter;
  for (let i = 0; i < valueOptions.length; i += 1) {
    const option = valueOptions[i];
    let value;
    if (valueFormatter) {
      if (typeof option === "object") {
        value = option.label;
      } else {
        value = String(colDef.valueFormatter(option, row, colDef, {
          current: api
        }));
      }
    } else {
      value = typeof option === "object" ? option.label : option;
    }
    callback(value, i);
  }
};
var commaRegex = /,/g;
var commaReplacement = "CHAR(44)";
var serializeRowUnsafe = (id, columns, apiRef, defaultValueOptionsFormulae, options2) => {
  const serializedRow = {};
  const dataValidation = {};
  const mergedCells = [];
  const row = apiRef.current.getRow(id);
  const rowNode = apiRef.current.getRowNode(id);
  if (!row || !rowNode) {
    throw new Error(`No row with id #${id} found`);
  }
  const outlineLevel = rowNode.depth;
  const hasColSpan = gridHasColSpanSelector(apiRef);
  if (hasColSpan) {
    apiRef.current.calculateColSpan(id, 0, columns.length, columns);
  }
  columns.forEach((column, colIndex) => {
    const colSpanInfo = hasColSpan ? apiRef.current.unstable_getCellColSpanInfo(id, colIndex) : void 0;
    if (colSpanInfo && colSpanInfo.spannedByColSpan) {
      return;
    }
    if (colSpanInfo && colSpanInfo.cellProps.colSpan > 1) {
      mergedCells.push({
        leftIndex: colIndex + 1,
        rightIndex: colIndex + colSpanInfo.cellProps.colSpan
      });
    }
    let cellValue;
    switch (column.type) {
      case "singleSelect": {
        const castColumn = column;
        if (typeof castColumn.valueOptions === "function") {
          const valueOptions = castColumn.valueOptions({
            id,
            row,
            field: column.field
          });
          let formulae = '"';
          getFormattedValueOptions(castColumn, row, valueOptions, apiRef.current, (value, index) => {
            const formatted = value.toString().replace(commaRegex, commaReplacement);
            formulae += formatted;
            if (index < valueOptions.length - 1) {
              formulae += ",";
            }
          });
          formulae += '"';
          dataValidation[castColumn.field] = {
            type: "list",
            allowBlank: true,
            formulae: [formulae]
          };
        } else {
          const address = defaultValueOptionsFormulae[column.field].address;
          dataValidation[castColumn.field] = {
            type: "list",
            allowBlank: true,
            formulae: [address]
          };
        }
        const formattedValue = apiRef.current.getRowFormattedValue(row, castColumn);
        if (true) {
          if (String(formattedValue) === "[object Object]") {
            warnOnce(["MUI X: When the value of a field is an object or a `renderCell` is provided, the Excel export might not display the value correctly.", "You can provide a `valueFormatter` with a string representation to be used."]);
          }
        }
        if (isObject(formattedValue)) {
          serializedRow[castColumn.field] = formattedValue == null ? void 0 : formattedValue.label;
        } else {
          serializedRow[castColumn.field] = formattedValue;
        }
        break;
      }
      case "boolean":
      case "number":
        cellValue = apiRef.current.getRowValue(row, column);
        break;
      case "date":
      case "dateTime": {
        const value = apiRef.current.getRowValue(row, column);
        if (!value) {
          break;
        }
        const utcDate = new Date(Date.UTC(value.getFullYear(), value.getMonth(), value.getDate(), value.getHours(), value.getMinutes(), value.getSeconds()));
        serializedRow[column.field] = utcDate;
        break;
      }
      case "actions":
        break;
      default:
        cellValue = apiRef.current.getRowFormattedValue(row, column);
        if (true) {
          if (String(cellValue) === "[object Object]") {
            warnOnce(["MUI X: When the value of a field is an object or a `renderCell` is provided, the Excel export might not display the value correctly.", "You can provide a `valueFormatter` with a string representation to be used."]);
          }
        }
        break;
    }
    if (typeof cellValue === "string" && options2.escapeFormulas) {
      if (["=", "+", "-", "@", "	", "\r"].includes(cellValue[0])) {
        cellValue = `'${cellValue}`;
      }
    }
    if (typeof cellValue !== "undefined") {
      serializedRow[column.field] = cellValue;
    }
  });
  return {
    row: serializedRow,
    dataValidation,
    outlineLevel,
    mergedCells
  };
};
var defaultColumnsStyles = {
  [GRID_DATE_COL_DEF.type]: {
    numFmt: "dd.mm.yyyy"
  },
  [GRID_DATETIME_COL_DEF.type]: {
    numFmt: "dd.mm.yyyy hh:mm"
  }
};
var serializeColumn = (column, columnsStyles) => {
  const {
    field,
    type
  } = column;
  return {
    key: field,
    headerText: column.headerName ?? column.field,
    // Excel width must stay between 0 and 255 (https://support.microsoft.com/en-us/office/change-the-column-width-and-row-height-72f5e3cc-994d-43e8-ae58-9774a0905f46)
    // From the example of column width behavior (https://docs.microsoft.com/en-US/office/troubleshoot/excel/determine-column-widths#example-of-column-width-behavior)
    // a value of 10 corresponds to 75px. This is an approximation, because column width depends on the font-size
    width: Math.min(255, column.width ? column.width / 7.5 : 8.43),
    style: _extends({}, type && (defaultColumnsStyles == null ? void 0 : defaultColumnsStyles[type]), columnsStyles == null ? void 0 : columnsStyles[field])
  };
};
function serializeColumns(columns, styles) {
  return columns.map((column) => serializeColumn(column, styles));
}
async function getDataForValueOptionsSheet(columns, valueOptionsSheetName, api) {
  const excelJS = await getExcelJs();
  const workbook = new excelJS.Workbook();
  const worksheet = workbook.addWorksheet("Sheet1");
  const record = {};
  const worksheetColumns = [];
  for (let i = 0; i < columns.length; i += 1) {
    const column = columns[i];
    const isCandidateColumn = isSingleSelectColDef(column) && Array.isArray(column.valueOptions);
    if (!isCandidateColumn) {
      continue;
    }
    worksheetColumns.push({
      key: column.field
    });
    worksheet.columns = worksheetColumns;
    const header = column.headerName ?? column.field;
    const values = [header];
    getFormattedValueOptions(column, {}, column.valueOptions, api, (value) => {
      values.push(value);
    });
    const letter = worksheet.getColumn(column.field).letter;
    const address = `${valueOptionsSheetName}!$${letter}$2:$${letter}$${values.length}`;
    record[column.field] = {
      values,
      address
    };
  }
  return record;
}
async function buildExcel(options2, apiRef) {
  const {
    columns,
    rowIds,
    includeHeaders,
    includeColumnGroupsHeaders,
    valueOptionsSheetName = "Options",
    exceljsPreProcess,
    exceljsPostProcess,
    columnsStyles = {}
  } = options2;
  const excelJS = await getExcelJs();
  const workbook = new excelJS.Workbook();
  const worksheet = workbook.addWorksheet("Sheet1");
  const serializedColumns = serializeColumns(columns, columnsStyles);
  worksheet.columns = serializedColumns;
  if (exceljsPreProcess) {
    await exceljsPreProcess({
      workbook,
      worksheet
    });
  }
  if (includeColumnGroupsHeaders) {
    const columnGroupPaths = columns.reduce((acc, column) => {
      acc[column.field] = apiRef.current.getColumnGroupPath(column.field);
      return acc;
    }, {});
    addColumnGroupingHeaders(worksheet, serializedColumns, columnGroupPaths, apiRef.current.getAllGroupDetails());
  }
  if (includeHeaders) {
    worksheet.addRow(columns.map((column) => column.headerName ?? column.field));
  }
  const valueOptionsData = await getDataForValueOptionsSheet(columns, valueOptionsSheetName, apiRef.current);
  createValueOptionsSheetIfNeeded(valueOptionsData, valueOptionsSheetName, workbook);
  apiRef.current.resetColSpan();
  rowIds.forEach((id) => {
    const serializedRow = serializeRowUnsafe(id, columns, apiRef, valueOptionsData, options2);
    addSerializedRowToWorksheet(serializedRow, worksheet);
  });
  apiRef.current.resetColSpan();
  if (exceljsPostProcess) {
    await exceljsPostProcess({
      workbook,
      worksheet
    });
  }
  return workbook;
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridExcelExportMenuItem.js
var React15 = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var _excluded5 = ["hideMenu", "options"];
function GridExcelExportMenuItem(props) {
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps2();
  const {
    hideMenu,
    options: options2
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  return (0, import_jsx_runtime9.jsx)(rootProps.slots.baseMenuItem, _extends({
    onClick: () => {
      apiRef.current.exportDataAsExcel(options2);
      hideMenu == null ? void 0 : hideMenu();
    }
  }, other, {
    children: apiRef.current.getLocaleText("toolbarExportExcel")
  }));
}
true ? GridExcelExportMenuItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  hideMenu: import_prop_types.default.func,
  options: import_prop_types.default.shape({
    allColumns: import_prop_types.default.bool,
    columnsStyles: import_prop_types.default.object,
    disableToolbarButton: import_prop_types.default.bool,
    escapeFormulas: import_prop_types.default.bool,
    exceljsPostProcess: import_prop_types.default.func,
    exceljsPreProcess: import_prop_types.default.func,
    fields: import_prop_types.default.arrayOf(import_prop_types.default.string),
    fileName: import_prop_types.default.string,
    getRowsToExport: import_prop_types.default.func,
    includeColumnGroupsHeaders: import_prop_types.default.bool,
    includeHeaders: import_prop_types.default.bool,
    valueOptionsSheetName: import_prop_types.default.string,
    worker: import_prop_types.default.func
  })
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/material/icons.js
var React16 = __toESM(require_react(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var GridWorkspacesIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("g", {
  children: (0, import_jsx_runtime10.jsx)("path", {
    d: "M6,13c-2.2,0-4,1.8-4,4s1.8,4,4,4s4-1.8,4-4S8.2,13,6,13z M12,3C9.8,3,8,4.8,8,7s1.8,4,4,4s4-1.8,4-4S14.2,3,12,3z M18,13 c-2.2,0-4,1.8-4,4s1.8,4,4,4s4-1.8,4-4S20.2,13,18,13z"
  })
}), "Workspaces");
var GridGroupWorkIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM8 17.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5zM9.5 8c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5S9.5 9.38 9.5 8zm6.5 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
}), "GroupWork");
var GridFunctionsIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M18 4H6v2l6.5 6L6 18v2h12v-3h-7l5-5-5-5h7z"
}), "Functions");
var GridSendIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"
}), "Send");
var GridMicIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3m5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72z"
}), "Mic");
var GridMicOffIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28m-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18zM4.27 3 3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73z"
}), "MicOff");
var GridMoveToTopIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "m7.41 18.205 4.59-4.59 4.59 4.59 1.41-1.41-6-6-6 6 1.41 1.41ZM6 7.795v-2h12v2H6Z"
}), "MoveToTop");
var GridMoveToBottomIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M16.59 5.795 12 10.385l-4.59-4.59L6 7.205l6 6 6-6-1.41-1.41ZM18 16.205v2H6v-2h12Z"
}), "MoveToBottom");
var GridExpandLessIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "m12 8.295-6 6 1.41 1.41 4.59-4.58 4.59 4.58 1.41-1.41-6-6Z"
}), "ExpandLess");
var GridPivotIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M10 8h11V5c0-1.1-.9-2-2-2h-9zM3 8h5V3H5c-1.1 0-2 .9-2 2zm2 13h3V10H3v9c0 1.1.9 2 2 2m8 1-4-4 4-4zm1-9 4-4 4 4zm.58 6H13v-2h1.58c1.33 0 2.42-1.08 2.42-2.42V13h2v1.58c0 2.44-1.98 4.42-4.42 4.42"
}), "Pivot");
var GridAssistantIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M19 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h4l3 3 3-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-5.12 10.88L12 17l-1.88-4.12L6 11l4.12-1.88L12 5l1.88 4.12L18 11z"
}), "Assistant");
var GridPromptIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "m19 9 1.25-2.75L23 5l-2.75-1.25L19 1l-1.25 2.75L15 5l2.75 1.25zm-7.5.5L9 4 6.5 9.5 1 12l5.5 2.5L9 20l2.5-5.5L17 12zM19 15l-1.25 2.75L15 19l2.75 1.25L19 23l1.25-2.75L23 19l-2.75-1.25z"
}), "Prompt");
var GridRerunIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8"
}), "Rerun");
var GridHistoryIcon = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"
}), "History");

// node_modules/@mui/x-data-grid-premium/esm/components/GridColumnMenuAggregationItem.js
var React18 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useId/useId.js
var React17 = __toESM(require_react(), 1);
var globalId = 0;
function useGlobalId(idOverride) {
  const [defaultId, setDefaultId] = React17.useState(idOverride);
  const id = idOverride || defaultId;
  React17.useEffect(() => {
    if (defaultId == null) {
      globalId += 1;
      setDefaultId(`mui-${globalId}`);
    }
  }, [defaultId]);
  return id;
}
var safeReact = {
  ...React17
};
var maybeReactUseId = safeReact.useId;
function useId(idOverride) {
  if (maybeReactUseId !== void 0) {
    const reactId = maybeReactUseId();
    return idOverride ?? reactId;
  }
  return useGlobalId(idOverride);
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridColumnMenuAggregationItem.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var _excluded6 = ["native"];
function GridColumnMenuAggregationItem(props) {
  var _a, _b;
  const {
    colDef
  } = props;
  const apiRef = useGridApiContext2();
  const inputRef = React18.useRef(null);
  const rootProps = useGridRootProps2();
  const id = useId();
  const aggregationModel = useGridSelector(apiRef, gridAggregationModelSelector);
  const availableAggregationFunctions = React18.useMemo(() => getAvailableAggregationFunctions({
    aggregationFunctions: rootProps.aggregationFunctions,
    colDef,
    isDataSource: !!rootProps.dataSource
  }), [colDef, rootProps.aggregationFunctions, rootProps.dataSource]);
  const _ref = ((_a = rootProps.slotProps) == null ? void 0 : _a.baseSelect) || {}, {
    native: isBaseSelectNative = false
  } = _ref, baseSelectProps = _objectWithoutPropertiesLoose(_ref, _excluded6);
  const baseSelectOptionProps = ((_b = rootProps.slotProps) == null ? void 0 : _b.baseSelectOption) || {};
  const selectedAggregationRule = React18.useMemo(() => {
    if (!colDef || !aggregationModel[colDef.field]) {
      return "";
    }
    const aggregationFunctionName = aggregationModel[colDef.field];
    if (canColumnHaveAggregationFunction({
      colDef,
      aggregationFunctionName,
      aggregationFunction: rootProps.aggregationFunctions[aggregationFunctionName],
      isDataSource: !!rootProps.dataSource
    })) {
      return aggregationFunctionName;
    }
    return "";
  }, [rootProps.aggregationFunctions, rootProps.dataSource, aggregationModel, colDef]);
  const handleAggregationItemChange = (event) => {
    var _a2;
    const newAggregationItem = ((_a2 = event.target) == null ? void 0 : _a2.value) || void 0;
    const currentModel = gridAggregationModelSelector(apiRef);
    const _colDef$field = colDef.field, otherColumnItems = _objectWithoutPropertiesLoose(currentModel, [_colDef$field].map(toPropertyKey));
    const newModel = newAggregationItem == null ? otherColumnItems : _extends({}, otherColumnItems, {
      [colDef == null ? void 0 : colDef.field]: newAggregationItem
    });
    apiRef.current.setAggregationModel(newModel);
    apiRef.current.hideColumnMenu();
  };
  const label = apiRef.current.getLocaleText("aggregationMenuItemHeader");
  const handleMenuItemKeyDown = React18.useCallback((event) => {
    if (event.key === "Enter" || event.key === " ") {
      inputRef.current.focus();
    }
  }, []);
  const handleSelectKeyDown = React18.useCallback((event) => {
    if (event.key === "ArrowDown" || event.key === "ArrowUp" || event.key === " ") {
      event.stopPropagation();
    }
  }, []);
  return (0, import_jsx_runtime11.jsx)(rootProps.slots.baseMenuItem, {
    inert: true,
    iconStart: (0, import_jsx_runtime11.jsx)(rootProps.slots.columnMenuAggregationIcon, {
      fontSize: "small"
    }),
    onKeyDown: handleMenuItemKeyDown,
    children: (0, import_jsx_runtime11.jsxs)(rootProps.slots.baseSelect, _extends({
      labelId: `${id}-label`,
      id: `${id}-input`,
      value: selectedAggregationRule,
      label,
      onChange: handleAggregationItemChange,
      onKeyDown: handleSelectKeyDown,
      onBlur: (event) => event.stopPropagation(),
      native: isBaseSelectNative,
      fullWidth: true,
      size: "small",
      style: {
        minWidth: 150
      },
      slotProps: {
        htmlInput: {
          ref: inputRef
        }
      }
    }, baseSelectProps, {
      children: [(0, import_jsx_runtime11.jsx)(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {
        native: isBaseSelectNative,
        value: "",
        children: "..."
      })), availableAggregationFunctions.map((aggFunc) => (0, import_react.createElement)(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {
        key: aggFunc,
        value: aggFunc,
        native: isBaseSelectNative
      }), getAggregationFunctionLabel({
        apiRef,
        aggregationRule: {
          aggregationFunctionName: aggFunc,
          aggregationFunction: rootProps.aggregationFunctions[aggFunc]
        }
      })))]
    }))
  });
}
true ? GridColumnMenuAggregationItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  colDef: import_prop_types2.default.object.isRequired,
  onClick: import_prop_types2.default.func.isRequired
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptField.js
var React20 = __toESM(require_react(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptFieldContext.js
var React19 = __toESM(require_react(), 1);
var PromptFieldContext = React19.createContext(void 0);
if (true) PromptFieldContext.displayName = "PromptFieldContext";
function usePromptFieldContext() {
  const context = React19.useContext(PromptFieldContext);
  if (context === void 0) {
    throw new Error("MUI X: Missing context. Prompt Field subcomponents must be placed within a <PromptField /> component.");
  }
  return context;
}

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptField.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var _excluded7 = ["render", "className", "lang", "onRecordError", "onSubmit"];
var PromptField = forwardRef(function PromptField2(props, ref) {
  const {
    render,
    className,
    lang,
    onRecordError,
    onSubmit
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const [value, setValue] = React20.useState("");
  const [recording, setRecording] = React20.useState(false);
  const [disabled, setDisabled] = React20.useState(false);
  const state = React20.useMemo(() => ({
    value,
    recording,
    disabled
  }), [value, recording, disabled]);
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const handleOnSubmit = React20.useCallback(async (prompt) => {
    setDisabled(true);
    setValue("");
    await onSubmit(prompt);
    setDisabled(false);
  }, [onSubmit]);
  const contextValue = React20.useMemo(() => ({
    state,
    lang,
    onValueChange: setValue,
    onRecordingChange: setRecording,
    onSubmit: handleOnSubmit,
    onError: onRecordError
  }), [state, lang, onRecordError, handleOnSubmit]);
  const element = useComponentRenderer("div", render, _extends({
    className: resolvedClassName
  }, other, {
    ref
  }), state);
  return (0, import_jsx_runtime12.jsx)(PromptFieldContext.Provider, {
    value: contextValue,
    children: element
  });
});
if (true) PromptField.displayName = "PromptField";
true ? PromptField.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  className: import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.string]),
  /**
   * Called when an speech recognition error occurs.
   * @param {string} error The error message
   */
  onRecordError: import_prop_types3.default.func,
  /**
   * Called when the user submits the prompt.
   * @param {string} prompt The prompt
   */
  onSubmit: import_prop_types3.default.func.isRequired,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types3.default.oneOfType([import_prop_types3.default.element, import_prop_types3.default.func])
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptFieldControl.js
var React21 = __toESM(require_react(), 1);
var import_prop_types4 = __toESM(require_prop_types(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var _excluded8 = ["render", "className", "onChange", "onKeyDown"];
var PromptFieldControl = forwardRef(function PromptFieldControl2(props, ref) {
  var _a;
  const {
    render,
    className,
    onChange,
    onKeyDown
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded8);
  const rootProps = useGridRootProps2();
  const {
    state,
    onValueChange,
    onSubmit
  } = usePromptFieldContext();
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const handleChange = (event) => {
    onValueChange(event.target.value);
    onChange == null ? void 0 : onChange(event);
  };
  const handleKeyDown = (event) => {
    if (event.key === "Enter" && state.value.trim()) {
      onSubmit(state.value);
    }
    onKeyDown == null ? void 0 : onKeyDown(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseTextField, render, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseTextField, {
    value: state.value,
    className: resolvedClassName
  }, other, {
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    ref
  }), state);
  return (0, import_jsx_runtime13.jsx)(React21.Fragment, {
    children: element
  });
});
if (true) PromptFieldControl.displayName = "PromptFieldControl";
true ? PromptFieldControl.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  autoComplete: import_prop_types4.default.string,
  autoFocus: import_prop_types4.default.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  className: import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.string]),
  color: import_prop_types4.default.oneOf(["error", "primary"]),
  disabled: import_prop_types4.default.bool,
  error: import_prop_types4.default.bool,
  fullWidth: import_prop_types4.default.bool,
  helperText: import_prop_types4.default.string,
  id: import_prop_types4.default.string,
  inputRef: import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.shape({
    current: import_prop_types4.default.object
  })]),
  label: import_prop_types4.default.node,
  multiline: import_prop_types4.default.bool,
  placeholder: import_prop_types4.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types4.default.oneOfType([import_prop_types4.default.element, import_prop_types4.default.func]),
  role: import_prop_types4.default.string,
  size: import_prop_types4.default.oneOf(["medium", "small"]),
  slotProps: import_prop_types4.default.object,
  style: import_prop_types4.default.object,
  tabIndex: import_prop_types4.default.number,
  type: import_prop_types4.default.oneOfType([import_prop_types4.default.oneOf(["button", "checkbox", "color", "date", "datetime-local", "email", "file", "hidden", "image", "month", "number", "password", "radio", "range", "reset", "search", "submit", "tel", "text", "time", "url", "week"]), import_prop_types4.default.object]),
  value: import_prop_types4.default.string
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptFieldRecord.js
var React24 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js
var React22 = __toESM(require_react(), 1);
var UNINITIALIZED = {};
function useLazyRef(init, initArg) {
  const ref = React22.useRef(UNINITIALIZED);
  if (ref.current === UNINITIALIZED) {
    ref.current = init(initArg);
  }
  return ref;
}

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useOnMount/useOnMount.js
var React23 = __toESM(require_react(), 1);
var EMPTY = [];
function useOnMount2(fn) {
  React23.useEffect(fn, EMPTY);
}

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useTimeout/useTimeout.js
var Timeout = class _Timeout {
  constructor() {
    __publicField(this, "currentId", null);
    __publicField(this, "clear", () => {
      if (this.currentId !== null) {
        clearTimeout(this.currentId);
        this.currentId = null;
      }
    });
    __publicField(this, "disposeEffect", () => {
      return this.clear;
    });
  }
  static create() {
    return new _Timeout();
  }
  /**
   * Executes `fn` after `delay`, clearing any previously scheduled call.
   */
  start(delay, fn) {
    this.clear();
    this.currentId = setTimeout(() => {
      this.currentId = null;
      fn();
    }, delay);
  }
};

// node_modules/@mui/x-data-grid-premium/esm/utils/speechRecognition.js
var BrowserSpeechRecognition = globalThis.SpeechRecognition || globalThis.webkitSpeechRecognition;
var IS_SPEECH_RECOGNITION_SUPPORTED = !!BrowserSpeechRecognition;

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptFieldRecord.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var _excluded9 = ["render", "className", "onClick"];
var PromptFieldRecord = forwardRef(function PromptFieldRecord2(props, ref) {
  var _a;
  const {
    render,
    className,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded9);
  const rootProps = useGridRootProps2();
  const {
    state,
    lang,
    onRecordingChange,
    onValueChange,
    onSubmit,
    onError
  } = usePromptFieldContext();
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const recognition = useLazyRef(() => {
    if (!BrowserSpeechRecognition) {
      return {
        start: () => {
        },
        abort: () => {
        }
      };
    }
    const timeout = new Timeout();
    const instance = new BrowserSpeechRecognition();
    instance.continuous = true;
    instance.interimResults = true;
    instance.lang = lang;
    let finalResult = "";
    let interimResult = "";
    function start(options2) {
      if (state.recording) {
        return;
      }
      onRecordingChange(true);
      instance.onresult = (event) => {
        finalResult = "";
        interimResult = "";
        if (typeof event.results === "undefined") {
          instance.stop();
          return;
        }
        for (let i = event.resultIndex; i < event.results.length; i += 1) {
          if (event.results[i].isFinal) {
            finalResult += event.results[i][0].transcript;
          } else {
            interimResult += event.results[i][0].transcript;
          }
        }
        if (finalResult === "") {
          options2.onUpdate(interimResult);
        }
        timeout.start(1e3, () => instance.stop());
      };
      instance.onsoundend = () => {
        instance.stop();
      };
      instance.onend = () => {
        options2.onDone(finalResult);
        onRecordingChange(false);
      };
      instance.onerror = (error) => {
        options2.onError(error.message);
        instance.stop();
        onRecordingChange(false);
      };
      instance.start();
    }
    function abort() {
      instance.abort();
    }
    return {
      start,
      abort
    };
  }).current;
  const handleClick = (event) => {
    if (!state.recording) {
      recognition.start({
        onDone: onSubmit,
        onUpdate: onValueChange,
        onError: onError ?? (() => {
        })
      });
      return;
    }
    recognition.abort();
    onClick == null ? void 0 : onClick(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
    className: resolvedClassName,
    disabled: state.disabled
  }, other, {
    ref,
    onClick: handleClick
  }), state);
  return (0, import_jsx_runtime14.jsx)(React24.Fragment, {
    children: element
  });
});
if (true) PromptFieldRecord.displayName = "PromptFieldRecord";
true ? PromptFieldRecord.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  className: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.string]),
  color: import_prop_types5.default.oneOf(["default", "inherit", "primary"]),
  disabled: import_prop_types5.default.bool,
  edge: import_prop_types5.default.oneOf(["end", "start", false]),
  id: import_prop_types5.default.string,
  label: import_prop_types5.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types5.default.oneOfType([import_prop_types5.default.element, import_prop_types5.default.func]),
  role: import_prop_types5.default.string,
  size: import_prop_types5.default.oneOf(["large", "medium", "small"]),
  style: import_prop_types5.default.object,
  tabIndex: import_prop_types5.default.number,
  title: import_prop_types5.default.string,
  touchRippleRef: import_prop_types5.default.any
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/PromptFieldSend.js
var React25 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var _excluded10 = ["render", "className", "onClick"];
var PromptFieldSend = forwardRef(function PromptFieldSend2(props, ref) {
  var _a;
  const {
    render,
    className,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded10);
  const rootProps = useGridRootProps2();
  const {
    state,
    onSubmit
  } = usePromptFieldContext();
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const handleClick = (event) => {
    onSubmit(state.value);
    onClick == null ? void 0 : onClick(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
    className: resolvedClassName,
    disabled: state.disabled || state.recording || !state.value.trim()
  }, other, {
    onClick: handleClick,
    ref
  }), state);
  return (0, import_jsx_runtime15.jsx)(React25.Fragment, {
    children: element
  });
});
if (true) PromptFieldSend.displayName = "PromptFieldSend";
true ? PromptFieldSend.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  className: import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.string]),
  color: import_prop_types6.default.oneOf(["default", "inherit", "primary"]),
  disabled: import_prop_types6.default.bool,
  edge: import_prop_types6.default.oneOf(["end", "start", false]),
  id: import_prop_types6.default.string,
  label: import_prop_types6.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types6.default.oneOfType([import_prop_types6.default.element, import_prop_types6.default.func]),
  role: import_prop_types6.default.string,
  size: import_prop_types6.default.oneOf(["large", "medium", "small"]),
  style: import_prop_types6.default.object,
  tabIndex: import_prop_types6.default.number,
  title: import_prop_types6.default.string,
  touchRippleRef: import_prop_types6.default.any
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/GridPremiumColumnMenu.js
var React29 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/GridColumnMenuRowGroupItem.js
var React26 = __toESM(require_react(), 1);
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
function GridColumnMenuRowGroupItem(props) {
  const {
    colDef,
    onClick
  } = props;
  const apiRef = useGridApiContext2();
  const rowGroupingModel = useGridSelector(apiRef, gridRowGroupingSanitizedModelSelector);
  const columnsLookup = useGridSelector(apiRef, gridColumnLookupSelector);
  const rootProps = useGridRootProps2();
  const renderUnGroupingMenuItem = (field) => {
    const ungroupColumn = (event) => {
      apiRef.current.removeRowGroupingCriteria(field);
      onClick(event);
    };
    const groupedColumn = columnsLookup[field];
    const name = groupedColumn.headerName ?? field;
    return (0, import_jsx_runtime16.jsx)(rootProps.slots.baseMenuItem, {
      onClick: ungroupColumn,
      disabled: !groupedColumn.groupable,
      iconStart: (0, import_jsx_runtime16.jsx)(rootProps.slots.columnMenuUngroupIcon, {
        fontSize: "small"
      }),
      children: apiRef.current.getLocaleText("unGroupColumn")(name)
    }, field);
  };
  if (true) renderUnGroupingMenuItem.displayName = "renderUnGroupingMenuItem";
  if (!colDef || !isGroupingColumn(colDef.field)) {
    return null;
  }
  if (colDef.field === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD) {
    return (0, import_jsx_runtime16.jsx)(React26.Fragment, {
      children: rowGroupingModel.map(renderUnGroupingMenuItem)
    });
  }
  return renderUnGroupingMenuItem(getRowGroupingCriteriaFromGroupingField(colDef.field));
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridColumnMenuRowUngroupItem.js
var React27 = __toESM(require_react(), 1);
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
function GridColumnMenuRowUngroupItem(props) {
  const {
    colDef,
    onClick
  } = props;
  const apiRef = useGridApiContext2();
  const rowGroupingModel = useGridSelector(apiRef, gridRowGroupingSanitizedModelSelector);
  const columnsLookup = useGridSelector(apiRef, gridColumnLookupSelector);
  const rootProps = useGridRootProps2();
  if (!colDef.groupable) {
    return null;
  }
  const ungroupColumn = (event) => {
    apiRef.current.removeRowGroupingCriteria(colDef.field);
    onClick(event);
  };
  const groupColumn = (event) => {
    apiRef.current.addRowGroupingCriteria(colDef.field);
    onClick(event);
  };
  const name = columnsLookup[colDef.field].headerName ?? colDef.field;
  if (rowGroupingModel.includes(colDef.field)) {
    return (0, import_jsx_runtime17.jsx)(rootProps.slots.baseMenuItem, {
      onClick: ungroupColumn,
      iconStart: (0, import_jsx_runtime17.jsx)(rootProps.slots.columnMenuUngroupIcon, {
        fontSize: "small"
      }),
      children: apiRef.current.getLocaleText("unGroupColumn")(name)
    });
  }
  return (0, import_jsx_runtime17.jsx)(rootProps.slots.baseMenuItem, {
    onClick: groupColumn,
    iconStart: (0, import_jsx_runtime17.jsx)(rootProps.slots.columnMenuGroupIcon, {
      fontSize: "small"
    }),
    children: apiRef.current.getLocaleText("groupColumn")(name)
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridColumnMenuPivotItem.js
var React28 = __toESM(require_react(), 1);
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
function GridColumnMenuPivotItem(props) {
  const {
    onClick
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const isPivotPanelOpen = useGridSelector(apiRef, gridPivotPanelOpenSelector);
  const openPivotSettings = (event) => {
    onClick(event);
    apiRef.current.showSidebar(GridSidebarValue.Pivot);
  };
  return (0, import_jsx_runtime18.jsx)(rootProps.slots.baseMenuItem, {
    onClick: openPivotSettings,
    iconStart: (0, import_jsx_runtime18.jsx)(rootProps.slots.pivotIcon, {
      fontSize: "small"
    }),
    disabled: isPivotPanelOpen,
    children: apiRef.current.getLocaleText("columnMenuManagePivot")
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridPremiumColumnMenu.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
function GridColumnMenuGroupingItem(props) {
  const {
    colDef
  } = props;
  if (isGroupingColumn(colDef.field)) {
    return (0, import_jsx_runtime19.jsx)(GridColumnMenuRowGroupItem, _extends({}, props));
  }
  return (0, import_jsx_runtime19.jsx)(GridColumnMenuRowUngroupItem, _extends({}, props));
}
var GRID_COLUMN_MENU_SLOTS_PREMIUM = _extends({}, GRID_COLUMN_MENU_SLOTS_PRO, {
  columnMenuAggregationItem: GridColumnMenuAggregationItem,
  columnMenuGroupingItem: GridColumnMenuGroupingItem,
  columnMenuPivotItem: GridColumnMenuPivotItem
});
var GRID_COLUMN_MENU_SLOT_PROPS_PREMIUM = _extends({}, GRID_COLUMN_MENU_SLOT_PROPS_PRO, {
  columnMenuAggregationItem: {
    displayOrder: 23
  },
  columnMenuGroupingItem: {
    displayOrder: 27
  },
  columnMenuPivotItem: {
    displayOrder: 28
  }
});
var GridPremiumColumnMenu = forwardRef(function GridPremiumColumnMenuSimple(props, ref) {
  return (0, import_jsx_runtime19.jsx)(GridGenericColumnMenu, _extends({}, props, {
    defaultSlots: GRID_COLUMN_MENU_SLOTS_PREMIUM,
    defaultSlotProps: GRID_COLUMN_MENU_SLOT_PROPS_PREMIUM,
    ref
  }));
});
if (true) GridPremiumColumnMenu.displayName = "GridPremiumColumnMenu";

// node_modules/@mui/x-data-grid-premium/esm/components/export/ExportExcel.js
var React30 = __toESM(require_react(), 1);
var import_prop_types7 = __toESM(require_prop_types(), 1);
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
var _excluded11 = ["render", "options", "onClick"];
var ExportExcel = forwardRef(function ExportExcel2(props, ref) {
  var _a;
  const {
    render,
    options: options2,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded11);
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const handleClick = (event) => {
    apiRef.current.exportDataAsExcel(options2);
    onClick == null ? void 0 : onClick(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseButton, render, _extends({
    onClick: handleClick
  }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseButton, other, {
    ref
  }));
  return (0, import_jsx_runtime20.jsx)(React30.Fragment, {
    children: element
  });
});
if (true) ExportExcel.displayName = "ExportExcel";
true ? ExportExcel.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  className: import_prop_types7.default.string,
  disabled: import_prop_types7.default.bool,
  id: import_prop_types7.default.string,
  /**
   * The options to apply on the Excel export.
   * @demos
   *   - [Excel export](/x/react-data-grid/export/#excel-export)
   */
  options: import_prop_types7.default.shape({
    allColumns: import_prop_types7.default.bool,
    columnsStyles: import_prop_types7.default.object,
    escapeFormulas: import_prop_types7.default.bool,
    exceljsPostProcess: import_prop_types7.default.func,
    exceljsPreProcess: import_prop_types7.default.func,
    fields: import_prop_types7.default.arrayOf(import_prop_types7.default.string),
    fileName: import_prop_types7.default.string,
    getRowsToExport: import_prop_types7.default.func,
    includeColumnGroupsHeaders: import_prop_types7.default.bool,
    includeHeaders: import_prop_types7.default.bool,
    valueOptionsSheetName: import_prop_types7.default.string,
    worker: import_prop_types7.default.func
  }),
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types7.default.oneOfType([import_prop_types7.default.element, import_prop_types7.default.func]),
  role: import_prop_types7.default.string,
  size: import_prop_types7.default.oneOf(["large", "medium", "small"]),
  startIcon: import_prop_types7.default.node,
  style: import_prop_types7.default.object,
  tabIndex: import_prop_types7.default.number,
  title: import_prop_types7.default.string,
  touchRippleRef: import_prop_types7.default.any
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/GridEmptyPivotOverlay.js
var React31 = __toESM(require_react(), 1);
var import_prop_types8 = __toESM(require_prop_types(), 1);
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
var GridEmptyPivotOverlay = forwardRef(function GridEmptyPivotOverlay2(props, ref) {
  const apiRef = useGridApiContext2();
  return (0, import_jsx_runtime21.jsx)(GridOverlay, _extends({}, props, {
    ref,
    children: apiRef.current.getLocaleText("emptyPivotOverlayLabel")
  }));
});
if (true) GridEmptyPivotOverlay.displayName = "GridEmptyPivotOverlay";
true ? GridEmptyPivotOverlay.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  sx: import_prop_types8.default.oneOfType([import_prop_types8.default.arrayOf(import_prop_types8.default.oneOfType([import_prop_types8.default.func, import_prop_types8.default.object, import_prop_types8.default.bool])), import_prop_types8.default.func, import_prop_types8.default.object])
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/PivotPanelTrigger.js
var React32 = __toESM(require_react(), 1);
var import_prop_types9 = __toESM(require_prop_types(), 1);
var import_jsx_runtime22 = __toESM(require_jsx_runtime(), 1);
var _excluded12 = ["render", "className", "onClick", "onPointerUp"];
var PivotPanelTrigger = forwardRef(function PivotPanelTrigger2(props, ref) {
  var _a;
  const {
    render,
    className,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded12);
  const rootProps = useGridRootProps2();
  const buttonId = useId();
  const panelId = useId();
  const apiRef = useGridApiContext2();
  const open = useGridSelector(apiRef, gridPivotPanelOpenSelector);
  const active = useGridSelector(apiRef, gridPivotActiveSelector);
  const state = {
    open,
    active
  };
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const handleClick = (event) => {
    if (open) {
      apiRef.current.hideSidebar();
    } else {
      apiRef.current.showSidebar(GridSidebarValue.Pivot, panelId, buttonId);
    }
    onClick == null ? void 0 : onClick(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseButton, render, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseButton, {
    id: buttonId,
    // TODO: Hook up the panel/trigger IDs to the pivot panel
    "aria-haspopup": "true",
    "aria-expanded": open ? "true" : void 0,
    "aria-controls": open ? panelId : void 0,
    onClick: handleClick,
    className: resolvedClassName
  }, other, {
    ref
  }), state);
  return (0, import_jsx_runtime22.jsx)(React32.Fragment, {
    children: element
  });
});
if (true) PivotPanelTrigger.displayName = "PivotPanelTrigger";
true ? PivotPanelTrigger.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * A function to customize rendering of the component.
   */
  className: import_prop_types9.default.oneOfType([import_prop_types9.default.func, import_prop_types9.default.string]),
  disabled: import_prop_types9.default.bool,
  id: import_prop_types9.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types9.default.oneOfType([import_prop_types9.default.element, import_prop_types9.default.func]),
  role: import_prop_types9.default.string,
  size: import_prop_types9.default.oneOf(["large", "medium", "small"]),
  startIcon: import_prop_types9.default.node,
  style: import_prop_types9.default.object,
  tabIndex: import_prop_types9.default.number,
  title: import_prop_types9.default.string,
  touchRippleRef: import_prop_types9.default.any
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanel.js
var React48 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelHeader.js
var React40 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/sidebar/Sidebar.js
var React37 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/resizablePanel/ResizablePanel.js
var React34 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/resizablePanel/ResizablePanelContext.js
var React33 = __toESM(require_react(), 1);
var ResizablePanelContext = React33.createContext(void 0);
if (true) ResizablePanelContext.displayName = "ResizablePanelContext";
function useResizablePanelContext() {
  const context = React33.useContext(ResizablePanelContext);
  if (context === void 0) {
    throw new Error("MUI X: Missing context. ResizablePanel subcomponents must be placed within a <ResizablePanel /> component.");
  }
  return context;
}

// node_modules/@mui/x-data-grid-premium/esm/components/resizablePanel/ResizablePanel.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime(), 1);
var _excluded13 = ["className", "children", "direction"];
var useUtilityClasses5 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["resizablePanel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var ResizablePanelRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "ResizablePanel"
})({
  position: "relative"
});
function ResizablePanel(props) {
  const {
    className,
    children,
    direction = "horizontal"
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded13);
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses5(rootProps);
  const ref = React34.useRef(null);
  const contextValue = React34.useMemo(() => ({
    rootRef: ref,
    direction
  }), [direction]);
  return (0, import_jsx_runtime23.jsx)(ResizablePanelContext.Provider, {
    value: contextValue,
    children: (0, import_jsx_runtime23.jsx)(ResizablePanelRoot, _extends({
      className: clsx_default(classes.root, className),
      ownerState: rootProps
    }, other, {
      ref,
      children
    }))
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/resizablePanel/ResizablePanelHandle.js
var React36 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useResize.js
var React35 = __toESM(require_react(), 1);
var useResize = (options2) => {
  const resizeHandleRef = React35.useRef(null);
  const optionsRef = React35.useRef(options2);
  React35.useEffect(() => {
    optionsRef.current = options2;
  }, [options2]);
  React35.useEffect(() => {
    const handle = resizeHandleRef.current;
    if (!handle) {
      return void 0;
    }
    const {
      onSizeChange,
      getInitialSize,
      direction = "horizontal"
    } = optionsRef.current;
    let startPosition = null;
    let startSize = null;
    const handlePointerMove = (event) => {
      event.preventDefault();
      if (startPosition === null || startSize === null) {
        return;
      }
      const delta = direction === "horizontal" ? startPosition - event.clientX : startPosition - event.clientY;
      const newSize = startSize + delta;
      onSizeChange(newSize, handle);
    };
    const handlePointerUp = (event) => {
      startPosition = null;
      startSize = null;
      handle.removeEventListener("pointermove", handlePointerMove);
      handle.releasePointerCapture(event.pointerId);
    };
    const handlePointerDown = (event) => {
      startPosition = direction === "horizontal" ? event.clientX : event.clientY;
      startSize = getInitialSize(handle);
      handle.addEventListener("pointermove", handlePointerMove);
      handle.setPointerCapture(event.pointerId);
    };
    handle.addEventListener("pointerdown", handlePointerDown);
    handle.addEventListener("pointerup", handlePointerUp);
    return () => {
      handle.removeEventListener("pointerdown", handlePointerDown);
      handle.removeEventListener("pointerup", handlePointerUp);
      handle.removeEventListener("pointermove", handlePointerMove);
    };
  }, []);
  return {
    ref: resizeHandleRef
  };
};

// node_modules/@mui/x-data-grid-premium/esm/components/resizablePanel/ResizablePanelHandle.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime(), 1);
var _excluded14 = ["className", "children"];
var useUtilityClasses6 = (ownerState) => {
  const {
    classes,
    direction
  } = ownerState;
  const slots = {
    root: ["resizablePanelHandle", `resizablePanelHandle--${direction}`]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var ResizablePanelHandleRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "ResizablePanelHandle",
  overridesResolver: (props, styles) => [{
    [`&.${gridClasses["resizablePanelHandle--horizontal"]}`]: styles["resizablePanelHandle--horizontal"]
  }, {
    [`&.${gridClasses["resizablePanelHandle--vertical"]}`]: styles["resizablePanelHandle--vertical"]
  }, styles.resizablePanelHandle]
})({
  position: "absolute",
  zIndex: 3,
  top: 0,
  left: 0,
  userSelect: "none",
  transition: vars.transition(["border-color"], {
    duration: vars.transitions.duration.short,
    easing: vars.transitions.easing.easeInOut
  }),
  "&:hover": {
    borderWidth: 2,
    borderColor: vars.colors.interactive.selected
  },
  variants: [{
    props: {
      direction: "horizontal"
    },
    style: {
      height: "100%",
      width: 8,
      cursor: "ew-resize",
      borderLeft: `1px solid ${vars.colors.border.base}`,
      touchAction: "pan-x"
    }
  }, {
    props: {
      direction: "vertical"
    },
    style: {
      width: "100%",
      height: 8,
      cursor: "ns-resize",
      borderTop: `1px solid ${vars.colors.border.base}`,
      touchAction: "pan-y"
    }
  }]
});
function ResizablePanelHandle(props) {
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded14);
  const {
    rootRef,
    direction
  } = useResizablePanelContext();
  const rootProps = useGridRootProps2();
  const ownerState = {
    classes: rootProps.classes,
    direction
  };
  const classes = useUtilityClasses6(ownerState);
  const {
    ref
  } = useResize({
    direction,
    getInitialSize: () => {
      return direction === "horizontal" ? rootRef.current.offsetWidth : rootRef.current.offsetHeight;
    },
    onSizeChange: (newSize) => {
      if (direction === "horizontal") {
        rootRef.current.style.width = `${newSize}px`;
      } else {
        rootRef.current.style.height = `${newSize}px`;
      }
    }
  });
  return (0, import_jsx_runtime24.jsx)(ResizablePanelHandleRoot, _extends({
    className: clsx_default(classes.root, className),
    ownerState,
    direction
  }, other, {
    ref
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/sidebar/Sidebar.js
var import_jsx_runtime25 = __toESM(require_jsx_runtime(), 1);
var _excluded15 = ["className", "children"];
var useUtilityClasses7 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["sidebar"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var SidebarRoot = styled_default2(ResizablePanel, {
  name: "MuiDataGrid",
  slot: "Sidebar"
})({
  display: "flex",
  flexDirection: "column",
  width: 300,
  minWidth: 260,
  maxWidth: 400,
  overflow: "hidden"
});
function Sidebar(props) {
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded15);
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses7(rootProps);
  const {
    value,
    sidebarId,
    labelId
  } = useGridSelector(apiRef, gridSidebarContentSelector);
  if (!value) {
    return null;
  }
  const sidebarContent = apiRef.current.unstable_applyPipeProcessors("sidebar", null, value);
  if (!sidebarContent) {
    return null;
  }
  return (0, import_jsx_runtime25.jsxs)(SidebarRoot, _extends({
    id: sidebarId,
    className: clsx_default(className, classes.root),
    ownerState: rootProps,
    "aria-labelledby": labelId
  }, other, {
    children: [(0, import_jsx_runtime25.jsx)(ResizablePanelHandle, {}), sidebarContent]
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/sidebar/SidebarHeader.js
var React38 = __toESM(require_react(), 1);
var import_jsx_runtime26 = __toESM(require_jsx_runtime(), 1);
var _excluded16 = ["className", "children"];
var useUtilityClasses8 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["sidebarHeader"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var SidebarHeaderRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "SidebarHeader"
})({
  position: "sticky",
  top: 0,
  borderBottom: `1px solid ${vars.colors.border.base}`
});
function SidebarHeader(props) {
  const {
    className,
    children
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded16);
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses8(rootProps);
  return (0, import_jsx_runtime26.jsx)(SidebarHeaderRoot, _extends({
    className: clsx_default(className, classes.root),
    ownerState: rootProps
  }, other, {
    children
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelSearch.js
var React39 = __toESM(require_react(), 1);
var import_jsx_runtime27 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses9 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    container: ["pivotPanelSearchContainer"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var GridPivotPanelSearchContainer = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelSearchContainer"
})({
  padding: vars.spacing(0, 1, 1)
});
function GridPivotPanelSearch(props) {
  var _a;
  const {
    onClear,
    value,
    onChange
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const classes = useUtilityClasses9(rootProps);
  const handleKeyDown = (event) => {
    if (event.key === "Escape") {
      onClear();
    }
  };
  return (0, import_jsx_runtime27.jsx)(GridPivotPanelSearchContainer, {
    ownerState: rootProps,
    className: classes.container,
    children: (0, import_jsx_runtime27.jsx)(rootProps.slots.baseTextField, _extends({
      size: "small",
      "aria-label": apiRef.current.getLocaleText("pivotSearchControlLabel"),
      placeholder: apiRef.current.getLocaleText("pivotSearchControlPlaceholder"),
      onKeyDown: handleKeyDown,
      fullWidth: true,
      slotProps: {
        input: {
          startAdornment: (0, import_jsx_runtime27.jsx)(rootProps.slots.pivotSearchIcon, {
            fontSize: "small"
          }),
          endAdornment: value ? (0, import_jsx_runtime27.jsx)(rootProps.slots.baseIconButton, {
            edge: "end",
            size: "small",
            onClick: onClear,
            "aria-label": apiRef.current.getLocaleText("pivotSearchControlClear"),
            children: (0, import_jsx_runtime27.jsx)(rootProps.slots.pivotSearchClearIcon, {
              fontSize: "small"
            })
          }) : null
        }
      }
    }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseTextField, {
      value,
      onChange
    }))
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelHeader.js
var import_jsx_runtime28 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses10 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["pivotPanelHeader"],
    switch: ["pivotPanelSwitch"],
    label: ["pivotPanelSwitchLabel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var GridPivotPanelHeaderRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelHeader"
})({
  display: "flex",
  alignItems: "center",
  gap: vars.spacing(1),
  padding: vars.spacing(0, 0.75, 0, 1),
  boxSizing: "border-box",
  height: 52
});
var GridPivotPanelSwitch = styled_default2(NotRendered, {
  name: "MuiDataGrid",
  slot: "PivotPanelSwitch"
})({
  marginRight: "auto"
});
var GridPivotPanelSwitchLabel = styled_default2("span", {
  name: "MuiDataGrid",
  slot: "PivotPanelSwitchLabel"
})({
  fontWeight: vars.typography.fontWeight.medium
});
function GridPivotPanelHeader(props) {
  var _a, _b;
  const {
    searchValue,
    onSearchValueChange
  } = props;
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps2();
  const pivotActive = useGridSelector(apiRef, gridPivotActiveSelector);
  const classes = useUtilityClasses10(rootProps);
  const rows = useGridSelector(apiRef, gridRowCountSelector);
  const isEmptyPivot = pivotActive && rows === 0;
  return (0, import_jsx_runtime28.jsxs)(SidebarHeader, {
    children: [(0, import_jsx_runtime28.jsxs)(GridPivotPanelHeaderRoot, {
      ownerState: rootProps,
      className: classes.root,
      children: [(0, import_jsx_runtime28.jsx)(GridPivotPanelSwitch, _extends({
        as: rootProps.slots.baseSwitch,
        ownerState: rootProps,
        className: classes.switch,
        checked: pivotActive,
        onChange: (event) => apiRef.current.setPivotActive(event.target.checked),
        size: "small",
        label: (0, import_jsx_runtime28.jsx)(GridPivotPanelSwitchLabel, {
          ownerState: rootProps,
          className: classes.label,
          children: apiRef.current.getLocaleText("pivotToggleLabel")
        })
      }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseSwitch)), (0, import_jsx_runtime28.jsx)(rootProps.slots.baseIconButton, _extends({
        onClick: () => {
          apiRef.current.hideSidebar();
          if (isEmptyPivot) {
            apiRef.current.setPivotActive(false);
          }
        },
        "aria-label": apiRef.current.getLocaleText("pivotCloseButton")
      }, (_b = rootProps.slotProps) == null ? void 0 : _b.baseIconButton, {
        children: (0, import_jsx_runtime28.jsx)(rootProps.slots.sidebarCloseIcon, {
          fontSize: "small"
        })
      }))]
    }), (0, import_jsx_runtime28.jsx)(GridPivotPanelSearch, {
      value: searchValue,
      onClear: () => onSearchValueChange(""),
      onChange: (event) => onSearchValueChange(event.target.value)
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelBody.js
var React47 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelField.js
var React42 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelFieldMenu.js
var React41 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useGridPrivateApiContext.js
var useGridPrivateApiContext2 = useGridPrivateApiContext;

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelFieldMenu.js
var import_jsx_runtime29 = __toESM(require_jsx_runtime(), 1);
function GridPivotPanelFieldMenu(props) {
  var _a, _b;
  const {
    field,
    modelKey
  } = props;
  const rootProps = useGridRootProps2();
  const [open, setOpen] = React41.useState(false);
  const apiRef = useGridPrivateApiContext2();
  const isAvailableField = modelKey === null;
  const pivotModel = useGridSelector(apiRef, gridPivotModelSelector);
  const fieldIndexInModel = !isAvailableField ? pivotModel[modelKey].findIndex((item) => item.field === field) : -1;
  const modelLength = !isAvailableField ? pivotModel[modelKey].length : 0;
  const canMoveUp = fieldIndexInModel > 0;
  const canMoveDown = !isAvailableField && fieldIndexInModel < modelLength - 1;
  const menuId = useId();
  const triggerId = useId();
  const triggerRef = React41.useRef(null);
  const getMenuItems = React41.useCallback(() => {
    if (isAvailableField) {
      return [{
        key: "rows",
        label: apiRef.current.getLocaleText("pivotMenuAddToRows")
      }, {
        key: "columns",
        label: apiRef.current.getLocaleText("pivotMenuAddToColumns")
      }, {
        key: "values",
        label: apiRef.current.getLocaleText("pivotMenuAddToValues")
      }];
    }
    return [{
      key: "up",
      label: apiRef.current.getLocaleText("pivotMenuMoveUp"),
      icon: (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuMoveUpIcon, {}),
      disabled: !canMoveUp
    }, {
      key: "down",
      label: apiRef.current.getLocaleText("pivotMenuMoveDown"),
      icon: (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuMoveDownIcon, {}),
      disabled: !canMoveDown
    }, {
      divider: true
    }, {
      key: "top",
      label: apiRef.current.getLocaleText("pivotMenuMoveToTop"),
      icon: (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuMoveToTopIcon, {}),
      disabled: !canMoveUp
    }, {
      key: "bottom",
      label: apiRef.current.getLocaleText("pivotMenuMoveToBottom"),
      icon: (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuMoveToBottomIcon, {}),
      disabled: !canMoveDown
    }, {
      divider: true
    }, {
      key: "rows",
      label: apiRef.current.getLocaleText("pivotMenuRows"),
      icon: modelKey === "rows" ? (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuCheckIcon, {}) : (0, import_jsx_runtime29.jsx)("span", {})
    }, {
      key: "columns",
      label: apiRef.current.getLocaleText("pivotMenuColumns"),
      icon: modelKey === "columns" ? (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuCheckIcon, {}) : (0, import_jsx_runtime29.jsx)("span", {})
    }, {
      key: "values",
      label: apiRef.current.getLocaleText("pivotMenuValues"),
      icon: modelKey === "values" ? (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuCheckIcon, {}) : (0, import_jsx_runtime29.jsx)("span", {})
    }, {
      divider: true
    }, {
      key: null,
      label: apiRef.current.getLocaleText("pivotMenuRemove"),
      icon: (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuRemoveIcon, {})
    }];
  }, [isAvailableField, apiRef, rootProps, canMoveUp, canMoveDown, modelKey]);
  const handleClick = () => {
    setOpen(!open);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleMove = (to) => {
    handleClose();
    if (to === modelKey) {
      return;
    }
    let targetField;
    let targetFieldPosition = null;
    let targetSection = modelKey;
    switch (to) {
      case "up":
        targetField = pivotModel[modelKey][fieldIndexInModel - 1].field;
        targetFieldPosition = "top";
        break;
      case "down":
        targetField = pivotModel[modelKey][fieldIndexInModel + 1].field;
        targetFieldPosition = "bottom";
        break;
      case "top":
        targetField = pivotModel[modelKey][0].field;
        targetFieldPosition = "top";
        break;
      case "bottom":
        targetField = pivotModel[modelKey][modelLength - 1].field;
        targetFieldPosition = "bottom";
        break;
      case "rows":
      case "columns":
      case "values":
      case null:
        targetSection = to;
        break;
      default:
        break;
    }
    apiRef.current.updatePivotModel({
      field,
      targetField,
      targetFieldPosition,
      targetSection,
      originSection: modelKey
    });
  };
  return (0, import_jsx_runtime29.jsxs)(React41.Fragment, {
    children: [(0, import_jsx_runtime29.jsx)(rootProps.slots.baseIconButton, _extends({
      size: "small"
    }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
      id: triggerId,
      "aria-haspopup": "true",
      "aria-controls": open ? menuId : void 0,
      "aria-expanded": open ? "true" : void 0,
      "aria-label": apiRef.current.getLocaleText("pivotMenuOptions"),
      onClick: handleClick,
      ref: triggerRef,
      children: isAvailableField ? (0, import_jsx_runtime29.jsx)(rootProps.slots.pivotMenuAddIcon, {
        fontSize: "small"
      }) : (0, import_jsx_runtime29.jsx)(rootProps.slots.columnMenuIcon, {
        fontSize: "small"
      })
    })), (0, import_jsx_runtime29.jsx)(GridMenu, {
      target: triggerRef.current,
      open,
      onClose: handleClose,
      position: "bottom-start",
      children: (0, import_jsx_runtime29.jsx)(rootProps.slots.baseMenuList, _extends({
        id: menuId,
        "aria-labelledby": triggerId,
        autoFocusItem: true
      }, (_b = rootProps.slotProps) == null ? void 0 : _b.baseMenuList, {
        children: getMenuItems().map((item, index) => {
          var _a2;
          return "divider" in item ? (0, import_jsx_runtime29.jsx)(rootProps.slots.baseDivider, {}, `divider-${index}`) : (0, import_jsx_runtime29.jsx)(rootProps.slots.baseMenuItem, _extends({
            disabled: item.disabled,
            onClick: () => handleMove(item.key),
            iconStart: item.icon
          }, (_a2 = rootProps.slotProps) == null ? void 0 : _a2.baseMenuItem, {
            children: item.label
          }), item.key);
        })
      }))
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelField.js
var import_jsx_runtime30 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses11 = (ownerState) => {
  const {
    classes,
    modelKey
  } = ownerState;
  const sorted = modelKey === "columns" && ownerState.modelValue.sort;
  const slots = {
    root: ["pivotPanelField", sorted && "pivotPanelField--sorted"],
    name: ["pivotPanelFieldName"],
    actionContainer: ["pivotPanelFieldActionContainer"],
    dragIcon: ["pivotPanelFieldDragIcon"],
    checkbox: ["pivotPanelFieldCheckbox"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var GridPivotPanelFieldRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelField",
  overridesResolver: (props, styles) => [{
    [`&.${gridClasses["pivotPanelField--sorted"]}`]: styles["pivotPanelField--sorted"]
  }, styles.pivotPanelField]
})({
  flexShrink: 0,
  position: "relative",
  padding: vars.spacing(0, 1, 0, 2),
  height: 32,
  display: "flex",
  alignItems: "center",
  gap: vars.spacing(0.5),
  borderWidth: 0,
  borderTopWidth: 2,
  borderBottomWidth: 2,
  borderStyle: "solid",
  borderColor: "transparent",
  margin: "-1px 0",
  // collapse vertical borders
  cursor: "grab",
  variants: [{
    props: {
      dropPosition: "top"
    },
    style: {
      borderTopColor: vars.colors.interactive.selected
    }
  }, {
    props: {
      dropPosition: "bottom"
    },
    style: {
      borderBottomColor: vars.colors.interactive.selected
    }
  }, {
    props: {
      section: null
    },
    style: {
      borderTopColor: "transparent",
      borderBottomColor: "transparent"
    }
  }],
  "&:hover": {
    backgroundColor: vars.colors.interactive.hover
  }
});
var GridPivotPanelFieldName = styled_default2("span", {
  name: "MuiDataGrid",
  slot: "PivotPanelFieldName"
})({
  flex: 1,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap"
});
var GridPivotPanelFieldActionContainer = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelFieldActionContainer"
})({
  display: "flex",
  alignItems: "center"
});
var GridPivotPanelFieldDragIcon = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelFieldDragIcon"
})({
  position: "absolute",
  left: -1,
  width: 16,
  display: "flex",
  justifyContent: "center",
  color: vars.colors.foreground.base,
  opacity: 0,
  '[draggable="true"]:hover > &': {
    opacity: 0.3
  }
});
var GridPivotPanelFieldCheckbox = styled_default2(NotRendered, {
  name: "MuiDataGrid",
  slot: "PivotPanelFieldCheckbox"
})({
  flex: 1,
  position: "relative",
  margin: vars.spacing(0, 0, 0, -1),
  cursor: "grab"
});
function AggregationSelect({
  aggFunc,
  field
}) {
  var _a, _b;
  const rootProps = useGridRootProps2();
  const [aggregationMenuOpen, setAggregationMenuOpen] = React42.useState(false);
  const aggregationMenuTriggerRef = React42.useRef(null);
  const aggregationMenuTriggerId = useId();
  const aggregationMenuId = useId();
  const apiRef = useGridApiContext2();
  const initialColumns = useGridSelector(apiRef, gridPivotInitialColumnsSelector);
  const colDef = initialColumns.get(field);
  const availableAggregationFunctions = React42.useMemo(() => getAvailableAggregationFunctions({
    aggregationFunctions: rootProps.aggregationFunctions,
    colDef,
    isDataSource: false
  }), [colDef, rootProps.aggregationFunctions]);
  const handleClick = (func) => {
    apiRef.current.setPivotModel((prev) => {
      return _extends({}, prev, {
        values: prev.values.map((col) => {
          if (col.field === field) {
            return _extends({}, col, {
              aggFunc: func
            });
          }
          return col;
        })
      });
    });
    setAggregationMenuOpen(false);
  };
  return (0, import_jsx_runtime30.jsxs)(React42.Fragment, {
    children: [(0, import_jsx_runtime30.jsx)(rootProps.slots.baseChip, {
      label: ((_a = rootProps.aggregationFunctions[aggFunc]) == null ? void 0 : _a.label) ?? aggFunc,
      size: "small",
      variant: "outlined",
      ref: aggregationMenuTriggerRef,
      id: aggregationMenuTriggerId,
      "aria-haspopup": "true",
      "aria-controls": aggregationMenuOpen ? aggregationMenuId : void 0,
      "aria-expanded": aggregationMenuOpen ? "true" : void 0,
      onClick: () => setAggregationMenuOpen(!aggregationMenuOpen)
    }), (0, import_jsx_runtime30.jsx)(GridMenu, {
      open: aggregationMenuOpen,
      onClose: () => setAggregationMenuOpen(false),
      target: aggregationMenuTriggerRef.current,
      position: "bottom-start",
      children: (0, import_jsx_runtime30.jsx)(rootProps.slots.baseMenuList, _extends({
        id: aggregationMenuId,
        "aria-labelledby": aggregationMenuTriggerId,
        autoFocusItem: true
      }, (_b = rootProps.slotProps) == null ? void 0 : _b.baseMenuList, {
        children: availableAggregationFunctions.map((func) => {
          var _a2, _b2;
          return (0, import_jsx_runtime30.jsx)(rootProps.slots.baseMenuItem, _extends({
            selected: aggFunc === func,
            onClick: () => handleClick(func)
          }, (_a2 = rootProps.slotProps) == null ? void 0 : _a2.baseMenuItem, {
            children: ((_b2 = rootProps.aggregationFunctions[func]) == null ? void 0 : _b2.label) ?? func
          }), func);
        })
      }))
    })]
  });
}
function GridPivotPanelField(props) {
  var _a;
  const {
    children,
    field,
    onDragStart,
    onDragEnd
  } = props;
  const rootProps = useGridRootProps2();
  const [dropPosition, setDropPosition] = React42.useState(null);
  const section = props.modelKey;
  const ownerState = _extends({}, props, {
    classes: rootProps.classes,
    dropPosition,
    section
  });
  const classes = useUtilityClasses11(ownerState);
  const apiRef = useGridPrivateApiContext2();
  const handleDragStart = React42.useCallback((event) => {
    const data = {
      field,
      modelKey: section
    };
    event.dataTransfer.setData("text/plain", JSON.stringify(data));
    event.dataTransfer.dropEffect = "move";
    onDragStart(section);
  }, [field, onDragStart, section]);
  const getDropPosition = React42.useCallback((event) => {
    const rect = event.target.getBoundingClientRect();
    const y = event.clientY - rect.top;
    if (y < rect.height / 2) {
      return "top";
    }
    return "bottom";
  }, []);
  const handleDragOver = React42.useCallback((event) => {
    if (!event.currentTarget.contains(event.relatedTarget)) {
      setDropPosition(getDropPosition(event));
    }
  }, [getDropPosition]);
  const handleDragLeave = React42.useCallback((event) => {
    if (!event.currentTarget.contains(event.relatedTarget)) {
      setDropPosition(null);
    }
  }, []);
  const handleDrop = React42.useCallback((event) => {
    setDropPosition(null);
    if (!event.currentTarget.contains(event.relatedTarget)) {
      event.preventDefault();
      const position = getDropPosition(event);
      const {
        field: droppedField,
        modelKey: originSection
      } = JSON.parse(event.dataTransfer.getData("text/plain"));
      apiRef.current.updatePivotModel({
        field: droppedField,
        targetField: field,
        targetFieldPosition: position,
        originSection,
        targetSection: section
      });
    }
  }, [getDropPosition, apiRef, field, section]);
  const handleSort = () => {
    const currentSort = section === "columns" ? props.modelValue.sort : null;
    let newValue;
    if (currentSort === "asc") {
      newValue = "desc";
    } else if (currentSort === "desc") {
      newValue = void 0;
    } else {
      newValue = "asc";
    }
    apiRef.current.setPivotModel((prev) => {
      return _extends({}, prev, {
        columns: prev.columns.map((col) => {
          if (col.field === field) {
            return _extends({}, col, {
              sort: newValue
            });
          }
          return col;
        })
      });
    });
  };
  const handleVisibilityChange = (event) => {
    if (section) {
      apiRef.current.setPivotModel((prev) => {
        return _extends({}, prev, {
          [section]: prev[section].map((col) => {
            if (col.field === field) {
              return _extends({}, col, {
                hidden: !event.target.checked
              });
            }
            return col;
          })
        });
      });
    }
  };
  const hideable = section !== null;
  return (0, import_jsx_runtime30.jsxs)(GridPivotPanelFieldRoot, {
    ownerState,
    className: classes.root,
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop,
    onDragStart: handleDragStart,
    onDragEnd,
    draggable: "true",
    children: [(0, import_jsx_runtime30.jsx)(GridPivotPanelFieldDragIcon, {
      ownerState,
      className: classes.dragIcon,
      children: (0, import_jsx_runtime30.jsx)(rootProps.slots.columnReorderIcon, {
        fontSize: "small"
      })
    }), hideable ? (0, import_jsx_runtime30.jsx)(GridPivotPanelFieldCheckbox, _extends({
      ownerState,
      className: classes.checkbox,
      as: rootProps.slots.baseCheckbox,
      size: "small",
      density: "compact"
    }, (_a = rootProps.slotProps) == null ? void 0 : _a.baseCheckbox, {
      checked: !props.modelValue.hidden || false,
      onChange: handleVisibilityChange,
      label: children
    })) : (0, import_jsx_runtime30.jsx)(GridPivotPanelFieldName, {
      ownerState,
      className: classes.name,
      children
    }), (0, import_jsx_runtime30.jsxs)(GridPivotPanelFieldActionContainer, {
      ownerState,
      className: classes.actionContainer,
      children: [section === "columns" && (0, import_jsx_runtime30.jsx)(GridColumnSortButton, {
        field,
        direction: props.modelValue.sort,
        sortingOrder: rootProps.sortingOrder,
        onClick: handleSort
      }), section === "values" && (0, import_jsx_runtime30.jsx)(AggregationSelect, {
        aggFunc: props.modelValue.aggFunc,
        field
      }), (0, import_jsx_runtime30.jsx)(GridPivotPanelFieldMenu, {
        field,
        modelKey: section
      })]
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/collapsible/Collapsible.js
var React44 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/collapsible/CollapsibleContext.js
var React43 = __toESM(require_react(), 1);
var CollapsibleContext = React43.createContext(void 0);
if (true) CollapsibleContext.displayName = "CollapsibleContext";
function useCollapsibleContext() {
  const context = React43.useContext(CollapsibleContext);
  if (context === void 0) {
    throw new Error("MUI X: Missing context. Collapsible subcomponents must be placed within a <Collapsible /> component.");
  }
  return context;
}

// node_modules/@mui/x-data-grid-premium/esm/components/collapsible/Collapsible.js
var import_jsx_runtime31 = __toESM(require_jsx_runtime(), 1);
var _excluded17 = ["className", "children"];
var useUtilityClasses12 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["collapsible"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var CollapsibleRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "Collapsible"
})(({
  ownerState
}) => ({
  display: "flex",
  flexDirection: "column",
  flex: ownerState.open ? "1 0 auto" : "0 0 auto",
  borderRadius: vars.radius.base
}));
function Collapsible(props) {
  const {
    className,
    children
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded17);
  const [open, setOpen] = React44.useState(true);
  const panelId = useId();
  const rootProps = useGridRootProps2();
  const ownerState = {
    classes: rootProps.classes,
    open
  };
  const classes = useUtilityClasses12(ownerState);
  const contextValue = React44.useMemo(() => ({
    open,
    onOpenChange: setOpen,
    panelId
  }), [open, setOpen, panelId]);
  return (0, import_jsx_runtime31.jsx)(CollapsibleContext.Provider, {
    value: contextValue,
    children: (0, import_jsx_runtime31.jsx)(CollapsibleRoot, _extends({
      ownerState,
      className: clsx_default(classes.root, className)
    }, other, {
      children
    }))
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/collapsible/CollapsibleTrigger.js
var React45 = __toESM(require_react(), 1);
var import_jsx_runtime32 = __toESM(require_jsx_runtime(), 1);
var _excluded18 = ["children", "className"];
var useUtilityClasses13 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["collapsibleTrigger"],
    icon: ["collapsibleIcon"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var CollapsibleTriggerRoot = styled_default2("button", {
  name: "MuiDataGrid",
  slot: "CollapsibleTrigger"
})(({
  ownerState
}) => ({
  position: "relative",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  height: 40,
  padding: vars.spacing(0, 1.5),
  border: `1px solid ${vars.colors.border.base}`,
  background: "none",
  outline: "none",
  borderTopLeftRadius: vars.radius.base,
  borderTopRightRadius: vars.radius.base,
  borderBottomLeftRadius: ownerState.open ? 0 : vars.radius.base,
  borderBottomRightRadius: ownerState.open ? 0 : vars.radius.base,
  "&:hover": {
    backgroundColor: vars.colors.interactive.hover,
    cursor: "pointer"
  },
  "&:focus-visible": {
    outline: `2px solid ${vars.colors.interactive.selected}`,
    outlineOffset: -2
  }
}));
var CollapsibleIcon = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "CollapsibleIcon"
})(({
  ownerState
}) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: vars.colors.foreground.muted,
  transform: ownerState.open ? "none" : "rotate(180deg)",
  transition: vars.transition(["transform"], {
    duration: vars.transitions.duration.short,
    easing: vars.transitions.easing.easeInOut
  })
}));
function CollapsibleTrigger(props) {
  const {
    children,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded18);
  const rootProps = useGridRootProps2();
  const {
    open,
    onOpenChange,
    panelId
  } = useCollapsibleContext();
  const ownerState = {
    classes: rootProps.classes,
    open
  };
  const classes = useUtilityClasses13(ownerState);
  return (0, import_jsx_runtime32.jsxs)(CollapsibleTriggerRoot, _extends({
    ownerState,
    className: clsx_default(classes.root, className),
    tabIndex: 0,
    "aria-controls": open ? panelId : void 0,
    "aria-expanded": !open,
    onClick: () => onOpenChange(!open)
  }, other, {
    children: [children, (0, import_jsx_runtime32.jsx)(CollapsibleIcon, {
      ownerState,
      className: classes.icon,
      children: (0, import_jsx_runtime32.jsx)(rootProps.slots.collapsibleIcon, {})
    })]
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/collapsible/CollapsiblePanel.js
var React46 = __toESM(require_react(), 1);
var import_jsx_runtime33 = __toESM(require_jsx_runtime(), 1);
var _excluded19 = ["aria-label", "children", "className"];
var useUtilityClasses14 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["collapsiblePanel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var CollapsiblePanelRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "CollapsiblePanel"
})({
  border: `1px solid ${vars.colors.border.base}`,
  borderTop: "none",
  borderBottomLeftRadius: vars.radius.base,
  borderBottomRightRadius: vars.radius.base,
  flex: 1,
  overflow: "hidden"
});
function CollapsiblePanel(props) {
  const {
    children,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded19);
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses14(rootProps);
  const id = useId();
  const {
    open
  } = useCollapsibleContext();
  if (!open) {
    return null;
  }
  return (0, import_jsx_runtime33.jsx)(CollapsiblePanelRoot, _extends({
    ownerState: rootProps,
    className: clsx_default(classes.root, className),
    id
  }, other, {
    children
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanelBody.js
var import_jsx_runtime34 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses15 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["pivotPanelBody"],
    availableFields: ["pivotPanelAvailableFields"],
    sections: ["pivotPanelSections"],
    scrollArea: ["pivotPanelScrollArea"],
    section: ["pivotPanelSection"],
    sectionTitle: ["pivotPanelSectionTitle"],
    fieldList: ["pivotPanelFieldList"],
    placeholder: ["pivotPanelPlaceholder"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var GridPivotPanelBodyRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelBody"
})({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  overflow: "hidden"
});
var GridPivotPanelAvailableFields = styled_default2(GridShadowScrollArea, {
  name: "MuiDataGrid",
  slot: "PivotPanelAvailableFields"
})({
  flex: 1,
  minHeight: 84,
  transition: vars.transition(["background-color"], {
    duration: vars.transitions.duration.short,
    easing: vars.transitions.easing.easeInOut
  }),
  '&[data-drag-over="true"]': {
    backgroundColor: vars.colors.interactive.hover
  }
});
var GridPivotPanelSections = styled_default2(ResizablePanel, {
  name: "MuiDataGrid",
  slot: "PivotPanelSections"
})({
  position: "relative",
  minHeight: 158,
  overflow: "hidden",
  display: "flex",
  flexDirection: "column"
});
var GridPivotPanelScrollArea = styled_default2(GridShadowScrollArea, {
  name: "MuiDataGrid",
  slot: "PivotPanelScrollArea"
})({
  height: "100%"
});
var GridPivotPanelSection = styled_default2(Collapsible, {
  name: "MuiDataGrid",
  slot: "PivotPanelSection"
})({
  margin: vars.spacing(0.5, 1),
  transition: vars.transition(["border-color", "background-color"], {
    duration: vars.transitions.duration.short,
    easing: vars.transitions.easing.easeInOut
  }),
  '&[data-drag-over="true"]': {
    backgroundColor: vars.colors.interactive.hover,
    outline: `2px solid ${vars.colors.interactive.selected}`
  }
});
var GridPivotPanelSectionTitle = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelSectionTitle"
})({
  flex: 1,
  marginRight: vars.spacing(1.75),
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  gap: vars.spacing(1),
  font: vars.typography.font.body,
  fontWeight: vars.typography.fontWeight.medium
});
var GridPivotPanelFieldList = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelFieldList"
})({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  padding: vars.spacing(0.5, 0)
});
var GridPivotPanelPlaceholder = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PivotPanelPlaceholder"
})({
  flex: 1,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  textWrap: "balance",
  textAlign: "center",
  minHeight: 38,
  height: "100%",
  padding: vars.spacing(0, 1),
  color: vars.colors.foreground.muted,
  font: vars.typography.font.body
});
var INITIAL_DRAG_STATE = {
  active: false,
  dropZone: null,
  initialModelKey: null
};
function GridPivotPanelBody({
  searchValue
}) {
  const apiRef = useGridPrivateApiContext2();
  const initialColumns = useGridSelector(apiRef, gridPivotInitialColumnsSelector);
  const fields = React47.useMemo(() => Array.from(initialColumns.keys()), [initialColumns]);
  const rootProps = useGridRootProps2();
  const [drag, setDrag] = React47.useState(INITIAL_DRAG_STATE);
  const pivotModel = useGridSelector(apiRef, gridPivotModelSelector);
  const classes = useUtilityClasses15(rootProps);
  const getColumnName = React47.useCallback((field) => {
    const column = initialColumns.get(field);
    return (column == null ? void 0 : column.headerName) || field;
  }, [initialColumns]);
  const pivotModelFields = React47.useMemo(() => {
    const pivotModelArray = pivotModel.rows.concat(pivotModel.columns, pivotModel.values);
    return new Set(pivotModelArray.map((item) => item.field));
  }, [pivotModel]);
  const availableFields = React47.useMemo(() => {
    return fields.filter((field) => {
      var _a;
      if (pivotModelFields.has(field)) {
        return false;
      }
      if (((_a = initialColumns.get(field)) == null ? void 0 : _a.pivotable) === false) {
        return false;
      }
      if (searchValue) {
        const fieldName = getColumnName(field);
        return fieldName.toLowerCase().includes(searchValue.toLowerCase());
      }
      return true;
    });
  }, [searchValue, fields, getColumnName, pivotModelFields, initialColumns]);
  const handleDragStart = (modelKey) => {
    setDrag({
      active: true,
      initialModelKey: modelKey,
      dropZone: null
    });
  };
  const handleDragEnd = () => {
    setDrag(INITIAL_DRAG_STATE);
  };
  const handleDrop = (event) => {
    setDrag(INITIAL_DRAG_STATE);
    if (event.defaultPrevented) {
      return;
    }
    event.preventDefault();
    const {
      field,
      modelKey: originSection
    } = JSON.parse(event.dataTransfer.getData("text/plain"));
    const targetSection = event.currentTarget.getAttribute("data-section");
    if (originSection === targetSection) {
      return;
    }
    apiRef.current.updatePivotModel({
      field,
      targetSection,
      originSection
    });
  };
  const handleDragOver = React47.useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);
  const handleDragEnter = React47.useCallback((event) => {
    if (!event.currentTarget.contains(event.relatedTarget)) {
      const dropZone = event.currentTarget.getAttribute("data-section");
      setDrag((v) => _extends({}, v, {
        active: true,
        dropZone
      }));
    }
  }, []);
  const handleDragLeave = React47.useCallback((event) => {
    if (!event.currentTarget.contains(event.relatedTarget)) {
      setDrag((v) => _extends({}, v, {
        active: true,
        dropZone: v.initialModelKey
      }));
    }
  }, []);
  const rowsLabel = apiRef.current.getLocaleText("pivotRows");
  const columnsLabel = apiRef.current.getLocaleText("pivotColumns");
  const valuesLabel = apiRef.current.getLocaleText("pivotValues");
  return (0, import_jsx_runtime34.jsxs)(GridPivotPanelBodyRoot, {
    ownerState: rootProps,
    className: classes.root,
    "data-dragging": drag.active,
    onDragLeave: handleDragLeave,
    children: [(0, import_jsx_runtime34.jsxs)(GridPivotPanelAvailableFields, {
      ownerState: rootProps,
      className: classes.availableFields,
      onDrop: handleDrop,
      onDragEnter: handleDragEnter,
      onDragOver: handleDragOver,
      "data-section": null,
      "data-drag-over": drag.active && drag.dropZone === null,
      children: [availableFields.length === 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelPlaceholder, {
        ownerState: rootProps,
        className: classes.placeholder,
        children: apiRef.current.getLocaleText("pivotNoFields")
      }), availableFields.length > 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelFieldList, {
        ownerState: rootProps,
        className: classes.fieldList,
        children: availableFields.map((field) => (0, import_jsx_runtime34.jsx)(GridPivotPanelField, {
          field,
          modelKey: null,
          onDragStart: handleDragStart,
          onDragEnd: handleDragEnd,
          children: getColumnName(field)
        }, field))
      })]
    }), (0, import_jsx_runtime34.jsxs)(GridPivotPanelSections, {
      ownerState: rootProps,
      className: classes.sections,
      direction: "vertical",
      children: [(0, import_jsx_runtime34.jsx)(ResizablePanelHandle, {}), (0, import_jsx_runtime34.jsxs)(GridPivotPanelScrollArea, {
        ownerState: rootProps,
        className: classes.scrollArea,
        children: [(0, import_jsx_runtime34.jsxs)(GridPivotPanelSection, {
          ownerState: rootProps,
          className: classes.section,
          onDrop: handleDrop,
          onDragEnter: handleDragEnter,
          onDragOver: handleDragOver,
          "data-section": "rows",
          "data-drag-over": drag.dropZone === "rows",
          children: [(0, import_jsx_runtime34.jsx)(CollapsibleTrigger, {
            "aria-label": apiRef.current.getLocaleText("pivotRows"),
            children: (0, import_jsx_runtime34.jsxs)(GridPivotPanelSectionTitle, {
              ownerState: rootProps,
              className: classes.sectionTitle,
              children: [rowsLabel, pivotModel.rows.length > 0 && (0, import_jsx_runtime34.jsx)(rootProps.slots.baseBadge, {
                badgeContent: pivotModel.rows.length
              })]
            })
          }), (0, import_jsx_runtime34.jsxs)(CollapsiblePanel, {
            children: [pivotModel.rows.length === 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelPlaceholder, {
              ownerState: rootProps,
              className: classes.placeholder,
              children: apiRef.current.getLocaleText("pivotDragToRows")
            }), pivotModel.rows.length > 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelFieldList, {
              ownerState: rootProps,
              className: classes.fieldList,
              children: pivotModel.rows.map((modelValue) => (0, import_jsx_runtime34.jsx)(GridPivotPanelField, {
                field: modelValue.field,
                modelKey: "rows",
                modelValue,
                "data-field": modelValue.field,
                onDragStart: handleDragStart,
                onDragEnd: handleDragEnd,
                children: getColumnName(modelValue.field)
              }, modelValue.field))
            })]
          })]
        }), (0, import_jsx_runtime34.jsxs)(GridPivotPanelSection, {
          ownerState: rootProps,
          className: classes.section,
          onDrop: handleDrop,
          onDragEnter: handleDragEnter,
          onDragOver: handleDragOver,
          "data-section": "columns",
          "data-drag-over": drag.dropZone === "columns",
          children: [(0, import_jsx_runtime34.jsx)(CollapsibleTrigger, {
            "aria-label": apiRef.current.getLocaleText("pivotColumns"),
            children: (0, import_jsx_runtime34.jsxs)(GridPivotPanelSectionTitle, {
              ownerState: rootProps,
              className: classes.sectionTitle,
              children: [columnsLabel, pivotModel.columns.length > 0 && (0, import_jsx_runtime34.jsx)(rootProps.slots.baseBadge, {
                badgeContent: pivotModel.columns.length
              })]
            })
          }), (0, import_jsx_runtime34.jsxs)(CollapsiblePanel, {
            children: [pivotModel.columns.length === 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelPlaceholder, {
              ownerState: rootProps,
              className: classes.placeholder,
              children: apiRef.current.getLocaleText("pivotDragToColumns")
            }), pivotModel.columns.length > 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelFieldList, {
              ownerState: rootProps,
              className: classes.fieldList,
              children: pivotModel.columns.map((modelValue) => (0, import_jsx_runtime34.jsx)(GridPivotPanelField, {
                field: modelValue.field,
                modelKey: "columns",
                modelValue,
                onDragStart: handleDragStart,
                onDragEnd: handleDragEnd,
                children: getColumnName(modelValue.field)
              }, modelValue.field))
            })]
          })]
        }), (0, import_jsx_runtime34.jsxs)(GridPivotPanelSection, {
          ownerState: rootProps,
          className: classes.section,
          onDrop: handleDrop,
          onDragEnter: handleDragEnter,
          onDragOver: handleDragOver,
          "data-section": "values",
          "data-drag-over": drag.dropZone === "values",
          children: [(0, import_jsx_runtime34.jsx)(CollapsibleTrigger, {
            "aria-label": apiRef.current.getLocaleText("pivotValues"),
            children: (0, import_jsx_runtime34.jsxs)(GridPivotPanelSectionTitle, {
              ownerState: rootProps,
              className: classes.sectionTitle,
              children: [valuesLabel, pivotModel.values.length > 0 && (0, import_jsx_runtime34.jsx)(rootProps.slots.baseBadge, {
                badgeContent: pivotModel.values.length
              })]
            })
          }), (0, import_jsx_runtime34.jsxs)(CollapsiblePanel, {
            children: [pivotModel.values.length === 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelPlaceholder, {
              ownerState: rootProps,
              className: classes.placeholder,
              children: apiRef.current.getLocaleText("pivotDragToValues")
            }), pivotModel.values.length > 0 && (0, import_jsx_runtime34.jsx)(GridPivotPanelFieldList, {
              ownerState: rootProps,
              className: classes.fieldList,
              children: pivotModel.values.map((modelValue) => (0, import_jsx_runtime34.jsx)(GridPivotPanelField, {
                field: modelValue.field,
                modelKey: "values",
                modelValue,
                onDragStart: handleDragStart,
                onDragEnd: handleDragEnd,
                children: getColumnName(modelValue.field)
              }, modelValue.field))
            })]
          })]
        })]
      })]
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/pivotPanel/GridPivotPanel.js
var import_jsx_runtime35 = __toESM(require_jsx_runtime(), 1);
function GridPivotPanel() {
  const [searchValue, setSearchValue] = React48.useState("");
  return (0, import_jsx_runtime35.jsxs)(React48.Fragment, {
    children: [(0, import_jsx_runtime35.jsx)(GridPivotPanelHeader, {
      searchValue,
      onSearchValueChange: setSearchValue
    }), (0, import_jsx_runtime35.jsx)(GridPivotPanelBody, {
      searchValue
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/AiAssistantPanelTrigger.js
var React49 = __toESM(require_react(), 1);
var import_prop_types10 = __toESM(require_prop_types(), 1);
var import_jsx_runtime36 = __toESM(require_jsx_runtime(), 1);
var _excluded20 = ["render", "className", "onClick", "onPointerUp"];
var AiAssistantPanelTrigger = forwardRef(function AiAssistantPanelTrigger2(props, ref) {
  var _a;
  const {
    render,
    className,
    onClick,
    onPointerUp
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded20);
  const rootProps = useGridRootProps2();
  const buttonId = useId();
  const panelId = useId();
  const apiRef = useGridApiContext2();
  const panelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);
  const open = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.aiAssistant;
  const state = {
    open
  };
  const resolvedClassName = typeof className === "function" ? className(state) : className;
  const {
    aiAssistantPanelTriggerRef
  } = useGridPanelContext();
  const handleRef = useForkRef_default(ref, aiAssistantPanelTriggerRef);
  const handleClick = (event) => {
    if (open) {
      apiRef.current.hidePreferences();
    } else {
      apiRef.current.showPreferences(GridPreferencePanelsValue.aiAssistant, panelId, buttonId);
    }
    onClick == null ? void 0 : onClick(event);
  };
  const handlePointerUp = (event) => {
    if (open) {
      event.stopPropagation();
    }
    onPointerUp == null ? void 0 : onPointerUp(event);
  };
  const element = useComponentRenderer(rootProps.slots.baseButton, render, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseButton, {
    id: buttonId,
    "aria-haspopup": "true",
    "aria-expanded": open ? "true" : void 0,
    "aria-controls": open ? panelId : void 0,
    className: resolvedClassName
  }, other, {
    onClick: handleClick,
    onPointerUp: handlePointerUp,
    ref: handleRef
  }), state);
  return (0, import_jsx_runtime36.jsx)(React49.Fragment, {
    children: element
  });
});
if (true) AiAssistantPanelTrigger.displayName = "AiAssistantPanelTrigger";
true ? AiAssistantPanelTrigger.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * A function to customize rendering of the component.
   */
  className: import_prop_types10.default.oneOfType([import_prop_types10.default.func, import_prop_types10.default.string]),
  disabled: import_prop_types10.default.bool,
  id: import_prop_types10.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types10.default.oneOfType([import_prop_types10.default.element, import_prop_types10.default.func]),
  role: import_prop_types10.default.string,
  size: import_prop_types10.default.oneOf(["large", "medium", "small"]),
  startIcon: import_prop_types10.default.node,
  style: import_prop_types10.default.object,
  tabIndex: import_prop_types10.default.number,
  title: import_prop_types10.default.string,
  touchRippleRef: import_prop_types10.default.any
} : void 0;

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanel.js
var React55 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aiAssistant/gridAiAssistantSelectors.js
var gridAiAssistantStateSelector = createRootSelector((state) => state.aiAssistant);
var gridAiAssistantActiveConversationIndexSelector = createSelector(gridAiAssistantStateSelector, (aiAssistant) => aiAssistant == null ? void 0 : aiAssistant.activeConversationIndex);
var gridAiAssistantConversationsSelector = createSelector(gridAiAssistantStateSelector, (aiAssistant) => aiAssistant == null ? void 0 : aiAssistant.conversations);
var gridAiAssistantActiveConversationSelector = createSelectorMemoized(gridAiAssistantConversationsSelector, gridAiAssistantActiveConversationIndexSelector, (conversations, index) => conversations[index]);

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanelConversation.js
var React51 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/prompt/GridPrompt.js
var React50 = __toESM(require_react(), 1);
var import_jsx_runtime37 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses16 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["prompt"],
    iconContainer: ["promptIconContainer"],
    icon: ["promptIcon"],
    text: ["promptText"],
    content: ["promptContent"],
    action: ["promptAction"],
    feedback: ["promptFeedback"],
    changeList: ["promptChangeList"],
    changesToggle: ["promptChangesToggle"],
    changesToggleIcon: ["promptChangesToggleIcon"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var fadeIn = keyframes({
  from: {
    opacity: 0
  },
  to: {
    opacity: 1
  }
});
var fadeInUp = keyframes({
  from: {
    opacity: 0,
    transform: "translateY(5px)"
  },
  to: {
    opacity: 1,
    transform: "translateY(0)"
  }
});
var PromptItem = styled_default2("li", {
  name: "MuiDataGrid",
  slot: "Prompt"
})`
  display: flex;
  padding: ${vars.spacing(1, 1.25)};
  align-items: flex-start;
  overflow: hidden;
  .${gridClasses.promptAction} {
    opacity: 0;
    transition: ${vars.transition(["opacity"], {
  duration: vars.transitions.duration.short
})};
  }
  &:hover .${gridClasses.promptAction}, & .${gridClasses.promptAction}:focus-visible {
    opacity: 1;
  }
  @media (prefers-reduced-motion: no-preference) {
    animation: ${fadeInUp} ${vars.transitions.duration.long} ${vars.transitions.easing.easeInOut};
  }
`;
var PromptContent = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PromptContent"
})({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  overflow: "hidden"
});
var PromptText = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PromptText"
})({
  font: vars.typography.font.body
});
var PromptIconContainer = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PromptIconContainer"
})({
  flexShrink: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: 36,
  height: 36,
  marginRight: vars.spacing(1.5)
});
var PromptIcon = styled_default2("svg", {
  name: "MuiDataGrid",
  slot: "PromptIcon"
})`
  color: ${({
  ownerState
}) => ownerState.variant === "error" ? vars.colors.foreground.error : vars.colors.foreground.muted};
  @media (prefers-reduced-motion: no-preference) {
    animation: ${fadeIn} ${vars.transitions.duration.short} ${vars.transitions.easing.easeInOut};
  }
`;
var PromptFeedback = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PromptFeedback"
})({
  font: vars.typography.font.small,
  color: vars.colors.foreground.muted,
  variants: [{
    props: {
      variant: "error"
    },
    style: {
      color: vars.colors.foreground.error
    }
  }]
});
var PromptChangeList = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "PromptChangeList"
})({
  display: "flex",
  flexWrap: "wrap",
  gap: vars.spacing(0.5),
  width: "100%",
  marginTop: vars.spacing(1),
  overflow: "hidden"
});
var PromptChangesToggle = styled_default2("button", {
  name: "MuiDataGrid",
  slot: "PromptChangesToggle"
})({
  display: "flex",
  alignItems: "center",
  gap: vars.spacing(0.25),
  padding: 0,
  font: vars.typography.font.small,
  color: vars.colors.foreground.accent,
  fontWeight: vars.typography.fontWeight.medium,
  cursor: "pointer",
  border: "none",
  background: "none",
  outline: "none",
  "&:hover, &:focus-visible": {
    textDecoration: "underline"
  }
});
var PromptChangesToggleIcon = styled_default2("svg", {
  name: "MuiDataGrid",
  slot: "PromptChangesToggleIcon"
})({
  variants: [{
    props: {
      showChanges: true
    },
    style: {
      transform: "rotate(180deg)"
    }
  }]
});
function GridPrompt(props) {
  const {
    value,
    response,
    helperText,
    variant,
    onRerun
  } = props;
  const rootProps = useGridRootProps2();
  const [showChanges, setShowChanges] = React50.useState(false);
  const ownerState = {
    classes: rootProps.classes,
    variant,
    showChanges
  };
  const classes = useUtilityClasses16(ownerState);
  const apiRef = useGridApiContext2();
  const columns = useGridSelector(apiRef, gridColumnLookupSelector);
  const changesListId = useId();
  const getColumnName = React50.useCallback((column) => {
    var _a;
    return ((_a = columns[column]) == null ? void 0 : _a.headerName) ?? column;
  }, [columns]);
  const getGroupingChanges = React50.useCallback((grouping) => {
    return grouping.map((group) => ({
      label: getColumnName(group.column),
      description: apiRef.current.getLocaleText("promptChangeGroupDescription")(getColumnName(group.column)),
      icon: rootProps.slots.promptGroupIcon
    }));
  }, [apiRef, getColumnName, rootProps.slots.promptGroupIcon]);
  const getAggregationChanges = React50.useCallback((aggregation) => {
    return Object.keys(aggregation).map((column) => ({
      label: apiRef.current.getLocaleText("promptChangeAggregationLabel")(getColumnName(column), aggregation[column]),
      description: apiRef.current.getLocaleText("promptChangeAggregationDescription")(getColumnName(column), aggregation[column]),
      icon: rootProps.slots.promptAggregationIcon
    }));
  }, [apiRef, getColumnName, rootProps.slots.promptAggregationIcon]);
  const getFilterChanges = React50.useCallback((filters) => {
    return filters.map((filter) => {
      const filterOperator = apiRef.current.getLocaleText(`filterOperator${capitalize(filter.operator)}`);
      let filterValue = filter.value;
      if (isSingleSelectColDef(columns[filter.column])) {
        const allOptions = getValueOptions(columns[filter.column]) ?? [];
        const colDef = columns[filter.column];
        const getOptionLabel = colDef.getOptionLabel ?? ((option) => typeof option === "object" ? option.label : String(option));
        const getOptionValue = colDef.getOptionValue ?? ((option) => typeof option === "object" ? option.value : option);
        if (Array.isArray(filterValue)) {
          filterValue = filterValue.map((filterVal) => {
            const option = allOptions.find((opt) => String(getOptionValue(opt)) === String(filterVal));
            return option ? getOptionLabel(option) : String(filterVal);
          }).join(", ");
        } else {
          const option = allOptions.find((opt) => String(getOptionValue(opt)) === String(filterValue));
          filterValue = option ? getOptionLabel(option) : String(filterValue);
        }
      }
      return {
        label: apiRef.current.getLocaleText("promptChangeFilterLabel")(getColumnName(filter.column), filterOperator, filterValue),
        description: apiRef.current.getLocaleText("promptChangeFilterDescription")(getColumnName(filter.column), filterOperator, filterValue),
        icon: rootProps.slots.promptFilterIcon
      };
    });
  }, [apiRef, columns, getColumnName, rootProps.slots.promptFilterIcon]);
  const getSortingChanges = React50.useCallback((sorting) => {
    return sorting.map((sort) => ({
      label: getColumnName(sort.column),
      description: apiRef.current.getLocaleText("promptChangeSortDescription")(getColumnName(sort.column), sort.direction),
      icon: sort.direction === "asc" ? rootProps.slots.promptSortAscIcon : rootProps.slots.promptSortDescIcon
    }));
  }, [apiRef, getColumnName, rootProps.slots.promptSortAscIcon, rootProps.slots.promptSortDescIcon]);
  const getPivotingChanges = React50.useCallback((pivoting) => {
    if (!("columns" in pivoting)) {
      return [];
    }
    const changes = [{
      label: apiRef.current.getLocaleText("promptChangePivotEnableLabel"),
      icon: rootProps.slots.promptPivotIcon,
      description: apiRef.current.getLocaleText("promptChangePivotEnableDescription")
    }];
    if (pivoting.columns.length) {
      changes.push({
        label: apiRef.current.getLocaleText("promptChangePivotColumnsLabel")(pivoting.columns.length),
        icon: rootProps.slots.columnMenuManageColumnsIcon,
        description: pivoting.columns.map((column) => apiRef.current.getLocaleText("promptChangePivotColumnsDescription")(getColumnName(column.column), column.direction)).join(`, `)
      });
    }
    if (pivoting.rows.length) {
      changes.push({
        label: apiRef.current.getLocaleText("promptChangePivotRowsLabel")(pivoting.rows.length),
        icon: rootProps.slots.densityStandardIcon,
        description: pivoting.rows.map((column) => getColumnName(column)).join(`, `)
      });
    }
    if (pivoting.values.length) {
      changes.push({
        label: apiRef.current.getLocaleText("promptChangePivotValuesLabel")(pivoting.values.length),
        icon: rootProps.slots.promptAggregationIcon,
        description: pivoting.values.map((aggregation) => Object.keys(aggregation).map((column) => apiRef.current.getLocaleText("promptChangePivotValuesDescription")(getColumnName(column), aggregation[column]))).join(`, `)
      });
    }
    return changes;
  }, [apiRef, getColumnName, rootProps.slots]);
  const changeList = React50.useMemo(() => {
    if (!response) {
      return [];
    }
    const changes = [];
    if (response.grouping.length) {
      changes.push(...getGroupingChanges(response.grouping));
    }
    if (response.aggregation && Object.keys(response.aggregation).length) {
      changes.push(...getAggregationChanges(response.aggregation));
    }
    if (response.filters.length) {
      changes.push(...getFilterChanges(response.filters));
    }
    if (response.sorting.length) {
      changes.push(...getSortingChanges(response.sorting));
    }
    if (response.pivoting && "columns" in response.pivoting) {
      changes.push(...getPivotingChanges(response.pivoting));
    }
    return changes;
  }, [response, getGroupingChanges, getAggregationChanges, getFilterChanges, getSortingChanges, getPivotingChanges]);
  return (0, import_jsx_runtime37.jsxs)(PromptItem, {
    ownerState,
    className: classes.root,
    children: [(0, import_jsx_runtime37.jsx)(PromptIconContainer, {
      ownerState,
      className: classes.iconContainer,
      children: !response && variant !== "error" ? (0, import_jsx_runtime37.jsx)(rootProps.slots.baseCircularProgress, {
        size: 20
      }) : (0, import_jsx_runtime37.jsx)(PromptIcon, {
        as: rootProps.slots.promptIcon,
        ownerState,
        className: classes.icon,
        fontSize: "small"
      })
    }), (0, import_jsx_runtime37.jsxs)(PromptContent, {
      ownerState,
      className: classes.content,
      children: [(0, import_jsx_runtime37.jsx)(PromptText, {
        ownerState,
        className: classes.text,
        children: value
      }), (0, import_jsx_runtime37.jsx)(PromptFeedback, {
        ownerState,
        className: classes.feedback,
        children: helperText
      }), changeList.length > 0 ? (0, import_jsx_runtime37.jsxs)(React50.Fragment, {
        children: [(0, import_jsx_runtime37.jsxs)(PromptChangesToggle, {
          ownerState,
          className: classes.changesToggle,
          "aria-expanded": showChanges,
          "aria-controls": changesListId,
          onClick: () => setShowChanges(!showChanges),
          children: [apiRef.current.getLocaleText("promptAppliedChanges"), (0, import_jsx_runtime37.jsx)(PromptChangesToggleIcon, {
            as: rootProps.slots.promptChangesToggleIcon,
            ownerState,
            fontSize: "small"
          })]
        }), showChanges && (0, import_jsx_runtime37.jsx)(PromptChangeList, {
          id: changesListId,
          ownerState,
          className: classes.changeList,
          children: changeList.map((change) => (0, import_jsx_runtime37.jsx)(rootProps.slots.baseTooltip, {
            title: change.description,
            children: (0, import_jsx_runtime37.jsx)(rootProps.slots.baseChip, {
              label: change.label,
              icon: (0, import_jsx_runtime37.jsx)(change.icon, {}),
              size: "small"
            })
          }, change.label))
        })]
      }) : null]
    }), (0, import_jsx_runtime37.jsx)(rootProps.slots.baseTooltip, {
      title: apiRef.current.getLocaleText("promptRerun"),
      enterDelay: 500,
      children: (0, import_jsx_runtime37.jsx)(rootProps.slots.baseIconButton, {
        size: "small",
        className: classes.action,
        onClick: onRerun,
        children: (0, import_jsx_runtime37.jsx)(rootProps.slots.promptRerunIcon, {
          fontSize: "small"
        })
      })
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanelConversation.js
var import_jsx_runtime38 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses17 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["aiAssistantPanelConversation"],
    list: ["aiAssistantPanelConversationList"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var AiAssistantPanelConversationRoot = styled_default2(GridShadowScrollArea, {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelConversation"
})({
  flexShrink: 0,
  height: "100%"
});
var AiAssistantPanelConversationList = styled_default2("ol", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelConversationList"
})({
  flex: 1,
  padding: 0,
  margin: 0
});
function GridAiAssistantPanelConversation(props) {
  const {
    conversation
  } = props;
  const rootProps = useGridRootProps2();
  const classes = useUtilityClasses17(rootProps);
  const ref = React51.useRef(null);
  const apiRef = useGridApiContext2();
  React51.useEffect(() => {
    var _a, _b;
    (_b = ref.current) == null ? void 0 : _b.scrollTo({
      top: (_a = ref.current) == null ? void 0 : _a.scrollHeight,
      behavior: "smooth"
    });
  }, [conversation]);
  return (0, import_jsx_runtime38.jsx)(AiAssistantPanelConversationRoot, {
    className: classes.root,
    ownerState: rootProps,
    ref,
    children: (0, import_jsx_runtime38.jsx)(AiAssistantPanelConversationList, {
      className: classes.list,
      ownerState: rootProps,
      children: conversation.prompts.map((item) => (0, import_jsx_runtime38.jsx)(GridPrompt, _extends({}, item, {
        onRerun: () => apiRef.current.aiAssistant.processPrompt(item.value)
      }), item.createdAt.toISOString()))
    })
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/promptField/GridPromptField.js
var React52 = __toESM(require_react(), 1);
var import_jsx_runtime39 = __toESM(require_jsx_runtime(), 1);
var _excluded21 = ["ref"];
function GridPromptField(props) {
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  let placeholder = apiRef.current.getLocaleText("promptFieldPlaceholder");
  if (IS_SPEECH_RECOGNITION_SUPPORTED) {
    placeholder = apiRef.current.getLocaleText("promptFieldPlaceholderWithRecording");
  }
  return (0, import_jsx_runtime39.jsx)(PromptField, _extends({}, props, {
    children: (0, import_jsx_runtime39.jsx)(PromptFieldControl, {
      onKeyDown: (event) => {
        if (event.key === "Enter") {
          event.preventDefault();
        }
      },
      render: (_ref, state) => {
        var _a;
        let {
          ref
        } = _ref, controlProps = _objectWithoutPropertiesLoose(_ref, _excluded21);
        return (0, import_jsx_runtime39.jsx)(rootProps.slots.baseTextField, _extends({}, controlProps, {
          fullWidth: true,
          inputRef: ref,
          "aria-label": apiRef.current.getLocaleText("promptFieldLabel"),
          placeholder: state.recording ? apiRef.current.getLocaleText("promptFieldPlaceholderListening") : placeholder,
          size: "small",
          multiline: true,
          autoFocus: true,
          slotProps: _extends({
            input: _extends({
              startAdornment: IS_SPEECH_RECOGNITION_SUPPORTED ? (0, import_jsx_runtime39.jsx)(rootProps.slots.baseTooltip, {
                title: state.recording ? apiRef.current.getLocaleText("promptFieldStopRecording") : apiRef.current.getLocaleText("promptFieldRecord"),
                children: (0, import_jsx_runtime39.jsx)(PromptFieldRecord, {
                  size: "small",
                  edge: "start",
                  color: state.recording ? "primary" : "default",
                  children: (0, import_jsx_runtime39.jsx)(rootProps.slots.promptSpeechRecognitionIcon, {
                    fontSize: "small"
                  })
                })
              }) : (0, import_jsx_runtime39.jsx)(rootProps.slots.baseTooltip, {
                title: apiRef.current.getLocaleText("promptFieldSpeechRecognitionNotSupported"),
                children: (0, import_jsx_runtime39.jsx)(rootProps.slots.promptSpeechRecognitionOffIcon, {
                  fontSize: "small"
                })
              }),
              endAdornment: (0, import_jsx_runtime39.jsx)(rootProps.slots.baseTooltip, {
                title: apiRef.current.getLocaleText("promptFieldSend"),
                children: (0, import_jsx_runtime39.jsx)("span", {
                  children: (0, import_jsx_runtime39.jsx)(PromptFieldSend, {
                    size: "small",
                    edge: "end",
                    color: "primary",
                    children: (0, import_jsx_runtime39.jsx)(rootProps.slots.promptSendIcon, {
                      fontSize: "small"
                    })
                  })
                })
              })
            }, (_a = controlProps.slotProps) == null ? void 0 : _a.input)
          }, controlProps.slotProps)
        }));
      }
    })
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanelSuggestions.js
var React53 = __toESM(require_react(), 1);
var import_jsx_runtime40 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses18 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["aiAssistantPanelSuggestions"],
    list: ["aiAssistantPanelSuggestionsList"],
    item: ["aiAssistantPanelSuggestionsItem"],
    label: ["aiAssistantPanelSuggestionsLabel"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var AiAssistantPanelSuggestionsRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelSuggestions"
})({
  display: "flex",
  flexDirection: "column",
  gap: vars.spacing(0.75)
});
var AiAssistantPanelSuggestionsList = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelSuggestionsList"
})({
  display: "flex",
  gap: vars.spacing(0.75),
  overflow: "auto",
  padding: vars.spacing(1),
  margin: vars.spacing(-1),
  scrollbarWidth: "thin"
});
var AiAssistantPanelSuggestionsLabel = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelSuggestionsLabel"
})({
  display: "flex",
  alignItems: "center",
  gap: vars.spacing(1),
  font: vars.typography.font.body,
  color: vars.colors.foreground.muted,
  paddingLeft: vars.spacing(0.5)
});
function GridAiAssistantPanelSuggestions(props) {
  const {
    suggestions
  } = props;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses18(ownerState);
  return (0, import_jsx_runtime40.jsxs)(AiAssistantPanelSuggestionsRoot, {
    className: classes.root,
    ownerState,
    children: [(0, import_jsx_runtime40.jsx)(AiAssistantPanelSuggestionsLabel, {
      className: classes.label,
      ownerState,
      children: apiRef.current.getLocaleText("aiAssistantSuggestions")
    }), (0, import_jsx_runtime40.jsx)(AiAssistantPanelSuggestionsList, {
      className: classes.list,
      ownerState,
      children: suggestions.map((suggestion) => (0, import_jsx_runtime40.jsx)(rootProps.slots.baseChip, {
        label: suggestion.value,
        className: classes.item,
        onClick: () => apiRef.current.aiAssistant.processPrompt(suggestion.value),
        variant: "outlined"
      }, suggestion.value))
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanelConversationsMenu.js
var React54 = __toESM(require_react(), 1);
var import_jsx_runtime41 = __toESM(require_jsx_runtime(), 1);
function GridAiAssistantPanelConversationsMenu() {
  var _a, _b;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const activeConversationIndex = useGridSelector(apiRef, gridAiAssistantActiveConversationIndexSelector);
  const conversations = useGridSelector(apiRef, gridAiAssistantConversationsSelector);
  const [open, setOpen] = React54.useState(false);
  const menuId = useId();
  const triggerId = useId();
  const triggerRef = React54.useRef(null);
  const handleOpen = () => {
    setOpen(!open);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const sortedConversations = React54.useMemo(() => {
    return [...conversations].sort((a, b) => {
      if (!a.prompts.length) {
        return -1;
      }
      if (!b.prompts.length) {
        return 1;
      }
      return b.prompts[b.prompts.length - 1].createdAt.getTime() - a.prompts[a.prompts.length - 1].createdAt.getTime();
    });
  }, [conversations]);
  return (0, import_jsx_runtime41.jsxs)(React54.Fragment, {
    children: [(0, import_jsx_runtime41.jsx)(rootProps.slots.baseTooltip, {
      title: apiRef.current.getLocaleText("aiAssistantPanelConversationHistory"),
      enterDelay: 500,
      children: (0, import_jsx_runtime41.jsx)("span", {
        children: (0, import_jsx_runtime41.jsx)(rootProps.slots.baseIconButton, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
          disabled: conversations.length === 0,
          id: triggerId,
          "aria-haspopup": "true",
          "aria-controls": open ? menuId : void 0,
          "aria-expanded": open ? "true" : void 0,
          "aria-label": apiRef.current.getLocaleText("aiAssistantPanelConversationHistory"),
          onClick: handleOpen,
          ref: triggerRef,
          children: (0, import_jsx_runtime41.jsx)(rootProps.slots.aiAssistantPanelHistoryIcon, {
            fontSize: "small"
          })
        }))
      })
    }), (0, import_jsx_runtime41.jsx)(GridMenu, {
      target: triggerRef.current,
      open,
      onClose: handleClose,
      position: "bottom-end",
      children: (0, import_jsx_runtime41.jsx)(rootProps.slots.baseMenuList, _extends({
        id: menuId,
        "aria-labelledby": triggerId,
        autoFocusItem: true
      }, (_b = rootProps.slotProps) == null ? void 0 : _b.baseMenuList, {
        children: sortedConversations.map((conversation, sortedIndex) => {
          const conversationIndex = conversations.findIndex((c) => c === conversation);
          return (0, import_jsx_runtime41.jsx)(rootProps.slots.baseMenuItem, {
            selected: conversationIndex === activeConversationIndex,
            onClick: () => {
              apiRef.current.aiAssistant.setActiveConversationIndex(conversationIndex);
              handleClose();
            },
            children: conversation.title
          }, `${conversation.id}-${sortedIndex}`);
        })
      }))
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/components/aiAssistantPanel/GridAiAssistantPanel.js
var import_jsx_runtime42 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses19 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["aiAssistantPanel"],
    header: ["aiAssistantPanelHeader"],
    title: ["aiAssistantPanelTitle"],
    titleContainer: ["aiAssistantPanelTitleContainer"],
    conversationTitle: ["aiAssistantPanelConversationTitle"],
    body: ["aiAssistantPanelBody"],
    emptyText: ["aiAssistantPanelEmptyText"],
    footer: ["aiAssistantPanelFooter"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var AiAssistantPanelRoot = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanel"
})({
  flexDirection: "column",
  width: 380,
  maxHeight: "none",
  overflow: "hidden"
});
var AiAssistantPanelHeader = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelHeader"
})({
  flexShrink: 0,
  display: "flex",
  alignItems: "center",
  width: "100%",
  boxSizing: "border-box",
  borderBottom: `1px solid ${vars.colors.border.base}`,
  height: 52,
  padding: vars.spacing(0, 0.75, 0, 2)
});
var AiAssistantPanelTitleContainer = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelTitleContainer"
})({
  display: "flex",
  flexDirection: "column",
  flex: 1,
  overflow: "hidden"
});
var AiAssistantPanelTitle = styled_default2("span", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelTitle"
})({
  font: vars.typography.font.body,
  fontWeight: vars.typography.fontWeight.medium,
  marginTop: vars.spacing(0.25)
});
var AiAssistantPanelConversationTitle = styled_default2("span", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelConversationTitle"
})({
  font: vars.typography.font.small,
  color: vars.colors.foreground.muted,
  marginTop: vars.spacing(-0.25),
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap"
});
var AiAssistantPanelBody = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelBody"
})({
  flexGrow: 0,
  flexShrink: 0,
  height: 260,
  display: "flex",
  justifyContent: "center",
  alignItems: "center"
});
var AiAssistantPanelEmptyText = styled_default2("span", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelEmptyText"
})({
  font: vars.typography.font.body,
  color: vars.colors.foreground.muted
});
var AiAssistantPanelFooter = styled_default2("div", {
  name: "MuiDataGrid",
  slot: "AiAssistantPanelFooter"
})({
  flexShrink: 0,
  display: "flex",
  flexDirection: "column",
  gap: vars.spacing(1),
  borderTop: `1px solid ${vars.colors.border.base}`,
  padding: vars.spacing(1)
});
function GridAiAssistantPanel() {
  var _a, _b;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const classes = useUtilityClasses19(rootProps);
  const activeConversation = useGridSelector(apiRef, gridAiAssistantActiveConversationSelector);
  const conversations = useGridSelector(apiRef, gridAiAssistantConversationsSelector);
  const conversationTitle = (activeConversation == null ? void 0 : activeConversation.title) || apiRef.current.getLocaleText("aiAssistantPanelNewConversation");
  const createConversation = React55.useCallback(() => {
    const newConversation = conversations.findIndex((conversation) => !conversation.prompts.length);
    if (newConversation !== -1) {
      apiRef.current.aiAssistant.setActiveConversationIndex(newConversation);
    } else {
      apiRef.current.aiAssistant.setConversations((newConversations) => [...newConversations, {
        title: apiRef.current.getLocaleText("aiAssistantPanelNewConversation"),
        prompts: []
      }]);
      apiRef.current.aiAssistant.setActiveConversationIndex(conversations.length);
    }
  }, [apiRef, conversations]);
  return (0, import_jsx_runtime42.jsxs)(AiAssistantPanelRoot, {
    className: classes.root,
    ownerState: rootProps,
    children: [(0, import_jsx_runtime42.jsxs)(AiAssistantPanelHeader, {
      className: classes.header,
      ownerState: rootProps,
      children: [(0, import_jsx_runtime42.jsxs)(AiAssistantPanelTitleContainer, {
        className: classes.titleContainer,
        ownerState: rootProps,
        children: [(0, import_jsx_runtime42.jsx)(AiAssistantPanelTitle, {
          className: classes.title,
          ownerState: rootProps,
          children: apiRef.current.getLocaleText("aiAssistantPanelTitle")
        }), (0, import_jsx_runtime42.jsx)(AiAssistantPanelConversationTitle, {
          className: classes.conversationTitle,
          ownerState: rootProps,
          title: conversationTitle,
          children: conversationTitle
        })]
      }), (0, import_jsx_runtime42.jsx)(rootProps.slots.baseTooltip, {
        title: apiRef.current.getLocaleText("aiAssistantPanelNewConversation"),
        enterDelay: 500,
        children: (0, import_jsx_runtime42.jsx)("span", {
          children: (0, import_jsx_runtime42.jsx)(rootProps.slots.baseIconButton, _extends({}, (_a = rootProps.slotProps) == null ? void 0 : _a.baseIconButton, {
            disabled: !conversations.length || !(activeConversation == null ? void 0 : activeConversation.prompts.length),
            onClick: createConversation,
            children: (0, import_jsx_runtime42.jsx)(rootProps.slots.aiAssistantPanelNewConversationIcon, {
              fontSize: "small"
            })
          }))
        })
      }), (0, import_jsx_runtime42.jsx)(GridAiAssistantPanelConversationsMenu, {}), (0, import_jsx_runtime42.jsx)(rootProps.slots.baseIconButton, _extends({}, (_b = rootProps.slotProps) == null ? void 0 : _b.baseIconButton, {
        "aria-label": apiRef.current.getLocaleText("aiAssistantPanelClose"),
        onClick: apiRef.current.hidePreferences,
        children: (0, import_jsx_runtime42.jsx)(rootProps.slots.aiAssistantPanelCloseIcon, {
          fontSize: "small"
        })
      }))]
    }), (0, import_jsx_runtime42.jsx)(AiAssistantPanelBody, {
      className: classes.body,
      ownerState: rootProps,
      children: activeConversation && activeConversation.prompts.length > 0 ? (0, import_jsx_runtime42.jsx)(GridAiAssistantPanelConversation, {
        conversation: activeConversation
      }) : (0, import_jsx_runtime42.jsx)(AiAssistantPanelEmptyText, {
        ownerState: rootProps,
        className: classes.emptyText,
        children: apiRef.current.getLocaleText("aiAssistantPanelEmptyConversation")
      })
    }), (0, import_jsx_runtime42.jsxs)(AiAssistantPanelFooter, {
      className: classes.footer,
      ownerState: rootProps,
      children: [(0, import_jsx_runtime42.jsx)(GridPromptField, {
        onSubmit: apiRef.current.aiAssistant.processPrompt
      }), rootProps.aiAssistantSuggestions && rootProps.aiAssistantSuggestions.length > 0 && (0, import_jsx_runtime42.jsx)(GridAiAssistantPanelSuggestions, {
        suggestions: rootProps.aiAssistantSuggestions
      })]
    })]
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/export/useGridExcelExport.js
var import_jsx_runtime43 = __toESM(require_jsx_runtime(), 1);
var _excluded23 = ["worker", "exceljsPostProcess", "exceljsPreProcess", "columnsStyles", "includeHeaders", "getRowsToExport", "valueOptionsSheetName"];
var useGridExcelExport = (apiRef, props) => {
  const logger = useGridLogger(apiRef, "useGridExcelExport");
  const getDataAsExcel = React56.useCallback((options2 = {}) => {
    logger.debug(`Get data as excel`);
    const getRowsToExport = options2.getRowsToExport ?? defaultGetRowsToExport;
    const exportedRowIds = getRowsToExport({
      apiRef
    });
    const exportedColumns = getColumnsToExport({
      apiRef,
      options: options2
    });
    return buildExcel({
      columns: exportedColumns,
      rowIds: exportedRowIds,
      includeHeaders: options2.includeHeaders ?? true,
      includeColumnGroupsHeaders: options2.includeColumnGroupsHeaders ?? true,
      valueOptionsSheetName: (options2 == null ? void 0 : options2.valueOptionsSheetName) || "Options",
      columnsStyles: options2 == null ? void 0 : options2.columnsStyles,
      exceljsPreProcess: options2 == null ? void 0 : options2.exceljsPreProcess,
      exceljsPostProcess: options2 == null ? void 0 : options2.exceljsPostProcess,
      escapeFormulas: options2.escapeFormulas ?? true
    }, apiRef);
  }, [logger, apiRef]);
  const exportDataAsExcel = React56.useCallback(async (options2 = {}) => {
    const {
      worker: workerFn,
      exceljsPostProcess,
      exceljsPreProcess,
      getRowsToExport = defaultGetRowsToExport,
      valueOptionsSheetName = "Options"
    } = options2, cloneableOptions = _objectWithoutPropertiesLoose(options2, _excluded23);
    const sendExcelToUser = (buffer) => {
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      });
      exportAs(blob, "xlsx", options2 == null ? void 0 : options2.fileName);
    };
    if (!workerFn) {
      apiRef.current.publishEvent("excelExportStateChange", "pending");
      const workbook = await getDataAsExcel(options2);
      if (workbook === null) {
        return;
      }
      const content = await workbook.xlsx.writeBuffer();
      apiRef.current.publishEvent("excelExportStateChange", "finished");
      sendExcelToUser(content);
      return;
    }
    if (true) {
      if (exceljsPostProcess) {
        console.warn([`MUI X: The exceljsPostProcess option is not supported when a web worker is used.`, "As alternative, pass the callback to the same option in setupExcelExportWebWorker."].join("\n"));
      }
      if (exceljsPreProcess) {
        console.warn([`MUI X: The exceljsPreProcess option is not supported when a web worker is used.`, "As alternative, pass the callback to the same option in setupExcelExportWebWorker."].join("\n"));
      }
    }
    const worker = workerFn();
    apiRef.current.publishEvent("excelExportStateChange", "pending");
    worker.onmessage = async (event) => {
      sendExcelToUser(event.data);
      apiRef.current.publishEvent("excelExportStateChange", "finished");
      worker.terminate();
    };
    const exportedRowIds = getRowsToExport({
      apiRef
    });
    const exportedColumns = getColumnsToExport({
      apiRef,
      options: options2
    });
    const valueOptionsData = await getDataForValueOptionsSheet(exportedColumns, valueOptionsSheetName, apiRef.current);
    const serializedColumns = serializeColumns(exportedColumns, options2.columnsStyles || {});
    apiRef.current.resetColSpan();
    const serializedRows = [];
    for (let i = 0; i < exportedRowIds.length; i += 1) {
      const id = exportedRowIds[i];
      const serializedRow = serializeRowUnsafe(id, exportedColumns, apiRef, valueOptionsData, {
        escapeFormulas: options2.escapeFormulas ?? true
      });
      serializedRows.push(serializedRow);
    }
    apiRef.current.resetColSpan();
    const columnGroupPaths = exportedColumns.reduce((acc, column) => {
      acc[column.field] = apiRef.current.getColumnGroupPath(column.field);
      return acc;
    }, {});
    const message = {
      // workers share the pub-sub channel namespace. Use this property to filter out messages.
      namespace: "mui-x-data-grid-export",
      serializedColumns,
      serializedRows,
      valueOptionsData,
      columnGroupPaths,
      columnGroupDetails: apiRef.current.getAllGroupDetails(),
      options: cloneableOptions,
      valueOptionsSheetName
    };
    worker.postMessage(message);
  }, [apiRef, getDataAsExcel]);
  const excelExportApi = {
    getDataAsExcel,
    exportDataAsExcel
  };
  useGridApiMethod(apiRef, excelExportApi, "public");
  const addExportMenuButtons = React56.useCallback((initialValue, options2) => {
    var _a;
    if ((_a = options2.excelOptions) == null ? void 0 : _a.disableToolbarButton) {
      return initialValue;
    }
    return [...initialValue, {
      component: (0, import_jsx_runtime43.jsx)(GridExcelExportMenuItem, {
        options: options2.excelOptions
      }),
      componentName: "excelExport"
    }];
  }, []);
  useGridRegisterPipeProcessor(apiRef, "exportMenu", addExportMenuButtons);
  useGridEventPriority(apiRef, "excelExportStateChange", props.onExcelExportStateChange);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/cellSelection/useGridCellSelection.js
var React59 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/ownerDocument/ownerDocument.js
function ownerDocument(node) {
  return node && node.ownerDocument || document;
}

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js
var React58 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js
var React57 = __toESM(require_react(), 1);
var useEnhancedEffect = typeof window !== "undefined" ? React57.useLayoutEffect : React57.useEffect;
var useEnhancedEffect_default = useEnhancedEffect;

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js
function useEventCallback(fn) {
  const ref = React58.useRef(fn);
  useEnhancedEffect_default(() => {
    ref.current = fn;
  });
  return React58.useRef((...args) => (
    // @ts-expect-error hide `this`
    (0, ref.current)(...args)
  )).current;
}
var useEventCallback_default = useEventCallback;

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/cellSelection/gridCellSelectionSelector.js
var gridCellSelectionStateSelector = createRootSelector((state) => state.cellSelection);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/cellSelection/useGridCellSelection.js
var cellSelectionStateInitializer = (state, props) => {
  var _a;
  return _extends({}, state, {
    cellSelection: _extends({}, props.cellSelectionModel ?? ((_a = props.initialState) == null ? void 0 : _a.cellSelection))
  });
};
function isKeyboardEvent(event) {
  return !!event.key;
}
var AUTO_SCROLL_SENSITIVITY = 50;
var AUTO_SCROLL_SPEED = 20;
var useGridCellSelection = (apiRef, props) => {
  const hasRootReference = apiRef.current.rootElementRef.current !== null;
  const cellWithVirtualFocus = React59.useRef(null);
  const lastMouseDownCell = React59.useRef(null);
  const mousePosition = React59.useRef(null);
  const autoScrollRAF = React59.useRef(null);
  const totalHeaderHeight = getTotalHeaderHeight(apiRef, props);
  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;
  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === "object" ? ignoreValueFormatterProp == null ? void 0 : ignoreValueFormatterProp.clipboardExport : ignoreValueFormatterProp) || false;
  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;
  apiRef.current.registerControlState({
    stateId: "cellSelection",
    propModel: props.cellSelectionModel,
    propOnChange: props.onCellSelectionModelChange,
    stateSelector: gridCellSelectionStateSelector,
    changeEvent: "cellSelectionChange"
  });
  const runIfCellSelectionIsEnabled = (callback) => (...args) => {
    if (props.cellSelection) {
      callback(...args);
    }
  };
  const isCellSelected = React59.useCallback((id, field) => {
    if (!props.cellSelection) {
      return false;
    }
    const cellSelectionModel = gridCellSelectionStateSelector(apiRef);
    return cellSelectionModel[id] ? !!cellSelectionModel[id][field] : false;
  }, [apiRef, props.cellSelection]);
  const getCellSelectionModel = React59.useCallback(() => {
    return gridCellSelectionStateSelector(apiRef);
  }, [apiRef]);
  const setCellSelectionModel = React59.useCallback((newModel) => {
    if (!props.cellSelection) {
      return;
    }
    apiRef.current.setState((prevState) => _extends({}, prevState, {
      cellSelection: newModel
    }));
  }, [apiRef, props.cellSelection]);
  const selectCellRange = React59.useCallback((start, end, keepOtherSelected = false) => {
    const startRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(start.id);
    const startColumnIndex = apiRef.current.getColumnIndex(start.field);
    const endRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(end.id);
    const endColumnIndex = apiRef.current.getColumnIndex(end.field);
    let finalStartRowIndex = startRowIndex;
    let finalStartColumnIndex = startColumnIndex;
    let finalEndRowIndex = endRowIndex;
    let finalEndColumnIndex = endColumnIndex;
    if (finalStartRowIndex > finalEndRowIndex) {
      finalStartRowIndex = endRowIndex;
      finalEndRowIndex = startRowIndex;
    }
    if (finalStartColumnIndex > finalEndColumnIndex) {
      finalStartColumnIndex = endColumnIndex;
      finalEndColumnIndex = startColumnIndex;
    }
    const visibleColumns = apiRef.current.getVisibleColumns();
    const visibleRows = getVisibleRows(apiRef);
    const rowsInRange = visibleRows.rows.slice(finalStartRowIndex, finalEndRowIndex + 1);
    const columnsInRange = visibleColumns.slice(finalStartColumnIndex, finalEndColumnIndex + 1);
    const newModel = keepOtherSelected ? _extends({}, apiRef.current.getCellSelectionModel()) : {};
    rowsInRange.forEach((row) => {
      if (!newModel[row.id]) {
        newModel[row.id] = {};
      }
      columnsInRange.forEach((column) => {
        newModel[row.id][column.field] = true;
      }, {});
    });
    apiRef.current.setCellSelectionModel(newModel);
  }, [apiRef]);
  const getSelectedCellsAsArray = React59.useCallback(() => {
    const selectionModel = apiRef.current.getCellSelectionModel();
    const currentVisibleRows = getVisibleRows(apiRef, props);
    const sortedEntries = currentVisibleRows.rows.reduce((result, row) => {
      if (row.id in selectionModel) {
        result.push([row.id, selectionModel[row.id]]);
      }
      return result;
    }, []);
    return sortedEntries.reduce((selectedCells, [id, fields]) => {
      selectedCells.push(...Object.entries(fields).reduce((selectedFields, [field, isSelected]) => {
        if (isSelected) {
          selectedFields.push({
            id,
            field
          });
        }
        return selectedFields;
      }, []));
      return selectedCells;
    }, []);
  }, [apiRef, props]);
  const cellSelectionApi = {
    isCellSelected,
    getCellSelectionModel,
    setCellSelectionModel,
    selectCellRange,
    getSelectedCellsAsArray
  };
  useGridApiMethod(apiRef, cellSelectionApi, "public");
  const hasClickedValidCellForRangeSelection = React59.useCallback((params) => {
    if (params.field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {
      return false;
    }
    if (params.field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {
      return false;
    }
    const column = apiRef.current.getColumn(params.field);
    if (column.type === GRID_ACTIONS_COLUMN_TYPE) {
      return false;
    }
    return params.rowNode.type !== "pinnedRow";
  }, [apiRef]);
  const handleMouseUp = useEventCallback_default(() => {
    var _a, _b;
    lastMouseDownCell.current = null;
    (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.classList.remove(gridClasses["root--disableUserSelection"]);
    stopAutoScroll();
  });
  const handleCellMouseDown = React59.useCallback((params, event) => {
    var _a, _b, _c;
    const isMacOs = window.navigator.platform.toUpperCase().indexOf("MAC") >= 0;
    if (event.button !== 0 || event.ctrlKey && isMacOs) {
      return;
    }
    if (params.field === GRID_REORDER_COL_DEF.field) {
      return;
    }
    const focusedCell = gridFocusCellSelector(apiRef);
    if (hasClickedValidCellForRangeSelection(params) && event.shiftKey && focusedCell) {
      event.preventDefault();
    }
    lastMouseDownCell.current = {
      id: params.id,
      field: params.field
    };
    (_b = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current) == null ? void 0 : _b.classList.add(gridClasses["root--disableUserSelection"]);
    const document2 = ownerDocument((_c = apiRef.current.rootElementRef) == null ? void 0 : _c.current);
    document2.addEventListener("mouseup", handleMouseUp, {
      once: true
    });
  }, [apiRef, handleMouseUp, hasClickedValidCellForRangeSelection]);
  const stopAutoScroll = React59.useCallback(() => {
    if (autoScrollRAF.current) {
      cancelAnimationFrame(autoScrollRAF.current);
      autoScrollRAF.current = null;
    }
  }, []);
  const handleCellFocusIn = React59.useCallback((params) => {
    cellWithVirtualFocus.current = {
      id: params.id,
      field: params.field
    };
  }, []);
  const startAutoScroll = React59.useCallback(() => {
    var _a;
    if (autoScrollRAF.current) {
      return;
    }
    if (!((_a = apiRef.current.virtualScrollerRef) == null ? void 0 : _a.current)) {
      return;
    }
    function autoScroll() {
      var _a2;
      if (!mousePosition.current || !((_a2 = apiRef.current.virtualScrollerRef) == null ? void 0 : _a2.current)) {
        return;
      }
      const dimensions = gridDimensionsSelector(apiRef);
      const {
        x: mouseX,
        y: mouseY
      } = mousePosition.current;
      const {
        width,
        height: viewportOuterHeight
      } = dimensions.viewportOuterSize;
      const height = viewportOuterHeight - totalHeaderHeight;
      let deltaX = 0;
      let deltaY = 0;
      let factor = 0;
      if (mouseY <= AUTO_SCROLL_SENSITIVITY && dimensions.hasScrollY) {
        factor = (AUTO_SCROLL_SENSITIVITY - mouseY) / -AUTO_SCROLL_SENSITIVITY;
        deltaY = AUTO_SCROLL_SPEED;
      } else if (mouseY >= height - AUTO_SCROLL_SENSITIVITY && dimensions.hasScrollY) {
        factor = (mouseY - (height - AUTO_SCROLL_SENSITIVITY)) / AUTO_SCROLL_SENSITIVITY;
        deltaY = AUTO_SCROLL_SPEED;
      } else if (mouseX <= AUTO_SCROLL_SENSITIVITY && dimensions.hasScrollX) {
        factor = (AUTO_SCROLL_SENSITIVITY - mouseX) / -AUTO_SCROLL_SENSITIVITY;
        deltaX = AUTO_SCROLL_SPEED;
      } else if (mouseX >= width - AUTO_SCROLL_SENSITIVITY && dimensions.hasScrollX) {
        factor = (mouseX - (width - AUTO_SCROLL_SENSITIVITY)) / AUTO_SCROLL_SENSITIVITY;
        deltaX = AUTO_SCROLL_SPEED;
      }
      if (deltaX !== 0 || deltaY !== 0) {
        const {
          scrollLeft,
          scrollTop
        } = apiRef.current.virtualScrollerRef.current;
        apiRef.current.scroll({
          top: scrollTop + deltaY * factor,
          left: scrollLeft + deltaX * factor
        });
      }
      autoScrollRAF.current = requestAnimationFrame(autoScroll);
    }
    autoScroll();
  }, [apiRef, totalHeaderHeight]);
  const handleCellMouseOver = React59.useCallback((params, event) => {
    var _a, _b;
    if (!lastMouseDownCell.current) {
      return;
    }
    const {
      id,
      field
    } = params;
    apiRef.current.selectCellRange(lastMouseDownCell.current, {
      id,
      field
    }, event.ctrlKey || event.metaKey);
    const virtualScrollerRect = (_b = (_a = apiRef.current.virtualScrollerRef) == null ? void 0 : _a.current) == null ? void 0 : _b.getBoundingClientRect();
    if (!virtualScrollerRect) {
      return;
    }
    const dimensions = gridDimensionsSelector(apiRef);
    const {
      x,
      y
    } = virtualScrollerRect;
    const {
      width,
      height: viewportOuterHeight
    } = dimensions.viewportOuterSize;
    const height = viewportOuterHeight - totalHeaderHeight;
    const mouseX = event.clientX - x;
    const mouseY = event.clientY - y - totalHeaderHeight;
    mousePosition.current = {
      x: mouseX,
      y: mouseY
    };
    const hasEnteredVerticalSensitivityArea = mouseY <= AUTO_SCROLL_SENSITIVITY || mouseY >= height - AUTO_SCROLL_SENSITIVITY;
    const hasEnteredHorizontalSensitivityArea = mouseX <= AUTO_SCROLL_SENSITIVITY || mouseX >= width - AUTO_SCROLL_SENSITIVITY;
    const hasEnteredSensitivityArea = hasEnteredVerticalSensitivityArea || hasEnteredHorizontalSensitivityArea;
    if (hasEnteredSensitivityArea) {
      startAutoScroll();
    } else {
      stopAutoScroll();
    }
  }, [apiRef, startAutoScroll, stopAutoScroll, totalHeaderHeight]);
  const handleCellClick = useEventCallback_default((params, event) => {
    const {
      id,
      field
    } = params;
    if (!hasClickedValidCellForRangeSelection(params)) {
      return;
    }
    const focusedCell = gridFocusCellSelector(apiRef);
    if (event.shiftKey && focusedCell) {
      apiRef.current.selectCellRange(focusedCell, {
        id,
        field
      });
      cellWithVirtualFocus.current = {
        id,
        field
      };
      return;
    }
    if (event.ctrlKey || event.metaKey) {
      const prevModel = apiRef.current.getCellSelectionModel();
      apiRef.current.setCellSelectionModel(_extends({}, prevModel, {
        [id]: _extends({}, prevModel[id], {
          [field]: !apiRef.current.isCellSelected(id, field)
        })
      }));
    } else {
      apiRef.current.setCellSelectionModel({
        [id]: {
          [field]: true
        }
      });
    }
  });
  const handleCellKeyDown = useEventCallback_default((params, event) => {
    if (!isNavigationKey(event.key) || !cellWithVirtualFocus.current) {
      return;
    }
    if (!event.shiftKey) {
      apiRef.current.setCellSelectionModel({});
      return;
    }
    const {
      current: otherCell
    } = cellWithVirtualFocus;
    let endRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(otherCell.id);
    let endColumnIndex = apiRef.current.getColumnIndex(otherCell.field);
    if (event.key === "ArrowDown") {
      endRowIndex += 1;
    } else if (event.key === "ArrowUp") {
      endRowIndex -= 1;
    } else if (event.key === "ArrowRight") {
      endColumnIndex += 1;
    } else if (event.key === "ArrowLeft") {
      endColumnIndex -= 1;
    }
    const visibleRows = getVisibleRows(apiRef);
    if (endRowIndex < 0 || endRowIndex >= visibleRows.rows.length) {
      return;
    }
    const visibleColumns = apiRef.current.getVisibleColumns();
    if (endColumnIndex < 0 || endColumnIndex >= visibleColumns.length) {
      return;
    }
    cellWithVirtualFocus.current = {
      id: visibleRows.rows[endRowIndex].id,
      field: visibleColumns[endColumnIndex].field
    };
    apiRef.current.scrollToIndexes({
      rowIndex: endRowIndex,
      colIndex: endColumnIndex
    });
    const {
      id,
      field
    } = params;
    apiRef.current.selectCellRange({
      id,
      field
    }, cellWithVirtualFocus.current);
  });
  useGridEvent(apiRef, "cellClick", runIfCellSelectionIsEnabled(handleCellClick));
  useGridEvent(apiRef, "cellFocusIn", runIfCellSelectionIsEnabled(handleCellFocusIn));
  useGridEvent(apiRef, "cellKeyDown", runIfCellSelectionIsEnabled(handleCellKeyDown));
  useGridEvent(apiRef, "cellMouseDown", runIfCellSelectionIsEnabled(handleCellMouseDown));
  useGridEvent(apiRef, "cellMouseOver", runIfCellSelectionIsEnabled(handleCellMouseOver));
  React59.useEffect(() => {
    if (props.cellSelectionModel) {
      apiRef.current.setCellSelectionModel(props.cellSelectionModel);
    }
  }, [apiRef, props.cellSelectionModel]);
  React59.useEffect(() => {
    var _a;
    const rootRef = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current;
    return () => {
      stopAutoScroll();
      const document2 = ownerDocument(rootRef);
      document2.removeEventListener("mouseup", handleMouseUp);
    };
  }, [apiRef, hasRootReference, handleMouseUp, stopAutoScroll]);
  const checkIfCellIsSelected = React59.useCallback((isSelected, {
    id,
    field
  }) => {
    return apiRef.current.isCellSelected(id, field);
  }, [apiRef]);
  const addClassesToCells = React59.useCallback((classes, {
    id,
    field
  }) => {
    const visibleRows = getVisibleRows(apiRef);
    if (!visibleRows.range || !apiRef.current.isCellSelected(id, field)) {
      return classes;
    }
    const newClasses = [...classes];
    const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(id);
    const columnIndex = apiRef.current.getColumnIndex(field);
    const visibleColumns = apiRef.current.getVisibleColumns();
    if (rowIndex > 0) {
      const {
        id: previousRowId
      } = visibleRows.rows[rowIndex - 1];
      if (!apiRef.current.isCellSelected(previousRowId, field)) {
        newClasses.push(gridClasses["cell--rangeTop"]);
      }
    } else {
      newClasses.push(gridClasses["cell--rangeTop"]);
    }
    if (rowIndex + visibleRows.range.firstRowIndex < visibleRows.range.lastRowIndex) {
      const {
        id: nextRowId
      } = visibleRows.rows[rowIndex + 1];
      if (!apiRef.current.isCellSelected(nextRowId, field)) {
        newClasses.push(gridClasses["cell--rangeBottom"]);
      }
    } else {
      newClasses.push(gridClasses["cell--rangeBottom"]);
    }
    if (columnIndex > 0) {
      const {
        field: previousColumnField
      } = visibleColumns[columnIndex - 1];
      if (!apiRef.current.isCellSelected(id, previousColumnField)) {
        newClasses.push(gridClasses["cell--rangeLeft"]);
      }
    } else {
      newClasses.push(gridClasses["cell--rangeLeft"]);
    }
    if (columnIndex < visibleColumns.length - 1) {
      const {
        field: nextColumnField
      } = visibleColumns[columnIndex + 1];
      if (!apiRef.current.isCellSelected(id, nextColumnField)) {
        newClasses.push(gridClasses["cell--rangeRight"]);
      }
    } else {
      newClasses.push(gridClasses["cell--rangeRight"]);
    }
    return newClasses;
  }, [apiRef]);
  const canUpdateFocus = React59.useCallback((initialValue, {
    event,
    cell
  }) => {
    if (!cell || !props.cellSelection || !event.shiftKey) {
      return initialValue;
    }
    if (isKeyboardEvent(event)) {
      return isNavigationKey(event.key) ? false : initialValue;
    }
    const focusedCell = gridFocusCellSelector(apiRef);
    if (hasClickedValidCellForRangeSelection(cell) && focusedCell) {
      return false;
    }
    return initialValue;
  }, [apiRef, props.cellSelection, hasClickedValidCellForRangeSelection]);
  const handleClipboardCopy = React59.useCallback((value) => {
    if (apiRef.current.getSelectedCellsAsArray().length <= 1) {
      return value;
    }
    const sortedRowIds = gridSortedRowIdsSelector(apiRef);
    const cellSelectionModel = apiRef.current.getCellSelectionModel();
    const unsortedSelectedRowIds = Object.keys(cellSelectionModel);
    const sortedSelectedRowIds = sortedRowIds.filter((id) => unsortedSelectedRowIds.includes(`${id}`));
    const copyData = sortedSelectedRowIds.reduce((acc, rowId) => {
      const fieldsMap = cellSelectionModel[rowId];
      const rowValues = Object.keys(fieldsMap).map((field) => {
        let cellData;
        if (fieldsMap[field]) {
          const cellParams = apiRef.current.getCellParams(rowId, field);
          cellData = serializeCellValue(cellParams, {
            csvOptions: {
              delimiter: clipboardCopyCellDelimiter,
              shouldAppendQuotes: false,
              escapeFormulas: false
            },
            ignoreValueFormatter
          });
        } else {
          cellData = "";
        }
        return cellData;
      }, "");
      const rowString = rowValues.join(clipboardCopyCellDelimiter);
      return acc === "" ? rowString : [acc, rowString].join("\r\n");
    }, "");
    return copyData;
  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);
  useGridRegisterPipeProcessor(apiRef, "isCellSelected", checkIfCellIsSelected);
  useGridRegisterPipeProcessor(apiRef, "cellClassName", addClassesToCells);
  useGridRegisterPipeProcessor(apiRef, "canUpdateFocus", canUpdateFocus);
  useGridRegisterPipeProcessor(apiRef, "clipboardCopy", handleClipboardCopy);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/clipboard/useGridClipboardImport.js
var React60 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/node_modules/@mui/utils/esm/debounce/debounce.js
function debounce(func, wait = 166) {
  let timeout;
  function debounced(...args) {
    const later = () => {
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  }
  debounced.clear = () => {
    clearTimeout(timeout);
  };
  return debounced;
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/clipboard/useGridClipboardImport.js
var columnFieldsToExcludeFromPaste = [GRID_CHECKBOX_SELECTION_FIELD, GRID_REORDER_COL_DEF.field, GRID_DETAIL_PANEL_TOGGLE_FIELD];
function batchRowUpdates(func, wait) {
  let rows = [];
  const debounced = debounce(() => {
    func(rows);
    rows = [];
  }, wait);
  return (row) => {
    rows.push(row);
    debounced();
  };
}
async function getTextFromClipboard(rootEl) {
  return new Promise((resolve) => {
    const focusedCell = getActiveElement(document);
    const el = document.createElement("input");
    el.style.width = "0px";
    el.style.height = "0px";
    el.style.border = "none";
    el.style.margin = "0";
    el.style.padding = "0";
    el.style.outline = "none";
    el.style.position = "absolute";
    el.style.top = "0";
    el.style.left = "0";
    const handlePasteEvent = (event) => {
      var _a;
      el.removeEventListener("paste", handlePasteEvent);
      const text = (_a = event.clipboardData) == null ? void 0 : _a.getData("text/plain");
      if (focusedCell instanceof HTMLElement) {
        focusedCell.focus({
          preventScroll: true
        });
      }
      el.remove();
      resolve(text || "");
    };
    el.addEventListener("paste", handlePasteEvent);
    rootEl.appendChild(el);
    el.focus({
      preventScroll: true
    });
  });
}
var CellValueUpdater = class {
  constructor(options2) {
    __publicField(this, "rowsToUpdate", {});
    this.options = options2;
    this.updateRow = batchRowUpdates(options2.apiRef.current.updateRows, 50);
  }
  updateCell({
    rowId,
    field,
    pastedCellValue
  }) {
    if (pastedCellValue === void 0) {
      return;
    }
    const {
      apiRef,
      getRowId
    } = this.options;
    const colDef = apiRef.current.getColumn(field);
    if (!colDef || !colDef.editable) {
      return;
    }
    const row = this.rowsToUpdate[rowId] || _extends({}, apiRef.current.getRow(rowId));
    if (!row) {
      return;
    }
    let parsedValue = pastedCellValue;
    if (colDef.pastedValueParser) {
      parsedValue = colDef.pastedValueParser(pastedCellValue, row, colDef, apiRef);
    } else if (colDef.valueParser) {
      parsedValue = colDef.valueParser(parsedValue, row, colDef, apiRef);
    }
    if (parsedValue === void 0) {
      return;
    }
    let rowCopy = _extends({}, row);
    if (typeof colDef.valueSetter === "function") {
      rowCopy = colDef.valueSetter(parsedValue, rowCopy, colDef, apiRef);
    } else {
      rowCopy[field] = parsedValue;
    }
    const newRowId = getRowIdFromRowModel(rowCopy, getRowId);
    if (String(newRowId) !== String(rowId)) {
      return;
    }
    this.rowsToUpdate[rowId] = rowCopy;
  }
  applyUpdates() {
    const {
      apiRef,
      processRowUpdate,
      onProcessRowUpdateError
    } = this.options;
    const rowsToUpdate = this.rowsToUpdate;
    const rowIdsToUpdate = Object.keys(rowsToUpdate);
    if (rowIdsToUpdate.length === 0) {
      apiRef.current.publishEvent("clipboardPasteEnd");
      return;
    }
    const handleRowUpdate = async (rowId) => {
      const newRow = rowsToUpdate[rowId];
      if (typeof processRowUpdate === "function") {
        const handleError = (errorThrown) => {
          if (onProcessRowUpdateError) {
            onProcessRowUpdateError(errorThrown);
          } else if (true) {
            warnOnce(["MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.", "To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.", "For more detail, see https://mui.com/x/react-data-grid/editing/persistence/."], "error");
          }
        };
        try {
          const oldRow = apiRef.current.getRow(rowId);
          const finalRowUpdate = await processRowUpdate(newRow, oldRow, {
            rowId
          });
          this.updateRow(finalRowUpdate);
        } catch (error) {
          handleError(error);
        }
      } else {
        this.updateRow(newRow);
      }
    };
    const promises = rowIdsToUpdate.map((rowId) => {
      return new Promise((resolve) => {
        handleRowUpdate(rowId).then(resolve).catch(resolve);
      });
    });
    Promise.all(promises).then(() => {
      this.rowsToUpdate = {};
      apiRef.current.publishEvent("clipboardPasteEnd");
    });
  }
};
function defaultPasteResolver({
  pastedData,
  apiRef,
  updateCell,
  pagination,
  paginationMode
}) {
  const isSingleValuePasted = pastedData.length === 1 && pastedData[0].length === 1;
  const cellSelectionModel = apiRef.current.getCellSelectionModel();
  const selectedCellsArray = apiRef.current.getSelectedCellsAsArray();
  if (cellSelectionModel && selectedCellsArray.length > 1) {
    let lastRowId = selectedCellsArray[0].id;
    let rowIndex = 0;
    let colIndex = 0;
    selectedCellsArray.forEach(({
      id: rowId,
      field
    }) => {
      if (rowId !== lastRowId) {
        lastRowId = rowId;
        rowIndex += 1;
        colIndex = 0;
      }
      const rowDataArr = pastedData[isSingleValuePasted ? 0 : rowIndex];
      const hasRowData = isSingleValuePasted ? true : rowDataArr !== void 0;
      if (hasRowData) {
        const cellValue = isSingleValuePasted ? rowDataArr[0] : rowDataArr[colIndex];
        updateCell({
          rowId,
          field,
          pastedCellValue: cellValue
        });
      }
      colIndex += 1;
    });
    return;
  }
  const visibleColumnFields = gridVisibleColumnFieldsSelector(apiRef).filter((field) => {
    if (columnFieldsToExcludeFromPaste.includes(field)) {
      return false;
    }
    return true;
  });
  if (gridRowSelectionCountSelector(apiRef) > 0 && !isSingleValuePasted) {
    const pastedRowsDataCount = pastedData.length;
    const selectedRows = gridRowSelectionIdsSelector(apiRef);
    selectedRows.forEach((row, rowId) => {
      let rowData;
      if (pastedRowsDataCount === 1) {
        rowData = pastedData[0];
      } else {
        rowData = pastedData.shift();
      }
      if (rowData === void 0) {
        return;
      }
      rowData.forEach((newCellValue, cellIndex) => {
        updateCell({
          rowId,
          field: visibleColumnFields[cellIndex],
          pastedCellValue: newCellValue
        });
      });
    });
    return;
  }
  let selectedCell = gridFocusCellSelector(apiRef);
  if (!selectedCell && selectedCellsArray.length === 1) {
    selectedCell = selectedCellsArray[0];
  }
  if (!selectedCell) {
    return;
  }
  if (columnFieldsToExcludeFromPaste.includes(selectedCell.field)) {
    return;
  }
  const selectedRowId = selectedCell.id;
  const selectedRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(selectedRowId);
  const visibleRowIds = pagination && paginationMode === "client" ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);
  const selectedFieldIndex = visibleColumnFields.indexOf(selectedCell.field);
  pastedData.forEach((rowData, index) => {
    const rowId = visibleRowIds[selectedRowIndex + index];
    if (typeof rowId === "undefined") {
      return;
    }
    for (let i = selectedFieldIndex; i < visibleColumnFields.length; i += 1) {
      const field = visibleColumnFields[i];
      const stringValue = rowData[i - selectedFieldIndex];
      updateCell({
        rowId,
        field,
        pastedCellValue: stringValue
      });
    }
  });
}
var useGridClipboardImport = (apiRef, props) => {
  const processRowUpdate = props.processRowUpdate;
  const onProcessRowUpdateError = props.onProcessRowUpdateError;
  const getRowId = props.getRowId;
  const enableClipboardPaste = !props.disableClipboardPaste;
  const logger = useGridLogger(apiRef, "useGridClipboardImport");
  const {
    clipboardCopyCellDelimiter,
    splitClipboardPastedText,
    pagination,
    paginationMode,
    onBeforeClipboardPasteStart
  } = props;
  const handlePaste = React60.useCallback(async (params, event) => {
    var _a;
    if (!enableClipboardPaste) {
      return;
    }
    if (!isPasteShortcut(event)) {
      return;
    }
    const focusedCell = gridFocusCellSelector(apiRef);
    if (focusedCell !== null) {
      const cellMode = apiRef.current.getCellMode(focusedCell.id, focusedCell.field);
      if (cellMode === "edit") {
        return;
      }
    }
    const rootEl = (_a = apiRef.current.rootElementRef) == null ? void 0 : _a.current;
    if (!rootEl) {
      return;
    }
    const text = await getTextFromClipboard(rootEl);
    if (!text) {
      return;
    }
    const pastedData = splitClipboardPastedText(text, clipboardCopyCellDelimiter);
    if (!pastedData) {
      return;
    }
    if (onBeforeClipboardPasteStart) {
      try {
        await onBeforeClipboardPasteStart({
          data: pastedData
        });
      } catch (error) {
        logger.debug("Clipboard paste operation cancelled");
        return;
      }
    }
    const cellUpdater = new CellValueUpdater({
      apiRef,
      processRowUpdate,
      onProcessRowUpdateError,
      getRowId
    });
    apiRef.current.publishEvent("clipboardPasteStart", {
      data: pastedData
    });
    defaultPasteResolver({
      pastedData,
      apiRef: getPublicApiRef(apiRef),
      updateCell: (...args) => {
        cellUpdater.updateCell(...args);
      },
      pagination,
      paginationMode
    });
    cellUpdater.applyUpdates();
  }, [apiRef, processRowUpdate, onProcessRowUpdateError, getRowId, enableClipboardPaste, splitClipboardPastedText, clipboardCopyCellDelimiter, pagination, paginationMode, onBeforeClipboardPasteStart, logger]);
  const checkIfCanStartEditing = React60.useCallback((initialValue, {
    event
  }) => {
    if (isPasteShortcut(event) && enableClipboardPaste) {
      return false;
    }
    return initialValue;
  }, [enableClipboardPaste]);
  useGridEvent(apiRef, "cellKeyDown", handlePaste);
  useGridEventPriority(apiRef, "clipboardPasteStart", props.onClipboardPasteStart);
  useGridEventPriority(apiRef, "clipboardPasteEnd", props.onClipboardPasteEnd);
  useGridRegisterPipeProcessor(apiRef, "canStartEditing", checkIfCanStartEditing);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/pivoting/useGridPivoting.js
var React61 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/pivoting/utils.js
var columnGroupIdSeparator = ">->";
var isPivotingAvailable = (props) => {
  return !props.disablePivoting;
};
var defaultGetPivotDerivedColumns = (column, getLocaleText) => {
  if (column.type === "date") {
    const field = column.field;
    return [{
      // String column type to avoid formatting the value as 2,025 instead of 2025
      field: `${field}-year`,
      headerName: `${column.headerName} ${getLocaleText("pivotYearColumnHeaderName")}`,
      valueGetter: (value, row) => new Date(row[field]).getFullYear()
    }, {
      field: `${field}-quarter`,
      headerName: `${column.headerName} ${getLocaleText("pivotQuarterColumnHeaderName")}`,
      valueGetter: (value, row) => `Q${Math.floor(new Date(row[field]).getMonth() / 3) + 1}`
    }];
  }
  return void 0;
};
var getInitialColumns = (originalColumns, getPivotDerivedColumns, getLocaleText) => {
  const initialColumns = /* @__PURE__ */ new Map();
  for (let i = 0; i < originalColumns.length; i += 1) {
    const originalColumn = originalColumns[i];
    const column = _extends({}, getDefaultColTypeDef(originalColumn.type), originalColumn);
    const field = column.field;
    if (!isGroupingColumn(field)) {
      initialColumns.set(field, column);
      const derivedColumns = getPivotDerivedColumns == null ? void 0 : getPivotDerivedColumns(column, getLocaleText);
      if (derivedColumns) {
        derivedColumns.forEach((col) => initialColumns.set(col.field, col));
      }
    }
  }
  return initialColumns;
};
function sortColumnGroups(columnGroups, pivotModelColumns, depth = 0) {
  if (depth > pivotModelColumns.length - 1) {
    return;
  }
  const sort = pivotModelColumns[depth].sort;
  columnGroups.sort((a, b) => {
    if (isLeaf(a) || isLeaf(b)) {
      return 0;
    }
    if (a.children) {
      sortColumnGroups(a.children, pivotModelColumns, depth + 1);
    }
    if (b.children) {
      sortColumnGroups(b.children, pivotModelColumns, depth + 1);
    }
    if (sort === void 0) {
      return 0;
    }
    return (sort === "asc" ? 1 : -1) * gridStringOrNumberComparator(a.rawHeaderName, b.rawHeaderName, {}, {});
  });
}
var getPivotedData = ({
  rows,
  columns,
  pivotModel,
  apiRef,
  pivotingColDef,
  groupingColDef
}) => {
  const visibleColumns = pivotModel.columns.filter((column) => !column.hidden);
  const visibleRows = pivotModel.rows.filter((row) => !row.hidden);
  const visibleValues = pivotModel.values.filter((value) => !value.hidden);
  let pivotColumns = [];
  const columnVisibilityModel = {};
  const pivotColumnsIncludedInPivotValues = [];
  const initialColumns = /* @__PURE__ */ new Map();
  for (const column of columns.values()) {
    if (!isGroupingColumn(column.field)) {
      initialColumns.set(column.field, column);
      const pivotValueIndex = visibleValues.findIndex(({
        field
      }) => field === column.field);
      const isVisiblePivotValueField = pivotValueIndex !== -1;
      const columnToAdd = _extends({}, column, {
        aggregable: false,
        groupable: false,
        hideable: false,
        editable: false,
        disableReorder: true
      });
      if (isVisiblePivotValueField) {
        pivotColumnsIncludedInPivotValues[pivotValueIndex] = columnToAdd;
      } else {
        pivotColumns.push(columnToAdd);
      }
      columnVisibilityModel[column.field] = false;
    }
  }
  pivotColumns = pivotColumns.concat(pivotColumnsIncludedInPivotValues);
  const getAttributesFromInitialColumn = (field) => {
    const initialColumn = initialColumns.get(field);
    if (!initialColumn) {
      return void 0;
    }
    const attributes = {
      width: initialColumn.width,
      minWidth: initialColumn.minWidth,
      maxWidth: initialColumn.maxWidth,
      valueFormatter: initialColumn.valueFormatter,
      headerName: initialColumn.headerName,
      renderCell: initialColumn.renderCell,
      display: initialColumn.display
    };
    return attributes;
  };
  const aggregationModel = {};
  const columnGroupingModel = [];
  const columnGroupingModelLookup = /* @__PURE__ */ new Map();
  let newRows = [];
  if (visibleColumns.length === 0) {
    newRows = rows;
    visibleValues.forEach((pivotValue) => {
      aggregationModel[pivotValue.field] = pivotValue.aggFunc;
      delete columnVisibilityModel[pivotValue.field];
    });
  } else {
    for (let i = 0; i < rows.length; i += 1) {
      const row = rows[i];
      const newRow = _extends({}, row);
      const columnGroupPath = [];
      for (let j = 0; j < visibleColumns.length; j += 1) {
        const {
          field: colGroupField
        } = visibleColumns[j];
        const depth = j;
        const column = initialColumns.get(colGroupField);
        if (!column) {
          continue;
        }
        let colValue = apiRef.current.getRowValue(row, column) ?? "(No value)";
        if (column.type === "singleSelect") {
          const singleSelectColumn = column;
          if (singleSelectColumn.getOptionLabel) {
            colValue = singleSelectColumn.getOptionLabel(colValue);
          }
        }
        if (column.type !== "number") {
          colValue = String(colValue);
        }
        const formattedHeaderName = apiRef.current.getRowFormattedValue(row, column) || colValue;
        columnGroupPath.push(colValue);
        const groupId = columnGroupPath.join(columnGroupIdSeparator);
        if (!columnGroupingModelLookup.has(groupId)) {
          const columnGroup = {
            groupId,
            headerName: formattedHeaderName,
            rawHeaderName: colValue,
            children: []
          };
          columnGroupingModelLookup.set(groupId, columnGroup);
          if (depth === 0) {
            columnGroupingModel.push(columnGroup);
          } else {
            const parentGroupId = columnGroupPath.slice(0, -1).join(columnGroupIdSeparator);
            const parentGroup = columnGroupingModelLookup.get(parentGroupId);
            if (parentGroup) {
              parentGroup.children.push(columnGroup);
            }
          }
        }
        const isLastColumnGroupLevel = depth === visibleColumns.length - 1;
        if (isLastColumnGroupLevel) {
          visibleValues.forEach((pivotValue) => {
            const valueField = pivotValue.field;
            const originalColumn = initialColumns.get(valueField);
            if (!originalColumn) {
              return;
            }
            const valueKey = `${columnGroupPath.join(columnGroupIdSeparator)}${columnGroupIdSeparator}${valueField}`;
            newRow[valueKey] = apiRef.current.getRowValue(row, originalColumn);
          });
        }
      }
      newRows.push(newRow);
    }
    sortColumnGroups(columnGroupingModel, visibleColumns);
  }
  function createColumns(columnGroups, depth = 0) {
    for (let i = 0; i < columnGroups.length; i += 1) {
      const columnGroup = columnGroups[i];
      if (isLeaf(columnGroup)) {
        continue;
      }
      const isLastColumnGroupLevel = depth === visibleColumns.length - 1;
      if (isLastColumnGroupLevel) {
        if (visibleValues.length === 0) {
          const emptyColumnField = `${columnGroup.groupId}${columnGroupIdSeparator}empty`;
          const emptyColumn = {
            field: emptyColumnField,
            headerName: "",
            sortable: false,
            filterable: false,
            groupable: false,
            aggregable: false,
            hideable: false,
            disableColumnMenu: true
          };
          pivotColumns.push(emptyColumn);
          if (columnGroup) {
            columnGroup.children.push({
              field: emptyColumnField
            });
          }
        } else {
          visibleValues.forEach((pivotValue) => {
            const valueField = pivotValue.field;
            const mapValueKey = `${columnGroup.groupId}${columnGroupIdSeparator}${valueField}`;
            const overrides = typeof pivotingColDef === "function" ? pivotingColDef(valueField, columnGroup.groupId.split(columnGroupIdSeparator)) : pivotingColDef;
            const column = _extends({
              headerName: String(valueField)
            }, getAttributesFromInitialColumn(pivotValue.field), overrides, {
              field: mapValueKey,
              aggregable: false,
              groupable: false,
              filterable: false,
              hideable: false,
              editable: false,
              disableReorder: true,
              availableAggregationFunctions: [pivotValue.aggFunc]
            });
            pivotColumns.push(column);
            aggregationModel[mapValueKey] = pivotValue.aggFunc;
            if (columnGroup) {
              columnGroup.children.push({
                field: mapValueKey
              });
            }
          });
        }
      } else {
        createColumns(columnGroup.children, depth + 1);
      }
    }
  }
  createColumns(columnGroupingModel);
  const groupingColDefOverrides = (params) => _extends({}, typeof groupingColDef === "function" ? groupingColDef(params) : groupingColDef || {}, {
    filterable: false,
    aggregable: false,
    hideable: false
  });
  return {
    rows: visibleRows.length > 0 ? newRows : [],
    columns: pivotColumns,
    rowGroupingModel: visibleRows.map((row) => row.field),
    aggregationModel,
    getAggregationPosition: defaultGetAggregationPosition,
    columnVisibilityModel,
    columnGroupingModel,
    groupingColDef: groupingColDefOverrides,
    headerFilters: false,
    disableAggregation: false,
    disableRowGrouping: false
  };
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/pivoting/useGridPivoting.js
var import_jsx_runtime44 = __toESM(require_jsx_runtime(), 1);
var emptyPivotModel = {
  rows: [],
  columns: [],
  values: []
};
var pivotingStateInitializer = (state, props, apiRef) => {
  var _a, _b, _c, _d, _e, _f;
  apiRef.current.caches.pivoting = {
    exportedStateRef: {
      current: null
    },
    nonPivotDataRef: {
      current: void 0
    }
  };
  if (!isPivotingAvailable(props)) {
    return _extends({}, state, {
      pivoting: {
        active: false,
        model: emptyPivotModel
      }
    });
  }
  const initialColumns = getInitialColumns(props.columns ?? [], props.getPivotDerivedColumns, apiRef.current.getLocaleText);
  const open = props.pivotPanelOpen ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.pivoting) == null ? void 0 : _b.panelOpen) ?? false;
  const sidebarStateUpdate = open ? {
    open,
    value: GridSidebarValue.Pivot
  } : {};
  return _extends({}, state, {
    pivoting: {
      active: props.pivotActive ?? ((_d = (_c = props.initialState) == null ? void 0 : _c.pivoting) == null ? void 0 : _d.enabled) ?? false,
      model: props.pivotModel ?? ((_f = (_e = props.initialState) == null ? void 0 : _e.pivoting) == null ? void 0 : _f.model) ?? emptyPivotModel,
      initialColumns
    },
    sidebar: _extends({}, state.sidebar, sidebarStateUpdate)
  });
};
var useGridPivoting = (apiRef, props, originalColumnsProp, originalRowsProp) => {
  const isPivotActive = useGridSelector(apiRef, gridPivotActiveSelector);
  const {
    exportedStateRef,
    nonPivotDataRef
  } = apiRef.current.caches.pivoting;
  const isPivotingAvailable2 = isPivotingAvailable(props);
  apiRef.current.registerControlState({
    stateId: "pivotModel",
    propModel: props.pivotModel,
    propOnChange: props.onPivotModelChange,
    stateSelector: gridPivotModelSelector,
    changeEvent: "pivotModelChange"
  });
  apiRef.current.registerControlState({
    stateId: "pivotMode",
    propModel: props.pivotActive,
    propOnChange: props.onPivotActiveChange,
    stateSelector: gridPivotActiveSelector,
    changeEvent: "pivotModeChange"
  });
  apiRef.current.registerControlState({
    stateId: "pivotPanelOpen",
    propModel: props.pivotPanelOpen,
    propOnChange: props.onPivotPanelOpenChange,
    stateSelector: gridPivotPanelOpenSelector,
    changeEvent: "pivotPanelOpenChange"
  });
  const getInitialData = React61.useCallback(() => {
    if (!exportedStateRef.current) {
      exportedStateRef.current = apiRef.current.exportState();
    }
    const rowIds = gridDataRowIdsSelector(apiRef);
    const rowsLookup = gridRowsLookupSelector(apiRef);
    const rows = rowIds.map((id) => rowsLookup[id]);
    const initialColumns = getInitialColumns(originalColumnsProp, props.getPivotDerivedColumns, apiRef.current.getLocaleText);
    return {
      rows,
      columns: initialColumns,
      originalRowsProp
    };
  }, [apiRef, props.getPivotDerivedColumns, originalColumnsProp, originalRowsProp, exportedStateRef]);
  const computePivotingState = React61.useCallback(({
    active,
    model: pivotModel
  }) => {
    if (active && pivotModel) {
      const {
        rows,
        columns
      } = nonPivotDataRef.current || {
        rows: [],
        columns: /* @__PURE__ */ new Map()
      };
      return {
        initialColumns: columns,
        // TODO: fix getPivotedData called twice in controlled mode
        propsOverrides: getPivotedData({
          rows,
          columns,
          pivotModel,
          apiRef,
          pivotingColDef: props.pivotingColDef,
          groupingColDef: props.groupingColDef
        })
      };
    }
    return void 0;
  }, [apiRef, props.pivotingColDef, props.groupingColDef, nonPivotDataRef]);
  useOnMount2(() => {
    if (!isPivotingAvailable2 || !isPivotActive) {
      return void 0;
    }
    nonPivotDataRef.current = getInitialData();
    const isLoading = gridRowsLoadingSelector(apiRef) ?? false;
    if (isLoading) {
      return void 0;
    }
    apiRef.current.setState((state) => {
      const pivotingState = _extends({}, state.pivoting, computePivotingState(state.pivoting));
      return _extends({}, state, {
        pivoting: pivotingState
      });
    });
    return void 0;
  });
  useEnhancedEffect_default(() => {
    if (!isPivotingAvailable2 || !isPivotActive) {
      if (nonPivotDataRef.current) {
        apiRef.current.caches.rows.rowsBeforePartialUpdates = nonPivotDataRef.current.originalRowsProp;
        apiRef.current.setRows(nonPivotDataRef.current.rows);
        nonPivotDataRef.current = void 0;
      }
      if (exportedStateRef.current) {
        apiRef.current.restoreState(exportedStateRef.current);
        exportedStateRef.current = null;
      }
    }
  }, [isPivotActive, apiRef, isPivotingAvailable2, nonPivotDataRef, exportedStateRef]);
  const setPivotModel = React61.useCallback((callback) => {
    if (!isPivotingAvailable2) {
      return;
    }
    apiRef.current.setState((state) => {
      var _a, _b;
      const newPivotModel = typeof callback === "function" ? callback((_a = state.pivoting) == null ? void 0 : _a.model) : callback;
      if (((_b = state.pivoting) == null ? void 0 : _b.model) === newPivotModel) {
        return state;
      }
      const newPivotingState = _extends({}, state.pivoting, computePivotingState(_extends({}, state.pivoting, {
        model: newPivotModel
      })), {
        model: newPivotModel
      });
      return _extends({}, state, {
        pivoting: newPivotingState
      });
    });
  }, [apiRef, computePivotingState, isPivotingAvailable2]);
  const updatePivotModel = React61.useCallback(({
    field,
    targetSection,
    originSection,
    targetField,
    targetFieldPosition
  }) => {
    if (field === targetField) {
      return;
    }
    apiRef.current.setPivotModel((prev) => {
      var _a, _b, _c;
      const newModel = _extends({}, prev);
      const isSameSection = targetSection === originSection;
      const hidden = originSection === null ? false : ((_a = prev[originSection].find((item) => item.field === field)) == null ? void 0 : _a.hidden) ?? false;
      if (targetSection) {
        const newSectionArray = [...prev[targetSection]];
        let toIndex = newSectionArray.length;
        if (targetField) {
          const fromIndex = newSectionArray.findIndex((item) => item.field === field);
          if (fromIndex > -1) {
            newSectionArray.splice(fromIndex, 1);
          }
          toIndex = newSectionArray.findIndex((item) => item.field === targetField);
          if (targetFieldPosition === "bottom") {
            toIndex += 1;
          }
        }
        if (targetSection === "values") {
          const initialColumns = gridPivotInitialColumnsSelector(apiRef);
          const aggFunc = isSameSection ? (_b = prev.values.find((item) => item.field === field)) == null ? void 0 : _b.aggFunc : getAvailableAggregationFunctions({
            aggregationFunctions: props.aggregationFunctions,
            colDef: initialColumns.get(field),
            isDataSource: false
          })[0];
          newSectionArray.splice(toIndex, 0, {
            field,
            aggFunc,
            hidden
          });
          newModel.values = newSectionArray;
        } else if (targetSection === "columns") {
          const sort = isSameSection ? (_c = prev.columns.find((item) => item.field === field)) == null ? void 0 : _c.sort : void 0;
          newSectionArray.splice(toIndex, 0, {
            field,
            sort,
            hidden
          });
          newModel.columns = newSectionArray;
        } else if (targetSection === "rows") {
          newSectionArray.splice(toIndex, 0, {
            field,
            hidden
          });
          newModel.rows = newSectionArray;
        }
      }
      if (!isSameSection && originSection) {
        newModel[originSection] = prev[originSection].filter((f) => f.field !== field);
      }
      return newModel;
    });
  }, [apiRef, props.aggregationFunctions]);
  const setPivotActive = React61.useCallback((callback) => {
    if (!isPivotingAvailable2) {
      return;
    }
    apiRef.current.setState((state) => {
      var _a, _b;
      const newPivotMode = typeof callback === "function" ? callback((_a = state.pivoting) == null ? void 0 : _a.active) : callback;
      if (((_b = state.pivoting) == null ? void 0 : _b.active) === newPivotMode) {
        return state;
      }
      if (newPivotMode) {
        nonPivotDataRef.current = getInitialData();
      }
      const newPivotingState = _extends({}, state.pivoting, computePivotingState(_extends({}, state.pivoting, {
        active: newPivotMode
      })), {
        active: newPivotMode
      });
      const newState = _extends({}, state, {
        pivoting: newPivotingState
      });
      return newState;
    });
    apiRef.current.selectRows([], false, true);
  }, [apiRef, computePivotingState, getInitialData, isPivotingAvailable2, nonPivotDataRef]);
  const setPivotPanelOpen = React61.useCallback((callback) => {
    if (!isPivotingAvailable2) {
      return;
    }
    const panelOpen = gridPivotPanelOpenSelector(apiRef);
    const newPanelOpen = typeof callback === "function" ? callback(panelOpen) : callback;
    if (panelOpen === newPanelOpen) {
      return;
    }
    if (newPanelOpen) {
      apiRef.current.showSidebar(GridSidebarValue.Pivot);
    } else {
      apiRef.current.hideSidebar();
    }
  }, [apiRef, isPivotingAvailable2]);
  const addColumnMenuButton = React61.useCallback((menuItems) => {
    if (isPivotingAvailable2) {
      return [...menuItems, "columnMenuPivotItem"];
    }
    return menuItems;
  }, [isPivotingAvailable2]);
  useGridRegisterPipeProcessor(apiRef, "columnMenu", addColumnMenuButton);
  const updateNonPivotColumns = React61.useCallback((columns, keepPreviousColumns = true) => {
    if (!nonPivotDataRef.current || !isPivotingAvailable2) {
      return;
    }
    if (keepPreviousColumns) {
      getInitialColumns(columns, props.getPivotDerivedColumns, apiRef.current.getLocaleText).forEach((col) => {
        nonPivotDataRef.current.columns.set(col.field, col);
      });
    } else {
      nonPivotDataRef.current.columns = getInitialColumns(columns, props.getPivotDerivedColumns, apiRef.current.getLocaleText);
    }
    apiRef.current.setState((state) => {
      var _a;
      return _extends({}, state, {
        pivoting: _extends({}, state.pivoting, computePivotingState(state.pivoting), {
          initialColumns: (_a = nonPivotDataRef.current) == null ? void 0 : _a.columns
        })
      });
    });
  }, [isPivotingAvailable2, apiRef, props.getPivotDerivedColumns, computePivotingState, nonPivotDataRef]);
  const updateNonPivotRows = React61.useCallback((rows, keepPreviousRows = true) => {
    if (!nonPivotDataRef.current || !isPivotingAvailable2 || !rows || rows.length === 0) {
      return;
    }
    if (keepPreviousRows) {
      const rowsMap = /* @__PURE__ */ new Map();
      nonPivotDataRef.current.rows.forEach((row) => {
        rowsMap.set(gridRowIdSelector(apiRef, row), row);
      });
      rows.forEach((row) => {
        const rowId = gridRowIdSelector(apiRef, row);
        if (row._action === "delete") {
          rowsMap.delete(rowId);
        } else {
          rowsMap.set(rowId, row);
        }
      });
      nonPivotDataRef.current.rows = Array.from(rowsMap.values());
    } else {
      nonPivotDataRef.current.rows = rows;
    }
    apiRef.current.setState((state) => {
      return _extends({}, state, {
        pivoting: _extends({}, state.pivoting, computePivotingState(state.pivoting))
      });
    });
  }, [apiRef, computePivotingState, isPivotingAvailable2, nonPivotDataRef]);
  const addPivotingPanel = React61.useCallback((initialValue, value) => {
    if (isPivotingAvailable2 && value === GridSidebarValue.Pivot) {
      return (0, import_jsx_runtime44.jsx)(GridPivotPanel, {});
    }
    return initialValue;
  }, [isPivotingAvailable2]);
  useGridRegisterPipeProcessor(apiRef, "sidebar", addPivotingPanel);
  useGridApiMethod(apiRef, {
    setPivotModel,
    setPivotActive,
    setPivotPanelOpen
  }, "public");
  useGridApiMethod(apiRef, {
    updatePivotModel,
    updateNonPivotColumns,
    updateNonPivotRows
  }, "private");
  useEnhancedEffect_default(() => {
    apiRef.current.updateNonPivotColumns(originalColumnsProp, false);
  }, [originalColumnsProp, apiRef]);
  useEnhancedEffect_default(() => {
    apiRef.current.updateNonPivotRows(originalRowsProp, false);
    if (nonPivotDataRef.current) {
      nonPivotDataRef.current.originalRowsProp = originalRowsProp;
    }
  }, [originalRowsProp, apiRef, nonPivotDataRef]);
  useEnhancedEffect_default(() => {
    if (props.pivotModel !== void 0) {
      apiRef.current.setPivotModel(props.pivotModel);
    }
  }, [apiRef, props.pivotModel]);
  useEnhancedEffect_default(() => {
    if (props.pivotActive !== void 0) {
      apiRef.current.setPivotActive(props.pivotActive);
    }
  }, [apiRef, props.pivotActive]);
  useEnhancedEffect_default(() => {
    if (props.pivotPanelOpen !== void 0) {
      apiRef.current.setPivotPanelOpen(props.pivotPanelOpen);
    }
  }, [apiRef, props.pivotPanelOpen]);
};
var useGridPivotingExportState = (apiRef) => {
  const stateExportPreProcessing = React61.useCallback((state) => {
    const isPivotActive = gridPivotActiveSelector(apiRef);
    if (!isPivotActive) {
      return state;
    }
    const newState = _extends({}, state, apiRef.current.caches.pivoting.exportedStateRef.current, {
      sorting: state.sorting
    });
    return newState;
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "exportState", stateExportPreProcessing);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aiAssistant/useGridAiAssistant.js
var React62 = __toESM(require_react(), 1);
var import_jsx_runtime45 = __toESM(require_jsx_runtime(), 1);
var DEFAULT_SAMPLE_COUNT = 5;
var aiAssistantStateInitializer = (state, props) => {
  var _a, _b;
  if (!props.aiAssistant) {
    return _extends({}, state, {
      aiAssistant: {
        activeConversationIndex: 0,
        conversations: []
      }
    });
  }
  return _extends({}, state, {
    aiAssistant: {
      activeConversationIndex: 0,
      conversations: props.aiAssistantConversations ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.aiAssistant) == null ? void 0 : _b.conversations) ?? []
    }
  });
};
var useGridAiAssistant = (apiRef, props) => {
  const {
    onPrompt,
    allowAiAssistantDataSampling,
    slots,
    disableColumnFilter,
    disableRowGrouping,
    disableAggregation,
    disableColumnSorting,
    disablePivoting
  } = props;
  const columnsLookup = gridColumnLookupSelector(apiRef);
  const columns = Object.values(columnsLookup);
  const rows = Object.values(gridRowsLookupSelector(apiRef));
  const isAiAssistantAvailable = !!props.aiAssistant;
  apiRef.current.registerControlState({
    stateId: "aiAssistantConversations",
    propModel: props.aiAssistantConversations,
    propOnChange: props.onAiAssistantConversationsChange,
    stateSelector: gridAiAssistantConversationsSelector,
    changeEvent: "aiAssistantConversationsChange"
  });
  apiRef.current.registerControlState({
    stateId: "aiAssistantActiveConversationIndex",
    propModel: props.aiAssistantActiveConversationIndex,
    propOnChange: props.onAiAssistantActiveConversationIndexChange,
    stateSelector: gridAiAssistantActiveConversationIndexSelector,
    changeEvent: "aiAssistantActiveConversationIndexChange"
  });
  const preferencePanelPreProcessing = React62.useCallback((initialValue, value) => {
    if (isAiAssistantAvailable && slots.aiAssistantPanel && value === GridPreferencePanelsValue.aiAssistant) {
      return (0, import_jsx_runtime45.jsx)(slots.aiAssistantPanel, {});
    }
    return initialValue;
  }, [isAiAssistantAvailable, slots]);
  const collectSampleData = React62.useCallback(() => {
    const columnExamples = {};
    columns.forEach((column) => {
      columnExamples[column.field] = Array.from({
        length: Math.min(DEFAULT_SAMPLE_COUNT, rows.length)
      }).map(() => {
        const row = rows[Math.floor(Math.random() * rows.length)];
        if (column.valueGetter) {
          return column.valueGetter(row[column.field], row, column, apiRef);
        }
        return row[column.field];
      });
    });
    return columnExamples;
  }, [apiRef, columns, rows]);
  const getPromptContext = React62.useCallback((allowDataSampling = false) => {
    if (!isAiAssistantAvailable) {
      return "";
    }
    const examples = allowDataSampling ? collectSampleData() : {};
    const columnsContext = columns.map((column) => {
      var _a;
      return {
        field: column.field,
        description: column.description ?? null,
        examples: examples[column.field] ?? column.examples ?? [],
        type: column.type ?? "string",
        allowedOperators: ((_a = column.filterOperators) == null ? void 0 : _a.map((operator) => operator.value)) ?? []
      };
    });
    return JSON.stringify(columnsContext);
  }, [columns, collectSampleData, isAiAssistantAvailable]);
  const applyPromptResult = React62.useCallback((result) => {
    if (!isAiAssistantAvailable) {
      return;
    }
    const interestColumns = [];
    if (!disableColumnFilter) {
      apiRef.current.setFilterModel({
        items: result.filters.map((filter, index) => {
          const item = {
            id: index,
            field: filter.column,
            operator: filter.operator,
            value: filter.value
          };
          const column = columnsLookup[filter.column];
          if (column.type === "singleSelect") {
            const options2 = getValueOptions(column) ?? [];
            const found = options2.find((option) => typeof option === "object" && option.label === filter.value);
            if (found) {
              item.value = found.value;
            }
          }
          return item;
        }),
        logicOperator: result.filterOperator ?? GridLogicOperator.And,
        quickFilterValues: []
      });
      interestColumns.push(...result.filters.map((f) => f.column));
    }
    let appliedPivoting = false;
    if (!disablePivoting && "columns" in result.pivoting) {
      apiRef.current.setPivotActive(true);
      apiRef.current.setPivotModel({
        columns: result.pivoting.columns.map((c) => ({
          field: c.column,
          sort: c.direction
        })),
        rows: result.pivoting.rows.map((r) => ({
          field: r
        })),
        values: result.pivoting.values.map((valueObj) => {
          const [field] = Object.keys(valueObj);
          return {
            field,
            aggFunc: valueObj[field]
          };
        })
      });
      appliedPivoting = true;
    } else if ("columns" in result.pivoting) {
      result.pivoting.columns.forEach((c) => {
        result.grouping.push({
          column: c.column
        });
      });
      result.pivoting.rows.forEach((r) => {
        result.grouping.push({
          column: r
        });
      });
      result.pivoting.values.forEach((valueObj) => {
        const [field] = Object.keys(valueObj);
        result.aggregation[field] = valueObj[field];
      });
      result.pivoting = {};
    }
    if (!disableRowGrouping && !appliedPivoting) {
      apiRef.current.setRowGroupingModel(result.grouping.map((g) => g.column));
    }
    if (!disableAggregation && !appliedPivoting) {
      apiRef.current.setAggregationModel(result.aggregation);
      interestColumns.push(...Object.keys(result.aggregation));
    }
    if (!disableColumnSorting) {
      apiRef.current.setSortModel(result.sorting.map((s) => ({
        field: s.column,
        sort: s.direction
      })));
    }
    const visibleRowsData = getVisibleRows(apiRef);
    const rowSelectionModel = {
      type: "include",
      ids: /* @__PURE__ */ new Set()
    };
    if (result.select !== -1) {
      for (let i = 0; i < result.select; i += 1) {
        const row = visibleRowsData.rows[i];
        const id = apiRef.current.getRowId(row);
        rowSelectionModel.ids.add(id);
      }
    }
    apiRef.current.setRowSelectionModel(rowSelectionModel);
    const targetIndex = Number(columnsLookup[GRID_CHECKBOX_SELECTION_FIELD] !== void 0) + Number(result.grouping.length);
    interestColumns.reverse().forEach((c) => apiRef.current.setColumnIndex(c, targetIndex));
  }, [apiRef, disableColumnFilter, disableRowGrouping, disableAggregation, disableColumnSorting, disablePivoting, columnsLookup, isAiAssistantAvailable]);
  const setActiveConversationId = React62.useCallback((id) => {
    if (!isAiAssistantAvailable) {
      return;
    }
    const conversations = gridAiAssistantConversationsSelector(apiRef);
    const activeConversationIndex = gridAiAssistantActiveConversationIndexSelector(apiRef);
    if (!conversations[activeConversationIndex]) {
      return;
    }
    conversations[activeConversationIndex].id = id;
    apiRef.current.setState((state) => _extends({}, state, {
      aiAssistant: _extends({}, state.aiAssistant, {
        conversations
      })
    }));
  }, [apiRef, isAiAssistantAvailable]);
  const setConversationPrompts = React62.useCallback((index, callback) => {
    if (!isAiAssistantAvailable) {
      return;
    }
    const currentConversations = gridAiAssistantConversationsSelector(apiRef);
    const targetConversation = currentConversations[index];
    const newPrompts = typeof callback === "function" ? callback(targetConversation === void 0 ? [] : targetConversation.prompts) : callback;
    const newConversations = currentConversations.toSpliced(targetConversation === void 0 ? currentConversations.length : index, 1, _extends({}, targetConversation, {
      title: newPrompts[newPrompts.length - 1].value,
      // TODO: make the title configurable
      prompts: newPrompts
    }));
    apiRef.current.setState((state) => _extends({}, state, {
      aiAssistant: _extends({}, state.aiAssistant, {
        conversations: newConversations
      })
    }));
  }, [apiRef, isAiAssistantAvailable]);
  const processPrompt = React62.useCallback(async (value) => {
    if (!onPrompt) {
      return void 0;
    }
    const activeConversationIndex = gridAiAssistantActiveConversationIndexSelector(apiRef);
    const activeConversation = gridAiAssistantActiveConversationSelector(apiRef);
    const date = Date.now();
    apiRef.current.setLoading(true);
    setConversationPrompts(activeConversationIndex, (prevPrompts) => [...prevPrompts, {
      value,
      createdAt: new Date(date),
      variant: "processing",
      helperText: apiRef.current.getLocaleText("promptProcessing")
    }]);
    try {
      const response = await onPrompt(value, getPromptContext(allowAiAssistantDataSampling), activeConversation == null ? void 0 : activeConversation.id);
      applyPromptResult(response);
      setActiveConversationId(response.conversationId);
      setConversationPrompts(activeConversationIndex, (prevPrompts) => prevPrompts.map((item) => item.createdAt.getTime() === date ? _extends({}, item, {
        response,
        variant: "success",
        helperText: ""
      }) : item));
      return response;
    } catch (error) {
      setConversationPrompts(activeConversationIndex, (prevPrompts) => prevPrompts.map((item) => item.createdAt.getTime() === date ? _extends({}, item, {
        variant: "error",
        helperText: error.message
      }) : item));
      return error;
    } finally {
      apiRef.current.setLoading(false);
    }
  }, [apiRef, allowAiAssistantDataSampling, onPrompt, getPromptContext, applyPromptResult, setConversationPrompts, setActiveConversationId]);
  const setActiveConversationIndex = React62.useCallback((index) => {
    apiRef.current.setState((state) => _extends({}, state, {
      aiAssistant: _extends({}, state.aiAssistant, {
        activeConversationIndex: index
      })
    }));
    const conversation = gridAiAssistantActiveConversationSelector(apiRef);
    if (!conversation) {
      throw new Error("Conversation not found");
    }
    return conversation;
  }, [apiRef]);
  const setConversations = React62.useCallback((callback) => {
    if (!isAiAssistantAvailable) {
      return;
    }
    apiRef.current.setState((state) => {
      var _a;
      return _extends({}, state, {
        aiAssistant: _extends({}, state.aiAssistant, {
          conversations: typeof callback === "function" ? callback((_a = state.aiAssistant) == null ? void 0 : _a.conversations) : callback
        })
      });
    });
  }, [apiRef, isAiAssistantAvailable]);
  React62.useEffect(() => {
    if (props.aiAssistantConversations) {
      setConversations(props.aiAssistantConversations);
    }
  }, [apiRef, props.aiAssistantConversations, setConversations]);
  React62.useEffect(() => {
    if (props.aiAssistantActiveConversationIndex) {
      setActiveConversationIndex(props.aiAssistantActiveConversationIndex);
    }
  }, [apiRef, props.aiAssistantActiveConversationIndex, setActiveConversationIndex]);
  useGridRegisterPipeProcessor(apiRef, "preferencePanel", preferencePanelPreProcessing);
  useGridApiMethod(apiRef, {
    aiAssistant: {
      processPrompt,
      setConversations,
      setActiveConversationIndex
    }
  }, "public");
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/sidebar/useGridSidebar.js
var React63 = __toESM(require_react(), 1);
var sidebarStateInitializer = (state, props) => {
  var _a;
  return _extends({}, state, {
    sidebar: ((_a = props.initialState) == null ? void 0 : _a.sidebar) ?? {
      open: false
    }
  });
};
var useGridSidebar = (apiRef, props) => {
  var _a;
  const hideSidebar = React63.useCallback(() => {
    apiRef.current.setState((state) => {
      if (!state.sidebar.open || !state.sidebar.value) {
        return state;
      }
      apiRef.current.publishEvent("sidebarClose", {
        value: state.sidebar.value
      });
      return _extends({}, state, {
        sidebar: {
          open: false
        }
      });
    });
  }, [apiRef]);
  const showSidebar = React63.useCallback((newValue, sidebarId, labelId) => {
    apiRef.current.setState((state) => _extends({}, state, {
      sidebar: _extends({}, state.sidebar, {
        open: true,
        value: newValue,
        sidebarId,
        labelId
      })
    }));
    apiRef.current.publishEvent("sidebarOpen", {
      value: newValue
    });
  }, [apiRef]);
  useGridApiMethod(apiRef, {
    showSidebar,
    hideSidebar
  }, "public");
  const stateExportPreProcessing = React63.useCallback((prevState, context) => {
    var _a2;
    const sidebarToExport = gridSidebarStateSelector(apiRef);
    const shouldExportSidebar = (
      // Always export if the `exportOnlyDirtyModels` property is not activated
      !context.exportOnlyDirtyModels || // Always export if the sidebar was initialized
      ((_a2 = props.initialState) == null ? void 0 : _a2.sidebar) != null || // Always export if the sidebar is opened
      sidebarToExport.open
    );
    if (!shouldExportSidebar) {
      return prevState;
    }
    return _extends({}, prevState, {
      sidebar: sidebarToExport
    });
  }, [apiRef, (_a = props.initialState) == null ? void 0 : _a.sidebar]);
  const stateRestorePreProcessing = React63.useCallback((params, context) => {
    const sidebar = context.stateToRestore.sidebar;
    if (sidebar != null) {
      apiRef.current.setState((state) => _extends({}, state, {
        sidebar
      }));
    }
    return params;
  }, [apiRef]);
  useGridRegisterPipeProcessor(apiRef, "exportState", stateExportPreProcessing);
  useGridRegisterPipeProcessor(apiRef, "restoreState", stateRestorePreProcessing);
  useGridEventPriority(apiRef, "sidebarClose", props.onSidebarClose);
  useGridEventPriority(apiRef, "sidebarOpen", props.onSidebarOpen);
};

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/useDataGridPremiumComponent.js
var useDataGridPremiumComponent = (apiRef, inProps, configuration2) => {
  const pivotPropsOverrides = useGridSelector(apiRef, gridPivotPropsOverridesSelector);
  const props = React64.useMemo(() => {
    if (pivotPropsOverrides) {
      return _extends({}, inProps, pivotPropsOverrides, {
        initialState: _extends({}, inProps.initialState, {
          columns: void 0
        })
      });
    }
    return inProps;
  }, [inProps, pivotPropsOverrides]);
  useGridInitialization(apiRef, props);
  const key = pivotPropsOverrides ? "pivoting" : void 0;
  useGridDetailPanelPreProcessors(apiRef, props);
  useGridRowGroupingPreProcessors(apiRef, props);
  useGridDataSourceRowGroupingPreProcessors(apiRef, props);
  useGridTreeDataPreProcessors(apiRef, props);
  useGridDataSourceTreeDataPreProcessors(apiRef, props);
  useGridRowSelectionPreProcessors(apiRef, props);
  useGridLazyLoaderPreProcessors(apiRef, props);
  useGridRowPinningPreProcessors(apiRef);
  useGridAggregationPreProcessors(apiRef, props);
  useGridRowReorderPreProcessors(apiRef, props);
  useGridColumnPinningPreProcessors(apiRef, props);
  useGridRowsPreProcessors(apiRef);
  useGridInitializeState(propsStateInitializer, apiRef, props);
  useGridInitializeState(headerFilteringStateInitializer, apiRef, props, key);
  useGridInitializeState(rowGroupingStateInitializer, apiRef, props, key);
  useGridInitializeState(aggregationStateInitializer, apiRef, props, key);
  useGridInitializeState(rowSelectionStateInitializer, apiRef, props);
  useGridInitializeState(rowReorderStateInitializer, apiRef, props);
  useGridInitializeState(cellSelectionStateInitializer, apiRef, props);
  useGridInitializeState(detailPanelStateInitializer, apiRef, props);
  useGridInitializeState(columnPinningStateInitializer, apiRef, props, key);
  useGridInitializeState(columnsStateInitializer, apiRef, props, key);
  useGridInitializeState(sidebarStateInitializer, apiRef, props);
  useGridInitializeState(pivotingStateInitializer, apiRef, props);
  useGridInitializeState(rowPinningStateInitializer, apiRef, props);
  useGridInitializeState(rowsStateInitializer, apiRef, props);
  useGridInitializeState(paginationStateInitializer, apiRef, props);
  useGridInitializeState(editingStateInitializer, apiRef, props);
  useGridInitializeState(focusStateInitializer, apiRef, props);
  useGridInitializeState(sortingStateInitializer, apiRef, props);
  useGridInitializeState(preferencePanelStateInitializer, apiRef, props);
  useGridInitializeState(filterStateInitializer, apiRef, props);
  useGridInitializeState(rowSpanningStateInitializer, apiRef, props);
  useGridInitializeState(densityStateInitializer, apiRef, props);
  useGridInitializeState(columnReorderStateInitializer, apiRef, props);
  useGridInitializeState(columnResizeStateInitializer, apiRef, props);
  useGridInitializeState(columnMenuStateInitializer, apiRef, props);
  useGridInitializeState(columnGroupsStateInitializer, apiRef, props, key);
  useGridInitializeState(virtualizationStateInitializer, apiRef, props);
  useGridInitializeState(dataSourceStateInitializer, apiRef, props);
  useGridInitializeState(dimensionsStateInitializer, apiRef, props);
  useGridInitializeState(rowsMetaStateInitializer, apiRef, props);
  useGridInitializeState(listViewStateInitializer, apiRef, props);
  useGridInitializeState(aiAssistantStateInitializer, apiRef, props);
  useGridVirtualizer(apiRef, props);
  useGridSidebar(apiRef, props);
  useGridPivoting(apiRef, props, inProps.columns, inProps.rows);
  useGridRowGrouping(apiRef, props);
  useGridHeaderFiltering(apiRef, props);
  useGridTreeData(apiRef, props);
  useGridAggregation(apiRef, props);
  useGridKeyboardNavigation(apiRef, props);
  useGridRowSelection(apiRef, props);
  useGridCellSelection(apiRef, props);
  useGridColumnPinning(apiRef, props);
  useGridRowPinning(apiRef, props);
  useGridColumns(apiRef, props);
  useGridRows(apiRef, props, configuration2);
  useGridRowSpanning(apiRef, props);
  useGridParamsApi(apiRef, props);
  useGridDetailPanel(apiRef, props);
  useGridColumnSpanning(apiRef);
  useGridColumnGrouping(apiRef, props);
  useGridClipboardImport(apiRef, props);
  useGridEditing(apiRef, props);
  useGridFocus(apiRef, props);
  useGridPreferencesPanel(apiRef, props);
  useGridFilter(apiRef, props);
  useGridSorting(apiRef, props);
  useGridDensity(apiRef, props);
  useGridColumnReorder(apiRef, props);
  useGridColumnResize(apiRef, props);
  useGridPagination(apiRef, props);
  useGridRowsMeta(apiRef, props);
  useGridRowReorder(apiRef, props);
  useGridScroll(apiRef, props);
  useGridInfiniteLoader(apiRef, props);
  useGridLazyLoader(apiRef, props);
  useGridDataSourceLazyLoader(apiRef, props);
  useGridInfiniteLoadingIntersection(apiRef, props);
  useGridColumnMenu(apiRef);
  useGridCsvExport(apiRef, props);
  useGridPrintExport(apiRef, props);
  useGridExcelExport(apiRef, props);
  useGridClipboard(apiRef, props);
  useGridDimensions(apiRef, props);
  useGridEvents(apiRef, props);
  useGridStatePersistence(apiRef);
  useGridDataSourcePremium(apiRef, props);
  useGridVirtualization(apiRef, props);
  useGridListView(apiRef, props);
  useGridAiAssistant(apiRef, props);
  useGridPivotingExportState(apiRef);
  React64.useEffect(() => {
    apiRef.current.runAppliersForPendingProcessors();
  });
  return props;
};

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/useDataGridPremiumProps.js
var React69 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aggregation/gridAggregationFunctions.js
var sumAgg = {
  apply: ({
    values
  }) => {
    let sum = 0;
    for (let i = 0; i < values.length; i += 1) {
      const value = values[i];
      if (typeof value === "number" && !Number.isNaN(value)) {
        sum += value;
      }
    }
    return sum;
  },
  columnTypes: ["number"]
};
var avgAgg = {
  apply: ({
    values
  }) => {
    if (values.length === 0) {
      return null;
    }
    let sum = 0;
    let valuesCount = 0;
    for (let i = 0; i < values.length; i += 1) {
      const value = values[i];
      if (typeof value === "number" && !Number.isNaN(value)) {
        valuesCount += 1;
        sum += value;
      }
    }
    if (sum === 0) {
      return null;
    }
    return sum / valuesCount;
  },
  columnTypes: ["number"]
};
var minAgg = {
  apply: ({
    values
  }) => {
    if (values.length === 0) {
      return null;
    }
    let hasValidValue = false;
    let min = Infinity;
    for (let i = 0; i < values.length; i += 1) {
      const value = values[i];
      if (value != null && value < min) {
        min = value;
        hasValidValue = true;
      }
    }
    if (!hasValidValue) {
      return null;
    }
    return min;
  },
  columnTypes: ["number", "date", "dateTime"]
};
var maxAgg = {
  apply: ({
    values
  }) => {
    if (values.length === 0) {
      return null;
    }
    let hasValidValue = false;
    let max = -Infinity;
    for (let i = 0; i < values.length; i += 1) {
      const value = values[i];
      if (value != null && value > max) {
        max = value;
        hasValidValue = true;
      }
    }
    if (!hasValidValue) {
      return null;
    }
    return max;
  },
  columnTypes: ["number", "date", "dateTime"]
};
var sizeAgg = {
  apply: ({
    values
  }) => {
    return values.filter((value) => typeof value !== "undefined").length;
  },
  valueFormatter: (value) => {
    if (value == null || !isNumber(value)) {
      return value;
    }
    return value.toLocaleString();
  },
  hasCellUnit: false
};
var GRID_AGGREGATION_FUNCTIONS = {
  sum: sumAgg,
  avg: avgAgg,
  min: minAgg,
  max: maxAgg,
  size: sizeAgg
};

// node_modules/@mui/x-data-grid-premium/esm/material/index.js
var iconsSlots = {
  collapsibleIcon: GridExpandMoreIcon,
  columnMenuUngroupIcon: GridWorkspacesIcon,
  columnMenuGroupIcon: GridGroupWorkIcon,
  columnMenuAggregationIcon: GridFunctionsIcon,
  pivotIcon: GridPivotIcon,
  pivotSearchIcon: GridSearchIcon,
  pivotSearchClearIcon: GridClearIcon,
  pivotMenuAddIcon: GridAddIcon,
  pivotMenuMoveUpIcon: GridExpandLessIcon,
  pivotMenuMoveDownIcon: GridExpandMoreIcon,
  pivotMenuMoveToTopIcon: GridMoveToTopIcon,
  pivotMenuMoveToBottomIcon: GridMoveToBottomIcon,
  pivotMenuCheckIcon: GridCheckIcon,
  pivotMenuRemoveIcon: GridDeleteIcon,
  sidebarCloseIcon: GridCloseIcon,
  aiAssistantIcon: GridAssistantIcon,
  aiAssistantPanelCloseIcon: GridCloseIcon,
  aiAssistantPanelNewConversationIcon: GridAddIcon,
  aiAssistantPanelHistoryIcon: GridHistoryIcon,
  promptIcon: GridPromptIcon,
  promptSendIcon: GridSendIcon,
  promptSpeechRecognitionIcon: GridMicIcon,
  promptSpeechRecognitionOffIcon: GridMicOffIcon,
  promptRerunIcon: GridRerunIcon,
  promptSortAscIcon: GridArrowUpwardIcon,
  promptSortDescIcon: GridArrowDownwardIcon,
  promptFilterIcon: GridFilterAltIcon,
  promptPivotIcon: GridPivotIcon,
  promptAggregationIcon: GridFunctionsIcon,
  promptGroupIcon: GridGroupWorkIcon,
  promptChangesToggleIcon: GridExpandMoreIcon
};
var materialSlots = _extends({}, iconsSlots);
var material_default = materialSlots;

// node_modules/@mui/x-data-grid-premium/esm/components/GridBottomContainer.js
var React67 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/components/GridAggregationRowOverlay.js
var React65 = __toESM(require_react(), 1);
var import_jsx_runtime46 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses20 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["aggregationRowOverlayWrapper"]
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
var GridAggregationRowOverlay = forwardRef(function GridAggregationRowOverlay2(props, forwardedRef) {
  const apiRef = useGridApiContext2();
  const rootProps = useGridRootProps();
  const classes = useUtilityClasses20({
    classes: rootProps.classes
  });
  const aggregationModel = useGridSelector(apiRef, gridAggregationModelSelector);
  const visibleColumns = new Set(Object.keys(aggregationModel));
  return (0, import_jsx_runtime46.jsx)("div", {
    className: classes.root,
    children: (0, import_jsx_runtime46.jsx)(GridSkeletonLoadingOverlayInner, _extends({}, props, {
      skeletonRowsCount: 1,
      visibleColumns,
      showFirstRowBorder: true,
      ref: forwardedRef
    }))
  });
});
if (true) GridAggregationRowOverlay.displayName = "GridAggregationRowOverlay";

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useGridApiRef.js
var useGridApiRef2 = useGridApiRef;

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/export/serializer/setupExcelExportWebWorker.js
function setupExcelExportWebWorker(workerOptions = {}) {
  globalThis.addEventListener("message", async (event) => {
    const {
      namespace,
      serializedColumns,
      serializedRows,
      options: options2,
      valueOptionsSheetName,
      valueOptionsData,
      columnGroupDetails,
      columnGroupPaths
    } = event.data;
    if (namespace !== "mui-x-data-grid-export") {
      return;
    }
    const {
      exceljsPostProcess,
      exceljsPreProcess
    } = workerOptions;
    const excelJS = await getExcelJs();
    const workbook = new excelJS.Workbook();
    const worksheet = workbook.addWorksheet("Sheet1");
    worksheet.columns = serializedColumns;
    if (exceljsPreProcess) {
      await exceljsPreProcess({
        workbook,
        worksheet
      });
    }
    if (options2.includeColumnGroupsHeaders) {
      addColumnGroupingHeaders(worksheet, serializedColumns, columnGroupPaths, columnGroupDetails);
    }
    const includeHeaders = options2.includeHeaders ?? true;
    if (includeHeaders) {
      worksheet.addRow(serializedColumns.map((column) => column.headerText));
    }
    createValueOptionsSheetIfNeeded(valueOptionsData, valueOptionsSheetName, workbook);
    serializedRows.forEach((serializedRow) => {
      addSerializedRowToWorksheet(serializedRow, worksheet);
    });
    if (exceljsPostProcess) {
      await exceljsPostProcess({
        workbook,
        worksheet
      });
    }
    postMessage(await workbook.xlsx.writeBuffer());
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/aiAssistant/api.js
function gridDefaultPromptResolver(url, query, context, conversationId, optionsOrAdditionalContext = "") {
  const options2 = typeof optionsOrAdditionalContext === "string" ? {
    additionalContext: optionsOrAdditionalContext
  } : optionsOrAdditionalContext;
  return fetch(url, {
    mode: "cors",
    method: "POST",
    headers: {
      "content-type": "application/json"
    },
    credentials: "include",
    body: JSON.stringify({
      context,
      query,
      conversationId,
      options: options2
    })
  }).then((result) => result.json()).then((result) => {
    if (result.ok === false) {
      return Promise.reject(new Error(result.message));
    }
    return result.data;
  });
}

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useKeepGroupedColumnsHidden.js
var React66 = __toESM(require_react(), 1);
var updateColumnVisibilityModel = (columnVisibilityModel, rowGroupingModel, prevRowGroupingModel) => {
  const newColumnVisibilityModel = _extends({}, columnVisibilityModel);
  rowGroupingModel == null ? void 0 : rowGroupingModel.forEach((field) => {
    if (!(prevRowGroupingModel == null ? void 0 : prevRowGroupingModel.includes(field))) {
      newColumnVisibilityModel[field] = false;
    }
  });
  prevRowGroupingModel == null ? void 0 : prevRowGroupingModel.forEach((field) => {
    if (!(rowGroupingModel == null ? void 0 : rowGroupingModel.includes(field))) {
      newColumnVisibilityModel[field] = true;
    }
  });
  return newColumnVisibilityModel;
};
var useKeepGroupedColumnsHidden = (props) => {
  var _a, _b;
  const initialProps = React66.useRef(props);
  const rowGroupingModel = React66.useRef(props.rowGroupingModel ?? ((_b = (_a = props.initialState) == null ? void 0 : _a.rowGrouping) == null ? void 0 : _b.model));
  React66.useEffect(() => {
    var _a2;
    (_a2 = props.apiRef.current) == null ? void 0 : _a2.subscribeEvent("rowGroupingModelChange", (newModel) => {
      var _a3;
      const columnVisibilityModel = updateColumnVisibilityModel(gridColumnVisibilityModelSelector(props.apiRef), newModel, rowGroupingModel.current);
      (_a3 = props.apiRef.current) == null ? void 0 : _a3.setColumnVisibilityModel(columnVisibilityModel);
      rowGroupingModel.current = newModel;
    });
  }, [props.apiRef]);
  return React66.useMemo(() => {
    var _a2;
    const invariantInitialState = initialProps.current.initialState;
    const columnVisibilityModel = updateColumnVisibilityModel((_a2 = invariantInitialState == null ? void 0 : invariantInitialState.columns) == null ? void 0 : _a2.columnVisibilityModel, rowGroupingModel.current, void 0);
    return _extends({}, invariantInitialState, {
      columns: _extends({}, invariantInitialState == null ? void 0 : invariantInitialState.columns, {
        columnVisibilityModel
      })
    });
  }, []);
};

// node_modules/@mui/x-data-grid-premium/esm/components/GridBottomContainer.js
var import_jsx_runtime47 = __toESM(require_jsx_runtime(), 1);
var _excluded24 = ["children"];
var useUtilityClasses21 = () => {
  const slots = {
    root: ["bottomContainer"]
  };
  return composeClasses(slots, getDataGridUtilityClass, {});
};
var Element = styled_default2("div")({
  position: "sticky",
  zIndex: 40,
  bottom: "calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))"
});
function GridBottomContainer(props) {
  const classes = useUtilityClasses21();
  const rootProps = useGridRootProps2();
  const apiRef = useGridPrivateApiContext2();
  const isLoading = useGridSelector(apiRef, gridRowsLoadingSelector);
  const tree = useGridSelector(apiRef, gridRowTreeSelector);
  const aggregationModel = useGridSelector(apiRef, gridAggregationModelSelector);
  const aggregationPosition = rootProps.getAggregationPosition(tree[GRID_ROOT_GROUP_ID]);
  const hasAggregation = React67.useMemo(() => Object.keys(aggregationModel).length > 0, [aggregationModel]);
  const {
    children
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded24);
  return (0, import_jsx_runtime47.jsx)(Element, _extends({}, other, {
    className: clsx_default(classes.root, gridClasses["container--bottom"]),
    role: "presentation",
    children: hasAggregation && isLoading && aggregationPosition === "footer" ? (0, import_jsx_runtime47.jsx)(GridAggregationRowOverlay, {}) : children
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/components/GridPremiumToolbar.js
var React68 = __toESM(require_react(), 1);
var import_jsx_runtime48 = __toESM(require_jsx_runtime(), 1);
var _excluded25 = ["excelOptions"];
function GridPremiumToolbar(props) {
  var _a;
  const rootProps = useGridRootProps2();
  const apiRef = useGridApiContext2();
  const other = _objectWithoutPropertiesLoose(props, _excluded25);
  const additionalItems = (0, import_jsx_runtime48.jsxs)(React68.Fragment, {
    children: [isPivotingAvailable(rootProps) && (0, import_jsx_runtime48.jsx)(PivotPanelTrigger, {
      render: (triggerProps, state) => (0, import_jsx_runtime48.jsx)(rootProps.slots.baseTooltip, {
        title: apiRef.current.getLocaleText("toolbarPivot"),
        children: (0, import_jsx_runtime48.jsx)(ToolbarButton, _extends({}, triggerProps, {
          color: state.active ? "primary" : "default",
          children: (0, import_jsx_runtime48.jsx)(rootProps.slots.pivotIcon, {
            fontSize: "small"
          })
        }))
      })
    }), rootProps.aiAssistant && (0, import_jsx_runtime48.jsx)(AiAssistantPanelTrigger, {
      render: (triggerProps) => (0, import_jsx_runtime48.jsx)(rootProps.slots.baseTooltip, {
        title: apiRef.current.getLocaleText("toolbarAssistant"),
        children: (0, import_jsx_runtime48.jsx)(ToolbarButton, _extends({}, triggerProps, {
          color: "default",
          children: (0, import_jsx_runtime48.jsx)(rootProps.slots.aiAssistantIcon, {
            fontSize: "small"
          })
        }))
      })
    })]
  });
  const additionalExportMenuItems = !((_a = props.excelOptions) == null ? void 0 : _a.disableToolbarButton) ? (onMenuItemClick) => {
    var _a2;
    return (0, import_jsx_runtime48.jsx)(ExportExcel, {
      render: (0, import_jsx_runtime48.jsx)(rootProps.slots.baseMenuItem, _extends({}, (_a2 = rootProps.slotProps) == null ? void 0 : _a2.baseMenuItem)),
      options: props.excelOptions,
      onClick: onMenuItemClick,
      children: apiRef.current.getLocaleText("toolbarExportExcel")
    });
  } : void 0;
  return (0, import_jsx_runtime48.jsx)(GridToolbar, _extends({}, other, {
    additionalItems,
    additionalExportMenuItems
  }));
}

// node_modules/@mui/x-data-grid-premium/esm/constants/dataGridPremiumDefaultSlotsComponents.js
var DATA_GRID_PREMIUM_DEFAULT_SLOTS_COMPONENTS = _extends({}, DATA_GRID_PRO_DEFAULT_SLOTS_COMPONENTS, material_default, {
  aiAssistantPanel: null,
  columnMenu: GridPremiumColumnMenu,
  bottomContainer: GridBottomContainer,
  emptyPivotOverlay: GridEmptyPivotOverlay,
  toolbar: GridPremiumToolbar
});

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/useDataGridPremiumProps.js
var getDataGridPremiumForcedProps = (themedProps) => _extends({
  signature: GridSignature.DataGridPremium
}, themedProps.dataSource ? {
  filterMode: "server",
  sortingMode: "server",
  paginationMode: "server"
} : {});
var DATA_GRID_PREMIUM_PROPS_DEFAULT_VALUES = _extends({}, DATA_GRID_PRO_PROPS_DEFAULT_VALUES, {
  cellSelection: false,
  disableAggregation: false,
  disableRowGrouping: false,
  rowGroupingColumnMode: "single",
  aggregationFunctions: GRID_AGGREGATION_FUNCTIONS,
  aggregationRowsScope: "filtered",
  getAggregationPosition: defaultGetAggregationPosition,
  disableClipboardPaste: false,
  splitClipboardPastedText: (pastedText, delimiter = "	") => {
    const text = pastedText.replace(/\r?\n$/, "");
    return text.split(/\r\n|\n|\r/).map((row) => row.split(delimiter));
  },
  disablePivoting: false,
  getPivotDerivedColumns: defaultGetPivotDerivedColumns,
  aiAssistant: false
});
var defaultSlots = DATA_GRID_PREMIUM_DEFAULT_SLOTS_COMPONENTS;
var useDataGridPremiumProps = (inProps) => {
  const theme = useTheme();
  const themedProps = React69.useMemo(() => getThemeProps({
    props: inProps,
    theme,
    name: "MuiDataGrid"
  }), [theme, inProps]);
  const localeText = React69.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);
  const slots = React69.useMemo(() => computeSlots({
    defaultSlots,
    slots: themedProps.slots
  }), [themedProps.slots]);
  return React69.useMemo(() => _extends({}, DATA_GRID_PREMIUM_PROPS_DEFAULT_VALUES, themedProps.dataSource ? {
    aggregationFunctions: {}
  } : {}, themedProps, {
    localeText,
    slots
  }, getDataGridPremiumForcedProps(themedProps)), [themedProps, localeText, slots]);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/utils/useGridAriaAttributes.js
var useGridAriaAttributesPremium = () => {
  const ariaAttributesPro = useGridAriaAttributesPro();
  const apiRef = useGridPrivateApiContext2();
  const gridRowGroupingModel = useGridSelector(apiRef, gridRowGroupingSanitizedModelSelector);
  const ariaAttributesPremium = gridRowGroupingModel.length > 0 ? {
    role: "treegrid"
  } : {};
  return _extends({}, ariaAttributesPro, ariaAttributesPremium);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rows/useGridRowAriaAttributes.js
var useGridRowAriaAttributesPremium = () => {
  const apiRef = useGridPrivateApiContext2();
  const gridRowGroupingModel = useGridSelector(apiRef, gridRowGroupingSanitizedModelSelector);
  return useGridRowAriaAttributesPro(gridRowGroupingModel.length > 0);
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rows/useGridRowsOverridableMethods.js
var React70 = __toESM(require_react(), 1);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowReorder/operations.js
var BaseReorderOperation = class {
};
var SameParentSwapOperation = class extends BaseReorderOperation {
  constructor() {
    super(...arguments);
    __publicField(this, "operationType", "same-parent-swap");
  }
  detectOperation(ctx) {
    const {
      sourceRowId,
      placeholderIndex,
      sortedFilteredRowIds,
      sortedFilteredRowIndexLookup,
      rowTree,
      apiRef
    } = ctx;
    const sourceNode = gridRowNodeSelector(apiRef, sourceRowId);
    if (!sourceNode || sourceNode.type === "footer") {
      return null;
    }
    let targetIndex = placeholderIndex;
    const sourceIndex = sortedFilteredRowIndexLookup[sourceRowId];
    if (targetIndex === sortedFilteredRowIds.length && sortedFilteredRowIds.length > 0) {
      targetIndex -= 1;
    }
    let targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[targetIndex]);
    if (placeholderIndex > sourceIndex && sourceNode.parent === targetNode.parent) {
      targetIndex = placeholderIndex - 1;
      targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[targetIndex]);
      if (targetNode && targetNode.depth !== sourceNode.depth) {
        while (targetNode.depth > sourceNode.depth && targetIndex >= 0) {
          targetIndex -= 1;
          targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[targetIndex]);
        }
      }
      if (targetIndex === -1) {
        return null;
      }
    }
    let isLastChild = false;
    if (!targetNode) {
      if (placeholderIndex >= sortedFilteredRowIds.length && sortedFilteredRowIds.length > 0) {
        targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[sortedFilteredRowIds.length - 1]);
        isLastChild = true;
      } else {
        return null;
      }
    }
    let adjustedTargetNode = targetNode;
    if (targetNode.type === "group" && sourceNode.parent !== targetNode.parent && sourceNode.depth > targetNode.depth) {
      let i = targetIndex - 1;
      while (i >= 0) {
        const node = gridRowNodeSelector(apiRef, sortedFilteredRowIds[i]);
        if (node && node.depth < sourceNode.depth) {
          return null;
        }
        if (node && node.depth === sourceNode.depth) {
          targetIndex = i;
          adjustedTargetNode = node;
          break;
        }
        i -= 1;
      }
    }
    const operationType = determineOperationType(sourceNode, adjustedTargetNode);
    if (operationType !== "same-parent-swap") {
      return null;
    }
    const actualTargetIndex = calculateTargetIndex(sourceNode, adjustedTargetNode, isLastChild, rowTree);
    targetNode = adjustedTargetNode;
    if (sourceNode.type !== targetNode.type) {
      return null;
    }
    return {
      sourceNode,
      targetNode,
      actualTargetIndex,
      isLastChild,
      operationType
    };
  }
  executeOperation(operation, ctx) {
    const {
      sourceNode,
      actualTargetIndex
    } = operation;
    const {
      apiRef,
      sourceRowId
    } = ctx;
    apiRef.current.setState((state) => {
      const group = gridRowTreeSelector(apiRef)[sourceNode.parent];
      const currentChildren = [...group.children];
      const oldIndex = currentChildren.findIndex((row) => row === sourceRowId);
      if (oldIndex === -1 || actualTargetIndex === -1 || oldIndex === actualTargetIndex) {
        return state;
      }
      currentChildren.splice(actualTargetIndex, 0, currentChildren.splice(oldIndex, 1)[0]);
      return _extends({}, state, {
        rows: _extends({}, state.rows, {
          tree: _extends({}, state.rows.tree, {
            [sourceNode.parent]: _extends({}, group, {
              children: currentChildren
            })
          })
        })
      });
    });
    apiRef.current.publishEvent("rowsSet");
  }
};
var CrossParentLeafOperation = class extends BaseReorderOperation {
  constructor() {
    super(...arguments);
    __publicField(this, "operationType", "cross-parent-leaf");
  }
  detectOperation(ctx) {
    const {
      sourceRowId,
      placeholderIndex,
      sortedFilteredRowIds,
      rowTree,
      apiRef
    } = ctx;
    const sourceNode = gridRowNodeSelector(apiRef, sourceRowId);
    if (!sourceNode || sourceNode.type === "footer") {
      return null;
    }
    let targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[placeholderIndex]);
    let isLastChild = false;
    if (!targetNode) {
      if (placeholderIndex >= sortedFilteredRowIds.length && sortedFilteredRowIds.length > 0) {
        targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[sortedFilteredRowIds.length - 1]);
        isLastChild = true;
      } else {
        return null;
      }
    }
    let adjustedTargetNode = targetNode;
    if (sourceNode.type === "leaf" && targetNode.type === "group" && targetNode.depth < sourceNode.depth) {
      const prevIndex = placeholderIndex - 1;
      if (prevIndex >= 0) {
        const prevRowId = sortedFilteredRowIds[prevIndex];
        const leafTargetNode = gridRowNodeSelector(apiRef, prevRowId);
        if (leafTargetNode && leafTargetNode.type === "leaf") {
          adjustedTargetNode = leafTargetNode;
          isLastChild = true;
        }
      }
    }
    const operationType = determineOperationType(sourceNode, adjustedTargetNode);
    if (operationType !== "cross-parent-leaf") {
      return null;
    }
    const actualTargetIndex = calculateTargetIndex(sourceNode, adjustedTargetNode, isLastChild, rowTree);
    targetNode = adjustedTargetNode;
    if (sourceNode.type === "leaf" && targetNode.type === "leaf") {
      if (sourceNode.depth !== targetNode.depth) {
        return null;
      }
    } else if (sourceNode.type === "leaf" && targetNode.type === "group") {
      if (targetNode.depth >= sourceNode.depth) {
        return null;
      }
    }
    return {
      sourceNode,
      targetNode: adjustedTargetNode,
      actualTargetIndex,
      isLastChild,
      operationType
    };
  }
  async executeOperation(operation, ctx) {
    const {
      sourceNode,
      targetNode,
      isLastChild
    } = operation;
    const {
      apiRef,
      sourceRowId,
      processRowUpdate,
      onProcessRowUpdateError
    } = ctx;
    let target = targetNode;
    if (targetNode.type === "group") {
      const prevIndex = ctx.placeholderIndex - 1;
      if (prevIndex >= 0) {
        const prevRowId = ctx.sortedFilteredRowIds[prevIndex];
        const prevNode = gridRowNodeSelector(apiRef, prevRowId);
        if (prevNode && prevNode.type === "leaf") {
          target = prevNode;
        }
      }
    }
    const rowTree = gridRowTreeSelector(apiRef);
    const sourceGroup = rowTree[sourceNode.parent];
    const targetGroup = rowTree[target.parent];
    const sourceChildren = sourceGroup.children;
    const targetChildren = targetGroup.children;
    const sourceIndex = sourceChildren.findIndex((row) => row === sourceRowId);
    const targetIndex = targetChildren.findIndex((row) => row === target.id);
    if (sourceIndex === -1 || targetIndex === -1) {
      return;
    }
    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);
    const columnsLookup = gridColumnLookupSelector(apiRef);
    const sanitizedRowGroupingModel = gridRowGroupingSanitizedModelSelector(apiRef);
    const originalSourceRow = dataRowIdToModelLookup[sourceRowId];
    let updatedSourceRow = _extends({}, originalSourceRow);
    const targetRow = dataRowIdToModelLookup[target.id];
    const groupingRules = getGroupingRules({
      sanitizedRowGroupingModel,
      columnsLookup
    });
    for (const groupingRule of groupingRules) {
      const colDef = columnsLookup[groupingRule.field];
      if (groupingRule.groupingValueSetter && colDef) {
        const targetGroupingValue = getCellGroupingCriteria({
          row: targetRow,
          colDef,
          groupingRule,
          apiRef
        }).key;
        updatedSourceRow = groupingRule.groupingValueSetter(targetGroupingValue, updatedSourceRow, colDef, apiRef);
      } else {
        updatedSourceRow[groupingRule.field] = targetRow[groupingRule.field];
      }
    }
    const commitStateUpdate = (finalSourceRow) => {
      apiRef.current.setState((state) => {
        const updatedSourceChildren = sourceChildren.filter((rowId) => rowId !== sourceRowId);
        const updatedTree = _extends({}, state.rows.tree);
        const removedGroups = /* @__PURE__ */ new Set();
        let rootLevelRemovals = 0;
        if (updatedSourceChildren.length === 0) {
          removedGroups.add(sourceGroup.id);
          rootLevelRemovals = removeEmptyAncestors(sourceGroup.parent, updatedTree, removedGroups);
        }
        removedGroups.forEach((groupId) => {
          const group = updatedTree[groupId];
          if (group && group.parent && updatedTree[group.parent]) {
            const parent = updatedTree[group.parent];
            updatedTree[group.parent] = _extends({}, parent, {
              children: parent.children.filter((childId) => childId !== groupId)
            });
          }
          delete updatedTree[groupId];
        });
        if (!removedGroups.has(sourceGroup.id)) {
          updatedTree[sourceNode.parent] = _extends({}, sourceGroup, {
            children: updatedSourceChildren
          });
        }
        const updatedTargetChildren = isLastChild ? [...targetChildren, sourceRowId] : [...targetChildren.slice(0, targetIndex), sourceRowId, ...targetChildren.slice(targetIndex)];
        updatedTree[target.parent] = _extends({}, targetGroup, {
          children: updatedTargetChildren
        });
        updatedTree[sourceNode.id] = _extends({}, sourceNode, {
          parent: target.parent
        });
        return _extends({}, state, {
          rows: _extends({}, state.rows, {
            totalTopLevelRowCount: state.rows.totalTopLevelRowCount - rootLevelRemovals,
            tree: updatedTree
          })
        });
      });
      apiRef.current.updateRows([finalSourceRow]);
      apiRef.current.publishEvent("rowsSet");
    };
    if (processRowUpdate && !isDeepEqual(originalSourceRow, updatedSourceRow)) {
      const params = {
        rowId: sourceRowId,
        previousRow: originalSourceRow,
        updatedRow: updatedSourceRow
      };
      apiRef.current.setLoading(true);
      try {
        const processedRow = await processRowUpdate(updatedSourceRow, originalSourceRow, params);
        const finalRow = processedRow || updatedSourceRow;
        commitStateUpdate(finalRow);
      } catch (error) {
        apiRef.current.setLoading(false);
        if (onProcessRowUpdateError) {
          onProcessRowUpdateError(error);
        } else if (true) {
          warnOnce(["MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.", "To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.", "For more detail, see https://mui.com/x/react-data-grid/editing/persistence/."], "error");
        }
      } finally {
        apiRef.current.setLoading(false);
      }
    } else {
      commitStateUpdate(updatedSourceRow);
    }
  }
};
var CrossParentGroupOperation = class extends BaseReorderOperation {
  constructor() {
    super(...arguments);
    __publicField(this, "operationType", "cross-parent-group");
  }
  detectOperation(ctx) {
    const {
      sourceRowId,
      placeholderIndex,
      sortedFilteredRowIds,
      rowTree,
      apiRef
    } = ctx;
    const sourceNode = gridRowNodeSelector(apiRef, sourceRowId);
    if (!sourceNode || sourceNode.type === "footer") {
      return null;
    }
    let targetIndex = placeholderIndex;
    let targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[placeholderIndex]);
    let isLastChild = false;
    if (!targetNode) {
      if (placeholderIndex >= sortedFilteredRowIds.length && sortedFilteredRowIds.length > 0) {
        targetNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[sortedFilteredRowIds.length - 1]);
        targetIndex = sortedFilteredRowIds.length - 1;
        isLastChild = true;
      } else {
        return null;
      }
    }
    let adjustedTargetNode = targetNode;
    if (sourceNode.type === "group" && targetNode.type === "group" && sourceNode.parent !== targetNode.parent && sourceNode.depth > targetNode.depth) {
      let prevIndex = targetIndex - 1;
      if (prevIndex < 0) {
        return null;
      }
      let prevNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[prevIndex]);
      if (prevNode && prevNode.depth !== sourceNode.depth) {
        while (prevNode.depth > sourceNode.depth && prevIndex >= 0) {
          prevIndex -= 1;
          prevNode = gridRowNodeSelector(apiRef, sortedFilteredRowIds[prevIndex]);
        }
      }
      if (!prevNode || prevNode.type !== "group" || prevNode.depth !== sourceNode.depth) {
        return null;
      }
      isLastChild = true;
      adjustedTargetNode = prevNode;
    }
    const operationType = determineOperationType(sourceNode, adjustedTargetNode);
    if (operationType !== "cross-parent-group") {
      return null;
    }
    const actualTargetIndex = calculateTargetIndex(sourceNode, adjustedTargetNode, isLastChild, rowTree);
    const operation = {
      sourceNode,
      targetNode: adjustedTargetNode,
      actualTargetIndex,
      isLastChild,
      operationType
    };
    targetNode = adjustedTargetNode;
    if (sourceNode.depth !== targetNode.depth) {
      return null;
    }
    return operation;
  }
  async executeOperation(operation, ctx) {
    const {
      sourceNode,
      targetNode,
      isLastChild
    } = operation;
    const {
      apiRef,
      processRowUpdate,
      onProcessRowUpdateError
    } = ctx;
    const tree = gridRowTreeSelector(apiRef);
    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);
    const columnsLookup = gridColumnLookupSelector(apiRef);
    const sanitizedRowGroupingModel = gridRowGroupingSanitizedModelSelector(apiRef);
    const allLeafIds = collectAllLeafDescendants(sourceNode, tree);
    if (allLeafIds.length === 0) {
      return;
    }
    const updater = new BatchRowUpdater(processRowUpdate, onProcessRowUpdateError);
    const groupingRules = getGroupingRules({
      sanitizedRowGroupingModel,
      columnsLookup
    });
    const targetParentPath = getNodePathInTree({
      id: targetNode.parent,
      tree
    });
    for (const leafId of allLeafIds) {
      const originalRow = dataRowIdToModelLookup[leafId];
      let updatedRow = _extends({}, originalRow);
      for (let depth = 0; depth < targetParentPath.length; depth += 1) {
        const pathItem = targetParentPath[depth];
        if (pathItem.field) {
          const groupingRule = groupingRules.find((rule) => rule.field === pathItem.field);
          if (groupingRule) {
            const colDef = columnsLookup[groupingRule.field];
            if (groupingRule.groupingValueSetter && colDef) {
              updatedRow = groupingRule.groupingValueSetter(pathItem.key, updatedRow, colDef, apiRef);
            } else {
              updatedRow[groupingRule.field] = pathItem.key;
            }
          }
        }
      }
      updater.queueUpdate(leafId, originalRow, updatedRow);
    }
    apiRef.current.setLoading(true);
    try {
      const {
        successful,
        failed,
        updates
      } = await updater.executeAll();
      if (successful.length > 0) {
        apiRef.current.setState((state) => {
          const updatedTree = _extends({}, state.rows.tree);
          const treeDepths = _extends({}, state.rows.treeDepths);
          let rootLevelRemovals = 0;
          if (failed.length === 0) {
            const sourceParentNode = updatedTree[sourceNode.parent];
            if (!sourceParentNode) {
              const targetParentNode = updatedTree[targetNode.parent];
              const targetIndex = targetParentNode.children.indexOf(targetNode.id);
              const newTargetChildren = [...targetParentNode.children];
              if (isLastChild) {
                newTargetChildren.push(sourceNode.id);
              } else {
                newTargetChildren.splice(targetIndex, 0, sourceNode.id);
              }
              updatedTree[targetNode.parent] = _extends({}, targetParentNode, {
                children: newTargetChildren
              });
              updatedTree[sourceNode.id] = _extends({}, sourceNode, {
                parent: targetNode.parent
              });
            } else {
              const updatedSourceParentChildren = sourceParentNode.children.filter((id) => id !== sourceNode.id);
              if (updatedSourceParentChildren.length === 0) {
                const removedGroups = /* @__PURE__ */ new Set();
                removedGroups.add(sourceNode.parent);
                const parentOfSourceParent = updatedTree[sourceNode.parent].parent;
                if (parentOfSourceParent) {
                  rootLevelRemovals = removeEmptyAncestors(parentOfSourceParent, updatedTree, removedGroups);
                }
                removedGroups.forEach((groupId) => {
                  const group = updatedTree[groupId];
                  if (group && group.parent && updatedTree[group.parent]) {
                    const parent = updatedTree[group.parent];
                    updatedTree[group.parent] = _extends({}, parent, {
                      children: parent.children.filter((childId) => childId !== groupId)
                    });
                  }
                  delete updatedTree[groupId];
                });
              } else {
                updatedTree[sourceNode.parent] = _extends({}, sourceParentNode, {
                  children: updatedSourceParentChildren
                });
              }
              const targetParentNode = updatedTree[targetNode.parent];
              const sourceGroupNode = sourceNode;
              const existingGroup = sourceGroupNode.groupingKey !== null && sourceGroupNode.groupingField !== null ? findExistingGroupWithSameKey(targetParentNode, sourceGroupNode.groupingKey, sourceGroupNode.groupingField, updatedTree) : null;
              if (existingGroup) {
                const updatedExistingGroup = _extends({}, existingGroup, {
                  children: [...existingGroup.children, ...sourceGroupNode.children]
                });
                updatedTree[existingGroup.id] = updatedExistingGroup;
                sourceGroupNode.children.forEach((childId) => {
                  const childNode = updatedTree[childId];
                  if (childNode) {
                    updatedTree[childId] = _extends({}, childNode, {
                      parent: existingGroup.id
                    });
                  }
                });
                delete updatedTree[sourceNode.id];
              } else {
                const targetIndex = targetParentNode.children.indexOf(targetNode.id);
                const newTargetChildren = [...targetParentNode.children];
                if (isLastChild) {
                  newTargetChildren.push(sourceNode.id);
                } else {
                  newTargetChildren.splice(targetIndex, 0, sourceNode.id);
                }
                updatedTree[targetNode.parent] = _extends({}, targetParentNode, {
                  children: newTargetChildren
                });
                updatedTree[sourceNode.id] = _extends({}, sourceNode, {
                  parent: targetNode.parent
                });
              }
            }
          }
          return _extends({}, state, {
            rows: _extends({}, state.rows, {
              totalTopLevelRowCount: state.rows.totalTopLevelRowCount - rootLevelRemovals,
              tree: updatedTree,
              treeDepths
            })
          });
        });
        apiRef.current.updateRows(updates);
        apiRef.current.publishEvent("rowsSet");
      }
    } finally {
      apiRef.current.setLoading(false);
    }
  }
};

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rowReorder/reorderExecutor.js
var RowReorderExecutor = class {
  constructor(operations) {
    this.operations = operations;
  }
  async execute(ctx) {
    for (const operation of this.operations) {
      const detectedOperation = operation.detectOperation(ctx);
      if (detectedOperation) {
        await operation.executeOperation(detectedOperation, ctx);
        return;
      }
    }
    warnOnce(["MUI X: The parameters provided to the `setRowIndex()` resulted in a no-op.", "Consider looking at the documentation at https://mui.com/x/react-data-grid/row-grouping/"], "warning");
  }
};
var rowGroupingReorderExecutor = new RowReorderExecutor([new SameParentSwapOperation(), new CrossParentLeafOperation(), new CrossParentGroupOperation()]);

// node_modules/@mui/x-data-grid-premium/esm/hooks/features/rows/useGridRowsOverridableMethods.js
var useGridRowsOverridableMethods = (apiRef, props) => {
  const {
    processRowUpdate,
    onProcessRowUpdateError
  } = props;
  const setRowIndex = React70.useCallback(async (sourceRowId, targetOriginalIndex) => {
    const sortedFilteredRowIds = gridExpandedSortedRowIdsSelector(apiRef);
    const sortedFilteredRowIndexLookup = gridExpandedSortedRowIndexLookupSelector(apiRef);
    const rowTree = gridRowTreeSelector(apiRef);
    const sourceNode = gridRowNodeSelector(apiRef, sourceRowId);
    if (!sourceNode) {
      throw new Error(`MUI X: No row with id #${sourceRowId} found.`);
    }
    if (sourceNode.type === "footer") {
      throw new Error(`MUI X: The row reordering do not support reordering of footer rows.`);
    }
    const executionContext = {
      sourceRowId,
      placeholderIndex: targetOriginalIndex,
      sortedFilteredRowIds,
      sortedFilteredRowIndexLookup,
      rowTree,
      apiRef,
      processRowUpdate,
      onProcessRowUpdateError
    };
    await rowGroupingReorderExecutor.execute(executionContext);
  }, [apiRef, processRowUpdate, onProcessRowUpdateError]);
  return {
    setRowIndex
  };
};

// node_modules/@mui/x-data-grid-premium/esm/DataGridPremium/DataGridPremium.js
var import_jsx_runtime49 = __toESM(require_jsx_runtime(), 1);
var configuration = {
  hooks: {
    useCSSVariables: useMaterialCSSVariables,
    useGridAriaAttributes: useGridAriaAttributesPremium,
    useGridRowAriaAttributes: useGridRowAriaAttributesPremium,
    useCellAggregationResult: (id, field) => {
      const apiRef = useGridApiContext2();
      return useGridSelector(apiRef, gridCellAggregationResultSelector, {
        id,
        field
      });
    },
    useGridRowsOverridableMethods
  }
};
var releaseInfo = "MTc1Njk0NDAwMDAwMA==";
var watermark = (0, import_jsx_runtime49.jsx)(MemoizedWatermark, {
  packageName: "x-data-grid-premium",
  releaseInfo
});
var dataGridPremiumPropValidators;
if (true) {
  dataGridPremiumPropValidators = [...propValidatorsDataGrid, ...propValidatorsDataGridPro];
}
var DataGridPremiumRaw = forwardRef(function DataGridPremium(inProps, ref) {
  var _a;
  const initialProps = useDataGridPremiumProps(inProps);
  const privateApiRef = useGridApiInitialization(initialProps.apiRef, initialProps);
  const props = useDataGridPremiumComponent(privateApiRef, initialProps, configuration);
  useLicenseVerifier("x-data-grid-premium", releaseInfo);
  if (true) {
    validateProps(props, dataGridPremiumPropValidators);
  }
  const sidebarOpen = useGridSelector(privateApiRef, gridSidebarOpenSelector);
  const sidePanel = sidebarOpen ? (0, import_jsx_runtime49.jsx)(Sidebar, {}) : null;
  return (0, import_jsx_runtime49.jsx)(GridContextProvider, {
    privateApiRef,
    configuration,
    props,
    children: (0, import_jsx_runtime49.jsx)(MemoizedGridRoot, _extends({
      className: props.className,
      style: props.style,
      sx: props.sx
    }, (_a = props.slotProps) == null ? void 0 : _a.root, {
      ref,
      sidePanel,
      children: watermark
    }))
  });
});
if (true) DataGridPremiumRaw.displayName = "DataGridPremiumRaw";
true ? DataGridPremiumRaw.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Aggregation functions available on the grid.
   * @default GRID_AGGREGATION_FUNCTIONS when `dataSource` is not provided, `{}` when `dataSource` is provided
   */
  aggregationFunctions: import_prop_types11.default.object,
  /**
   * Set the aggregation model of the grid.
   */
  aggregationModel: import_prop_types11.default.object,
  /**
   * Rows used to generate the aggregated value.
   * If `filtered`, the aggregated values are generated using only the rows currently passing the filtering process.
   * If `all`, the aggregated values are generated using all the rows.
   * @default "filtered"
   */
  aggregationRowsScope: import_prop_types11.default.oneOf(["all", "filtered"]),
  /**
   * If `true`, the AI Assistant is enabled.
   * @default false
   */
  aiAssistant: import_prop_types11.default.bool,
  /**
   * The index of the active AI Assistant conversation.
   */
  aiAssistantActiveConversationIndex: import_prop_types11.default.number,
  /**
   * The conversations with the AI Assistant.
   */
  aiAssistantConversations: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
    id: import_prop_types11.default.string,
    prompts: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
      createdAt: import_prop_types11.default.instanceOf(Date).isRequired,
      helperText: import_prop_types11.default.string,
      response: import_prop_types11.default.shape({
        aggregation: import_prop_types11.default.object.isRequired,
        conversationId: import_prop_types11.default.string.isRequired,
        filterOperator: import_prop_types11.default.oneOf(["and", "or"]),
        filters: import_prop_types11.default.arrayOf(import_prop_types11.default.object).isRequired,
        grouping: import_prop_types11.default.arrayOf(import_prop_types11.default.object).isRequired,
        pivoting: import_prop_types11.default.object.isRequired,
        select: import_prop_types11.default.number.isRequired,
        sorting: import_prop_types11.default.arrayOf(import_prop_types11.default.object).isRequired
      }),
      value: import_prop_types11.default.string.isRequired,
      variant: import_prop_types11.default.oneOf(["error", "processing", "success"])
    })).isRequired,
    title: import_prop_types11.default.string
  })),
  /**
   * The suggestions of the AI Assistant.
   */
  aiAssistantSuggestions: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
    value: import_prop_types11.default.string.isRequired
  })),
  /**
   * If `true`, the AI Assistant is allowed to pick up values from random cells from each column to build the prompt context.
   */
  allowAiAssistantDataSampling: import_prop_types11.default.bool,
  /**
   * The ref object that allows grid manipulation. Can be instantiated with `useGridApiRef()`.
   */
  apiRef: import_prop_types11.default.shape({
    current: import_prop_types11.default.object
  }),
  /**
   * The `aria-label` of the Data Grid.
   */
  "aria-label": import_prop_types11.default.string,
  /**
   * The `id` of the element containing a label for the Data Grid.
   */
  "aria-labelledby": import_prop_types11.default.string,
  /**
   * If `true`, the Data Grid height is dynamic and follows the number of rows in the Data Grid.
   * @default false
   * @deprecated Use flex parent container instead: https://mui.com/x/react-data-grid/layout/#flex-parent-container
   * @example
   * <div style={{ display: 'flex', flexDirection: 'column' }}>
   *   <DataGrid />
   * </div>
   */
  autoHeight: import_prop_types11.default.bool,
  /**
   * If `true`, the pageSize is calculated according to the container size and the max number of rows to avoid rendering a vertical scroll bar.
   * @default false
   */
  autoPageSize: import_prop_types11.default.bool,
  /**
   * If `true`, columns are autosized after the datagrid is mounted.
   * @default false
   */
  autosizeOnMount: import_prop_types11.default.bool,
  /**
   * The options for autosize when user-initiated.
   */
  autosizeOptions: import_prop_types11.default.shape({
    columns: import_prop_types11.default.arrayOf(import_prop_types11.default.string),
    disableColumnVirtualization: import_prop_types11.default.bool,
    expand: import_prop_types11.default.bool,
    includeHeaders: import_prop_types11.default.bool,
    includeOutliers: import_prop_types11.default.bool,
    outliersFactor: import_prop_types11.default.number
  }),
  /**
   * Controls the modes of the cells.
   */
  cellModesModel: import_prop_types11.default.object,
  /**
   * If `true`, the cell selection mode is enabled.
   * @default false
   */
  cellSelection: import_prop_types11.default.bool,
  /**
   * Set the cell selection model of the grid.
   */
  cellSelectionModel: import_prop_types11.default.object,
  /**
   * If `true`, the Data Grid will display an extra column with checkboxes for selecting rows.
   * @default false
   */
  checkboxSelection: import_prop_types11.default.bool,
  /**
   * If `true`, the "Select All" header checkbox selects only the rows on the current page. To be used in combination with `checkboxSelection`.
   * It only works if the pagination is enabled.
   * @default false
   */
  checkboxSelectionVisibleOnly: import_prop_types11.default.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types11.default.object,
  className: import_prop_types11.default.string,
  /**
   * The character used to separate cell values when copying to the clipboard.
   * @default '\t'
   */
  clipboardCopyCellDelimiter: import_prop_types11.default.string,
  /**
   * Column region in pixels to render before/after the viewport
   * @default 150
   */
  columnBufferPx: import_prop_types11.default.number,
  /**
   * The milliseconds delay to wait after a keystroke before triggering filtering in the columns menu.
   * @default 150
   */
  columnFilterDebounceMs: import_prop_types11.default.number,
  /**
   * Sets the height in pixels of the column group headers in the Data Grid.
   * Inherits the `columnHeaderHeight` value if not set.
   */
  columnGroupHeaderHeight: import_prop_types11.default.number,
  columnGroupingModel: import_prop_types11.default.arrayOf(import_prop_types11.default.object),
  /**
   * Sets the height in pixel of the column headers in the Data Grid.
   * @default 56
   */
  columnHeaderHeight: import_prop_types11.default.number,
  /**
   * Set of columns of type [[GridColDef]][].
   */
  columns: import_prop_types11.default.arrayOf(import_prop_types11.default.object).isRequired,
  /**
   * Set the column visibility model of the Data Grid.
   * If defined, the Data Grid will ignore the `hide` property in [[GridColDef]].
   */
  columnVisibilityModel: import_prop_types11.default.object,
  /**
   * Data source object.
   */
  dataSource: import_prop_types11.default.shape({
    getAggregatedValue: import_prop_types11.default.func,
    getChildrenCount: import_prop_types11.default.func,
    getGroupKey: import_prop_types11.default.func,
    getRows: import_prop_types11.default.func.isRequired,
    updateRow: import_prop_types11.default.func
  }),
  /**
   * Data source cache object.
   */
  dataSourceCache: import_prop_types11.default.shape({
    clear: import_prop_types11.default.func.isRequired,
    get: import_prop_types11.default.func.isRequired,
    set: import_prop_types11.default.func.isRequired
  }),
  /**
   * If above 0, the row children will be expanded up to this depth.
   * If equal to -1, all the row children will be expanded.
   * @default 0
   */
  defaultGroupingExpansionDepth: import_prop_types11.default.number,
  /**
   * Set the density of the Data Grid.
   * @default "standard"
   */
  density: import_prop_types11.default.oneOf(["comfortable", "compact", "standard"]),
  /**
   * The row ids to show the detail panel.
   */
  detailPanelExpandedRowIds: import_prop_types11.default.instanceOf(Set),
  /**
   * If `true`, aggregation is disabled.
   * @default false
   */
  disableAggregation: import_prop_types11.default.bool,
  /**
   * If `true`, column autosizing on header separator double-click is disabled.
   * @default false
   */
  disableAutosize: import_prop_types11.default.bool,
  /**
   * If `true`, the filtering will only be applied to the top level rows when grouping rows with the `treeData` prop.
   * @default false
   */
  disableChildrenFiltering: import_prop_types11.default.bool,
  /**
   * If `true`, the sorting will only be applied to the top level rows when grouping rows with the `treeData` prop.
   * @default false
   */
  disableChildrenSorting: import_prop_types11.default.bool,
  /**
   * If `true`, the clipboard paste is disabled.
   * @default false
   */
  disableClipboardPaste: import_prop_types11.default.bool,
  /**
   * If `true`, column filters are disabled.
   * @default false
   */
  disableColumnFilter: import_prop_types11.default.bool,
  /**
   * If `true`, the column menu is disabled.
   * @default false
   */
  disableColumnMenu: import_prop_types11.default.bool,
  /**
   * If `true`, the column pinning is disabled.
   * @default false
   */
  disableColumnPinning: import_prop_types11.default.bool,
  /**
   * If `true`, reordering columns is disabled.
   * @default false
   */
  disableColumnReorder: import_prop_types11.default.bool,
  /**
   * If `true`, resizing columns is disabled.
   * @default false
   */
  disableColumnResize: import_prop_types11.default.bool,
  /**
   * If `true`, hiding/showing columns is disabled.
   * @default false
   */
  disableColumnSelector: import_prop_types11.default.bool,
  /**
   * If `true`, the column sorting feature will be disabled.
   * @default false
   */
  disableColumnSorting: import_prop_types11.default.bool,
  /**
   * If `true`, the density selector is disabled.
   * @default false
   */
  disableDensitySelector: import_prop_types11.default.bool,
  /**
   * If `true`, `eval()` is not used for performance optimization.
   * @default false
   */
  disableEval: import_prop_types11.default.bool,
  /**
   * If `true`, filtering with multiple columns is disabled.
   * @default false
   */
  disableMultipleColumnsFiltering: import_prop_types11.default.bool,
  /**
   * If `true`, the sorting with multiple columns is disabled.
   * @default false
   */
  disableMultipleColumnsSorting: import_prop_types11.default.bool,
  /**
   * If `true`, multiple selection using the Ctrl/CMD or Shift key is disabled.
   * The MIT DataGrid will ignore this prop, unless `checkboxSelection` is enabled.
   * @default false (`!props.checkboxSelection` for MIT Data Grid)
   */
  disableMultipleRowSelection: import_prop_types11.default.bool,
  /**
   * If `true`, the pivoting feature is disabled.
   * @default false
   */
  disablePivoting: import_prop_types11.default.bool,
  /**
   * If `true`, the row grouping is disabled.
   * @default false
   */
  disableRowGrouping: import_prop_types11.default.bool,
  /**
   * If `true`, the selection on click on a row or cell is disabled.
   * @default false
   */
  disableRowSelectionOnClick: import_prop_types11.default.bool,
  /**
   * If `true`, the virtualization is disabled.
   * @default false
   */
  disableVirtualization: import_prop_types11.default.bool,
  /**
   * Controls whether to use the cell or row editing.
   * @default "cell"
   */
  editMode: import_prop_types11.default.oneOf(["cell", "row"]),
  /**
   * Use if the actual rowCount is not known upfront, but an estimation is available.
   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.
   * Applicable only with `paginationMode="server"` and when `rowCount="-1"`
   */
  estimatedRowCount: import_prop_types11.default.number,
  /**
   * Unstable features, breaking changes might be introduced.
   * For each feature, if the flag is not explicitly set to `true`, then the feature is fully disabled, and neither property nor method calls will have any effect.
   */
  experimentalFeatures: import_prop_types11.default.shape({
    warnIfFocusStateIsNotSynced: import_prop_types11.default.bool
  }),
  /**
   * The milliseconds delay to wait after a keystroke before triggering filtering.
   * @default 150
   */
  filterDebounceMs: import_prop_types11.default.number,
  /**
   * Filtering can be processed on the server or client-side.
   * Set it to 'server' if you would like to handle filtering on the server-side.
   * @default "client"
   */
  filterMode: import_prop_types11.default.oneOf(["client", "server"]),
  /**
   * Set the filter model of the Data Grid.
   */
  filterModel: import_prop_types11.default.shape({
    items: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
      field: import_prop_types11.default.string.isRequired,
      id: import_prop_types11.default.oneOfType([import_prop_types11.default.number, import_prop_types11.default.string]),
      operator: import_prop_types11.default.string.isRequired,
      value: import_prop_types11.default.any
    })).isRequired,
    logicOperator: import_prop_types11.default.oneOf(["and", "or"]),
    quickFilterExcludeHiddenColumns: import_prop_types11.default.bool,
    quickFilterLogicOperator: import_prop_types11.default.oneOf(["and", "or"]),
    quickFilterValues: import_prop_types11.default.array
  }),
  /**
   * Determines the position of an aggregated value.
   * @param {GridGroupNode} groupNode The current group.
   * @returns {GridAggregationPosition | null} Position of the aggregated value (if `null`, the group isn't aggregated).
   * @default (groupNode) => (groupNode.depth === -1 ? 'footer' : 'inline')
   */
  getAggregationPosition: import_prop_types11.default.func,
  /**
   * Function that applies CSS classes dynamically on cells.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @returns {string} The CSS class to apply to the cell.
   */
  getCellClassName: import_prop_types11.default.func,
  /**
   * Function that returns the element to render in row detail.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {React.JSX.Element} The row detail element.
   */
  getDetailPanelContent: import_prop_types11.default.func,
  /**
   * Function that returns the height of the row detail panel.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {number | string} The height in pixels or "auto" to use the content height.
   * @default "() => 500"
   */
  getDetailPanelHeight: import_prop_types11.default.func,
  /**
   * Function that returns the estimated height for a row.
   * Only works if dynamic row height is used.
   * Once the row height is measured this value is discarded.
   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].
   * @returns {number | null} The estimated row height value. If `null` or `undefined` then the default row height, based on the density, is applied.
   */
  getEstimatedRowHeight: import_prop_types11.default.func,
  /**
   * Allows to generate derived columns from actual columns that will be used for pivoting.
   * Useful e.g. for date columns to generate year, quarter, month, etc.
   * @param {GridColDef} column The column to generate derived columns for.
   * @param {GridLocaleTextApi['getLocaleText']} getLocaleText The function to get the locale text.
   * @returns {GridColDef[] | undefined} The derived columns.
   * @default {defaultGetPivotDerivedColumns} Creates year and quarter columns for date columns.
   */
  getPivotDerivedColumns: import_prop_types11.default.func,
  /**
   * Function that applies CSS classes dynamically on rows.
   * @param {GridRowClassNameParams} params With all properties from [[GridRowClassNameParams]].
   * @returns {string} The CSS class to apply to the row.
   */
  getRowClassName: import_prop_types11.default.func,
  /**
   * Function that sets the row height per row.
   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].
   * @returns {GridRowHeightReturnValue} The row height value. If `null` or `undefined` then the default row height is applied. If "auto" then the row height is calculated based on the content.
   */
  getRowHeight: import_prop_types11.default.func,
  /**
   * Return the id of a given [[GridRowModel]].
   * Ensure the reference of this prop is stable to avoid performance implications.
   * It could be done by either defining the prop outside of the component or by memoizing it.
   */
  getRowId: import_prop_types11.default.func,
  /**
   * Function that allows to specify the spacing between rows.
   * @param {GridRowSpacingParams} params With all properties from [[GridRowSpacingParams]].
   * @returns {GridRowSpacing} The row spacing values.
   */
  getRowSpacing: import_prop_types11.default.func,
  /**
   * Determines the path of a row in the tree data.
   * For instance, a row with the path ["A", "B"] is the child of the row with the path ["A"].
   * Note that all paths must contain at least one element.
   * @template R
   * @param {R} row The row from which we want the path.
   * @returns {string[]} The path to the row.
   */
  getTreeDataPath: import_prop_types11.default.func,
  /**
   * The grouping column used by the tree data.
   */
  groupingColDef: import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.object]),
  /**
   * Override the height of the header filters.
   */
  headerFilterHeight: import_prop_types11.default.number,
  /**
   * If `true`, the header filters feature is enabled.
   * @default false
   */
  headerFilters: import_prop_types11.default.bool,
  /**
   * If `true`, the footer component is hidden.
   * @default false
   */
  hideFooter: import_prop_types11.default.bool,
  /**
   * If `true`, the pagination component in the footer is hidden.
   * @default false
   */
  hideFooterPagination: import_prop_types11.default.bool,
  /**
   * If `true`, the row count in the footer is hidden.
   * It has no effect if the pagination is enabled.
   * @default false
   */
  hideFooterRowCount: import_prop_types11.default.bool,
  /**
   * If `true`, the selected row count in the footer is hidden.
   * @default false
   */
  hideFooterSelectedRowCount: import_prop_types11.default.bool,
  /**
   * If `true`, the diacritics (accents) are ignored when filtering or quick filtering.
   * E.g. when filter value is `cafe`, the rows with `café` will be visible.
   * @default false
   */
  ignoreDiacritics: import_prop_types11.default.bool,
  /**
   * If `true`, the Data Grid will not use `valueFormatter` when exporting to CSV or copying to clipboard.
   * If an object is provided, you can choose to ignore the `valueFormatter` for CSV export or clipboard export.
   * @default false
   */
  ignoreValueFormatterDuringExport: import_prop_types11.default.oneOfType([import_prop_types11.default.shape({
    clipboardExport: import_prop_types11.default.bool,
    csvExport: import_prop_types11.default.bool
  }), import_prop_types11.default.bool]),
  /**
   * The initial state of the DataGridPremium.
   * The data in it is set in the state on initialization but isn't controlled.
   * If one of the data in `initialState` is also being controlled, then the control state wins.
   */
  initialState: import_prop_types11.default.object,
  /**
   * Callback fired when a cell is rendered, returns true if the cell is editable.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @returns {boolean} A boolean indicating if the cell is editable.
   */
  isCellEditable: import_prop_types11.default.func,
  /**
   * Determines if a group should be expanded after its creation.
   * This prop takes priority over the `defaultGroupingExpansionDepth` prop.
   * @param {GridGroupNode} node The node of the group to test.
   * @returns {boolean} A boolean indicating if the group is expanded.
   */
  isGroupExpandedByDefault: import_prop_types11.default.func,
  /**
   * Determines if a row can be selected.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @returns {boolean} A boolean indicating if the row is selectable.
   */
  isRowSelectable: import_prop_types11.default.func,
  /**
   * If `true`, moving the mouse pointer outside the grid before releasing the mouse button
   * in a column re-order action will not cause the column to jump back to its original position.
   * @default false
   */
  keepColumnPositionIfDraggedOutside: import_prop_types11.default.bool,
  /**
   * If `true`, the selection model will retain selected rows that do not exist.
   * Useful when using server side pagination and row selections need to be retained
   * when changing pages.
   * @default false
   */
  keepNonExistentRowsSelected: import_prop_types11.default.bool,
  /**
   * The label of the Data Grid.
   * If the `showToolbar` prop is `true`, the label will be displayed in the toolbar and applied to the `aria-label` attribute of the grid.
   * If the `showToolbar` prop is `false`, the label will not be visible but will be applied to the `aria-label` attribute of the grid.
   */
  label: import_prop_types11.default.string,
  /**
   * Used together with `dataSource` to enable lazy loading.
   * If enabled, the grid stops adding `paginationModel` to the data requests (`getRows`)
   * and starts sending `start` and `end` values depending on the loading mode and the scroll position.
   * @default false
   */
  lazyLoading: import_prop_types11.default.bool,
  /**
   * If positive, the Data Grid will throttle data source requests on rendered rows interval change.
   * @default 500
   */
  lazyLoadingRequestThrottleMs: import_prop_types11.default.number,
  /**
   * If `true`, displays the data in a list view.
   * Use in combination with `listViewColumn`.
   */
  listView: import_prop_types11.default.bool,
  /**
   * Definition of the column rendered when the `listView` prop is enabled.
   */
  listViewColumn: import_prop_types11.default.shape({
    align: import_prop_types11.default.oneOf(["center", "left", "right"]),
    cellClassName: import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.string]),
    display: import_prop_types11.default.oneOf(["flex", "text"]),
    field: import_prop_types11.default.string.isRequired,
    renderCell: import_prop_types11.default.func
  }),
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types11.default.bool,
  /**
   * Set the locale text of the Data Grid.
   * You can find all the translation keys supported in [the source](https://github.com/mui/mui-x/blob/HEAD/packages/x-data-grid/src/constants/localeTextConstants.ts) in the GitHub repository.
   */
  localeText: import_prop_types11.default.object,
  /**
   * Pass a custom logger in the components that implements the [[Logger]] interface.
   * @default console
   */
  logger: import_prop_types11.default.shape({
    debug: import_prop_types11.default.func.isRequired,
    error: import_prop_types11.default.func.isRequired,
    info: import_prop_types11.default.func.isRequired,
    warn: import_prop_types11.default.func.isRequired
  }),
  /**
   * Allows to pass the logging level or false to turn off logging.
   * @default "error" ("warn" in dev mode)
   */
  logLevel: import_prop_types11.default.oneOf(["debug", "error", "info", "warn", false]),
  /**
   * If set to "always", the multi-sorting is applied without modifier key.
   * Otherwise, the modifier key is required for multi-sorting to be applied.
   * @see See https://mui.com/x/react-data-grid/sorting/#multi-sorting
   * @default "withModifierKey"
   */
  multipleColumnsSortingMode: import_prop_types11.default.oneOf(["always", "withModifierKey"]),
  /**
   * Nonce of the inline styles for [Content Security Policy](https://www.w3.org/TR/2016/REC-CSP2-20161215/#script-src-the-nonce-attribute).
   */
  nonce: import_prop_types11.default.string,
  /**
   * Callback fired when the row grouping model changes.
   * @param {GridAggregationModel} model The aggregated columns.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onAggregationModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the AI Assistant active conversation index changes.
   * @param {number} aiAssistantActiveConversationIndex The new active conversation index.
   */
  onAiAssistantActiveConversationIndexChange: import_prop_types11.default.func,
  /**
   * Callback fired when the AI Assistant conversations change.
   * @param {Conversation[]} conversations The new AI Assistant conversations.
   */
  onAiAssistantConversationsChange: import_prop_types11.default.func,
  /**
   * Callback fired before the clipboard paste operation starts.
   * Use it to confirm or cancel the paste operation.
   * @param {object} params Params passed to the callback.
   * @param {string[][]} params.data The raw pasted data split by rows and cells.
   * @returns {Promise<any>} A promise that resolves to confirm the paste operation, and rejects to cancel it.
   */
  onBeforeClipboardPasteStart: import_prop_types11.default.func,
  /**
   * Callback fired when any cell is clicked.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellClick: import_prop_types11.default.func,
  /**
   * Callback fired when a double click event comes from a cell element.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellDoubleClick: import_prop_types11.default.func,
  /**
   * Callback fired when the cell turns to edit mode.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.
   */
  onCellEditStart: import_prop_types11.default.func,
  /**
   * Callback fired when the cell turns to view mode.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.
   */
  onCellEditStop: import_prop_types11.default.func,
  /**
   * Callback fired when a keydown event comes from a cell element.
   * @param {GridCellParams} params With all properties from [[GridCellParams]].
   * @param {MuiEvent<React.KeyboardEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellKeyDown: import_prop_types11.default.func,
  /**
   * Callback fired when the `cellModesModel` prop changes.
   * @param {GridCellModesModel} cellModesModel Object containing which cells are in "edit" mode.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellModesModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the selection state of one or multiple cells changes.
   * @param {GridCellSelectionModel} cellSelectionModel Object in the shape of [[GridCellSelectionModel]] containing the selected cells.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onCellSelectionModelChange: import_prop_types11.default.func,
  /**
   * Callback called when the data is copied to the clipboard.
   * @param {string} data The data copied to the clipboard.
   */
  onClipboardCopy: import_prop_types11.default.func,
  /**
   * Callback fired when the clipboard paste operation ends.
   */
  onClipboardPasteEnd: import_prop_types11.default.func,
  /**
   * Callback fired when the clipboard paste operation starts.
   */
  onClipboardPasteStart: import_prop_types11.default.func,
  /**
   * Callback fired when a click event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderClick: import_prop_types11.default.func,
  /**
   * Callback fired when a contextmenu event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   */
  onColumnHeaderContextMenu: import_prop_types11.default.func,
  /**
   * Callback fired when a double click event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderDoubleClick: import_prop_types11.default.func,
  /**
   * Callback fired when a mouse enter event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderEnter: import_prop_types11.default.func,
  /**
   * Callback fired when a mouse leave event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderLeave: import_prop_types11.default.func,
  /**
   * Callback fired when a mouseout event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderOut: import_prop_types11.default.func,
  /**
   * Callback fired when a mouseover event comes from a column header element.
   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnHeaderOver: import_prop_types11.default.func,
  /**
   * Callback fired when a column is reordered.
   * @param {GridColumnOrderChangeParams} params With all properties from [[GridColumnOrderChangeParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnOrderChange: import_prop_types11.default.func,
  /**
   * Callback fired while a column is being resized.
   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnResize: import_prop_types11.default.func,
  /**
   * Callback fired when the column visibility model changes.
   * @param {GridColumnVisibilityModel} model The new model.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnVisibilityModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the width of a column is changed.
   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onColumnWidthChange: import_prop_types11.default.func,
  /**
   * Callback fired when a data source request fails.
   * @param {GridGetRowsError | GridUpdateRowError} error The data source error object.
   */
  onDataSourceError: import_prop_types11.default.func,
  /**
   * Callback fired when the density changes.
   * @param {GridDensity} density New density value.
   */
  onDensityChange: import_prop_types11.default.func,
  /**
   * Callback fired when the detail panel of a row is opened or closed.
   * @param {GridRowId[]} ids The ids of the rows which have the detail panel open.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onDetailPanelExpandedRowIdsChange: import_prop_types11.default.func,
  /**
   * Callback fired when the state of the Excel export changes.
   * @param {string} inProgress Indicates if the task is in progress.
   */
  onExcelExportStateChange: import_prop_types11.default.func,
  /**
   * Callback fired when rowCount is set and the next batch of virtualized rows is rendered.
   * @param {GridFetchRowsParams} params With all properties from [[GridFetchRowsParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#viewport-loading Server-side data-Viewport loading} instead.
   */
  onFetchRows: import_prop_types11.default.func,
  /**
   * Callback fired when the Filter model changes before the filters are applied.
   * @param {GridFilterModel} model With all properties from [[GridFilterModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onFilterModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the menu is closed.
   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onMenuClose: import_prop_types11.default.func,
  /**
   * Callback fired when the menu is opened.
   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onMenuOpen: import_prop_types11.default.func,
  /**
   * Callback fired when the pagination meta has changed.
   * @param {GridPaginationMeta} paginationMeta Updated pagination meta.
   */
  onPaginationMetaChange: import_prop_types11.default.func,
  /**
   * Callback fired when the pagination model has changed.
   * @param {GridPaginationModel} model Updated pagination model.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPaginationModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the pinned columns have changed.
   * @param {GridPinnedColumnFields} pinnedColumns The changed pinned columns.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPinnedColumnsChange: import_prop_types11.default.func,
  /**
   * Callback fired when the pivot active state changes.
   * @param {boolean} isPivotActive Whether the data grid is in pivot mode.
   */
  onPivotActiveChange: import_prop_types11.default.func,
  /**
   * Callback fired when the pivot model changes.
   * @param {GridPivotModel} pivotModel The new pivot model.
   */
  onPivotModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the pivot side panel open state changes.
   * @param {boolean} pivotPanelOpen Whether the pivot side panel is visible.
   * @deprecated Use the `sidebarOpen` and `sidebarClose` events or corresponding event handlers `onSidebarOpen()` and `onSidebarClose()` instead.
   */
  onPivotPanelOpenChange: import_prop_types11.default.func,
  /**
   * Callback fired when the preferences panel is closed.
   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPreferencePanelClose: import_prop_types11.default.func,
  /**
   * Callback fired when the preferences panel is opened.
   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onPreferencePanelOpen: import_prop_types11.default.func,
  /**
   * Callback called when `processRowUpdate()` throws an error or rejects.
   * @param {any} error The error thrown.
   */
  onProcessRowUpdateError: import_prop_types11.default.func,
  /**
   * The function to be used to process the prompt.
   * @param {string} prompt The prompt to be processed.
   * @param {string} promptContext The prompt context.
   * @param {string} conversationId The id of the conversation the prompt is part of. If not passed, prompt response will return a new conversation id that can be used to continue the newly started conversation.
   * @returns {Promise<PromptResponse>} The prompt response.
   */
  onPrompt: import_prop_types11.default.func,
  /**
   * Callback fired when the Data Grid is resized.
   * @param {ElementSize} containerSize With all properties from [[ElementSize]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onResize: import_prop_types11.default.func,
  /**
   * Callback fired when a row is clicked.
   * Not called if the target clicked is an interactive element added by the built-in columns.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowClick: import_prop_types11.default.func,
  /**
   * Callback fired when the row count has changed.
   * @param {number} count Updated row count.
   */
  onRowCountChange: import_prop_types11.default.func,
  /**
   * Callback fired when a double click event comes from a row container element.
   * @param {GridRowParams} params With all properties from [[RowParams]].
   * @param {MuiEvent<React.MouseEvent>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowDoubleClick: import_prop_types11.default.func,
  /**
   * Callback fired when the row turns to edit mode.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.
   */
  onRowEditStart: import_prop_types11.default.func,
  /**
   * Callback fired when the row turns to view mode.
   * @param {GridRowParams} params With all properties from [[GridRowParams]].
   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.
   */
  onRowEditStop: import_prop_types11.default.func,
  /**
   * Callback fired when the row grouping model changes.
   * @param {GridRowGroupingModel} model Columns used as grouping criteria.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowGroupingModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the `rowModesModel` prop changes.
   * @param {GridRowModesModel} rowModesModel Object containing which rows are in "edit" mode.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowModesModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when a row is being reordered.
   * @param {GridRowOrderChangeParams} params With all properties from [[GridRowOrderChangeParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowOrderChange: import_prop_types11.default.func,
  /**
   * Callback fired when the selection state of one or multiple rows changes.
   * @param {GridRowSelectionModel} rowSelectionModel With all the row ids [[GridSelectionModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onRowSelectionModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when scrolling to the bottom of the grid viewport.
   * @param {GridRowScrollEndParams} params With all properties from [[GridRowScrollEndParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#infinite-loading Server-side data-Infinite loading} instead.
   */
  onRowsScrollEnd: import_prop_types11.default.func,
  /**
   * Callback fired when the sidebar is closed.
   * @param {GridSidebarParams} params With all properties from [[GridSidebarParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onSidebarClose: import_prop_types11.default.func,
  /**
   * Callback fired when the sidebar is opened.
   * @param {GridSidebarParams} params With all properties from [[GridSidebarParams]].
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onSidebarOpen: import_prop_types11.default.func,
  /**
   * Callback fired when the sort model changes before a column is sorted.
   * @param {GridSortModel} model With all properties from [[GridSortModel]].
   * @param {GridCallbackDetails} details Additional details for this callback.
   */
  onSortModelChange: import_prop_types11.default.func,
  /**
   * Callback fired when the state of the Data Grid is updated.
   * @param {GridState} state The new state.
   * @param {MuiEvent<{}>} event The event object.
   * @param {GridCallbackDetails} details Additional details for this callback.
   * @ignore - do not document.
   */
  onStateChange: import_prop_types11.default.func,
  /**
   * Select the pageSize dynamically using the component UI.
   * @default [25, 50, 100]
   */
  pageSizeOptions: import_prop_types11.default.arrayOf(import_prop_types11.default.oneOfType([import_prop_types11.default.number, import_prop_types11.default.shape({
    label: import_prop_types11.default.string.isRequired,
    value: import_prop_types11.default.number.isRequired
  })]).isRequired),
  /**
   * If `true`, pagination is enabled.
   * @default false
   */
  pagination: import_prop_types11.default.bool,
  /**
   * The extra information about the pagination state of the Data Grid.
   * Only applicable with `paginationMode="server"`.
   */
  paginationMeta: import_prop_types11.default.shape({
    hasNextPage: import_prop_types11.default.bool
  }),
  /**
   * Pagination can be processed on the server or client-side.
   * Set it to 'client' if you would like to handle the pagination on the client-side.
   * Set it to 'server' if you would like to handle the pagination on the server-side.
   * @default "client"
   */
  paginationMode: import_prop_types11.default.oneOf(["client", "server"]),
  /**
   * The pagination model of type [[GridPaginationModel]] which refers to current `page` and `pageSize`.
   */
  paginationModel: import_prop_types11.default.shape({
    page: import_prop_types11.default.number.isRequired,
    pageSize: import_prop_types11.default.number.isRequired
  }),
  /**
   * The column fields to display pinned to left or right.
   */
  pinnedColumns: import_prop_types11.default.object,
  /**
   * Rows data to pin on top or bottom.
   */
  pinnedRows: import_prop_types11.default.shape({
    bottom: import_prop_types11.default.arrayOf(import_prop_types11.default.object),
    top: import_prop_types11.default.arrayOf(import_prop_types11.default.object)
  }),
  /**
   * If `true`, the data grid will show data in pivot mode using the `pivotModel`.
   * @default false
   */
  pivotActive: import_prop_types11.default.bool,
  /**
   * The column definition overrides for the columns generated by the pivoting feature.
   * @param {string} originalColumnField The field of the original column.
   * @param {string[]} columnGroupPath The path of the column groups the column belongs to.
   * @returns {Partial<GridPivotingColDefOverrides> | undefined | void} The column definition overrides.
   * @default undefined
   */
  pivotingColDef: import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.shape({
    align: import_prop_types11.default.oneOf(["center", "left", "right"]),
    cellClassName: import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.string]),
    description: import_prop_types11.default.string,
    display: import_prop_types11.default.oneOf(["flex", "text"]),
    flex: import_prop_types11.default.number,
    headerAlign: import_prop_types11.default.oneOf(["center", "left", "right"]),
    headerClassName: import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.string]),
    headerName: import_prop_types11.default.string,
    maxWidth: import_prop_types11.default.number,
    minWidth: import_prop_types11.default.number,
    resizable: import_prop_types11.default.bool,
    sortingOrder: import_prop_types11.default.arrayOf(import_prop_types11.default.oneOf(["asc", "desc"])),
    width: import_prop_types11.default.number
  })]),
  /**
   * The pivot model of the grid.
   * Will be used to generate the pivot data.
   * In case of `pivotActive` being `false`, the pivot model is still used to populate the pivot panel.
   */
  pivotModel: import_prop_types11.default.shape({
    columns: import_prop_types11.default.arrayOf(import_prop_types11.default.object).isRequired,
    rows: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
      field: import_prop_types11.default.string.isRequired,
      hidden: import_prop_types11.default.bool
    })).isRequired,
    values: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
      aggFunc: import_prop_types11.default.string.isRequired,
      field: import_prop_types11.default.string.isRequired,
      hidden: import_prop_types11.default.bool
    })).isRequired
  }),
  /**
   * If `true`, the pivot side panel is visible.
   * @default false
   * @deprecated Use `initialState.sidebar.open` instead.
   */
  pivotPanelOpen: import_prop_types11.default.bool,
  /**
   * Callback called before updating a row with new values in the row and cell editing.
   * @template R
   * @param {R} newRow Row object with the new values.
   * @param {R} oldRow Row object with the old values.
   * @param {{ rowId: GridRowId }} params Additional parameters.
   * @returns {Promise<R> | R} The final values to update the row.
   */
  processRowUpdate: import_prop_types11.default.func,
  /**
   * The milliseconds throttle delay for resizing the grid.
   * @default 60
   */
  resizeThrottleMs: import_prop_types11.default.number,
  /**
   * Row region in pixels to render before/after the viewport
   * @default 150
   */
  rowBufferPx: import_prop_types11.default.number,
  /**
   * Set the total number of rows, if it is different from the length of the value `rows` prop.
   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.
   * Only works with `paginationMode="server"`, ignored when `paginationMode="client"`.
   */
  rowCount: import_prop_types11.default.number,
  /**
   * If `single`, all the columns that are grouped are represented in the same grid column.
   * If `multiple`, each column that is grouped is represented in its own grid column.
   * @default 'single'
   */
  rowGroupingColumnMode: import_prop_types11.default.oneOf(["multiple", "single"]),
  /**
   * Set the row grouping model of the grid.
   */
  rowGroupingModel: import_prop_types11.default.arrayOf(import_prop_types11.default.string),
  /**
   * Sets the height in pixel of a row in the Data Grid.
   * @default 52
   */
  rowHeight: import_prop_types11.default.number,
  /**
   * Controls the modes of the rows.
   */
  rowModesModel: import_prop_types11.default.object,
  /**
   * If `true`, the reordering of rows is enabled.
   * @default false
   */
  rowReordering: import_prop_types11.default.bool,
  /**
   * Set of rows of type [[GridRowsProp]].
   * @default []
   */
  rows: import_prop_types11.default.arrayOf(import_prop_types11.default.object),
  /**
   * If `false`, the row selection mode is disabled.
   * @default true
   */
  rowSelection: import_prop_types11.default.bool,
  /**
   * Sets the row selection model of the Data Grid.
   */
  rowSelectionModel: import_prop_types11.default.shape({
    ids: import_prop_types11.default.instanceOf(Set).isRequired,
    type: import_prop_types11.default.oneOf(["exclude", "include"]).isRequired
  }),
  /**
   * When `rowSelectionPropagation.descendants` is set to `true`.
   * - Selecting a parent selects all its filtered descendants automatically.
   * - Deselecting a parent row deselects all its filtered descendants automatically.
   *
   * When `rowSelectionPropagation.parents` is set to `true`
   * - Selecting all the filtered descendants of a parent selects the parent automatically.
   * - Deselecting a descendant of a selected parent deselects the parent automatically.
   *
   * Works with tree data and row grouping on the client-side only.
   * @default { parents: true, descendants: true }
   */
  rowSelectionPropagation: import_prop_types11.default.shape({
    descendants: import_prop_types11.default.bool,
    parents: import_prop_types11.default.bool
  }),
  /**
   * Loading rows can be processed on the server or client-side.
   * Set it to 'client' if you would like enable infnite loading.
   * Set it to 'server' if you would like to enable lazy loading.
   * @default "client"
   * @deprecated Use the {@link https://mui.com/x/react-data-grid/server-side-data/lazy-loading/#viewport-loading Server-side data-Viewport loading} instead.
   */
  rowsLoadingMode: import_prop_types11.default.oneOf(["client", "server"]),
  /**
   * Sets the type of space between rows added by `getRowSpacing`.
   * @default "margin"
   */
  rowSpacingType: import_prop_types11.default.oneOf(["border", "margin"]),
  /**
   * If `true`, the Data Grid will auto span the cells over the rows having the same value.
   * @default false
   */
  rowSpanning: import_prop_types11.default.bool,
  /**
   * Override the height/width of the Data Grid inner scrollbar.
   */
  scrollbarSize: import_prop_types11.default.number,
  /**
   * Set the area in `px` at the bottom of the grid viewport where onRowsScrollEnd is called.
   * If combined with `lazyLoading`, it defines the area where the next data request is triggered.
   * @default 80
   */
  scrollEndThreshold: import_prop_types11.default.number,
  /**
   * If `true`, vertical borders will be displayed between cells.
   * @default false
   */
  showCellVerticalBorder: import_prop_types11.default.bool,
  /**
   * If `true`, vertical borders will be displayed between column header items.
   * @default false
   */
  showColumnVerticalBorder: import_prop_types11.default.bool,
  /**
   * If `true`, the toolbar is displayed.
   * @default false
   */
  showToolbar: import_prop_types11.default.bool,
  /**
   * Overridable components props dynamically passed to the component at rendering.
   */
  slotProps: import_prop_types11.default.object,
  /**
   * Overridable components.
   */
  slots: import_prop_types11.default.object,
  /**
   * Sorting can be processed on the server or client-side.
   * Set it to 'client' if you would like to handle sorting on the client-side.
   * Set it to 'server' if you would like to handle sorting on the server-side.
   * @default "client"
   */
  sortingMode: import_prop_types11.default.oneOf(["client", "server"]),
  /**
   * The order of the sorting sequence.
   * @default ['asc', 'desc', null]
   */
  sortingOrder: import_prop_types11.default.arrayOf(import_prop_types11.default.oneOf(["asc", "desc"])),
  /**
   * Set the sort model of the Data Grid.
   */
  sortModel: import_prop_types11.default.arrayOf(import_prop_types11.default.shape({
    field: import_prop_types11.default.string.isRequired,
    sort: import_prop_types11.default.oneOf(["asc", "desc"])
  })),
  /**
   * The function is used to split the pasted text into rows and cells.
   * @param {string} text The text pasted from the clipboard.
   * @param {string} delimiter The delimiter used to split the text. Default is the tab character and can be set with the `clipboardCopyCellDelimiter` prop.
   * @returns {string[][] | null} A 2D array of strings. The first dimension is the rows, the second dimension is the columns.
   * @default (pastedText, delimiter = '\t') => { const text = pastedText.replace(/\r?\n$/, ''); return text.split(/\r\n|\n|\r/).map((row) => row.split(delimiter)); }
   */
  splitClipboardPastedText: import_prop_types11.default.func,
  style: import_prop_types11.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types11.default.oneOfType([import_prop_types11.default.arrayOf(import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.object, import_prop_types11.default.bool])), import_prop_types11.default.func, import_prop_types11.default.object]),
  /**
   * If positive, the Data Grid will throttle updates coming from `apiRef.current.updateRows` and `apiRef.current.setRows`.
   * It can be useful if you have a high update rate but do not want to do heavy work like filtering / sorting or rendering on each  individual update.
   * @default 0
   */
  throttleRowsMs: import_prop_types11.default.number,
  /**
   * If `true`, the rows will be gathered in a tree structure according to the `getTreeDataPath` prop.
   * @default false
   */
  treeData: import_prop_types11.default.bool,
  /**
   * If `true`, the Data Grid enables column virtualization when `getRowHeight` is set to `() => 'auto'`.
   * By default, column virtualization is disabled when dynamic row height is enabled to measure the row height correctly.
   * For datasets with a large number of columns, this can cause performance issues.
   * The downside of enabling this prop is that the row height will be estimated based the cells that are currently rendered, which can cause row height change when scrolling horizontally.
   * @default false
   */
  virtualizeColumnsWithAutoRowHeight: import_prop_types11.default.bool
} : void 0;
var DataGridPremium2 = React71.memo(DataGridPremiumRaw);
if (true) DataGridPremium2.displayName = "DataGridPremium";
export {
  AiAssistantPanelTrigger,
  COMFORTABLE_DENSITY_FACTOR,
  COMPACT_DENSITY_FACTOR,
  ColumnsPanelTrigger,
  DATA_GRID_PREMIUM_PROPS_DEFAULT_VALUES,
  DEFAULT_GRID_AUTOSIZE_OPTIONS,
  DEFAULT_GRID_COL_TYPE_KEY,
  DataGrid,
  DataGridPremium2 as DataGridPremium,
  DataGridPro,
  EMPTY_PINNED_COLUMN_FIELDS,
  EMPTY_RENDER_CONTEXT,
  ExportCsv,
  ExportExcel,
  ExportPrint,
  FilterPanelTrigger,
  GRID_ACTIONS_COLUMN_TYPE,
  GRID_ACTIONS_COL_DEF,
  GRID_AGGREGATION_FUNCTIONS,
  GRID_AGGREGATION_ROOT_FOOTER_ROW_ID,
  GRID_BOOLEAN_COL_DEF,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GRID_CHECKBOX_SELECTION_FIELD,
  GRID_COLUMN_MENU_SLOTS_PREMIUM as GRID_COLUMN_MENU_SLOTS,
  GRID_COLUMN_MENU_SLOT_PROPS_PREMIUM as GRID_COLUMN_MENU_SLOT_PROPS,
  GRID_DATETIME_COL_DEF,
  GRID_DATE_COL_DEF,
  GRID_DEFAULT_LOCALE_TEXT,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GRID_EXPERIMENTAL_ENABLED,
  GRID_NUMERIC_COL_DEF,
  GRID_REORDER_COL_DEF,
  GRID_ROOT_GROUP_ID,
  GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD,
  GRID_SINGLE_SELECT_COL_DEF,
  GRID_STRING_COL_DEF,
  GRID_TREE_DATA_GROUPING_FIELD,
  GridActionsCell,
  GridActionsCellItem,
  GridAddIcon,
  GridAiAssistantPanel,
  GridApiContext,
  GridArrowDownwardIcon,
  GridArrowUpwardIcon,
  GridAssistantIcon,
  GridVirtualScroller as GridBody,
  GridBooleanCell,
  MemoizedGridCell as GridCell,
  GridCellCheckboxForwardRef,
  GridCellCheckboxRenderer,
  GridCellEditStartReasons,
  GridCellEditStopReasons,
  GridCellModes,
  GridCheckCircleIcon,
  GridCheckIcon,
  GridClearIcon,
  GridCloseIcon,
  GridColumnHeaderFilterIconButtonWrapped as GridColumnHeaderFilterIconButton,
  Memoized2 as GridColumnHeaderItem,
  GridColumnHeaderMenu,
  GridColumnHeaderSeparator,
  GridColumnHeaderSeparatorSides,
  GridColumnHeaderSortIcon,
  GridColumnHeaderTitle,
  GridColumnHeaders,
  GridColumnIcon,
  GridPremiumColumnMenu as GridColumnMenu,
  GridColumnMenuAggregationItem,
  GridColumnMenuColumnsItem,
  GridColumnMenuContainer,
  GridColumnMenuFilterItem,
  GridColumnMenuGroupingItem,
  GridColumnMenuHideItem,
  GridColumnMenuManageItem,
  GridColumnMenuPinningItem,
  GridColumnMenuSortItem,
  GridColumnsManagement,
  GridColumnsPanel,
  GridContextProvider,
  GridCsvExportMenuItem,
  GridDataSourceCacheDefault,
  GridDeleteForeverIcon,
  GridDeleteIcon,
  GridDetailPanelToggleCell,
  GridDownloadIcon,
  GridDragIcon,
  GridEditBooleanCell,
  GridEditDateCell,
  GridEditInputCell,
  GridEditModes,
  GridEditSingleSelectCell,
  GridEmptyPivotOverlay,
  GridExcelExportMenuItem,
  GridExpandLessIcon,
  GridExpandMoreIcon,
  GridFilterAltIcon,
  GridFilterForm,
  GridFilterInputBoolean,
  GridFilterInputDate,
  GridFilterInputMultipleSingleSelect,
  GridFilterInputMultipleValue,
  GridFilterInputSingleSelect,
  GridFilterInputValue,
  GridFilterListIcon,
  GridFilterPanel,
  GridFooter,
  GridFooterContainer,
  GridFooterPlaceholder,
  GridFunctionsIcon,
  GridGenericColumnMenu,
  GridGetRowsError,
  GridGroupWorkIcon,
  GridHeader,
  GridHeaderCheckbox,
  Memoized3 as GridHeaderFilterCell,
  GridHeaderFilterMenu,
  GridHeaderFilterMenuContainer,
  GridHistoryIcon,
  GridKeyboardArrowRight,
  GridLoadIcon,
  GridLoadingOverlay,
  GridLogicOperator,
  GridMenu,
  GridMenuIcon,
  GridMicIcon,
  GridMicOffIcon,
  GridMoreVertIcon,
  GridMoveToBottomIcon,
  GridMoveToTopIcon,
  GridNoColumnsOverlay,
  GridNoRowsOverlay,
  GridOverlay,
  GridPagination,
  GridPanel,
  GridPanelContent,
  GridPanelFooter,
  GridPanelHeader,
  GridPanelWrapper,
  GridPinnedColumnPosition,
  GridPivotIcon,
  GridPivotPanel,
  GridPortalWrapper,
  GridPreferencePanelsValue,
  GridPrintExportMenuItem,
  GridPromptIcon,
  GridPushPinLeftIcon,
  GridPushPinRightIcon,
  GridRemoveIcon,
  GridRerunIcon,
  MemoizedGridRoot as GridRoot,
  MemoizedGridRow as GridRow,
  GridRowCount,
  GridRowEditStartReasons,
  GridRowEditStopReasons,
  GridRowModes,
  GridRowReorderCell,
  GridSearchIcon,
  GridSelectedRowCount,
  GridSendIcon,
  GridSeparatorIcon,
  GridShadowScrollArea,
  GridSidebarValue,
  GridSignature,
  Memoized as GridSkeletonCell,
  GridTableRowsIcon,
  GridToolbar2 as GridToolbar,
  GridToolbarColumnsButton,
  GridToolbarContainer,
  GridToolbarDensitySelector,
  GridToolbarExport,
  GridToolbarExportContainer,
  GridToolbarFilterButton,
  GridToolbarQuickFilter,
  GridTreeDataGroupingCell,
  GridTripleDotsVerticalIcon,
  GridUpdateRowError,
  GridViewColumnIcon,
  GridViewHeadlineIcon,
  GridViewStreamIcon,
  GridVisibilityOffIcon,
  GridWorkspacesIcon,
  IS_SPEECH_RECOGNITION_SUPPORTED,
  PivotPanelTrigger,
  PromptField,
  PromptFieldControl,
  PromptFieldRecord,
  PromptFieldSend,
  QuickFilter,
  QuickFilterClear,
  QuickFilterControl,
  QuickFilterTrigger,
  Toolbar,
  ToolbarButton,
  checkGridRowIdIsValid,
  createRowSelectionManager,
  getAggregationFooterRowIdFromGroupId,
  getDataGridUtilityClass,
  getDefaultGridFilterModel,
  getGridBooleanOperators,
  getGridDateOperators,
  getGridDefaultColumnTypes,
  getGridNumericOperators,
  getGridNumericQuickFilterFn,
  getGridSingleSelectOperators,
  getGridStringOperators,
  getGridStringQuickFilterFn,
  getGroupRowIdFromPath,
  getRowGroupingFieldFromGroupingCriteria,
  gridAggregationLookupSelector,
  gridAggregationModelSelector,
  gridAggregationStateSelector,
  gridClasses,
  gridColumnDefinitionsSelector,
  gridColumnFieldsSelector,
  gridColumnGroupingSelector,
  gridColumnGroupsHeaderMaxDepthSelector,
  gridColumnGroupsHeaderStructureSelector,
  gridColumnGroupsLookupSelector,
  gridColumnGroupsUnwrappedModelSelector,
  gridColumnLookupSelector,
  gridColumnMenuSelector,
  gridColumnPositionsSelector,
  gridColumnReorderDragColSelector,
  gridColumnReorderSelector,
  gridColumnResizeSelector,
  gridColumnVisibilityModelSelector,
  gridColumnsStateSelector,
  gridColumnsTotalWidthSelector,
  gridDataRowIdsSelector,
  gridDateComparator,
  gridDateFormatter,
  gridDateTimeFormatter,
  gridDensityFactorSelector,
  gridDensitySelector,
  gridDetailPanelExpandedRowIdsSelector,
  gridDetailPanelExpandedRowsContentCacheSelector,
  gridDimensionsSelector,
  gridEditCellStateSelector,
  gridEditRowsStateSelector,
  gridExpandedRowCountSelector,
  gridExpandedSortedRowEntriesSelector,
  gridExpandedSortedRowIdsSelector,
  gridFilterActiveItemsLookupSelector,
  gridFilterActiveItemsSelector,
  gridFilterModelSelector,
  gridFilterableColumnDefinitionsSelector,
  gridFilterableColumnLookupSelector,
  gridFilteredDescendantCountLookupSelector,
  gridFilteredDescendantRowCountSelector,
  gridFilteredRowCountSelector,
  gridFilteredRowsLookupSelector,
  gridFilteredSortedRowEntriesSelector,
  gridFilteredSortedRowIdsSelector,
  gridFilteredSortedTopLevelRowEntriesSelector,
  gridFilteredTopLevelRowCountSelector,
  gridFocusCellSelector,
  gridFocusColumnGroupHeaderSelector,
  gridFocusColumnHeaderFilterSelector,
  gridFocusColumnHeaderSelector,
  gridFocusStateSelector,
  gridHasColSpanSelector,
  gridHeaderFilteringEditFieldSelector,
  gridHeaderFilteringEnabledSelector,
  gridHeaderFilteringMenuSelector,
  gridHeaderFilteringStateSelector,
  gridListColumnSelector,
  gridListViewSelector,
  gridNumberComparator,
  gridPageCountSelector,
  gridPageSelector,
  gridPageSizeSelector,
  gridPaginatedVisibleSortedGridRowEntriesSelector,
  gridPaginatedVisibleSortedGridRowIdsSelector,
  gridPaginationEnabledClientSideSelector,
  gridPaginationMetaSelector,
  gridPaginationModelSelector,
  gridPaginationRowCountSelector,
  gridPaginationRowRangeSelector,
  gridPaginationSelector,
  gridPanelClasses,
  gridPinnedColumnsSelector,
  gridPreferencePanelStateSelector,
  gridQuickFilterValuesSelector,
  gridRenderContextColumnsSelector,
  gridRenderContextSelector,
  gridResizingColumnFieldSelector,
  gridRowCountSelector,
  gridRowGroupingModelSelector,
  gridRowGroupingNameSelector,
  gridRowGroupingSanitizedModelSelector,
  gridRowIdSelector,
  gridRowIsEditingSelector,
  gridRowMaximumTreeDepthSelector,
  gridRowNodeSelector,
  gridRowSelectionCountSelector,
  gridRowSelectionIdsSelector,
  gridRowSelectionManagerSelector,
  gridRowSelectionStateSelector,
  gridRowTreeDepthsSelector,
  gridRowTreeSelector,
  gridRowsLoadingSelector,
  gridRowsLookupSelector,
  gridRowsMetaSelector,
  gridSidebarContentSelector,
  gridSidebarOpenSelector,
  gridSidebarStateSelector,
  gridSortColumnLookupSelector,
  gridSortModelSelector,
  gridSortedRowEntriesSelector,
  gridSortedRowIdsSelector,
  gridStringOrNumberComparator,
  gridTabIndexCellSelector,
  gridTabIndexColumnGroupHeaderSelector,
  gridTabIndexColumnHeaderFilterSelector,
  gridTabIndexColumnHeaderSelector,
  gridTabIndexStateSelector,
  gridTopLevelRowCountSelector,
  gridVirtualizationColumnEnabledSelector,
  gridVirtualizationEnabledSelector,
  gridVirtualizationRowEnabledSelector,
  gridVirtualizationSelector,
  gridVisibleColumnDefinitionsSelector,
  gridVisibleColumnFieldsSelector,
  gridVisiblePinnedColumnDefinitionsSelector,
  gridVisibleRowsLookupSelector,
  gridVisibleRowsSelector,
  isAutogeneratedRow,
  isGroupingColumn,
  isLeaf,
  renderActionsCell,
  renderBooleanCell,
  renderEditBooleanCell,
  renderEditDateCell,
  renderEditInputCell,
  renderEditSingleSelectCell,
  renderRowReorderCell,
  setupExcelExportWebWorker,
  gridDefaultPromptResolver as unstable_gridDefaultPromptResolver,
  unstable_resetCleanupTracking,
  useGridPivoting as unstable_useGridPivoting,
  useFirstRender,
  useGridApiContext2 as useGridApiContext,
  useGridApiMethod,
  useGridApiRef2 as useGridApiRef,
  useGridEvent,
  useGridEventPriority,
  useGridLogger,
  useGridNativeEventListener,
  useGridRootProps2 as useGridRootProps,
  useGridSelector,
  useGridVirtualization,
  useKeepGroupedColumnsHidden,
  useOnMount,
  useRunOnce,
  useRunOncePerLoop,
  virtualizationStateInitializer
};
/*! Bundled license information:

@mui/x-data-grid-premium/esm/index.js:
  (**
   * @mui/x-data-grid-premium v8.11.1
   *
   * @license SEE LICENSE IN LICENSE
   * This source code is licensed under the SEE LICENSE IN LICENSE license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=@mui_x-data-grid-premium.js.map
