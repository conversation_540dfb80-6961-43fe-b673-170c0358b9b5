import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsUsersGetUserEndpoint: build.query<
      AtApiServiceEndpointsUsersGetUserEndpointApiResponse,
      AtApiServiceEndpointsUsersGetUserEndpointApiArg
    >({
      query: (queryArg) => ({ url: `/api/v2/users/${queryArg.userId}` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as usersGeneratedApi };
export type AtApiServiceEndpointsUsersGetUserEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsUsersModelsGetUserResponse;
export type AtApiServiceEndpointsUsersGetUserEndpointApiArg = {
  userId: string;
};
export type AtApiServiceEndpointsUsersModelsGetUserResponse = {
  name?: string;
  userId?: string;
};
export const { useAtApiServiceEndpointsUsersGetUserEndpointQuery } =
  injectedRtkApi;
